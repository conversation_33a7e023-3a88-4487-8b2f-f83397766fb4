# How automated releases work for the Experience monorepo

## The purpose of this article

This article will document the current automated release process for the Experience monorepo.

## Trigger

For anyone who wants to understand how our software is automatically released each day.

## Stages of the automated release process

### Pipeline

- On each commit to main, a pipeline run is triggered in [GitHub Actions](https://github.com/Pod-Point/experience/actions/workflows/pipeline.yml).
- The pipeline will first build, unit test and lint each application.
- It will then deploy each application to the dev environment, run e2e tests and smoke tests.
- If the deployment to dev and tests are successful, the pipeline will stop at this stage.

### Release to staging

- Each morning, a release is created by [GitHub Actions](https://github.com/Pod-Point/experience/actions/workflows/release.yml).
  - The workflow for releases is triggered twice as a workaround for GitHub Actions lack of support for daylight saving time. However only a single release will be created each day.
- The automated release will trigger a new pipeline run which will run the same steps as a regular pipeline but will continue to deploy to the staging environment and run smoke tests.
- If the deployment to staging and tests are successful, the pipeline will pause at this stage.

### Release to production

- The production environment is configured with a deployment protection rule using a wait timer.
  - This allows the teams some time to detect any regressions which might have been introduced and abort the pipeline.
  - The deployment protection rules can be configured [here](https://github.com/Pod-Point/experience/settings/environments/577930747/edit).
- When the wait timer completes, the pipeline will continue to deploy to production and run smoke tests.
- The wait timer can be bypassed manually if a release needs to be deployed to production sooner.
