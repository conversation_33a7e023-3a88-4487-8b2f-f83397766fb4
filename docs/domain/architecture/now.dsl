workspace "Experience Domain" "This is a model of the experience domain as it currently is." {

    !docs .

    model {
        driver = person "Driver" {
          description "A user who drives an EV."
          tags "Customer,Mobile"
        }
        installer = person "Installer" {
          description "A person who installs Pod Point chargers."
          tags "Customer,Mobile"
        }
        siteManager = person "Site Manager" {
          description "A user who manages a Commercial EV Charging solution."
          tags "Customer,Commercial"
        }

        group "Pod Point" {
            # domain systems
            experienceDomain = softwareSystem "Experience Domain Systems" {
              description "Provides tailored user experiences for personal and commercial EV charger customers."
              tags "Experience"
            }
            networkDomain = softwareSystem "Network Domain Systems" {
              description "Manages network charging including connectivity, platform, maintenance and grid interactions."
              tags "Network"

              assetsConfigurationService = container "Assets Configuration Service" {
                description "Provides APIs for managing network assets (e.g. charging station, PCB)."
                tags "Network"
              }

              arch1 = container "Arch1 Service" {
                description "Manages Arch1 charge points current configuration and relay communications."
                tags "Network"
              }

              connectivityService = container "Connectivity Service" {
                description "Provides connectivity status for chargers."
                tags "Network"
              }

              competitionsService = container "Competitions Service" {
                description "."
                tags "Network"
              }

              diagnosticsService = container "Diagnostics Service" {
                description "Provides diagnostic and security event logs from chargers."
                tags "Network"
              }

              firmwareUpgrade = container "Firmware Upgrade" {
                description "Provides firmware version information from chargers."
                tags "Network"
              }

              mis = container "MIS" {
                description "System used to access, monitor, and control Pod Point units."
                tags "Network"
              }

              ocppService = container "OCPP Service" {
                description "Communicate with OCPP charge points."
                tags "Network"
              }

              podadminDb = container "Podadmin Database" {
                description "Stores and manages majority of business data."
                technology "Amazon Aurora MySQL"
                tags "Shared,AuroraMySQL"
              }

              smartChargingService = container "Smart Charging Service" {
                description "."
                tags "Network"
              }
            }
            ownershipDomain = softwareSystem "Ownership Domain Systems" {
              description "Supports onboarding and management of new and existing customers."
              tags "Ownership"

              orderingTool = container "Ordering Tool" {
                description "Service to facilitate customer order pre and post-install."
                tags "Ownership"
              }

              unitLinkVerificationApp = container "Unit Link Verification App" {
                description "Application to help customers link their chargers to their account."
                tags "Ownership"
              }
            }

            # subdomain systems
            commercial = softwareSystem "Commercial System" {
              description "Collection of web applications used to administer commercial charging."
              tags "Commercial"

              destinationSiteAdminApi = container "Destination Site Admin API" {
                description "Backend for frontend (BFF) for the Destination Site Admin Webapp."
                technology "NestJS"
                tags "Commercial,NestJS"
              }
              destinationSiteAdminWebapp = container "Destination Site Admin Webapp" {
                description "Externally facing web application used by Site Managers."
                technology "Next.js"
                tags "Commercial,Next.js"
              }
            }

            dataPlatform = softwareSystem "Experience Data Platform System" {
              description "Provides common services and data to user facing subdomains within Experience, manages integrations with other domain systems and data."
              tags "DataPlatform"

              api3 = container "API3 (Network API)" {
                description "API used by mobile clients, MIS and others."
                technology "PHP, Laravel & AWS Fargate"
                tags "DataPlatform,PHP,Fargate"
                url https://podpoint.atlassian.net/wiki/spaces/ED/pages/3841589373/API3+ownership+proposal
              }
              dataPlatformApi = container "Data Platform API" {
                description "Aggregation and orchestration for experience data, and shared capabilities."
                technology "Go, Goa & AWS Fargate"
                tags "DataPlatform,Golang,Fargate"
              }
              dataPlatformPodadminDatafeed = container "Podadmin Datafeed" {
                description "Near real-time replication of podadmin data from MySQL master."
                technology "AWS Databse Migration Service"
                tags "DataPlatform,DatabaseMigrationService"
              }
              dataPlatformVpcEndpoint = container "Data Platform VPC Endpoints" {
                description "Provide VPC endpoints to Data Platform VPC."
                technology "AWS Interface VPC Endpoint"
                tags "DataPlatform,VPCEndpoint"
              }
            }

            mobile = softwareSystem "Mobile System" {
              description "A description of Mobile."
              tags "Mobile"

              authService = container "Authentication (Account) Service" {
                description "OAuth2 server and store for user data."
                technology "PHP, Laravel & AWS Fargate"
                tags "Mobile,PHP,Fargate"
              }

              driverAccountApi = container "Driver Account API" {
                description "Orchestration of user authentication flows for the driver user pool."
                technology "NestJS & AWS Fargate"
                tags "Mobile,Fargate,NestJS"
              }

              driverAccountWebapp = container "Driver Account Webapp" {
                description "Web application that handles user authentication admin flows for the driver user pool."
                technology "Next.js"
                tags "Mobile,Next.js"
              }

              installerAccountWebapp = container "Installer Account Webapp" {
                description "Web application that handles user authentication admin flows for the installer user pool."
                technology "Next.js"
                tags "Mobile,Next.js"
              }

              installerApi = container "Installer API" {
                description "Installation service."
                technology "NestJS & AWS Fargate"
                tags "Mobile,Fargate,NestJS"
              }

              installerBff = container "Installer BFF" {
                description "Backend for frontend (BFF) for Installer app."
                technology "NestJS & AWS Fargate"
                tags "Mobile,Fargate,NestJS"
              }

              mobileApi = container "Mobile API" {
                description "Backend for frontend (BFF) for OpenCharge Web application and OpenCharge Mobile app."
                technology "NestJS & AWS Fargate"
                tags "Mobile,Fargate,NestJS"
              }

              billingAPI = container "Billing API" {
                description "Billing service."
                technology "NestJS & AWS Fargate"
                tags "Mobile,Fargate,NestJS"
              }
            }
        }

        # domain level relationships

        ## relationships between people and software systems
        driver -> experienceDomain "Uses"
        installer -> experienceDomain "Uses"
        siteManager -> experienceDomain "Uses"

        ## relationships between software systems and software systems
        experienceDomain -> networkDomain "Uses"
        experienceDomain -> ownershipDomain "Uses"
        networkDomain -> experienceDomain "Uses"
        ownershipDomain -> experienceDomain "Uses"
        siteManager -> commercial "Uses"

        # subdomain level relationships

        ## relationships between people and software systems
        driver -> mobile "Uses"
        installer -> mobile "Uses"

        ## relationships between software systems and software systems
        commercial -> dataPlatform "Uses"
        commercial -> networkDomain "Uses"
        dataPlatform -> networkDomain "Uses"
        mobile -> dataPlatform "Uses"
        networkDomain -> dataPlatform "Uses"
        ownershipDomain -> dataPlatform "Uses"

        ## relationships to/from containers
        api3 -> authService "Authenticate user requests" "JSON/HTTPS"
        api3 -> podadminDb "Reads from and writes to" "ODBC"
        api3 -> ocppService "Make API calls to" "JSON/HTTPS"
        api3 -> unitLinkVerificationApp "Make API calls to" "JSON/HTTPS"
        arch1 -> api3 "Make API calls to" "JSON/HTTPS"
        destinationSiteAdminApi -> dataPlatformApi "Make API calls to" "JSON/HTTPS"
        destinationSiteAdminApi -> diagnosticsService "Make API calls to" "JSON/HTTPS"
        destinationSiteAdminApi -> podadminDb "Reads from and writes to" "JDBC"
        mis -> api3 "Make API calls to" "JSON/HTTPS"
        mobileApi -> api3 "Make API calls to" "JSON/HTTPS"
        mobileApi -> assetsConfigurationService "Make API calls to" "JSON/HTTPS"
        mobileApi -> competitionsService "Make API calls to" "JSON/HTTPS"
        mobileApi -> connectivityService "Make API calls to" "JSON/HTTPS"
        mobileApi -> dataPlatformApi "Make API calls to" "JSON/HTTPS"
        mobileApi -> smartChargingService "Make API calls to" "JSON/HTTPS"
        orderingTool -> api3 "Make API calls to" "JSON/HTTPS"
        podadminDb -> dataPlatformPodadminDatafeed "Replicate data changes to" "TLS/SSL"
    }

    views {
        systemContext experienceDomain "DomainSystemContext" {
            include *
            autoLayout
        }
        container networkDomain "NetworkDomainContainers" {
            include *
            autoLayout
        }
        container ownershipDomain "OwnershipDomainContainers" {
            include *
            autoLayout
        }
        !include styles.dsl
        !include themes.dsl
    }
}
