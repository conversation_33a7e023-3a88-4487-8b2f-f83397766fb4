workspace extends ../now.dsl  {
    name "Commercial"
    description "This is a model of the Commercial system as it currently is."

    !docs .

    model {
      # relationships between people and software systems
      siteManager -> destinationSiteAdminWebapp "Uses"

      # relationships to/from containers
      destinationSiteAdminWebapp -> destinationSiteAdminApi "Make API calls to" "JSON/HTTPS"

      aws = deploymentEnvironment "AWS" {
        deploymentNode "Amazon Web Services" {
          tags "Amazon Web Services - Cloud"
          region = deploymentNode "EU-West-1" {
            tags "Amazon Web Services - Region"
            route53 = infrastructureNode "Route 53" {
              description "Highly available and scalable DNS."
              tags "Amazon Web Services - Route 53"
            }
            cloudFront = infrastructureNode "CloudFront" {
              description "CDN built for high performance and security."
              tags "Amazon Web Services - CloudFront"
            }
            waf = infrastructureNode "WAF" {
              description "Protects against common web exploits and bots."
              tags "Amazon Web Services - WAF"
            }
            elb = infrastructureNode "Elastic Load Balancer" {
              description "Distributes incoming application traffic across multiple targets."
              tags "Amazon Web Services - Elastic Load Balancing"
            }
            deploymentNode "Fargate" {
              tags "Amazon Web Services - Fargate"
              destinationSiteAdminWebappInstance = containerInstance destinationSiteAdminWebapp
              destinationSiteAdminApiInstance = containerInstance destinationSiteAdminApi
            }
          }
        }
        route53 -> cloudFront "Forwards requests to" "HTTPS"
        cloudFront -> waf "Forwards requests to" "HTTPS"
        waf -> elb "Forwards requests to" "HTTPS"
        elb -> destinationSiteAdminWebappInstance "Forwards requests to" "HTTPS"
      }
    }

    views {
        systemContext commercial "CommercialSystemContext" {
            include *
            autoLayout
        }
        container commercial "CommercialContainers" {
            include *
            autoLayout
        }
        deployment commercial "AWS" "AmazonWebServicesDeployment" {
            include *
            autolayout lr
        }
    }
}
