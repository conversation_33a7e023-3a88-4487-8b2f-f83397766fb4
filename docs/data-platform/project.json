{"name": "data-platform-docs", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "docs/data-platform", "targets": {"build": {"executor": "nx:run-commands", "options": {"commands": ["rm -rf ../../dist/docs/data-platform/", "mkdir -p ../../dist/docs/data-platform/", "docker run --rm -w /content -v ${PWD}:/content spotify/techdocs build", "cp -R ./site/* ../../dist/docs/data-platform/", "rm -rf ./site/ || true"], "parallel": false, "cwd": "docs/data-platform"}}, "serve": {"executor": "nx:run-commands", "options": {"cwd": "docs/data-platform", "parallel": true, "commands": ["exit 1"]}, "configurations": {"docs": {"commands": ["docker run --rm --name xdp-docs -w /content -v $(pwd):/content -p 8080:8000 -i spotify/techdocs serve -a 0.0.0.0:8000", "open http://localhost:8080"]}, "target": {"commands": ["docker run --rm -p 8281:8080 -v ${PWD}:/usr/local/structurizr -e STRUCTURIZR_WORKSPACE_FILENAME=architecture/target structurizr/lite"]}, "stop": {"commands": ["docker stop $(docker ps -a -q)"]}}}}, "tags": ["data-platform"]}