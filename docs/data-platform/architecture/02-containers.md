## Container diagram

This diagram shows the target high-level shape of the Experience Data Platform target architecture and how
responsibilities should be distributed across it.

It also shows the major technology choices and how the containers should communicate with one another.

![](embed:XdpContainers)

The container level diagram again illustrates:

1. We provide API based services to other services within the Experience Domain
2. Other domains may want to use these API services too
3. We will integrate with other domains services or receive events from them as part of providing our services
4. We will integrate with 3rd Party Services as part of providing our services
5. Experience Data Platform does not interact directly with users

It also starts to make concrete some technology and structural choices including:

1. We will expose our services to clients as RESTful APIs
2. Our services will be built using the Golang programming language
3. We will deploy and scale our services as containerised applications on AWS ECS Fargate
4. Our services will be deployed as a monolithic service, we will however look to modularise the codebase within it
5. Experience data we manage and maintain will be stored in a PostgresSQL database, the cluster being provisioned on 
   Amazon Aurora
6. We will integrate with other domains events as part of maintaining and managing experience data
7. Events from other domains will be made available via subscriptions to Amazon SNS Topics
8. We will use Amazon SQS queues to provide durable subscriptions to the event Topics to ensure we don't miss any 
   events

### Architectural decisions/constraints

- We will require to process events from other domains in order to maintain and serve our data
- Separating out the write and read elements of our data model will allow us to store a historically accurate, audit of 
  data changes. and also allow us optimise the response time of queried data
- Storing out data changes as events will allow for building out, or adding richer elements to our data at later 
  points
- Storing the write (Event store) and read (Projections) elements of our data in the same Postgres database will allow
  for transactional updates of the read model at the same time the events are stored
- We will utilise queue based processing to prevent any loss of system state whether that be events that encounter
  issues being processed or commands that have issues being executed
  - Monitoring of Dead Letter Queues and resolution of any issues will also be required to do this
- In order to be responsive and serve data quickly we will support concurrent consumption of messages/events
  - This means we need to support processing of messages/events independent of order they occurred
  - We will use AWS SQS Standard Queues, 
    - As these provide an at-least once guarantee we will require to handle duplicate events
    - To manage this our events and command processing should be idempotent
- Where processing required after the receipt of an event is less time sensitive, or involves integrations which cannot
  be guaranteed to complete in the same transaction we will handle those events later using subscriptions that observe
  events as they are saved to the event store and then carryout any necessary actions

### ADRs

Architectural Decision Records that are relevant to this level include:

- [EXP-002: Access between domain services will use security groups](https://github.com/Pod-Point/experience/blob/main/docs/domain/docs/architecture/decisions/002-service-access-control.md)
- [EXP-003: Authentication and authorisation of user requests will be the responsiblitity of user facing subdomains](https://github.com/Pod-Point/experience/blob/main/docs/domain/docs/architecture/decisions/003-user-authorisation.md)
- [XDP-002: Development will be in Go](https://github.com/Pod-Point/experience/blob/main/docs/data-platform/docs/architecture/decisions/002-golang.md)
- [XDP-004: Services will use AWS Fargate (ECS) for compute](https://github.com/Pod-Point/experience/blob/main/docs/data-platform/docs/architecture/decisions/004-ecs.md)
- [XDP-008: Adopt contract first development approach](https://github.com/Pod-Point/experience/blob/main/docs/data-platform/docs/architecture/decisions/008-contract-first.md)
- [XDP-009: APIs support REST semantics](https://github.com/Pod-Point/experience/blob/main/docs/data-platform/docs/architecture/decisions/009-rest.md)
- [XDP-012: API service will be a Monolith](https://github.com/Pod-Point/experience/blob/main/docs/data-platform/docs/architecture/decisions/012-monolith.md)
- [XDP-013: Data will be stored in Postgres](https://github.com/Pod-Point/experience/blob/main/docs/data-platform/docs/architecture/decisions/013-postgres.md)

---

![](https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg)

Driving shouldn’t cost the earth 🌍

Made with ❤️&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
