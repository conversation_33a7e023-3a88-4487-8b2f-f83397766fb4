# Naming

## Context

Here we list the naming conventions used in `data-platform` code which come on top of the Go standard.

### Business entities

1. **Submitted charge** - it's a charge that has been submitted to be expensed in the future. This name should be used
   throughout the code and docs, to keep it consistent. There are some synonyms / alternative names we used
   in the past (or still use, but about to change it), like: expense, expensable charge, submitted expense.

### Methods / functions

1. Keep in mind the context and don't repeat package on struct method if not necessary. For example, if a function is in
   `charges` package, the function to get a charge by id, doesn't need to be named `GetChargeById`, `GetById` is enough.
