# Podadmin Expense Submission Error

When a log message containing `Podadmin expense submission error:` is written, <PERSON><PERSON><PERSON><PERSON> raises an alert on the
[#experience-data-platform-alerts](https://pod-point.slack.com/archives/C04CZJWBD0C) slack channel. See [monitoring.md](../monitoring.md) for details of how the alerts are configured.

The message will contain

> ALARM: "data-platform-api-DataPlatformPodadminExpenseSubmitErrorCount-gt-0" in EU (Ireland)

indicating that in the most recent time period, there was at least one instance of this log message.

## Setup Investigation

_This guide will involve running SQL commands, including SELECTs, UPDATEs and maybe DELETEs -
if you are unsure/uncomfortable with this, reach out for assistance on the [#vs-experience-data-platform](https://pod-point.slack.com/archives/C040TUPLL3U) slack channel._

1. Identify the environment from the end of the alert title or from `Tags` in the slack alert
   > New Alert created via Experience*Data_Platform_Squad*-\_Experience_CloudWatch\_\_**Dev**
2. Open the [Experience/Application Logs (data-platform-api) Grafana dashboard](https://g-ef7d2baf7b.grafana-workspace.eu-west-1.amazonaws.com/d/g1_JsXF4z/application-logs-data-platform-api?orgId=1&refresh=10s) containing filtered logs
3. Update timeframe to match when the alert was triggered and scroll to `Expense Submission Failure Logs - {Environment}`
4. Note down the `driverID`, `orgID` and `chargeIDs` of any log messages that should look like the following
   <br/>![Error log](assets/sample-podadmin-err-log.png)
5. Follow the steps in [connect-db.md](../connect-db.md) for **data-platform AND podadmin** databases

## Step 1. Verify expense exists in data-platform's database (Postgres)

Refer to your list of `chargeIDs` from before and put them in the following query, separated by commas

    SELECT * FROM commercial.submitted_charges sc WHERE sc.charge_id IN (chargeIDs);

E.g. `SELECT * FROM commercial.submitted_charges sc WHERE sc.charge_id IN (1274, 35475);`

It is highly likely that all the charges will appear & `driver_id` and `organisation_id` should match the logs.

| Scenario | Observation                                        |
| :------: | -------------------------------------------------- |
|    1     | None of the charge ids exist in submitted_charges  |
|    2     | Some charges are listed                            |
|    3     | All charges appear                                 |
|    4     | Charges exist but driver/organisation id are wrong |

Note down the corresponding scenario for now and continue to step 2.

## Step 2. Verify which charges are expensed in podadmin (MySQL)

Again, refer to your list of `chargeIDs` from before and put them in the following query, separated by commas

    SELECT id, group_id, updated_at FROM podpoint.charges WHERE id IN (chargeIDs);

E.g. `SELECT id, group_id, updated_at FROM podpoint.charges WHERE id IN (1274, 35475);`

| Scenario | Observation                                        |
| :------: | -------------------------------------------------- |
|    A     | None of the charges exist in podpoint.charges      |
|    B     | `group_id` is empty for one or more of the charges |
|    C     | All charges appear with `group_id` populated       |

Note down the corresponding scenario for now and continue to step 3.

## Step 3. Take relevant action

| Step 1 Scenario | Step 2 Scenario | Action                                                                                                                             |
| :-------------: | :-------------: | ---------------------------------------------------------------------------------------------------------------------------------- |
|        1        |        A        | No immediate action - charge no longer exists, perhaps this requires a wider investigation as to why the charge does not exist     |
|        1        |        B        | No action - charge will not appear as expensed to user, they can resubmit                                                          |
|        1        |        C        | TBD - probably no action as it was likely expensed via the old method and should not have been submitted again                     |
|        2        |        A        | Follow [Action 1](#action-1) - Remove entries from submitted_charges - it is no longer a charge in podadmin so cannot be submitted |
|        2        |        B        | Follow [Action 2](#action-2) for **only the chargeIDs that appeared in Step 1**                                                    |
|        2        |        C        | TBD - probably no action as some were likely expensed via the old method and should not have been submitted again                  |
|        3        |        A        | Follow [Action 1](#action-1) - Remove entries from submitted_charges - it is no longer a charge in podadmin so cannot be submitted |
|        3        |        B        | Follow [Action 2](#action-2) - Manually update `group_id` in podadmin for all of your chargeIDs                                    |
|        3        |        C        | No action - this is the expected state                                                                                             |
|        4        |        A        | Follow [Action 1](#action-1) - Remove entries from submitted_charges - it is no longer a charge in podadmin so cannot be submitted |
|        4        |        B        | TBD - Unknown state                                                                                                                |
|        4        |        C        | TBD - Unknown state                                                                                                                |

Encounter a TBD or need help? Reach out on [#vs-experience-data-platform](https://pod-point.slack.com/archives/C040TUPLL3U) slack channel.

## Actions

### Action 1

<details>
<summary>[Click to Expand] Delete one or more submitted_charges  (Postgres data-platform database)</summary>

WARNING - take care when running any DELETE command. If unsure, run a SELECT first to verify which entries you will delete. (Replace `DELETE` with `SELECT *` in the following query)

Refer to your list of `chargeIDs` from before and put them in the following query, separated by commas

    DELETE FROM commercial.submitted_charges WHERE charge_id IN (chargeIDs);

E.g. `DELETE FROM commercial.submitted_charges WHERE charge_id IN (1274, 35475);`

</details>

### Action 2

<details>
<summary>[Click to Expand] Update group_id for one or more charges (MySQL podadmin)</summary>

Refer to your list of `chargeIDs` from before, but also the `orgID` from the logs and put them in the following query, chargeIDs separated by commas

    UPDATE podpoint.charges c SET c.group_id = orgID WHERE c.id IN (chargeIDs);

E.g. `UPDATE podpoint.charges c SET c.group_id = 473 WHERE c.id IN (1274, 35475);`

</details>
