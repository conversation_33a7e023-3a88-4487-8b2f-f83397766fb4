# Getting Started

## Developer setup

Run the following [ansible-macos-setup](https://github.com/Pod-Point/ansible-macos-setup) ansible playbooks:

- `base-setup.yml`
- `aws-setup.yml`
- `golang-setup.yml`

## Technology Stack

We use the following technologies, services and tools:

1. _Cloud provider_ - [AWS](https://aws.amazon.com/)
2. _Infrastructure management_ - [Terraform](https://www.terraform.io/) and [Terraform Enterprise](https://developer.hashicorp.com/terraform/enterprise)
3. _Service discovery_ - [AWS Cloud Map](https://aws.amazon.com/cloud-map/)
4. _Secure connectivity_ - [AWS PrivateLink](https://aws.amazon.com/privatelink/)
5. _Access control_ - [AWS IAM](https://aws.amazon.com/iam/) and [Amazon Security Groups](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-security-groups.html)
6. _Secrets management_ - [AWS Secrets Manager](https://aws.amazon.com/secrets-manager/) and [AWS Systems Manager Parameter Store](https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-parameter-store.html)
7. _Application configuration_ - [AWS Secrets Manager](https://aws.amazon.com/secrets-manager/)
8. _API documentation_ - [goa](https://goa.design/) and [OpenAPI](https://swagger.io/specification/)
9. _Services_ - [Golang](https://go.dev/) applications
10. _Containerisation_ - [Docker](https://www.docker.com/)
11. _Container OS_ - [Alpine Linux](https://www.alpinelinux.org/about/) and Ubuntu 20.04 (Github Actions)
12. _Compute_ - [Amazon ECS on AWS Fargate](https://aws.amazon.com/fargate/)
13. Task scheduling - [Amazon EventBridge rules](https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-create-rule-schedule.html) and [Amazon ECS tasks](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/scheduling_tasks.html)
14. _Datastore_ - [Amazon Aurora PostgreSQL](https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/Aurora.AuroraPostgreSQL.html)
15. _Data replication_ - [AWS Database Migration Service](https://aws.amazon.com/dms/)
16. _Messaging_ - [Amazon Simple Notification Service](https://aws.amazon.com/sns/) and [Amazon Simple Queue Service](https://aws.amazon.com/sqs/)
17. _Logging_ - [Amazon CloudWatch Logs](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/WhatIsCloudWatchLogs.html)
18. _Monitoring_ - [Grafana](https://grafana.com/) and [Amazon CloudWatch](https://aws.amazon.com/cloudwatch/)
19. _Version control_ - [Git](https://git-scm.com/) and [GitHub](https://github.com/)
20. _Build system_ - [Nx](https://nx.dev/) and [Nx Cloud](https://nx.app/)
21. _Dependency management_ - [go mod](https://go.dev/doc/modules/managing-dependencies) and [Renovate](https://github.com/renovatebot/renovate)
22. _Vulnerability management_ - [Dependabout alerts](https://github.com/dependabot)
23. _Code analysis_ - [golangci-lint](https://golangci-lint.run/)
24. _Test automation_ - [go test](https://pkg.go.dev/testing) and [AWS CodeBuild](https://aws.amazon.com/codebuild/)
25. _Continuous Integration_ - [GitHub Actions](https://github.com/actions)
26. _Application deployment_ - [GitHub Actions](https://github.com/actions)
27. Local development - [LocalStack](https://docs.localstack.cloud/overview/)
28. _Developer setup_ - [Ansible](https://www.ansible.com/) and [Homebrew](https://brew.sh/)
29. _IDE_ - [GoLand](https://www.jetbrains.com/go/)

---

<img src="https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg" align="right" />

Driving shouldn’t cost the earth 🌍

Made with ❤️&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
