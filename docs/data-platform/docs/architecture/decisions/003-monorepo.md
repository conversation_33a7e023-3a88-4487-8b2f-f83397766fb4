# XDP-003: Codebase will reside in a shared monorepo

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
- **Date**: 2022-11-22

Extended by: [004](004-ecs.md), and
[005](005-github-actions.md).

## Context

As part of beginning to work with the Destination team we need to decide whether we want to share the same monorepo
repository, practices and tooling, or not.

## Decision Drivers

- Success of PoC carried out by team to establish Go could be supported within existing Destination setup
- Increases opportunities for collaboration across teams, simplifying delivery
- Ability to adopt and benefit from work done by Destinations team to prove use of nx.dev
- Cost to change decision in the future is low

## Considered Options

- Monorepo setup with Destination
- Monorepo setup without Destination
- Multi-repo setup

## Decision

Chosen option: **Monorepo setup with Destination**, as it simplifies and shortens not only version control/Github setup,
but AWS account creation; delivery; deployment; and architectural decisions, together with increasing visibility and
collaboration between teams as well as enabling a number of code management and other practices that are not possible
in a multi-repo setup.

## Consequences

### Positive Consequences

- Simplified setup and management of Github permissions, etc
- Initial work and proving of concepts and working patterns had already been established by destination team
- Destination of nx.dev as underlying build system for monorepo reduces management overheads required for monorepos
- Github actions and deployment of AWS Fargate (ECS) already in place
- Adopting destination monorepo also came with access to destination AWS accounts
- Increased collaboration and visibility of changes across other teams work in monorepo
- Code and contract sharing of code becomes possible
- Standardisation of practice, versions and tooling is possible
- Dependency management is simplified
- Allows the creation of shared delivery pipeline and deployment practices across teams
- Move to multi-repo setup still possible at a later point, if desired

### Negative Consequences

- Monorepo setup requires additional tooling
- Contention of changes across codebase requires to be considered/managed
- Permissions, by necessity, require to be less granular than for individual repositories
