# Recharts will be used for displaying data in chart format

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>
- **Date**: 2023-06-12

## Context

We need to choose a chart library to show aggregate charging data.
We want to show these with each months value visible without any interaction.
In future we will likely want to be able to select dates and filter.

## Decision Drivers

We want to create an insights area which shows aggregate charging data over time. Initially we want to show:

- kWh delivered
- CO2 avoided
- Revenue
- Energy Cost

## Considered options

- Tanstack’s React Charts
- Recharts
- Nivo
- Tremor

## Decision

Chosen option: "Recharts" becuase it is a very popular library with 1.2m weekly downloads and libraries such as Tremor are built on top of it. It gives us a wide range of decoupled chart components, which we can use to build out our own chart components for reusability if required.

Other options:
Tanstack's React Chart had initial setup issues, which possibly relate to it being incorporated with Next.js.

Nivo is a great library for data visualisation but seems unnecessary for simple bar chart data displaying.

Tremor seems to be gaining some traction, but we would be limited in the bar chart colour selection. Though this is built with TailwindCSS, a big plus, it is built on top of recharts. We can therefore use this libraries codebase as guidence for building our own Recharts extended components.

## Consequences

### Positive Consequences

- Compatibility with Next.js.
- Large array of chart types to choose from, including line, area and bar charts, amongst others.

### Negative Consequences

- Some examples use old React version in the documentation (Looks like they are replacing these with Storybook example tho).
- Responsive component did not render the chart component, as this was timeboxed a solution wasn't found, though have seen this working in examples so likely to be a ref issue.
