# Editing of site addresses via the fleet / site services will be deferred

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON>, <PERSON><PERSON>
- **Date**: 2023-02-23

## Context

In readiness to launch the Site Service as an eventual replacement to MIS we have been implementing the ability to edit
various attributes of a site in order to offer functional parity. In so doing we learned that MIS writes these updates
to an Elasticsearch index which is used by the search on the driver mobile app. MIS also publishes an event to the OCPI
system which maintains its own copy of the state of the network.

## Decision Drivers

- MIS and the Site Service will need to run in parallel for a period of time
- The ability for an _external_ user to edit the address of a site is not considered critical in order to retire MIS as
  an externally facing service
- The ability for an _internal_ user to edit the address of a site is considered critical in order to retire MIS as an
  internally facing service
- Our immediate goal is to retire MIS as an externally facing service
- MIS is likely to live on for some time as an internally facing service

## Considered Options

- Replicate MIS behaviour
- Enhance MIS to detect external database updates
- Extract pod locations from podadmin into a new service
- Defer implementation of editing site addresses

## Decision

Chosen option: "Defer implementation of editing site addresses", because the ability for an external user to edit the
address of a site is not considered critical in order to retire MIS as an externally facing service, which is our
immediate goal.

## Consequences

### Positive Consequences

- We will have minimal further development effort

### Negative Consequences

- External users will not be able to edit the address of their sites
- Internal users will be required to action any requests to edit site addresses

## Pros and Cons of the Options

### Replicate MIS behaviour

Under this option we would enhance the Site Service to push any relevant updates made to podadmin to both Elasticsearch
and OCPI.

- Good, because updates could be made in either MIS or the Site Service
- Bad, because would require development effort to get cross AWS account updates to MIS / OCPI working
- Bad, because increases complexity
- Bad, because Elasticsearch and OCPI are already out of sync with podadmin, and we'd likely be making it worse

### Enhance MIS to detect external database updates

Under this option we would enhance MIS to poll podadmin using the created_at / updated_at / deleted_at columns to detect
changes and then push these to Elasticsearch and OCPI.

- Good, because would reuse existing integrations to Elasticsearch and OCPI
- Bad, because requires enhancing a system we want to retire
- Bad, because the team are not experienced in developing on MIS

### Extract pod locations from podadmin into a new service

Under this option we would extract addresses / locations from podadmin into new services. The exact nature of these
services is unclear at this time, as is relevant domain / squad who would own them.

- Good, because this is probably roughly speaking where we want to get to eventually
- Bad, because development effort would be huge
- Bad, because development effort would also include MIS
- Bad, because would have dependencies on multiple other teams who do not have this on their immediate roadmap

### Defer implementation of editing site addresses

Under this option we would not offer the ability to edit site addresses on the Site Service.

- Good, because we will have minimal further development effort
- Bad, because external users will not be able to edit the address of their sites
- Bad, because internal users will be required to action any requests to edit site addresses
