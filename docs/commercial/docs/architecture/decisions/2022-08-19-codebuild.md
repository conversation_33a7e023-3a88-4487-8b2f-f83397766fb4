# Pipeline automation will be extended using Codebuild

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON>
- **Date**: 2022-08-19

Based on: [2022-08-05](2022-08-05-continuous-deployment.md)

## Context

As part of our delivery pipeline we require a mechanism to carry out post-deployment checks and tests in order to gain
the required confidence to progress changes through environments without the delay required by manual testing.

## Decision Drivers

- Need to integrate between GitHub and AWS eco-systems
- Complexity of managing additional integrations and technologies
- Desire to remove the risk and delay that manual testing introduces
- Establish early good testing good practice
- Footprint of build infrastructure required needs to be manageable

## Considered Options

- [AWS CodeBuild](https://aws.amazon.com/codebuild/)
- Jenkins
- CircleCI
- [Self-hosted runners](https://docs.github.com/en/actions/hosting-your-own-runners) for GitHub Actions

## Decision

Chosen option: **AWS CodeBuild**, because it provides a compute build and execute capability that can be integrated
across GitHub and AWS that enables test integrations to be executed within our pipeline with a low management overhead.

Self-hosted GitHub Actions Runners looked interesting but too complicated a proposal to look into further. This may
be revisited at a future point as there may be cost and operational benefits that as our use grows become relevant.

## Consequences

### Positive Consequences

- Provides capability to execute integration, acceptance and performance tests against representative environments
- Reduce risk of human error
- Reduce costs of releases
- Increase quality and confidence in released software
- Increase speed of delivery

### Negative Consequences

- Integration between AWS and GitHub may prove to be brittle or inadequate when additional requirements are identified
- Deployments will be affected by start times and availability of AWS CodeBuild service
