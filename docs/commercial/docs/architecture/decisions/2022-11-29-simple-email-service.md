# AWS Simple Email Service will be used to send outbound emails

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON>
- **Date**: 2022-11-29

## Context

We have a requirement to send outbound emails from our services to invite users to set themselves up with the Pod Point
app. This happens when site administrators permit drivers to use chargers on their sites.

## Decision Drivers

- We are developing our new systems on AWS
- Existing systems such as MIS and the Account Service use a combination of Mandrill and Mailtrap
- The use of Mandrill and Mailtrap in non-production environments mean that emails are not sent, complicating testing
- We do not want our non-production environments to be capable of sending emails to non pod-point.com email addresses

## Considered Options

- Mandrill
- Mailtrap
- AWS Simple Email Service

## Decision

Chosen option: "AWS Simple Email Service", because it fits well with our current infrastructure. Only our production
environment will be moved out of sandbox mode, enabling it to send emails externally to Pod Point.

## Consequences

### Positive Consequences

- Relatively simple to provision using Terraform alongside the rest of our infrastructure
- Straightforward to monitor using CloudWatch / Grafana as with other AWS services which we consume
- Easy to integrate with using the AWS SDK
- Isolated from other systems within Pod Point
- Possible to send emails to pod-point.com addresses in non-production environments, improving the quality of the
  testing that we can carry out

### Negative Consequences

- Different approach than existing systems so may cause some confusion
- Will require additional effort to support local development (e.g. using LocalStack)
- Sandbox mode has volume restrictions (200 messages every 24 hours / 1 message per second)
- Coupled to AWS since communication is not over SMTP
