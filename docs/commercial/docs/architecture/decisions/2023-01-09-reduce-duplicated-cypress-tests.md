# Reduce Duplicated Cypress Tests

| **Status**   | Accepted                                                               |
| ------------ | ---------------------------------------------------------------------- |
| **Deciders** | <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> |
| **Date**     | 2023-01-09                                                             |
| **Story**    | [XFL-135](https://podpoint.atlassian.net/browse/XFL-135)               |

---

## Problem

While developing the Fleet application we noticed that our Cypress test suite runs for a long time which slows the
pipeline down.

An investigation was
done [here](https://podpoint.atlassian.net/wiki/spaces/ED/pages/3798138951/Cypress+Test+Execution+And+Analysis#Execution-Time)
and a decision was made to try and reduce the time.

---

## Considered Options

- Keep all the tests

  - We decided to remove some tests as with three teams all using the pipeline slow E2E tests can hold up every
    teams work.

- Remove just the tests duplicated by jest.
  - This will have reduced the time taken for the tests to run but at the most by 1/4 of what we have reduced it by. A
    lot of the duplication between tests came from almost identical E2E flows across the three pages instead.

---

## Solution

We decided to reduce the number of tests in the following ways:

Remove tests that were duplicated by jest tests

- In this case all the search tests were already unit tested and could be removed.
- We could also remove tests that just asserted rows of data on the page as they are also unit tests

Remove duplicated tests between E2E files - checkboxes, state and csv tests

- These tests were written on all three of our test files with the difference being the page the button was clicked on.
- These all got removed as the common functionality was being tested three times which felt unnecessary and
  time-consuming.

---

## Consequences of Decisions

This has led to a reduction in Cypress test running time of about a minute as
documented [here](https://podpoint.atlassian.net/wiki/spaces/~193347659/pages/3807641646/Fleet+Cypress+Tests)

The downside is that we have some reduction in test coverage in terms of not testing the export CSV button on each page
does the same thing. This was removed to reduce the test time as the rest of the flow was identical. A similar situation
exists for checkboxes and state management between the three pages.
