# How to test third-party API alerts

## The purpose of this article

This article describes the process of triggering alerts that monitor the status of a third-party API using environment variables.

## Trigger

When we have integrated a new third-party service and set up an alert that needs testing.

## Before you begin

- A new alert configured in Grafana pointing to the development environment

## Step 1 - Navigate to the application's environment variables page.

- Login to the correct AWS account for example, `experience-commercial-dev`.
- Navigate to ECS - Elastic Container Service.
- Navigate to _Task definitions_ in the sidebar.
- Pick the definition that matches the service you need to change.

## Step 2 - Create a new revision

- Open the latest revision and click on _Create a new revision_.
- Find and update the value of the environment variable that provides the third-party URL.

## Step 3 - Deploy your changes

- Navigate to _Clusters_ in the sidebar .
- Open the corresponding service and click _Update service_.
- Change the revision to the one you created - it will be labelled "latest".
- Click save and monitor the deployment in the "Deployments" tabs.

## Step 4 - Test your alert

- Run through your steps to trigger the Grafana alert - you may have to wait a minute or two for the logs.
- Acknowledge your alert in OpsGenie, write a note to explain that it was a test alert and close it.

## Step 5 - Deploy a working application

Follow steps 1-3 again but this time put the correct URL back in the environment variable.
