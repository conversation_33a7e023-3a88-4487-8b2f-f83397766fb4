{"containerDefinitions": [{"cpu": 0, "environment": [{"name": "ENVIRONMENT", "value": "dev"}], "essential": true, "image": "591651182110.dkr.ecr.eu-west-1.amazonaws.com/data-platform-task-runner:latest", "linuxParameters": {"initProcessEnabled": true}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/data-platform-task-runner", "awslogs-region": "eu-west-1", "awslogs-stream-prefix": "ecs"}}, "mountPoints": [], "name": "data-platform-task-runner", "volumesFrom": []}], "cpu": "256", "executionRoleArn": "arn:aws:iam::146549662676:role/data-platform-task-runner-ecs-task-execution", "family": "data-platform-task-runner", "memory": "512", "networkMode": "awsvpc", "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "taskRoleArn": "arn:aws:iam::146549662676:role/data-platform-task-runner-ecs-task", "volumes": []}