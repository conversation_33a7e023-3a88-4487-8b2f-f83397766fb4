//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for CurrentIntentDtoImpl
void main() {
  // final instance = CurrentIntentDtoImpl();

  group('test CurrentIntentDtoImpl', () {
    // Whether or not the intent can meet the target
    // bool canMeetTarget
    test('to test the property `canMeetTarget`', () async {
      // TODO
    });

    // The charge details
    // VehicleChargeInfoDtoImpl chargeDetail
    test('to test the property `chargeDetail`', () async {
      // TODO
    });

    // The charging station
    // CurrentIntentDtoChargingStationImpl chargingStation
    test('to test the property `chargingStation`', () async {
      // TODO
    });


  });

}
