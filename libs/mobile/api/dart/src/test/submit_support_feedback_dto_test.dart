//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for SubmitSupportFeedbackDTO
void main() {
  // final instance = SubmitSupportFeedbackDTO();

  group('test SubmitSupportFeedbackDTO', () {
    // The region the support case is relevant to
    // String region
    test('to test the property `region`', () async {
      // TODO
    });

    // The email of the user submitting the feedback
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // The free-text entered by the user
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // The name of the charger, if relevant
    // String chargerName
    test('to test the property `chargerName`', () async {
      // TODO
    });

    // The name of the site at which the charger is located, if relevant
    // String siteName
    test('to test the property `siteName`', () async {
      // TODO
    });

    // The address of the site at which the charger is located, if relevant
    // String siteAddress
    test('to test the property `siteAddress`', () async {
      // TODO
    });


  });

}
