//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for TrackLoginRequest
void main() {
  // final instance = TrackLoginRequest();

  group('test TrackLoginRequest', () {
    // String ipAddress
    test('to test the property `ipAddress`', () async {
      // TODO
    });

    // String userAgent
    test('to test the property `userAgent`', () async {
      // TODO
    });

    // String timestamp
    test('to test the property `timestamp`', () async {
      // TODO
    });

    // String authId
    test('to test the property `authId`', () async {
      // TODO
    });


  });

}
