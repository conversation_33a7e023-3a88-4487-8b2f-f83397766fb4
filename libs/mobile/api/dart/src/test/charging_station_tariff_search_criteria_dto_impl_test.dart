//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for ChargingStationTariffSearchCriteriaDtoImpl
void main() {
  // final instance = ChargingStationTariffSearchCriteriaDtoImpl();

  group('test ChargingStationTariffSearchCriteriaDtoImpl', () {
    // Charging Station PPID
    // String ppid
    test('to test the property `ppid`', () async {
      // TODO
    });

    // The effective from date
    // String effectiveFrom
    test('to test the property `effectiveFrom`', () async {
      // TODO
    });

    // The effective to date
    // String effectiveTo
    test('to test the property `effectiveTo`', () async {
      // TODO
    });


  });

}
