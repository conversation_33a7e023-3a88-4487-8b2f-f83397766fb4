//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for ExtendedUserInfoResponseDto
void main() {
  // final instance = ExtendedUserInfoResponseDto();

  group('test ExtendedUserInfoResponseDto', () {
    // First name
    // String firstName
    test('to test the property `firstName`', () async {
      // TODO
    });

    // Last name
    // String lastName
    test('to test the property `lastName`', () async {
      // TODO
    });

    // Locale
    // String locale
    test('to test the property `locale`', () async {
      // TODO
    });

    // Email
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // auth id
    // String uid
    test('to test the property `uid`', () async {
      // TODO
    });

    // PreferencesDto preferences
    test('to test the property `preferences`', () async {
      // TODO
    });

    // BalanceDto balance
    test('to test the property `balance`', () async {
      // TODO
    });

    // String paymentProcessorId
    test('to test the property `paymentProcessorId`', () async {
      // TODO
    });

    // bool emailVerified
    test('to test the property `emailVerified`', () async {
      // TODO
    });

    // String lastSignInTimestamp
    test('to test the property `lastSignInTimestamp`', () async {
      // TODO
    });

    // String accountCreationTimestamp
    test('to test the property `accountCreationTimestamp`', () async {
      // TODO
    });

    // String status
    test('to test the property `status`', () async {
      // TODO
    });

    // String deletedAtTimestamp
    test('to test the property `deletedAtTimestamp`', () async {
      // TODO
    });


  });

}
