//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for LocationControllerSearch400Response
void main() {
  // final instance = LocationControllerSearch400Response();

  group('test LocationControllerSearch400Response', () {
    // num statusCode
    test('to test the property `statusCode`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });

    // String error
    test('to test the property `error`', () async {
      // TODO
    });


  });

}
