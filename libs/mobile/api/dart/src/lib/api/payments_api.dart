//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class PaymentsApi {
  PaymentsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// create setup intent for payment
  ///
  /// Creates setup intent for payment
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [CreatePaymentRequest] createPaymentRequest (required):
  Future<Response> paymentControllerCreatePaymentIntentWithHttpInfo(CreatePaymentRequest createPaymentRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/payments/create-payment-intent';

    // ignore: prefer_final_locals
    Object? postBody = createPaymentRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// create setup intent for payment
  ///
  /// Creates setup intent for payment
  ///
  /// Parameters:
  ///
  /// * [CreatePaymentRequest] createPaymentRequest (required):
  Future<CreateRegisteredUserPaymentResponse?> paymentControllerCreatePaymentIntent(CreatePaymentRequest createPaymentRequest,) async {
    final response = await paymentControllerCreatePaymentIntentWithHttpInfo(createPaymentRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateRegisteredUserPaymentResponse',) as CreateRegisteredUserPaymentResponse;
    
    }
    return null;
  }

  /// create setup intent for payment
  ///
  /// Creates setup intent for payment
  ///
  /// Note: This method returns the HTTP [Response].
  Future<Response> paymentControllerSetupIntentWithHttpInfo() async {
    // ignore: prefer_const_declarations
    final path = r'/payments/setup-intent';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// create setup intent for payment
  ///
  /// Creates setup intent for payment
  Future<CreateIntentResponse?> paymentControllerSetupIntent() async {
    final response = await paymentControllerSetupIntentWithHttpInfo();
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateIntentResponse',) as CreateIntentResponse;
    
    }
    return null;
  }

  /// Performs an HTTP 'POST /payments/webhook' operation and returns the [Response].
  /// Parameters:
  ///
  /// * [String] stripeSignature (required):
  Future<Response> paymentControllerWebhookWithHttpInfo(String stripeSignature,) async {
    // ignore: prefer_const_declarations
    final path = r'/payments/webhook';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    headerParams[r'stripe-signature'] = parameterToString(stripeSignature);

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Parameters:
  ///
  /// * [String] stripeSignature (required):
  Future<void> paymentControllerWebhook(String stripeSignature,) async {
    final response = await paymentControllerWebhookWithHttpInfo(stripeSignature,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}
