//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class TrackLoginRequest {
  /// Returns a new [TrackLoginRequest] instance.
  TrackLoginRequest({
    required this.ipAddress,
    required this.userAgent,
    required this.timestamp,
    this.authId,
  });

  String ipAddress;

  String userAgent;

  String timestamp;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? authId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is TrackLoginRequest &&
    other.ipAddress == ipAddress &&
    other.userAgent == userAgent &&
    other.timestamp == timestamp &&
    other.authId == authId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (ipAddress.hashCode) +
    (userAgent.hashCode) +
    (timestamp.hashCode) +
    (authId == null ? 0 : authId!.hashCode);

  @override
  String toString() => 'TrackLoginRequest[ipAddress=$ipAddress, userAgent=$userAgent, timestamp=$timestamp, authId=$authId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'ipAddress'] = this.ipAddress;
      json[r'userAgent'] = this.userAgent;
      json[r'timestamp'] = this.timestamp;
    if (this.authId != null) {
      json[r'authId'] = this.authId;
    } else {
      json[r'authId'] = null;
    }
    return json;
  }

  /// Returns a new [TrackLoginRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static TrackLoginRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "TrackLoginRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "TrackLoginRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return TrackLoginRequest(
        ipAddress: mapValueOfType<String>(json, r'ipAddress')!,
        userAgent: mapValueOfType<String>(json, r'userAgent')!,
        timestamp: mapValueOfType<String>(json, r'timestamp')!,
        authId: mapValueOfType<String>(json, r'authId'),
      );
    }
    return null;
  }

  static List<TrackLoginRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <TrackLoginRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = TrackLoginRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, TrackLoginRequest> mapFromJson(dynamic json) {
    final map = <String, TrackLoginRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = TrackLoginRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of TrackLoginRequest-objects as value to a dart map
  static Map<String, List<TrackLoginRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<TrackLoginRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = TrackLoginRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'ipAddress',
    'userAgent',
    'timestamp',
  };
}

