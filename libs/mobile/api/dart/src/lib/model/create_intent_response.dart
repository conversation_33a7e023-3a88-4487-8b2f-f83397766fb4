//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateIntentResponse {
  /// Returns a new [CreateIntentResponse] instance.
  CreateIntentResponse({
    required this.customer,
    required this.setupIntent,
    required this.ephemeralKey,
  });

  /// The customer id
  String customer;

  /// The setup intent secret
  String? setupIntent;

  /// The ephemeral key
  String ephemeralKey;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateIntentResponse &&
    other.customer == customer &&
    other.setupIntent == setupIntent &&
    other.ephemeralKey == ephemeralKey;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (customer.hashCode) +
    (setupIntent == null ? 0 : setupIntent!.hashCode) +
    (ephemeralKey.hashCode);

  @override
  String toString() => 'CreateIntentResponse[customer=$customer, setupIntent=$setupIntent, ephemeralKey=$ephemeralKey]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'customer'] = this.customer;
    if (this.setupIntent != null) {
      json[r'setupIntent'] = this.setupIntent;
    } else {
      json[r'setupIntent'] = null;
    }
      json[r'ephemeralKey'] = this.ephemeralKey;
    return json;
  }

  /// Returns a new [CreateIntentResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateIntentResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateIntentResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateIntentResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateIntentResponse(
        customer: mapValueOfType<String>(json, r'customer')!,
        setupIntent: mapValueOfType<String>(json, r'setupIntent'),
        ephemeralKey: mapValueOfType<String>(json, r'ephemeralKey')!,
      );
    }
    return null;
  }

  static List<CreateIntentResponse> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateIntentResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateIntentResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateIntentResponse> mapFromJson(dynamic json) {
    final map = <String, CreateIntentResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateIntentResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateIntentResponse-objects as value to a dart map
  static Map<String, List<CreateIntentResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateIntentResponse>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateIntentResponse.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'customer',
    'setupIntent',
    'ephemeralKey',
  };
}

