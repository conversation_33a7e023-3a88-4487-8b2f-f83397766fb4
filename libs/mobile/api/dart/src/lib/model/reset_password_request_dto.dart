//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ResetPasswordRequestDto {
  /// Returns a new [ResetPasswordRequestDto] instance.
  ResetPasswordRequestDto({
    required this.email,
    this.resetPasswordContinueUrl,
  });

  /// Email
  String email;

  /// Continue url
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? resetPasswordContinueUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ResetPasswordRequestDto &&
    other.email == email &&
    other.resetPasswordContinueUrl == resetPasswordContinueUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (email.hashCode) +
    (resetPasswordContinueUrl == null ? 0 : resetPasswordContinueUrl!.hashCode);

  @override
  String toString() => 'ResetPasswordRequestDto[email=$email, resetPasswordContinueUrl=$resetPasswordContinueUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'email'] = this.email;
    if (this.resetPasswordContinueUrl != null) {
      json[r'reset_password_continue_url'] = this.resetPasswordContinueUrl;
    } else {
      json[r'reset_password_continue_url'] = null;
    }
    return json;
  }

  /// Returns a new [ResetPasswordRequestDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ResetPasswordRequestDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ResetPasswordRequestDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ResetPasswordRequestDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ResetPasswordRequestDto(
        email: mapValueOfType<String>(json, r'email')!,
        resetPasswordContinueUrl: mapValueOfType<String>(json, r'reset_password_continue_url'),
      );
    }
    return null;
  }

  static List<ResetPasswordRequestDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ResetPasswordRequestDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ResetPasswordRequestDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ResetPasswordRequestDto> mapFromJson(dynamic json) {
    final map = <String, ResetPasswordRequestDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ResetPasswordRequestDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ResetPasswordRequestDto-objects as value to a dart map
  static Map<String, List<ResetPasswordRequestDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ResetPasswordRequestDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = ResetPasswordRequestDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'email',
  };
}

