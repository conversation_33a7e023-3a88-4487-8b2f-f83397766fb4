//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class TariffRequest {
  /// Returns a new [TariffRequest] instance.
  TariffRequest({
    required this.userId,
    required this.energySupplierId,
    this.tiers = const [],
  });

  /// User Id
  num userId;

  /// Energy supplier id
  num? energySupplierId;

  /// A list of tarrifs
  List<Tier> tiers;

  @override
  bool operator ==(Object other) => identical(this, other) || other is TariffRequest &&
    other.userId == userId &&
    other.energySupplierId == energySupplierId &&
    _deepEquality.equals(other.tiers, tiers);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (userId.hashCode) +
    (energySupplierId == null ? 0 : energySupplierId!.hashCode) +
    (tiers.hashCode);

  @override
  String toString() => 'TariffRequest[userId=$userId, energySupplierId=$energySupplierId, tiers=$tiers]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'user_id'] = this.userId;
    if (this.energySupplierId != null) {
      json[r'energy_supplier_id'] = this.energySupplierId;
    } else {
      json[r'energy_supplier_id'] = null;
    }
      json[r'tiers'] = this.tiers;
    return json;
  }

  /// Returns a new [TariffRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static TariffRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "TariffRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "TariffRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return TariffRequest(
        userId: num.parse('${json[r'user_id']}'),
        energySupplierId: json[r'energy_supplier_id'] == null
            ? null
            : num.parse('${json[r'energy_supplier_id']}'),
        tiers: Tier.listFromJson(json[r'tiers']),
      );
    }
    return null;
  }

  static List<TariffRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <TariffRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = TariffRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, TariffRequest> mapFromJson(dynamic json) {
    final map = <String, TariffRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = TariffRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of TariffRequest-objects as value to a dart map
  static Map<String, List<TariffRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<TariffRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = TariffRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'user_id',
    'energy_supplier_id',
    'tiers',
  };
}

