//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class PayoutRequestDTOImpl {
  /// Returns a new [PayoutRequestDTOImpl] instance.
  PayoutRequestDTOImpl({
    required this.bankAccountId,
  });

  /// The ID of the bank account to make the payout from
  String bankAccountId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is PayoutRequestDTOImpl &&
    other.bankAccountId == bankAccountId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (bankAccountId.hashCode);

  @override
  String toString() => 'PayoutRequestDTOImpl[bankAccountId=$bankAccountId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'bankAccountId'] = this.bankAccountId;
    return json;
  }

  /// Returns a new [PayoutRequestDTOImpl] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static PayoutRequestDTOImpl? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "PayoutRequestDTOImpl[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "PayoutRequestDTOImpl[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return PayoutRequestDTOImpl(
        bankAccountId: mapValueOfType<String>(json, r'bankAccountId')!,
      );
    }
    return null;
  }

  static List<PayoutRequestDTOImpl> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <PayoutRequestDTOImpl>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = PayoutRequestDTOImpl.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, PayoutRequestDTOImpl> mapFromJson(dynamic json) {
    final map = <String, PayoutRequestDTOImpl>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = PayoutRequestDTOImpl.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of PayoutRequestDTOImpl-objects as value to a dart map
  static Map<String, List<PayoutRequestDTOImpl>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<PayoutRequestDTOImpl>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = PayoutRequestDTOImpl.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'bankAccountId',
  };
}

