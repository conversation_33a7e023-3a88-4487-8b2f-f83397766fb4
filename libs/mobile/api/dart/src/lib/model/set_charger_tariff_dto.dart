//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SetChargerTariffDto {
  /// Returns a new [SetChargerTariffDto] instance.
  SetChargerTariffDto({
    required this.effectiveFrom,
    this.maxChargePrice,
    required this.supplierId,
    this.tariffInfo = const [],
    required this.timezone,
  });

  /// The date from which the tariff is effective (inclusive)
  String effectiveFrom;

  /// The maximum price during off-peak hours in pence
  num? maxChargePrice;

  /// Reference to the supplier. Null if the supplier is unknown
  String? supplierId;

  /// Tariff information applicable to specific days and times.
  List<TariffInfoDtoImpl> tariffInfo;

  /// Timezone the tariff information applies to
  String timezone;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SetChargerTariffDto &&
    other.effectiveFrom == effectiveFrom &&
    other.maxChargePrice == maxChargePrice &&
    other.supplierId == supplierId &&
    _deepEquality.equals(other.tariffInfo, tariffInfo) &&
    other.timezone == timezone;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (effectiveFrom.hashCode) +
    (maxChargePrice == null ? 0 : maxChargePrice!.hashCode) +
    (supplierId == null ? 0 : supplierId!.hashCode) +
    (tariffInfo.hashCode) +
    (timezone.hashCode);

  @override
  String toString() => 'SetChargerTariffDto[effectiveFrom=$effectiveFrom, maxChargePrice=$maxChargePrice, supplierId=$supplierId, tariffInfo=$tariffInfo, timezone=$timezone]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'effectiveFrom'] = this.effectiveFrom;
    if (this.maxChargePrice != null) {
      json[r'maxChargePrice'] = this.maxChargePrice;
    } else {
      json[r'maxChargePrice'] = null;
    }
    if (this.supplierId != null) {
      json[r'supplierId'] = this.supplierId;
    } else {
      json[r'supplierId'] = null;
    }
      json[r'tariffInfo'] = this.tariffInfo;
      json[r'timezone'] = this.timezone;
    return json;
  }

  /// Returns a new [SetChargerTariffDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SetChargerTariffDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SetChargerTariffDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SetChargerTariffDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SetChargerTariffDto(
        effectiveFrom: mapValueOfType<String>(json, r'effectiveFrom')!,
        maxChargePrice: json[r'maxChargePrice'] == null
            ? null
            : num.parse('${json[r'maxChargePrice']}'),
        supplierId: mapValueOfType<String>(json, r'supplierId'),
        tariffInfo: TariffInfoDtoImpl.listFromJson(json[r'tariffInfo']),
        timezone: mapValueOfType<String>(json, r'timezone')!,
      );
    }
    return null;
  }

  static List<SetChargerTariffDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SetChargerTariffDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SetChargerTariffDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SetChargerTariffDto> mapFromJson(dynamic json) {
    final map = <String, SetChargerTariffDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SetChargerTariffDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SetChargerTariffDto-objects as value to a dart map
  static Map<String, List<SetChargerTariffDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SetChargerTariffDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SetChargerTariffDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'effectiveFrom',
    'supplierId',
    'tariffInfo',
    'timezone',
  };
}

