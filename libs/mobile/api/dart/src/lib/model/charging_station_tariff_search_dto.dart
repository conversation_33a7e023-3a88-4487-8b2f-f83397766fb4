//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ChargingStationTariffSearchDto {
  /// Returns a new [ChargingStationTariffSearchDto] instance.
  ChargingStationTariffSearchDto({
    this.data = const [],
    required this.metadata,
  });

  List<ChargerTariffDto> data;

  ChargingStationTariffSearchMetadataDtoImpl metadata;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ChargingStationTariffSearchDto &&
    _deepEquality.equals(other.data, data) &&
    other.metadata == metadata;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (data.hashCode) +
    (metadata.hashCode);

  @override
  String toString() => 'ChargingStationTariffSearchDto[data=$data, metadata=$metadata]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'data'] = this.data;
      json[r'metadata'] = this.metadata;
    return json;
  }

  /// Returns a new [ChargingStationTariffSearchDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ChargingStationTariffSearchDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ChargingStationTariffSearchDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ChargingStationTariffSearchDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ChargingStationTariffSearchDto(
        data: ChargerTariffDto.listFromJson(json[r'data']),
        metadata: ChargingStationTariffSearchMetadataDtoImpl.fromJson(json[r'metadata'])!,
      );
    }
    return null;
  }

  static List<ChargingStationTariffSearchDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ChargingStationTariffSearchDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ChargingStationTariffSearchDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ChargingStationTariffSearchDto> mapFromJson(dynamic json) {
    final map = <String, ChargingStationTariffSearchDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ChargingStationTariffSearchDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ChargingStationTariffSearchDto-objects as value to a dart map
  static Map<String, List<ChargingStationTariffSearchDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ChargingStationTariffSearchDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = ChargingStationTariffSearchDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'data',
    'metadata',
  };
}

