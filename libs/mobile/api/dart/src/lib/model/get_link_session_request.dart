//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetLinkSessionRequest {
  /// Returns a new [GetLinkSessionRequest] instance.
  GetLinkSessionRequest({
    this.vendor,
    this.enodeUserId,
  });

  /// By specifying a vendor, the brand selection step in Link UI will be skipped. Instead, your user will go directly to the service selection view (if applicable for the specified vendor), or to the review data access step.
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? vendor;

  /// A unique identifier identifying the user to be used with the link session generated at the capture vehicle stage
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? enodeUserId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetLinkSessionRequest &&
    other.vendor == vendor &&
    other.enodeUserId == enodeUserId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (vendor == null ? 0 : vendor!.hashCode) +
    (enodeUserId == null ? 0 : enodeUserId!.hashCode);

  @override
  String toString() => 'GetLinkSessionRequest[vendor=$vendor, enodeUserId=$enodeUserId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.vendor != null) {
      json[r'vendor'] = this.vendor;
    } else {
      json[r'vendor'] = null;
    }
    if (this.enodeUserId != null) {
      json[r'enodeUserId'] = this.enodeUserId;
    } else {
      json[r'enodeUserId'] = null;
    }
    return json;
  }

  /// Returns a new [GetLinkSessionRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetLinkSessionRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetLinkSessionRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetLinkSessionRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetLinkSessionRequest(
        vendor: mapValueOfType<String>(json, r'vendor'),
        enodeUserId: mapValueOfType<String>(json, r'enodeUserId'),
      );
    }
    return null;
  }

  static List<GetLinkSessionRequest> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetLinkSessionRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetLinkSessionRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetLinkSessionRequest> mapFromJson(dynamic json) {
    final map = <String, GetLinkSessionRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetLinkSessionRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetLinkSessionRequest-objects as value to a dart map
  static Map<String, List<GetLinkSessionRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetLinkSessionRequest>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetLinkSessionRequest.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

