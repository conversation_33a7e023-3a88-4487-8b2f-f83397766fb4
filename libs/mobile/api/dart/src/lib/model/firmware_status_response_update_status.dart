//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class FirmwareStatusResponseUpdateStatus {
  /// Returns a new [FirmwareStatusResponseUpdateStatus] instance.
  FirmwareStatusResponseUpdateStatus({
    required this.isUpdateAvailable,
    this.status,
    this.updateId,
    this.updateVersion,
  });

  bool isUpdateAvailable;

  FirmwareStatusResponseUpdateStatusStatusEnum? status;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? updateId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  FirmwareStatusResponseManifestType? updateVersion;

  @override
  bool operator ==(Object other) => identical(this, other) || other is FirmwareStatusResponseUpdateStatus &&
    other.isUpdateAvailable == isUpdateAvailable &&
    other.status == status &&
    other.updateId == updateId &&
    other.updateVersion == updateVersion;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (isUpdateAvailable.hashCode) +
    (status == null ? 0 : status!.hashCode) +
    (updateId == null ? 0 : updateId!.hashCode) +
    (updateVersion == null ? 0 : updateVersion!.hashCode);

  @override
  String toString() => 'FirmwareStatusResponseUpdateStatus[isUpdateAvailable=$isUpdateAvailable, status=$status, updateId=$updateId, updateVersion=$updateVersion]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'isUpdateAvailable'] = this.isUpdateAvailable;
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    if (this.updateId != null) {
      json[r'updateId'] = this.updateId;
    } else {
      json[r'updateId'] = null;
    }
    if (this.updateVersion != null) {
      json[r'updateVersion'] = this.updateVersion;
    } else {
      json[r'updateVersion'] = null;
    }
    return json;
  }

  /// Returns a new [FirmwareStatusResponseUpdateStatus] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static FirmwareStatusResponseUpdateStatus? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "FirmwareStatusResponseUpdateStatus[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "FirmwareStatusResponseUpdateStatus[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return FirmwareStatusResponseUpdateStatus(
        isUpdateAvailable: mapValueOfType<bool>(json, r'isUpdateAvailable')!,
        status: FirmwareStatusResponseUpdateStatusStatusEnum.fromJson(json[r'status']),
        updateId: mapValueOfType<String>(json, r'updateId'),
        updateVersion: FirmwareStatusResponseManifestType.fromJson(json[r'updateVersion']),
      );
    }
    return null;
  }

  static List<FirmwareStatusResponseUpdateStatus> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FirmwareStatusResponseUpdateStatus>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FirmwareStatusResponseUpdateStatus.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, FirmwareStatusResponseUpdateStatus> mapFromJson(dynamic json) {
    final map = <String, FirmwareStatusResponseUpdateStatus>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = FirmwareStatusResponseUpdateStatus.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of FirmwareStatusResponseUpdateStatus-objects as value to a dart map
  static Map<String, List<FirmwareStatusResponseUpdateStatus>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<FirmwareStatusResponseUpdateStatus>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = FirmwareStatusResponseUpdateStatus.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'isUpdateAvailable',
  };
}


class FirmwareStatusResponseUpdateStatusStatusEnum {
  /// Instantiate a new enum with the provided [value].
  const FirmwareStatusResponseUpdateStatusStatusEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const NOT_REQUESTED = FirmwareStatusResponseUpdateStatusStatusEnum._(r'NOT_REQUESTED');
  static const NOT_ACCEPTED = FirmwareStatusResponseUpdateStatusStatusEnum._(r'NOT_ACCEPTED');
  static const IN_PROGRESS = FirmwareStatusResponseUpdateStatusStatusEnum._(r'IN_PROGRESS');
  static const FAILED = FirmwareStatusResponseUpdateStatusStatusEnum._(r'FAILED');

  /// List of all possible values in this [enum][FirmwareStatusResponseUpdateStatusStatusEnum].
  static const values = <FirmwareStatusResponseUpdateStatusStatusEnum>[
    NOT_REQUESTED,
    NOT_ACCEPTED,
    IN_PROGRESS,
    FAILED,
  ];

  static FirmwareStatusResponseUpdateStatusStatusEnum? fromJson(dynamic value) => FirmwareStatusResponseUpdateStatusStatusEnumTypeTransformer().decode(value);

  static List<FirmwareStatusResponseUpdateStatusStatusEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FirmwareStatusResponseUpdateStatusStatusEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FirmwareStatusResponseUpdateStatusStatusEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [FirmwareStatusResponseUpdateStatusStatusEnum] to String,
/// and [decode] dynamic data back to [FirmwareStatusResponseUpdateStatusStatusEnum].
class FirmwareStatusResponseUpdateStatusStatusEnumTypeTransformer {
  factory FirmwareStatusResponseUpdateStatusStatusEnumTypeTransformer() => _instance ??= const FirmwareStatusResponseUpdateStatusStatusEnumTypeTransformer._();

  const FirmwareStatusResponseUpdateStatusStatusEnumTypeTransformer._();

  String encode(FirmwareStatusResponseUpdateStatusStatusEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a FirmwareStatusResponseUpdateStatusStatusEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  FirmwareStatusResponseUpdateStatusStatusEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'NOT_REQUESTED': return FirmwareStatusResponseUpdateStatusStatusEnum.NOT_REQUESTED;
        case r'NOT_ACCEPTED': return FirmwareStatusResponseUpdateStatusStatusEnum.NOT_ACCEPTED;
        case r'IN_PROGRESS': return FirmwareStatusResponseUpdateStatusStatusEnum.IN_PROGRESS;
        case r'FAILED': return FirmwareStatusResponseUpdateStatusStatusEnum.FAILED;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [FirmwareStatusResponseUpdateStatusStatusEnumTypeTransformer] instance.
  static FirmwareStatusResponseUpdateStatusStatusEnumTypeTransformer? _instance;
}


