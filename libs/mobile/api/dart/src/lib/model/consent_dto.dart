//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ConsentDto {
  /// Returns a new [ConsentDto] instance.
  ConsentDto({
    required this.marketing,
  });

  /// Marketing
  MarketingDto marketing;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ConsentDto &&
    other.marketing == marketing;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (marketing.hashCode);

  @override
  String toString() => 'ConsentDto[marketing=$marketing]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'marketing'] = this.marketing;
    return json;
  }

  /// Returns a new [ConsentDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ConsentDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ConsentDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ConsentDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ConsentDto(
        marketing: MarketingDto.fromJson(json[r'marketing'])!,
      );
    }
    return null;
  }

  static List<ConsentDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ConsentDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ConsentDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ConsentDto> mapFromJson(dynamic json) {
    final map = <String, ConsentDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ConsentDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ConsentDto-objects as value to a dart map
  static Map<String, List<ConsentDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ConsentDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = ConsentDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'marketing',
  };
}

