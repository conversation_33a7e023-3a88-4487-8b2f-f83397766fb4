# openapi.model.FirmwareStatusResponseManifestType

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name             | Type                                                                                          | Description | Notes      |
| ---------------- | --------------------------------------------------------------------------------------------- | ----------- | ---------- |
| **architecture** | **String**                                                                                    |             |
| **createdDate**  | **String**                                                                                    |             | [optional] |
| **details**      | [**FirmwareStatusResponseManifestTypeDetails**](FirmwareStatusResponseManifestTypeDetails.md) |             |
| **manifestId**   | **String**                                                                                    |             | [optional] |
| **status**       | **String**                                                                                    |             | [optional] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
