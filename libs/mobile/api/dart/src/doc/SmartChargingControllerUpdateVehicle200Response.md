# openapi.model.SmartChargingControllerUpdateVehicle200Response

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                   | Type                                                    | Description               | Notes      |
| ---------------------- | ------------------------------------------------------- | ------------------------- | ---------- |
| **id**                 | **String**                                              | Id                        |
| **lastSeen**           | **String**                                              | Last seen date            | [optional] |
| **enodeUserId**        | **String**                                              | The user's Enode ID       | [optional] |
| **enodeVehicleId**     | **String**                                              | The vehicles's Enode ID   | [optional] |
| **vehicleInformation** | [**VehicleInformationImpl**](VehicleInformationImpl.md) | Vehicle data              |
| **chargeState**        | [**GenericChargeStateImpl**](GenericChargeStateImpl.md) | Vehicle charge state data |
| **interventions**      | [**InterventionDtoImpl**](InterventionDtoImpl.md)       | Vehicle interventions     |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
