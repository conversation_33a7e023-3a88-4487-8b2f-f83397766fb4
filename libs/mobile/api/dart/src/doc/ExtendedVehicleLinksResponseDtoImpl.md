# openapi.model.ExtendedVehicleLinksResponseDtoImpl

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                         | Type                                                                          | Description                                  | Notes |
| ---------------------------- | ----------------------------------------------------------------------------- | -------------------------------------------- | ----- |
| **id**                       | **String**                                                                    | Id                                           |
| **isPrimary**                | **bool**                                                                      | Is this the primary charger for this vehicle |
| **isPluggedInToThisCharger** | **bool**                                                                      | Is this vehicle plugged into the charger     |
| **vehicle**                  | [**VehicleLinkResponseDtoImplVehicle**](VehicleLinkResponseDtoImplVehicle.md) |                                              |
| **intents**                  | [**VehicleIntentsResponseDtoImpl**](VehicleIntentsResponseDtoImpl.md)         | Intent data                                  |
| **currentIntent**            | [**CurrentIntentDtoImpl**](CurrentIntentDtoImpl.md)                           | The current intent for the vehicle           |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
