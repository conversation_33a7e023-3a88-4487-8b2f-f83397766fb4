# openapi.model.InterventionDtoImpl

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name            | Type             | Description                                          | Notes                 |
| --------------- | ---------------- | ---------------------------------------------------- | --------------------- |
| **all**         | **String**       | The endpoint to extract all interventions            |
| **chargeState** | **List<String>** | The individual interventions for charge state        | [default to const []] |
| **information** | **List<String>** | The individual interventions for vehicle information | [default to const []] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
