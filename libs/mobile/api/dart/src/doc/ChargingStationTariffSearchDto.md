# openapi.model.ChargingStationTariffSearchDto

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name         | Type                                                                                            | Description | Notes                 |
| ------------ | ----------------------------------------------------------------------------------------------- | ----------- | --------------------- |
| **data**     | [**List<ChargerTariffDto>**](ChargerTariffDto.md)                                               |             | [default to const []] |
| **metadata** | [**ChargingStationTariffSearchMetadataDtoImpl**](ChargingStationTariffSearchMetadataDtoImpl.md) |             |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
