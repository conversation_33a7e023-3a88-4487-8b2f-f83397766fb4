# openapi.model.ChargerTariffDto

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                  | Type                                                | Description                                                | Notes                 |
| --------------------- | --------------------------------------------------- | ---------------------------------------------------------- | --------------------- |
| **id**                | **String**                                          | The ID of the tariff                                       |
| **effectiveFrom**     | **String**                                          | The date from which the tariff is effective (inclusive)    |
| **maxChargePrice**    | **num**                                             | The maximum price during off-peak hours in pence           | [optional]            |
| **ppid**              | **String**                                          | The PPID of the charger related to the tariff              |
| **supplierId**        | **String**                                          | Reference to the supplier. Null if the supplier is unknown |
| **tariffInfo**        | [**List<TariffInfoDtoImpl>**](TariffInfoDtoImpl.md) | Tariff information applicable to specific days and times.  | [default to const []] |
| **timezone**          | **String**                                          | Timezone the tariff information applies to                 |
| **cheapestUnitPrice** | **num**                                             | The cheapest unit price (e.g., £0.10).                     |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
