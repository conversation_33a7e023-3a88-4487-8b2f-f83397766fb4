# openapi.model.ReportsDtoPayload

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                     | Type              | Description                                                                                                     | Notes      |
| ------------------------ | ----------------- | --------------------------------------------------------------------------------------------------------------- | ---------- |
| **from**                 | **String**        | Statistics report inclusive start date eg: 2022-01-01                                                           |
| **to**                   | **String**        | Statistics report inclusive end date eg: 2022-01-02                                                             |
| **distances**            | [**Object**](.md) | Total and business distances                                                                                    | [optional] |
| **organisationOnly**     | **bool**          | A filter for presenting only organisation only expenses                                                         |
| **unitOfDistance**       | **String**        | The unit of distance mi                                                                                         | km         |
| **overrideEmailAddress** | **String**        | Override the delivery email address for the report. Used for testing purposes only, does nothing in production. | [optional] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
