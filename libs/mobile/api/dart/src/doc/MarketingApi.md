# openapi.api.MarketingApi

## Load the API package

```dart
import 'package:openapi/api.dart';
```

All URIs are relative to _http://localhost_

| Method                                                                                                             | HTTP request                            | Description                                      |
| ------------------------------------------------------------------------------------------------------------------ | --------------------------------------- | ------------------------------------------------ |
| [**marketingControllerGetOpportunitiesForCharger**](MarketingApi.md#marketingcontrollergetopportunitiesforcharger) | **GET** /marketing/opportunities/{ppid} | gets marketing opportunities for a given charger |

# **marketingControllerGetOpportunitiesForCharger**

> MarketingOpportunitiesDTO marketingControllerGetOpportunitiesForCharger(ppid)

gets marketing opportunities for a given charger

For a given charger, get its marketing opportunties

### Example

```dart
import 'package:openapi/api.dart';

final api_instance = MarketingApi();
final ppid = ppid_example; // String |

try {
    final result = api_instance.marketingControllerGetOpportunitiesForCharger(ppid);
    print(result);
} catch (e) {
    print('Exception when calling MarketingApi->marketingControllerGetOpportunitiesForCharger: $e\n');
}
```

### Parameters

| Name     | Type       | Description | Notes |
| -------- | ---------- | ----------- | ----- |
| **ppid** | **String** |             |

### Return type

[**MarketingOpportunitiesDTO**](MarketingOpportunitiesDTO.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
