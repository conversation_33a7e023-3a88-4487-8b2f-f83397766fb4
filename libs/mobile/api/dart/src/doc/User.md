# openapi.model.User

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name              | Type                          | Description | Notes                            |
| ----------------- | ----------------------------- | ----------- | -------------------------------- |
| **email**         | **String**                    |             |
| **firstName**     | **String**                    |             |
| **hasHomeCharge** | **num**                       |             | [optional]                       |
| **id**            | **num**                       |             |
| **lastName**      | **String**                    |             |
| **role**          | **String**                    |             |
| **locale**        | **String**                    |             |
| **tariff**        | [**Object**](.md)             |             | [optional]                       |
| **unit**          | [**Object**](.md)             |             | [optional]                       |
| **vehicle**       | [**Object**](.md)             |             | [optional]                       |
| **account**       | [**Object**](.md)             |             | [optional]                       |
| **preferences**   | [**List<Object>**](Object.md) |             | [optional] [default to const []] |
| **notifications** | [**List<Object>**](Object.md) |             | [optional] [default to const []] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
