# openapi.model.Item

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name            | Type       | Description                                                                                                                                                                 | Notes |
| --------------- | ---------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----- |
| **id**          | **String** | This can be an address Id or a container Id for further results.                                                                                                            |
| **description** | **String** | Descriptive information about the result.                                                                                                                                   |
| **type**        | **String** | If the Type is \"Address\" then the Id can be passed to the Retrieve service. Any other Id should be passed as the Container to a further Find request to get more results. |
| **text**        | **String** | The name of the result.                                                                                                                                                     |
| **highlight**   | **String** | A list of number ranges identifying the matched characters in the Text and Description.                                                                                     |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
