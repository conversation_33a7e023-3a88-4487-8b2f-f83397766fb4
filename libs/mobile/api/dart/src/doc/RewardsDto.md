# openapi.model.RewardsDto

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                | Type                                                | Description                                                | Notes                 |
| ------------------- | --------------------------------------------------- | ---------------------------------------------------------- | --------------------- |
| **totalMiles**      | **num**                                             | The total amount of reward miles from individual chargers  |
| **balance**         | **num**                                             | The balance in the lowest unit of currency (pence for GBP) |
| **currency**        | **String**                                          | The currency represented by the balance                    |
| **payoutThreshold** | **num**                                             | The minimum amount of rewards miles required for payout    |
| **chargers**        | [**List<RewardsChargerDto>**](RewardsChargerDto.md) | The chargers for which the user is eligible for the reward | [default to const []] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
