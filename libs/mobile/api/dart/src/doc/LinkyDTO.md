# openapi.model.LinkyDTO

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                | Type     | Description                                 | Notes |
| ------------------- | -------- | ------------------------------------------- | ----- |
| **linkyCapable**    | **bool** | Wether or not the charger is Linky capable  |
| **scheduleEnabled** | **bool** | Whether or not Linky is used for scheduling |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
