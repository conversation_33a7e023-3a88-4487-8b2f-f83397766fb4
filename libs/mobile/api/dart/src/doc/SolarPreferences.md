# openapi.model.SolarPreferences

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                   | Type       | Description                                                                                                   | Notes      |
| ---------------------- | ---------- | ------------------------------------------------------------------------------------------------------------- | ---------- |
| **powerGeneration**    | **String** |                                                                                                               |
| **solarMatching**      | **String** |                                                                                                               |
| **solarThreshold**     | **num**    | Deprecated: please use solarMaxGridImport instead                                                             | [optional] |
| **solarMaxGridImport** | **num**    | The maximum amount of power in kWh to import from the grid. Replaces solarThreshold which has been deprecated | [optional] |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
