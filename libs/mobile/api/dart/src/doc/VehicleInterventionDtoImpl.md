# openapi.model.VehicleInterventionDtoImpl

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name             | Type                                                                                | Description                              | Notes |
| ---------------- | ----------------------------------------------------------------------------------- | ---------------------------------------- | ----- |
| **id**           | **String**                                                                          | The identifier of the intervention       |
| **vendor**       | **String**                                                                          | The vendor of the intervention           |
| **vendorType**   | **String**                                                                          | The type of the intervention             |
| **brand**        | **String**                                                                          | The brand of the vehicle                 |
| **introducedAt** | **String**                                                                          | The date the intervention was introduced |
| **domain**       | **String**                                                                          | The domain of the intervention           |
| **resolution**   | [**VehicleInterventionResolutionDtoImpl**](VehicleInterventionResolutionDtoImpl.md) | The resolution of the intervention       |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
