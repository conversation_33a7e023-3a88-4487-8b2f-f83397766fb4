/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface User {
  email: string;
  first_name: string;
  hasHomeCharge?: number;
  id: number;
  last_name: string;
  role: string;
  locale: string;
  tariff?: object;
  unit?: object;
  vehicle?: object;
  account?: object;
  preferences?: Array<object>;
  notifications?: Array<object>;
}
