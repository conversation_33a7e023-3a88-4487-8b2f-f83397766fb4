/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Intensity } from './intensity';

export interface Period {
  /**
   * Start time in the format hh:mm. Time is based on a 24 hour system. Seconds are not given.
   */
  from: string;
  /**
   * End time in the format hh:mm. Time is based on a 24 hour system. Seconds are not given.
   */
  to: string;
  /**
   * The carbon intensity for current half hour.
   */
  intensity: Intensity;
}
