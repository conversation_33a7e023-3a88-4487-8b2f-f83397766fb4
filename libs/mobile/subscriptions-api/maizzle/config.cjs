const FALLBACK_LANGUAGE_CODE = 'en';
const MAIN_LAYOUT_TRANSLATIONS = 'main-layout';

/** @type {import('@maizzle/framework').Config} */
module.exports = {
  build: {
    content: ['src/templates/**/*.html'],
    output: { from: 'src/templates' },
    no_cache: true,
  },
  async beforeRender({ html, config }) {
    const baseTranslations = require(
      `./messages/${FALLBACK_LANGUAGE_CODE}.json`
    );
    const template = config.build.current.path.name;
    let translations = {
      ...baseTranslations[MAIN_LAYOUT_TRANSLATIONS],
      ...baseTranslations[template],
    };

    if (config.env !== FALLBACK_LANGUAGE_CODE) {
      try {
        const lang = require(`./messages/${config.env}.json`);

        translations = {
          ...translations,
          ...lang[template],
          ...lang[MAIN_LAYOUT_TRANSLATIONS],
        };
      } catch (error) {
        console.warn(
          `Unable to load translations for ${config.env}! Will fall back to ${FALLBACK_LANGUAGE_CODE}.`
        );
      }
    }

    config.translations = translations;

    return html;
  },
  css: {
    inline: true,
    purge: {},
  },
  plaintext: true,
};
