export interface CleanLogger {
  /**
   * Write a 'log' level log.
   */
  log(message: unknown, ...optionalParams: unknown[]): unknown;
  /**
   * Write an 'error' level log.
   */
  error(message: unknown, ...optionalParams: unknown[]): unknown;
  /**
   * Write a 'warn' level log.
   */
  warn(message: unknown, ...optionalParams: unknown[]): unknown;
  /**
   * Write a 'debug' level log.
   */
  debug(message: unknown, ...optionalParams: unknown[]): unknown;
  /**
   * Write a 'verbose' level log.
   */
  verbose(message: unknown, ...optionalParams: unknown[]): unknown;
  /**
   * Write a 'fatal' level log.
   */
  fatal(message: unknown, ...optionalParams: unknown[]): unknown;
}
