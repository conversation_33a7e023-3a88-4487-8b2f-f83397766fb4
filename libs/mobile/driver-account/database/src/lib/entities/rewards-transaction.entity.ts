import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DecimalToNumberTransformer } from '../conversion';
import { U } from 'ts-toolbelt';

@Entity({ name: 'transactions', schema: 'rewards' })
export class RewardsTransaction {
  @PrimaryGeneratedColumn('uuid', {
    primaryKeyConstraintName: 'reward_transactions_pk',
  })
  id: string;

  @Column({ name: 'account_id', type: 'uuid', nullable: false })
  accountId: string;

  @Column({
    name: 'amount',
    type: 'decimal',
    precision: 19,
    scale: 2,
    nullable: false,
    transformer: new DecimalToNumberTransformer(),
  })
  amount: number;

  @Column({ name: 'currency', type: 'text', nullable: false })
  currency: string;

  @Column({ name: 'status', type: 'text', nullable: false })
  status: string;

  @Column({ name: 'transaction_hash', type: 'text', nullable: false })
  transactionHash: string;

  @Column({ name: 'transaction_date', type: 'timestamptz', nullable: false })
  transactionDate: Date;

  @Column({ name: 'reference', type: 'text', nullable: true })
  reference: U.Nullable<string>;

  @Column({ name: 'idempotency_key', type: 'text', nullable: false })
  idempotencyKey: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, string | boolean | number | null> | null;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: U.Nullable<Date>;
}
