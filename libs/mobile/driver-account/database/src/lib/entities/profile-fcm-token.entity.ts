import {
  Column,
  CreateDateColumn,
  Entity,
  <PERSON>inColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProfileUser } from './profile-user.entity';

@Entity({ name: 'fcm_tokens', schema: 'profile' })
export class ProfileFcmToken {
  @PrimaryGeneratedColumn({
    primaryKeyConstraintName: 'fcm_tokens_pk',
  })
  id: number;

  @JoinColumn({ name: 'user_id' })
  @ManyToOne(() => ProfileUser)
  user: ProfileUser;

  @Column({ name: 'fcm_token' })
  fcmToken: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
