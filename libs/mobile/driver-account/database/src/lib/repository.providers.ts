import { DataSource } from 'typeorm';
import {
  OAUTH_APPLICATION_REPOSITORY,
  OAUTH_SCOPE_REPOSITORY,
  PAYMENTS_BANK_ACCOUNTS_REPOSITORY,
  PAYMENTS_TRANSACTIONS_REPOSITORY,
  PROFILE_FCM_TOKENS_REPOSITORY,
  PROFILE_USERS_REPOSITORY,
  REWARDS_ACCOUNT_REPOSITORY,
  REWARDS_TRANSACTION_REPOSITORY,
  REWARDS_WALLET_REPOSITORY,
  SERVICE_DATASOURCE,
  SUBSCRIPTION_ACTION_REPOSITORY,
  SUBSCRIPTION_PLAN_REPOSITORY,
  SUBSCRIPTION_REPOSITORY,
} from './constants';
import { OAuthApplication } from './entities/oauth-application.entity';
import { OAuthScope } from './entities/oauth-scope.entity';
import { PaymentsBankAccounts } from './entities/payments-bank-accounts.entity';
import { PaymentsTransactions } from './entities/payments-transactions.entity';
import { ProfileFcmToken } from './entities/profile-fcm-token.entity';
import { ProfileUser } from './entities/profile-user.entity';
import { RewardsAccount } from './entities/rewards-account.entity';
import { RewardsTransaction } from './entities/rewards-transaction.entity';
import { RewardsWallet } from './entities/rewards-wallet.entity';
import { Subscription } from './entities/subscription.entity';
import { SubscriptionAction } from './entities/subscription-action.entity';
import { SubscriptionPlan } from './entities/subscription-plan.entity';

export const oauthApplicationRepository = {
  provide: OAUTH_APPLICATION_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(OAuthApplication),
  inject: [SERVICE_DATASOURCE],
};

export const oauthScopeRepository = {
  provide: OAUTH_SCOPE_REPOSITORY,
  useFactory: (dataSource: DataSource) => dataSource.getRepository(OAuthScope),
  inject: [SERVICE_DATASOURCE],
};

export const profileUserRepository = {
  provide: PROFILE_USERS_REPOSITORY,
  useFactory: (dataSource: DataSource) => dataSource.getRepository(ProfileUser),
  inject: [SERVICE_DATASOURCE],
};

export const profileFCMTokensRepository = {
  provide: PROFILE_FCM_TOKENS_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(ProfileFcmToken),
  inject: [SERVICE_DATASOURCE],
};

export const subscriptionRepository = {
  provide: SUBSCRIPTION_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(Subscription),
  inject: [SERVICE_DATASOURCE],
};

export const subscriptionPlanRepository = {
  provide: SUBSCRIPTION_PLAN_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(SubscriptionPlan),
  inject: [SERVICE_DATASOURCE],
};

export const subscriptionActionRepository = {
  provide: SUBSCRIPTION_ACTION_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(SubscriptionAction),
  inject: [SERVICE_DATASOURCE],
};

export const rewardsAccountRepository = {
  provide: REWARDS_ACCOUNT_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(RewardsAccount),
  inject: [SERVICE_DATASOURCE],
};

export const rewardsWalletRepository = {
  provide: REWARDS_WALLET_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(RewardsWallet),
  inject: [SERVICE_DATASOURCE],
};

export const rewardsTransactionRepository = {
  provide: REWARDS_TRANSACTION_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(RewardsTransaction),
  inject: [SERVICE_DATASOURCE],
};

export const paymentsBankAccountsRepository = {
  provide: PAYMENTS_BANK_ACCOUNTS_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(PaymentsBankAccounts),
  inject: [SERVICE_DATASOURCE],
};

export const paymentsTransactionsRepository = {
  provide: PAYMENTS_TRANSACTIONS_REPOSITORY,
  useFactory: (dataSource: DataSource) =>
    dataSource.getRepository(PaymentsTransactions),
  inject: [SERVICE_DATASOURCE],
};

export const repositoryProviders = [
  oauthApplicationRepository,
  oauthScopeRepository,
  profileUserRepository,
  profileFCMTokensRepository,
  subscriptionRepository,
  subscriptionPlanRepository,
  subscriptionActionRepository,
  rewardsAccountRepository,
  rewardsWalletRepository,
  rewardsTransactionRepository,
  paymentsBankAccountsRepository,
  paymentsTransactionsRepository,
];
