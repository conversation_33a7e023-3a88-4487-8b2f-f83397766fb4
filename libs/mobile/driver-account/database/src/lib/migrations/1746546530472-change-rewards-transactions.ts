import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class ChangeRewardsTransactions1746546530472
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.startTransaction();
    await queryRunner.changeColumn(
      'rewards.transactions',
      'source_account_id',
      new TableColumn({
        name: 'account_id',
        type: 'text',
        isNullable: false,
      })
    );
    await queryRunner.dropColumn(
      'rewards.transactions',
      'destination_account_id'
    );
    await queryRunner.addColumn(
      'rewards.transactions',
      new TableColumn({
        name: 'transaction_hash',
        type: 'text',
        isNullable: false,
      })
    );
    await queryRunner.commitTransaction();
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.startTransaction();
    await queryRunner.dropColumn('rewards.transactions', 'transaction_hash');
    await queryRunner.addColumn(
      'rewards.transactions',
      new TableColumn({
        name: 'destination_account_id',
        type: 'uuid',
        isNullable: false,
      })
    );
    await queryRunner.changeColumn(
      'rewards.transactions',
      'account_id',
      new TableColumn({
        name: 'source_account_id',
        type: 'text',
        isNullable: false,
      })
    );
    await queryRunner.commitTransaction();
  }
}
