import { AddOAuthSecret************* } from './*************-add-oauth-secret';
import { AddUserIdToWallets************* } from './*************-add-user-id-to-wallets';
import { AddUuidToBillingAccount************* } from './*************-add-uuid-to-billing-account';
import { AirbytePaymentsUser************* } from './*************-airbyte-payments-user';
import { AirbyteRewardsUser************* } from './*************-airbyte-rewards-user';
import { AirbyteSubscriptionsUser************* } from './*************-airbyte-subscriptions-user';
import { ApplicationsScopesJoinTable************* } from './*************-applications-scopes-join-table';
import { ApplicationsTable************* } from './*************-applications-table';
import { ChangeRewardsTransactions************* } from './*************-change-rewards-transactions';
import { ChangeSystemAccountType************* } from './*************-change-system-account-type';
import { ChangeSystemUserId************* } from './*************-change-system-user-id';
import { CreateAccountsTableInRewardsSchema************* } from './*************-create-rewards-accounts-table';
import { CreateBalanceTable************* } from './*************-create-balance-table';
import { CreateOAuthSchema************* } from './*************-create-oauth-schema';
import { CreatePaymentsSchema************* } from './*************-create-payments-schema';
import { CreatePaymentsTables************* } from './*************-create-payments-tables';
import { CreatePaymentsTransactionMetadataIndexes************* } from './*************-create-payments-transaction-metadata-indexes';
import { CreateRewardsApiUser************* } from './*************-create-rewards-api-user';
import { CreateRewardsApiUserPermissions************* } from './*************-create-rewards-api-user-permissions';
import { CreateRewardsSchema************* } from './*************-create-rewards-schema';
import { CreateRewardsTransactionsTable************* } from './*************-create-rewards-transactions-table';
import { CreateServiceUser************* } from './*************-create-service-user';
import { CreateSubscriptionsSchema1740673726810 } from './1740673726810-create-subscriptions-schema';
import { CreateSubscriptionsTableInSubscriptionsSchema1741089409471 } from './1741089409471-create-subscriptions-table-in-subscriptions-schema';
import { CreateWalletsTableInRewardsSchema1746026006845 } from './1746026006845-create-rewards-wallets-table';
import { FcmTokensAddUpdatedAt1730455789887 } from './1730455789887-fcm-tokens-add-updated-at';
import { FcmTokensDeletePermission1730908352740 } from './1730908352740-fcm-tokens-delete-permission';
import { FcmTokensIndex1729783199000 } from './1729783199000-fcm-tokens-index';
import { FcmTokensTable1729696870816 } from './1729696870816-fcm-tokens-table';
import { FcmTokensUpdatedAtIndex1730974401000 } from './1730974401000-fcm-tokens-updated-at-index';
import { FixRewardsSchemaPermissions1746613833072 } from './1746613833072-fix-rewards-schema-permissions';
import { FlexDriveToPodDrive1744126975114 } from './1744126975114-flex-drive-to-pod-drive';
import { IndexAcquiredTransactionId1751965277644 } from './1751965277644-index-acquired-transaction-id';
import { MigrateTransactionStatus1740148914523 } from './1740148914523-migrate-transaction-status';
import { PaymentTransactionMetadataAndIdempotency1751362839352 } from './1751362839352-payment-transaction-metadata-and-idempotency';
import { PaymentsDropExistingTables1751572637147 } from './1751572637147-payments-drop-existing-tables';
import { PaymentsNewSchema1751572995127 } from './1751572995127-payments-new-schema';
import { ProfileRewardsAddColumns1737974572337 } from './1737974572337-profile-rewards-add-columns';
import { ProfileSchema************* } from './*************-profile-schema';
import { RemoveBalanceTable************* } from './*************-remove-balance-table';
import { RemoveMilesRenewalDate************* } from './*************-remove-miles-renewal-date';
import { RemoveOldRewardsTables************* } from './*************-remove-old-rewards-tables';
import { RewardsAddTransactionsTable************* } from './*************-rewards-add-transactions-table';
import { RewardsBankAccountsAddSortCode************* } from './*************-rewards-bank-accounts-add-sort-code';
import { RewardsBankAccountsTable************* } from './*************-rewards-bank-accounts-table';
import { RewardsBillingAccountsTable************* } from './*************-rewards-billing-accounts-table';
import { RewardsRecipientsAddDetails************* } from './*************-rewards-recipients-add-details';
import { RewardsSystemWalletAndAccount************* } from './*************-rewards-system-wallet-and-account';
import { RewardsTransactionMetadataFix************* } from './*************-rewards-transaction-metadata-fix';
import { RewardsTransactionsAddIdempotencyColumn************* } from './*************-rewards-transactions-add-idempotency-column';
import { RewardsTransactionsMetadata************* } from './*************-rewards-transactions-metadata';
import { RewardsTransactionsPaginationKey************* } from './*************-rewards-transactions-pagination-key';
import { RewardsTransactionsUpdates************* } from './*************-rewards-transactions-updates';
import { ScopesTable************* } from './*************-scopes-table';
import { SubscriptionPlanTable************* } from './*************-subscription-plan-table';
import { SubscriptionsActionsDropVersion************* } from './*************-subscriptions-actions-drop-version';
import { SubscriptionsActionsTable************* } from './*************-subscriptions-actions-table';
import { SubscriptionsAddActivatedAt1744726967730 } from './1744726967730-subscriptions-add-activated-at';
import { SubscriptionsMilesRenewalDate1749029285411 } from './1749029285411-subscriptions-miles-renewal-date';
import { SubscriptionsOrderAddEcommerceId1748879638036 } from './1748879638036-subscriptions-order-add-ecommerce-id';
import { SubscriptionsTableAddOrderField************* } from './*************-subscriptions-table-add-order-field';
import { SubscriptionsTableAddUserIdIndex************* } from './*************-subscriptions-table-add-user-id-index';
import { SubscriptionsTableGrantPermissions************* } from './*************-subscriptions-table-grant-permissions';
import { TransactionChargerDataTable************* } from './*************-transaction-charger-data-table';
import { TransactionsBankAccountIdAmends************* } from './*************-transactions-bank-account-id-amends';
import { UpdateSubscriptionsActionsTableColumns************* } from './*************-update-subscriptions-actions-table-columns';
import { UsersTable************* } from './*************-users-table';
import { IndexMilesRenewalDate************* } from './*************-index-miles-renewal-date';
import { MilesRenewalDateNull************* } from './*************-miles-renewal-date-null';
import { ActivatedSubsMilesRenewalDate************* } from './*************-activated-subs-miles-renewal-date';

export const migrations = [
  CreateOAuthSchema*************,
  CreateServiceUser*************,
  ScopesTable*************,
  ApplicationsTable*************,
  ApplicationsScopesJoinTable*************,
  AddOAuthSecret*************,
  ProfileSchema*************,
  UsersTable*************,
  FcmTokensTable1729696870816,
  FcmTokensIndex1729783199000,
  FcmTokensAddUpdatedAt1730455789887,
  FcmTokensDeletePermission1730908352740,
  FcmTokensUpdatedAtIndex1730974401000,
  RewardsBillingAccountsTable*************,
  RewardsBankAccountsTable*************,
  CreateRewardsApiUser*************,
  CreateRewardsApiUserPermissions*************,
  CreateRewardsTransactionsTable*************,
  CreateBalanceTable*************,
  RemoveBalanceTable*************,
  RewardsTransactionsUpdates*************,
  ProfileRewardsAddColumns1737974572337,
  TransactionChargerDataTable*************,
  AddUuidToBillingAccount*************,
  MigrateTransactionStatus1740148914523,
  CreateSubscriptionsSchema1740673726810,
  CreateSubscriptionsTableInSubscriptionsSchema1741089409471,
  SubscriptionsTableAddUserIdIndex*************,
  SubscriptionsTableAddOrderField*************,
  SubscriptionPlanTable*************,
  SubscriptionsTableGrantPermissions*************,
  SubscriptionsActionsTable*************,
  UpdateSubscriptionsActionsTableColumns*************,
  SubscriptionsActionsDropVersion*************,
  FlexDriveToPodDrive1744126975114,
  SubscriptionsAddActivatedAt1744726967730,
  CreateRewardsSchema*************,
  CreateWalletsTableInRewardsSchema1746026006845,
  CreateAccountsTableInRewardsSchema*************,
  RewardsSystemWalletAndAccount*************,
  RewardsAddTransactionsTable*************,
  AddUserIdToWallets*************,
  ChangeRewardsTransactions*************,
  FixRewardsSchemaPermissions1746613833072,
  ChangeSystemAccountType*************,
  ChangeSystemUserId*************,
  CreatePaymentsSchema*************,
  CreatePaymentsTables*************,
  RemoveOldRewardsTables*************,
  AirbyteSubscriptionsUser*************,
  SubscriptionsMilesRenewalDate1749029285411,
  SubscriptionsOrderAddEcommerceId1748879638036,
  RemoveMilesRenewalDate*************,
  AirbyteRewardsUser*************,
  RewardsTransactionsAddIdempotencyColumn*************,
  RewardsRecipientsAddDetails*************,
  PaymentTransactionMetadataAndIdempotency1751362839352,
  RewardsBankAccountsAddSortCode*************,
  PaymentsDropExistingTables1751572637147,
  PaymentsNewSchema1751572995127,
  TransactionsBankAccountIdAmends*************,
  IndexAcquiredTransactionId1751965277644,
  RewardsTransactionsPaginationKey*************,
  CreatePaymentsTransactionMetadataIndexes*************,
  RewardsTransactionsMetadata*************,
  RewardsTransactionMetadataFix*************,
  AirbytePaymentsUser*************,
  IndexMilesRenewalDate*************,
  MilesRenewalDateNull*************,
  ActivatedSubsMilesRenewalDate*************,
];
