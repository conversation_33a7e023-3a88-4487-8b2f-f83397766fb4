import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const SCHEMA = 'rewards';
const USER = 'rewards_api';
const TABLE = 'accounts';

export class CreateAccountsTableInRewardsSchema1746026006907
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: SCHEMA,
        name: TABLE,
        columns: [
          {
            name: 'id',
            type: 'uuid',
            default: 'uuid_generate_v4()',
            isPrimary: true,
            primaryKeyConstraintName: 'reward_accounts_pk',
          },
          {
            name: 'type',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'reward_wallet_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'deleted_at',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            name: 'fk_reward_wallet',
            columnNames: ['reward_wallet_id'],
            referencedTableName: 'wallets',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      })
    );

    await queryRunner.query(`
      GRANT SELECT, INSERT, UPDATE, DELETE ON ${SCHEMA}.${TABLE} TO ${USER};
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      REVOKE SELECT, INSERT, UPDATE, DELETE ON ${SCHEMA}.${TABLE} FROM ${USER};
    `);

    await queryRunner.dropTable(
      new Table({
        schema: SCHEMA,
        name: TABLE,
      })
    );
  }
}
