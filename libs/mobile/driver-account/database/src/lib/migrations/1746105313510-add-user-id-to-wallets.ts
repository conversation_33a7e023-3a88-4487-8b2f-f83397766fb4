import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddUserIdToWallets1746105313510 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'rewards.wallets',
      new TableColumn({
        name: 'user_id',
        type: 'text',
        // Needed so we can create the column non-nullable but
        // temporally allow system to exist without a user_id
        // Escaped strings are required so it is interpreted
        // as a SQL string rather than a keyword
        default: "'unknown'",
        isNullable: false,
      })
    );

    await queryRunner.query(`
        UPDATE rewards.wallets SET user_id = 'system' WHERE type = 'SYSTEM';
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('rewards.wallets', 'user_id');
  }
}
