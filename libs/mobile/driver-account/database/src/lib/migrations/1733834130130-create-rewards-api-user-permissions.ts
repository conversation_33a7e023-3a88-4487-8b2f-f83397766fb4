import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRewardsApiUserPermissions1733834130130
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE ON profile.users TO rewards_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.users_id_seq TO rewards_api;
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_billing_accounts TO rewards_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_billing_accounts_id_seq TO rewards_api;
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_bank_accounts TO rewards_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_bank_accounts_id_seq TO rewards_api;
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE ON profile.users FROM rewards_api
        REVOKE USAGE, SELECT ON SEQUENCE profile.users_id_seq FROM rewards_api;
        <PERSON><PERSON><PERSON><PERSON> SELECT, INSERT, UPDATE, DELETE ON profile.rewards_billing_accounts TO rewards_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_billing_accounts_id_seq TO rewards_api;
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_bank_accounts TO rewards_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_bank_accounts_id_seq TO rewards_api;
      `);
  }
}
