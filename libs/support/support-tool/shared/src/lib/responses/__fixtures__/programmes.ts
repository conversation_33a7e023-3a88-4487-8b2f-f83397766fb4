import {
  ChargingStationProgramme,
  ProgrammeResponse,
} from '@experience/shared/axios/competitions-service-client';
import { List } from 'immutable';
import { getGetProgrammes200Response } from '@experience/shared/axios/competitions-service-client-msw';
import { mockChargingStationProgrammes } from './charger';
import { sort } from 'fast-sort';

const mockProgrammeResponse = List(
  getGetProgrammes200Response()
).toObject() as unknown as ProgrammeResponse[];

export const mockLimitedChargingStationProgrammes =
  mockChargingStationProgrammes.programmes.slice(0, 3);

export const mockLinkedProgrammeResponse: ProgrammeResponse[] =
  mockLimitedChargingStationProgrammes.map((programme, index) => ({
    ...mockProgrammeResponse[index],
    id: programme.programme.id as string,
  }));

export const mockGetProgrammesData = (): (ChargingStationProgramme &
  ProgrammeResponse)[] => {
  const programmes = mockLimitedChargingStationProgrammes.map(
    (programme, index) => ({
      ...programme,
      ...mockLinkedProgrammeResponse[index],
    })
  );

  return sort(programmes).desc((programme) => programme.enrolledAt);
};
