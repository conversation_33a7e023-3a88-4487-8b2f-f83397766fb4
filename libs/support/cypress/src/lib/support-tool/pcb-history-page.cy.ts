export const describePcbHistoryPage = () => {
  describe('PCB History Page', () => {
    it('should have the correct page title', () => {
      cy.visit('/chargers/PSL-12345/pcbs/history');
      cy.title().should('eq', 'Support Tool - PCB history - PSL-12345');
    });

    it('should show the PCB history page', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345 (Socket A)');
      cy.clickButton('Go');
      cy.clickLink('History');
      cy.shouldHaveText('PCB history - PSL-12345 (Socket A)');
      cy.shouldHaveTable('Table of pcb history');
      cy.shouldHaveText('Date/Time');
      cy.shouldHaveText('Serial number');
      cy.shouldHaveText('Error code');
      cy.findAllByText('Status').should('have.length', 2);
    });
  });
};
