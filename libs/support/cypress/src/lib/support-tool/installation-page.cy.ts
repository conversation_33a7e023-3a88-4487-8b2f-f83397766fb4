export const describeInstallationPage = (): void => {
  describe('Installation page', () => {
    it('should have the correct page title', () => {
      cy.visit('/chargers/PSL-12345/installation?socket=A');
      cy.title().should(
        'eq',
        'Support Tool - Installation details - PSL-12345 (Socket A)'
      );
    });

    it('should render correctly', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickLink('View installation details');

      cy.shouldHaveText('Installation details - PSL-12345 (Socket A)');
      cy.shouldHaveText('Installation date');
      cy.shouldHaveText('Installed by');
      cy.shouldHaveText('Company');
      cy.shouldHaveText('Out of service');
      cy.shouldHaveText('Max supply at CT clamp (Amps)');
      cy.shouldHaveText('EV charger RCBO rating (Amps)');
      cy.shouldHaveText('Charger rating (Amps)');
      cy.shouldHaveText('Power balancing installed');
      cy.shouldHaveText('Power balancing enabled');
      cy.shouldHaveText('Power balancing sensor');
      cy.shouldHaveText('PV solar system installed');
      cy.shouldHaveText('Installation images');
    });
  });
};
