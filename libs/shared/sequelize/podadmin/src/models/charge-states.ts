import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Charges, ChargesId } from './charges';

export interface ChargeStatesAttributes {
  id: number;
  chargeId: number;
  pilot: string;
  energy?: number;
  midenergy?: number;
  generationEnergy?: number;
  current1?: number;
  v1Rms?: number;
  startsAt: Date;
  endsAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type ChargeStatesPk = 'id';
export type ChargeStatesId = ChargeStates[ChargeStatesPk];
export type ChargeStatesOptionalAttributes =
  | 'id'
  | 'energy'
  | 'midenergy'
  | 'generationEnergy'
  | 'current1'
  | 'v1Rms'
  | 'startsAt'
  | 'endsAt'
  | 'createdAt'
  | 'updatedAt';
export type ChargeStatesCreationAttributes = Optional<
  ChargeStatesAttributes,
  ChargeStatesOptionalAttributes
>;

export class ChargeStates
  extends Model<ChargeStatesAttributes, ChargeStatesCreationAttributes>
  implements ChargeStatesAttributes
{
  id!: number;
  chargeId!: number;
  pilot!: string;
  energy?: number;
  midenergy?: number;
  generationEnergy?: number;
  current1?: number;
  v1Rms?: number;
  startsAt!: Date;
  endsAt?: Date;
  createdAt!: Date;
  updatedAt!: Date;

  // ChargeStates belongsTo Charges via chargeId
  charge!: Charges;
  getCharge!: Sequelize.BelongsToGetAssociationMixin<Charges>;
  setCharge!: Sequelize.BelongsToSetAssociationMixin<Charges, ChargesId>;
  createCharge!: Sequelize.BelongsToCreateAssociationMixin<Charges>;

  static initModel(sequelize: Sequelize.Sequelize): typeof ChargeStates {
    return ChargeStates.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        chargeId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'charges',
            key: 'id',
          },
          field: 'charge_id',
        },
        pilot: {
          type: DataTypes.CHAR(1),
          allowNull: false,
        },
        energy: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: true,
        },
        midenergy: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: true,
        },
        generationEnergy: {
          type: DataTypes.DECIMAL(10, 3),
          allowNull: true,
          field: 'generation_energy',
        },
        current1: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
        },
        v1Rms: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          field: 'v1rms',
        },
        startsAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'starts_at',
        },
        endsAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'ends_at',
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'updated_at',
        },
      },
      {
        sequelize,
        tableName: 'charge_states',
        timestamps: true,
        paranoid: false,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'charge_states_charge_id_foreign',
            using: 'BTREE',
            fields: [{ name: 'charge_id' }],
          },
        ],
      }
    );
  }
}
