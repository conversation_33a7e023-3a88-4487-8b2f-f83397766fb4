import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Pcbs, PcbsId } from './pcbs';
import type { PodDoors, PodDoorsId } from './pod-doors';
import type { PodUnits, PodUnitsId } from './pod-units';
import type { Users, UsersId } from './users';

export interface PcbPodUnitAttributes {
  pcbId: number;
  unitId: number;
  doorId?: number;
  insertedAt: Date;
  insertedBy?: number;
  removedAt?: Date;
  removedBy?: number;
}

export type PcbPodUnitPk = 'pcbId' | 'insertedAt';
export type PcbPodUnitId = PcbPodUnit[PcbPodUnitPk];
export type PcbPodUnitOptionalAttributes =
  | 'doorId'
  | 'insertedAt'
  | 'insertedBy'
  | 'removedAt'
  | 'removedBy';
export type PcbPodUnitCreationAttributes = Optional<
  PcbPodUnitAttributes,
  PcbPodUnitOptionalAttributes
>;

export class PcbPodUnit
  extends Model<PcbPodUnitAttributes, PcbPodUnitCreationAttributes>
  implements PcbPodUnitAttributes
{
  pcbId!: number;
  unitId!: number;
  doorId?: number;
  insertedAt!: Date;
  insertedBy?: number;
  removedAt?: Date;
  removedBy?: number;

  // PcbPodUnit belongsTo Pcbs via pcbId
  pcb!: Pcbs;
  getPcb!: Sequelize.BelongsToGetAssociationMixin<Pcbs>;
  setPcb!: Sequelize.BelongsToSetAssociationMixin<Pcbs, PcbsId>;
  createPcb!: Sequelize.BelongsToCreateAssociationMixin<Pcbs>;
  // PcbPodUnit belongsTo PodDoors via doorId
  door!: PodDoors;
  getDoor!: Sequelize.BelongsToGetAssociationMixin<PodDoors>;
  setDoor!: Sequelize.BelongsToSetAssociationMixin<PodDoors, PodDoorsId>;
  createDoor!: Sequelize.BelongsToCreateAssociationMixin<PodDoors>;
  // PcbPodUnit belongsTo PodUnits via unitId
  unit!: PodUnits;
  getUnit!: Sequelize.BelongsToGetAssociationMixin<PodUnits>;
  setUnit!: Sequelize.BelongsToSetAssociationMixin<PodUnits, PodUnitsId>;
  createUnit!: Sequelize.BelongsToCreateAssociationMixin<PodUnits>;
  // PcbPodUnit belongsTo Users via insertedBy
  insertedByUser!: Users;
  getInsertedByUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setInsertedByUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createInsertedByUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;
  // PcbPodUnit belongsTo Users via removedBy
  removedByUser!: Users;
  getRemovedByUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setRemovedByUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createRemovedByUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof PcbPodUnit {
    return PcbPodUnit.init(
      {
        pcbId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'pcbs',
            key: 'id',
          },
          field: 'pcb_id',
        },
        unitId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'pod_units',
            key: 'id',
          },
          field: 'unit_id',
        },
        doorId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          references: {
            model: 'pod_doors',
            key: 'id',
          },
          field: 'door_id',
        },
        insertedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          primaryKey: true,
          field: 'inserted_at',
        },
        insertedBy: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
          field: 'inserted_by',
        },
        removedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'removed_at',
        },
        removedBy: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
          field: 'removed_by',
        },
      },
      {
        sequelize,
        tableName: 'pcb_pod_unit',
        timestamps: false,
        paranoid: false,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'pcb_id' }, { name: 'inserted_at' }],
          },
          {
            name: 'unit_id',
            using: 'BTREE',
            fields: [{ name: 'unit_id' }],
          },
          {
            name: 'inserted_at',
            using: 'BTREE',
            fields: [{ name: 'inserted_at' }],
          },
          {
            name: 'removed_at',
            using: 'BTREE',
            fields: [{ name: 'removed_at' }],
          },
          {
            name: 'pcb_pod_unit_doors',
            using: 'BTREE',
            fields: [{ name: 'door_id' }],
          },
          {
            name: 'pcb_pod_unit_inserted_by',
            using: 'BTREE',
            fields: [{ name: 'inserted_by' }],
          },
          {
            name: 'pcb_pod_unit_removed_by',
            using: 'BTREE',
            fields: [{ name: 'removed_by' }],
          },
        ],
      }
    );
  }
}
