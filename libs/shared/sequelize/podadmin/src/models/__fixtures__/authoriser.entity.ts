import { Authorisers } from '../authorisers';

export const TEST_AUTHORISER_ENTITY_WITH_TYPE_USER: Authorisers = {
  id: 1,
  uid: '69d5d242-0cb2-4253-9678-c2b6ec92f85c',
  type: 'user',
} as Authorisers;

export const TEST_AUTHORISER_ENTITY_WITH_TYPE_RFID: Authorisers = {
  id: 2,
  uid: 'a885525c-a693-4691-afc8-8efd730c67a9',
  type: 'rfid',
} as Authorisers;

export const TEST_AUTHORISER_ENTITY_WITH_TYPE_APP: Authorisers = {
  id: 3,
  uid: '1f2c62a0-527f-43fe-ab73-a01ebb438ee1',
  type: 'app',
} as Authorisers;

export const TEST_AUTHORISER_ENTITY_WITH_TYPE_OCPI: Authorisers = {
  id: 4,
  uid: '06846f53-5edb-48a6-8c75-f0e77d4aaa15',
  type: 'ocpi',
} as Authorisers;
