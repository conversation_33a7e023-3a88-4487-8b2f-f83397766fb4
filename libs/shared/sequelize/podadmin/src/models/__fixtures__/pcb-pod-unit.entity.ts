import { PcbPodUnit } from '../init-models';
import { TEST_PCBS_ENTITY_1, TEST_PCBS_ENTITY_2 } from './pcbs.entity';
import {
  TEST_POD_DOORS_ENTITY_A,
  TEST_POD_DOORS_ENTITY_B,
} from './pod-doors.entity';

export const TEST_PCB_POD_UNIT_ENTITY: PcbPodUnit = {
  pcbId: TEST_PCBS_ENTITY_1.id,
  pcb: TEST_PCBS_ENTITY_1,
  unitId: 1,
} as PcbPodUnit;

export const TEST_PCB_POD_UNIT_ENTITY_A: PcbPodUnit = {
  door: TEST_POD_DOORS_ENTITY_A,
  doorId: TEST_POD_DOORS_ENTITY_A.id,
  pcbId: TEST_PCBS_ENTITY_1.id,
  pcb: TEST_PCBS_ENTITY_1,
  unitId: 1,
} as PcbPodUnit;

export const TEST_PCB_POD_UNIT_ENTITY_B: PcbPodUnit = {
  door: TEST_POD_DOORS_ENTITY_B,
  doorId: TEST_POD_DOORS_ENTITY_B.id,
  pcbId: TEST_PCBS_ENTITY_2.id,
  pcb: TEST_PCBS_ENTITY_2,
  unitId: 1,
} as PcbPodUnit;
