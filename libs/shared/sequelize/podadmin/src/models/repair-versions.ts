import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Pcbs, PcbsId } from './pcbs';

export interface RepairVersionsAttributes {
  id: number;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export type RepairVersionsPk = 'id';
export type RepairVersionsId = RepairVersions[RepairVersionsPk];
export type RepairVersionsOptionalAttributes =
  | 'id'
  | 'createdAt'
  | 'updatedAt'
  | 'deletedAt';
export type RepairVersionsCreationAttributes = Optional<
  RepairVersionsAttributes,
  RepairVersionsOptionalAttributes
>;

export class RepairVersions
  extends Model<RepairVersionsAttributes, RepairVersionsCreationAttributes>
  implements RepairVersionsAttributes
{
  id!: number;
  version!: string;
  createdAt!: Date;
  updatedAt!: Date;
  deletedAt?: Date;

  // RepairVersions hasMany Pcbs via repairVersionId
  pcbs!: Pcbs[];
  getPcbs!: Sequelize.HasManyGetAssociationsMixin<Pcbs>;
  setPcbs!: Sequelize.HasManySetAssociationsMixin<Pcbs, PcbsId>;
  addPcb!: Sequelize.HasManyAddAssociationMixin<Pcbs, PcbsId>;
  addPcbs!: Sequelize.HasManyAddAssociationsMixin<Pcbs, PcbsId>;
  createPcb!: Sequelize.HasManyCreateAssociationMixin<Pcbs>;
  removePcb!: Sequelize.HasManyRemoveAssociationMixin<Pcbs, PcbsId>;
  removePcbs!: Sequelize.HasManyRemoveAssociationsMixin<Pcbs, PcbsId>;
  hasPcb!: Sequelize.HasManyHasAssociationMixin<Pcbs, PcbsId>;
  hasPcbs!: Sequelize.HasManyHasAssociationsMixin<Pcbs, PcbsId>;
  countPcbs!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof RepairVersions {
    return RepairVersions.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        version: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'updated_at',
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'deleted_at',
        },
      },
      {
        sequelize,
        tableName: 'repair_versions',
        timestamps: true,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
        ],
      }
    );
  }
}
