import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { PodConnectors, PodConnectorsId } from './pod-connectors';

export interface ChargeMethodsAttributes {
  id: number;
  name: string;
  createdAt: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export type ChargeMethodsPk = 'id';
export type ChargeMethodsId = ChargeMethods[ChargeMethodsPk];
export type ChargeMethodsOptionalAttributes =
  | 'id'
  | 'name'
  | 'createdAt'
  | 'updatedAt'
  | 'deletedAt';
export type ChargeMethodsCreationAttributes = Optional<
  ChargeMethodsAttributes,
  ChargeMethodsOptionalAttributes
>;

export class ChargeMethods
  extends Model<ChargeMethodsAttributes, ChargeMethodsCreationAttributes>
  implements ChargeMethodsAttributes
{
  id!: number;
  name!: string;
  createdAt!: Date;
  updatedAt?: Date;
  deletedAt?: Date;

  // ChargeMethods hasMany PodConnectors via chargeMethodId
  podConnectors!: PodConnectors[];
  getPodConnectors!: Sequelize.HasManyGetAssociationsMixin<PodConnectors>;
  setPodConnectors!: Sequelize.HasManySetAssociationsMixin<
    PodConnectors,
    PodConnectorsId
  >;
  addPodConnector!: Sequelize.HasManyAddAssociationMixin<
    PodConnectors,
    PodConnectorsId
  >;
  addPodConnectors!: Sequelize.HasManyAddAssociationsMixin<
    PodConnectors,
    PodConnectorsId
  >;
  createPodConnector!: Sequelize.HasManyCreateAssociationMixin<PodConnectors>;
  removePodConnector!: Sequelize.HasManyRemoveAssociationMixin<
    PodConnectors,
    PodConnectorsId
  >;
  removePodConnectors!: Sequelize.HasManyRemoveAssociationsMixin<
    PodConnectors,
    PodConnectorsId
  >;
  hasPodConnector!: Sequelize.HasManyHasAssociationMixin<
    PodConnectors,
    PodConnectorsId
  >;
  hasPodConnectors!: Sequelize.HasManyHasAssociationsMixin<
    PodConnectors,
    PodConnectorsId
  >;
  countPodConnectors!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof ChargeMethods {
    return ChargeMethods.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING(191),
          allowNull: false,
          unique: 'name',
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'updated_at',
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'deleted_at',
        },
      },
      {
        sequelize,
        tableName: 'charge_methods',
        timestamps: true,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'name',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'name' }],
          },
        ],
      }
    );
  }
}
