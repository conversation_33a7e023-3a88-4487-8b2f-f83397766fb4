import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { PodUnits, PodUnitsId } from './pod-units';

export interface OcppEndpointsAttributes {
  id: number;
  url: string;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export type OcppEndpointsPk = 'id';
export type OcppEndpointsId = OcppEndpoints[OcppEndpointsPk];
export type OcppEndpointsOptionalAttributes =
  | 'id'
  | 'createdAt'
  | 'updatedAt'
  | 'deletedAt';
export type OcppEndpointsCreationAttributes = Optional<
  OcppEndpointsAttributes,
  OcppEndpointsOptionalAttributes
>;

export class OcppEndpoints
  extends Model<OcppEndpointsAttributes, OcppEndpointsCreationAttributes>
  implements OcppEndpointsAttributes
{
  id!: number;
  url!: string;
  version!: string;
  createdAt!: Date;
  updatedAt!: Date;
  deletedAt?: Date;

  // OcppEndpoints hasMany PodUnits via ocppEndpointId
  podUnits!: PodUnits[];
  getPodUnits!: Sequelize.HasManyGetAssociationsMixin<PodUnits>;
  setPodUnits!: Sequelize.HasManySetAssociationsMixin<PodUnits, PodUnitsId>;
  addPodUnit!: Sequelize.HasManyAddAssociationMixin<PodUnits, PodUnitsId>;
  addPodUnits!: Sequelize.HasManyAddAssociationsMixin<PodUnits, PodUnitsId>;
  createPodUnit!: Sequelize.HasManyCreateAssociationMixin<PodUnits>;
  removePodUnit!: Sequelize.HasManyRemoveAssociationMixin<PodUnits, PodUnitsId>;
  removePodUnits!: Sequelize.HasManyRemoveAssociationsMixin<
    PodUnits,
    PodUnitsId
  >;
  hasPodUnit!: Sequelize.HasManyHasAssociationMixin<PodUnits, PodUnitsId>;
  hasPodUnits!: Sequelize.HasManyHasAssociationsMixin<PodUnits, PodUnitsId>;
  countPodUnits!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof OcppEndpoints {
    return OcppEndpoints.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        url: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        version: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'updated_at',
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'deleted_at',
        },
      },
      {
        sequelize,
        tableName: 'ocpp_endpoints',
        timestamps: true,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
        ],
      }
    );
  }
}
