import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Groups, GroupsId } from './groups';

export interface EvDriverDomainsAttributes {
  id: number;
  domainName: string;
  groupId: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export type EvDriverDomainsPk = 'id';
export type EvDriverDomainsId = EvDriverDomains[EvDriverDomainsPk];
export type EvDriverDomainsOptionalAttributes =
  | 'id'
  | 'createdAt'
  | 'updatedAt'
  | 'deletedAt';
export type EvDriverDomainsCreationAttributes = Optional<
  EvDriverDomainsAttributes,
  EvDriverDomainsOptionalAttributes
>;

export class EvDriverDomains
  extends Model<EvDriverDomainsAttributes, EvDriverDomainsCreationAttributes>
  implements EvDriverDomainsAttributes
{
  id!: number;
  domainName!: string;
  groupId!: number;
  createdAt!: Date;
  updatedAt!: Date;
  deletedAt?: Date;

  // EvDriverDomains belongsTo Groups via groupId
  group!: Groups;
  getGroup!: Sequelize.BelongsToGetAssociationMixin<Groups>;
  setGroup!: Sequelize.BelongsToSetAssociationMixin<Groups, GroupsId>;
  createGroup!: Sequelize.BelongsToCreateAssociationMixin<Groups>;

  static initModel(sequelize: Sequelize.Sequelize): typeof EvDriverDomains {
    return EvDriverDomains.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        domainName: {
          type: DataTypes.STRING(190),
          allowNull: false,
          unique: 'ev_driver_domains_domain_name_unique',
          field: 'domain_name',
        },
        groupId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'groups',
            key: 'id',
          },
          field: 'group_id',
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'updated_at',
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'deleted_at',
        },
      },
      {
        sequelize,
        tableName: 'ev_driver_domains',
        timestamps: true,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'ev_driver_domains_domain_name_unique',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'domain_name' }],
          },
          {
            name: 'ev_driver_domains_group_id_foreign',
            using: 'BTREE',
            fields: [{ name: 'group_id' }],
          },
        ],
      }
    );
  }
}
