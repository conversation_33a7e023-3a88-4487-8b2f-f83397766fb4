import { CONNECTION_NAME } from './podadmin.module';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { Sequelize } from 'sequelize-typescript';
import { initModels } from '../models/init-models';

const logger = new Logger(CONNECTION_NAME);

export const databaseProviders = [
  {
    provide: 'SEQUELIZE',
    inject: [ConfigService],
    useFactory: async (configService: ConfigService) => {
      const sequelize = new Sequelize({
        dialect: 'mysql',
        port: configService.get('DB_PORT'),
        database: configService.get('DB_DATABASE'),
        username: configService.get('DB_USERNAME'),
        password: configService.get('DB_PASSWORD'),
        replication: {
          read: [{ host: configService.get('DB_HOST_RO') }],
          write: { host: configService.get('DB_HOST') },
        },
        logQueryParameters: true,
        logging: (msg) => logger.log(msg),
      });
      initModels(sequelize);
      return sequelize;
    },
  },
];
