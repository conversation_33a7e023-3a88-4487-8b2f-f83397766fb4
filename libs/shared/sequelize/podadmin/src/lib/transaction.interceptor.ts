import * as cls from 'cls-hooked';
import { CONNECTION_NAME } from './podadmin.module';
import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { InjectConnection } from '@nestjs/sequelize';
import { Observable, lastValueFrom, of } from 'rxjs';
import { Sequelize } from 'sequelize-typescript';

/*
 * Call useCLS in order to automatically pass transactions to all queries.
 * https://sequelize.org/docs/v6/other-topics/transactions/#automatically-pass-transactions-to-all-queries
 *
 * Use __proto__ as workaround for bug when using Sequelize Typescript
 * https://github.com/sequelize/sequelize-typescript/issues/58#issuecomment-344098256
 */

// eslint-disable-next-line @typescript-eslint/no-explicit-any
(Sequelize as any).__proto__.useCLS(cls.createNamespace('sequelize'));

@Injectable()
export class TransactionInterceptor implements NestInterceptor {
  constructor(
    @InjectConnection({ name: CONNECTION_NAME })
    private readonly sequelize: Sequelize
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallH<PERSON>ler
  ): Promise<Observable<unknown>> {
    return of(
      await this.sequelize.transaction(
        async () => await lastValueFrom(next.handle())
      )
    );
  }
}
