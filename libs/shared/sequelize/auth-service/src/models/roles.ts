import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { RoleUser, RoleUserId } from './role-user';

export interface RolesAttributes {
  id: number;
  name: string;
}

export type RolesPk = 'id';
export type RolesId = Roles[RolesPk];
export type RolesOptionalAttributes = 'id';
export type RolesCreationAttributes = Optional<
  RolesAttributes,
  RolesOptionalAttributes
>;

export class Roles
  extends Model<RolesAttributes, RolesCreationAttributes>
  implements RolesAttributes
{
  id!: number;
  name!: string;

  // Roles hasMany RoleUser via roleId
  roleUsers!: RoleUser[];
  getRoleUsers!: Sequelize.HasManyGetAssociationsMixin<RoleUser>;
  setRoleUsers!: Sequelize.HasManySetAssociationsMixin<RoleUser, RoleUserId>;
  addRoleUser!: Sequelize.HasManyAddAssociationMixin<RoleUser, RoleUserId>;
  addRoleUsers!: Sequelize.HasManyAddAssociationsMixin<RoleUser, RoleUserId>;
  createRoleUser!: Sequelize.HasManyCreateAssociationMixin<RoleUser>;
  removeRoleUser!: Sequelize.HasManyRemoveAssociationMixin<
    RoleUser,
    RoleUserId
  >;
  removeRoleUsers!: Sequelize.HasManyRemoveAssociationsMixin<
    RoleUser,
    RoleUserId
  >;
  hasRoleUser!: Sequelize.HasManyHasAssociationMixin<RoleUser, RoleUserId>;
  hasRoleUsers!: Sequelize.HasManyHasAssociationsMixin<RoleUser, RoleUserId>;
  countRoleUsers!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof Roles {
    return Roles.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
      },
      {
        sequelize,
        tableName: 'roles',
        timestamps: false,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
        ],
      }
    );
  }
}
