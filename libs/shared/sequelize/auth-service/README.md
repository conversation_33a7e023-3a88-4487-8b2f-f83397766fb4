# Auth Service Sequelize

This library contains a NestJS module which can be used to integrate with the auth-service database.

## Running unit tests

Run `nx test shared-sequelize-auth-service` to execute the unit tests via [Jest](https://jestjs.io).

## Generating model

Run `nx generate-sources shared-sequelize-auth-service --pass=**********` to generate a TypeScript model from
the
sequelize-auto.config.json file.

**Note:** use double quotes and escape any breaking characters such as '&' when injecting the password.

The password can be found in AWS Secrets Manager under the 'experience-commercial-staging' account.

Note that it is then necessary to run `npx nx format:write` across the workspace in order to format the generated source
files in accordance with the workspace wide lint conventions.

## Configuring the database connection

In your application which is consuming this library you should configure the database connection as shown below:

```
AUTH_DB_HOST=localhost
AUTH_DB_PORT=3306
AUTH_DB_USERNAME=root
AUTH_DB_PASSWORD=secret
AUTH_DB_DATABASE=podpoint
```
