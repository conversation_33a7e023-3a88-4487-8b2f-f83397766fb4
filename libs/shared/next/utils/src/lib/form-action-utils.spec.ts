import { convertToFormData } from './form-action-utils';

describe('form action utils', () => {
  describe('convert to form data', () => {
    it('should convert a key value pair record to a FormData instance', () => {
      const formData = {
        key1: 'value1',
        key2: 'value2',
      };

      const result = convertToFormData(formData);

      expect(result instanceof FormData).toBe(true);
      expect(result.get('key1')).toBe('value1');
      expect(result.get('key2')).toBe('value2');
    });

    it('should not append non-string or non-Blob values to the FormData instance', () => {
      const formData = {
        key1: 'value1',
        key2: new Blob(),
        key3: true,
        key4: { key: 'value' },
      };

      const result = convertToFormData(formData);

      expect(result.get('key1')).toBe('value1');
      expect(result.get('key2')).toBeInstanceOf(Blob);
      expect(result.get('key3')).toBeNull();
      expect(result.get('key4')).toBeNull();
    });
  });
});
