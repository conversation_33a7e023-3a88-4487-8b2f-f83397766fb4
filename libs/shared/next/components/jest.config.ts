export default {
  coverageDirectory: '../../../../coverage/libs/shared/next/components',
  displayName: 'shared-next-components',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  preset: '../../../../jest.preset.js',
  setupFilesAfterEnv: ['@testing-library/jest-dom'],
  transform: {
    '^(?!.*\\.(js|jsx|ts|tsx|css|json)$)': '@nx/react/plugins/jest',
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nx/react/babel'] }],
  },
};
