import NavigationLink, {
  NavigationLinkProps,
} from '../navigation-link/navigation-link';
import React from 'react';

export interface NavigationProps extends React.HTMLProps<HTMLElement> {
  links: NavigationLinkProps[];
}

export const Navigation = (props: NavigationProps) => (
  <nav className="mt-5 flex-1 px-6 space-y-1">
    {props.links.map(({ name, href, icon, id }) => (
      <NavigationLink href={href} key={name} name={name} icon={icon} id={id} />
    ))}
  </nav>
);

export default Navigation;
