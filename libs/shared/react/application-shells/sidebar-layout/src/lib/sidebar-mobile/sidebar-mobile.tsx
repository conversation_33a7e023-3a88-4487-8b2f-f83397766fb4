import { NavigationLinkProps } from '../navigation-link/navigation-link';
import { Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Sidebar from '../sidebar/sidebar';

export interface SidebarMobileProps extends React.HTMLProps<HTMLElement> {
  bottomNavigationLinks?: React.ReactNode[];
  show: boolean;
  close: () => void;
  navigationLinks: NavigationLinkProps[];
  showNavigationBottom?: boolean;
}

export const SidebarMobile = (props: SidebarMobileProps) => (
  <Transition
    show={props.show}
    enter="transition ease-in-out duration-300 transform"
    enterFrom="-translate-x-full w-full flex"
    enterTo="translate-x-0 w-full flex"
    leave="transition ease-in-out duration-300 transform"
    leaveFrom="translate-x-0 w-full flex"
    leaveTo="-translate-x-full w-full flex"
  >
    <aside className="md:hidden fixed w-full inset-0 flex z-40 bg-smoke print:hidden">
      <div className="absolute top-0 right-0 mt-2 mr-2">
        <button
          className="flex items-center justify-center h-10 w-10 rounded-full focus:outline-hidden focus:ring-2 focus:ring-inset focus:ring-neutral"
          onClick={props.close}
        >
          <span className="sr-only">Close sidebar</span>
          <XMarkIcon className="h-6 w-6 text-neutral" aria-hidden="true" />
        </button>
      </div>
      <div className="relative flex flex-1 flex-col max-w-xs w-full translate-x-0">
        <Sidebar {...props} />
      </div>
    </aside>
  </Transition>
);

export default SidebarMobile;
