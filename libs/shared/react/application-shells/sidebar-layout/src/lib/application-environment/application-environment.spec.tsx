import { render, screen } from '@testing-library/react';
import ApplicationEnvironment from './application-environment';
import getConfig from 'next/config';

jest.mock('next/config');

const nextConfig = {
  publicRuntimeConfig: {
    environment: 'dev',
  },
};

jest.mocked(getConfig).mockReturnValue(nextConfig);

describe('ApplicationEnvironment', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<ApplicationEnvironment />);
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<ApplicationEnvironment />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should display application environment if environment is not prod', () => {
    render(<ApplicationEnvironment />);
    expect(screen.getByText('You are viewing dev')).toBeInTheDocument();
  });

  it('should not display application environment if environment is prod', () => {
    jest.mocked(getConfig).mockReturnValueOnce({
      publicRuntimeConfig: {
        ...nextConfig.publicRuntimeConfig,
        environment: 'prod',
      },
    });

    render(<ApplicationEnvironment />);
    expect(screen.queryByText('You are viewing prod')).not.toBeInTheDocument();
  });
});
