import { InfoIcon } from '@experience/shared/react/design-system';
import getConfig from 'next/config';

export const ApplicationEnvironment = () => {
  const publicRuntimeConfig = getConfig()?.publicRuntimeConfig ?? {};
  const isNotProduction =
    publicRuntimeConfig.environment &&
    publicRuntimeConfig.environment !== 'prod';

  return isNotProduction ? (
    <div className="flex mt-2">
      <div className="shrink-0">
        <InfoIcon.SOLID />
      </div>
      <div className="ml-3 flex-1 md:flex md:justify-between">
        <p className="text-xs">
          You are viewing {publicRuntimeConfig.environment}
        </p>
      </div>
    </div>
  ) : null;
};

export default ApplicationEnvironment;
