import { Session } from 'next-auth';
import { SessionProvider } from 'next-auth/react';
import { render, screen } from '@testing-library/react';
import SidebarLayout from './sidebar-layout';
import getConfig from 'next/config';

jest.mock('next/config');

const nextConfig = {
  publicRuntimeConfig: {
    environment: 'dev',
  },
};

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));
jest.mocked(getConfig).mockReturnValue(nextConfig);

describe('SidebarLayout', () => {
  const mockSession: Session = {
    expires: '1',
    user: { email: '<EMAIL>', name: 'Bob' },
  };

  const layout = (
    <SessionProvider session={mockSession}>
      <SidebarLayout navigationLinks={[]}>
        <div>Page Content</div>
      </SidebarLayout>
    </SessionProvider>
  );

  it('should render successfully', () => {
    const { baseElement } = render(layout);
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(layout);
    expect(baseElement).toMatchSnapshot();
  });

  it('should render navigation component', () => {
    render(layout);
    expect(screen.getAllByRole('link')).toBeTruthy();
  });

  it('should render page content', () => {
    render(layout);
    expect(screen.getByText('Page Content')).toBeTruthy();
  });

  it('should not render the navigation bottom if the showNavigationBottom flag is false', () => {
    render(
      <SessionProvider session={mockSession}>
        <SidebarLayout navigationLinks={[]} showNavigationBottom={false}>
          <div>Page Content</div>
        </SidebarLayout>
      </SessionProvider>
    );

    expect(screen.queryByText('Profile')).not.toBeTruthy();
    expect(screen.queryByText('Logout')).not.toBeTruthy();
  });
});
