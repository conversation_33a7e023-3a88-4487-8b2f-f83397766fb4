describe('loading overlay component', () => {
  beforeEach(() => {
    cy.visit('/?path=/story/loading-overlay--playground');
    cy.resetControls();
  });
  afterEach(() => {
    cy.checkAccessibility();
  });

  it('should render loading overlay with svg', () => {
    cy.getStoryBookPreviewFrame()
      .findByRole('status', { name: 'Loading...' })
      .find('svg')
      .should('exist');
  });

  it('should apply aria hidden attribute', () => {
    cy.getStoryBookPreviewFrame()
      .findByRole('status', { name: 'Loading...' })
      .find('svg')
      .should('have.attr', 'aria-hidden', 'true');
  });

  it('should apply title attribute', () => {
    cy.getStoryBookPreviewFrame()
      .findByRole('status', { name: 'Loading...' })
      .should('have.attr', 'title', 'Loading...');
  });
});
