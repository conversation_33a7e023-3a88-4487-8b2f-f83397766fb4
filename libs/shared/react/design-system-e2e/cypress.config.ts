import { defineConfig } from 'cypress';
import { nxE2EPreset } from '@nx/cypress/plugins/cypress-preset';

const cypressJsonConfig = {
  baseUrl: 'http://localhost:4400',
  chromeWebSecurity: false,
  defaultCommandTimeout: 60000,
  fileServerFolder: '.',
  fixturesFolder: './src/fixtures',
  requestTimeout: 60000,
  retries: 1,
  screenshotsFolder:
    '../../../../dist/cypress/libs/shared/react/design-system-e2e/screenshots',
  specPattern: 'src/e2e/**/*.cy.{js,jsx,ts,tsx}',
  supportFile: 'src/support/e2e.ts',
  video: false,
  videosFolder:
    '../../../../dist/cypress/libs/shared/react/design-system-e2e/videos',
};
export default defineConfig({
  e2e: {
    ...nxE2EPreset(__dirname),
    ...cypressJsonConfig,
    // Please ensure you use `cy.origin()` when navigating between domains and remove this option.
    // See https://docs.cypress.io/app/references/migration-guide#Changes-to-cyorigin
    injectDocumentDomain: true,
  },
});
