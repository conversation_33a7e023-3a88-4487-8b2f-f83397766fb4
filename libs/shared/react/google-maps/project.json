{"name": "shared-react-google-maps", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/react/google-maps/src", "projectType": "library", "tags": ["shared"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/react/google-maps/jest.config.ts", "passWithNoTests": false}}}}