import { Tab, Tabs } from './tabs';
import { fireEvent, render, screen } from '@testing-library/react';

const tabsFixture = (
  <Tabs>
    <Tab title="Foo">
      <p role="presentation">Foo</p>
    </Tab>
    <Tab title="Bar">
      <p role="presentation">Bar</p>
    </Tab>
  </Tabs>
);

describe('Tabs', () => {
  it('should render successfully', () => {
    const { asFragment } = render(tabsFixture);

    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the default tab (first tab)', () => {
    render(tabsFixture);

    expect(screen.getByRole('presentation')).toHaveTextContent('Foo');
  });

  it('should render all the possible tab buttons for each associated tab in the tab-bar', () => {
    render(tabsFixture);

    expect(screen.getAllByRole('tab')).toHaveLength(2);
  });

  it('should render a tab-bar content when the tab button is clicked', () => {
    render(tabsFixture);

    expect(screen.getByRole('presentation')).toHaveTextContent('Foo');

    fireEvent.click(screen.getByText('Bar'));

    expect(screen.getByRole('presentation')).toHaveTextContent('Bar');
  });
});
