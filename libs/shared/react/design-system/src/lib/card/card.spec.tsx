import { Card } from './card';
import { render, screen } from '@testing-library/react';

const onClickHandler = jest.fn();
const CardComponent = (
  <Card>
    <div className="flex justify-between">
      <Card.Header>Header</Card.Header>
      <Card.EditButton
        id="edit-card"
        ariaLabel="Edit card"
        onClick={onClickHandler}
      />
    </div>
    Body copy
    <Card.Metric value={22000} unitSuffix="kWh" />
  </Card>
);

describe('Card', () => {
  it('should render successfully', () => {
    const { baseElement } = render(CardComponent);
    expect(baseElement).toBeTruthy();
  });

  it('should return unknown if metric value is undefined', () => {
    render(
      <Card>
        <Card.Metric value={undefined} />
      </Card>
    );
    expect(screen.getByText('Unknown')).toBeInTheDocument();
  });

  it('should call on click when the edit button is clicked', () => {
    render(CardComponent);
    screen.getByRole('button', { name: 'Edit card' }).click();
    expect(onClickHandler).toHaveBeenCalledTimes(1);
  });

  it('should match the snapshot', () => {
    const { baseElement } = render(CardComponent);
    expect(baseElement).toMatchSnapshot();
  });
});
