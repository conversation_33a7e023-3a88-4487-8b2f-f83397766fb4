// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Checkbox should match the snapshot 1`] = `
<body>
  <div>
    <div
      class=""
    >
      <label
        class="text-md font-bold block mb-2"
        for="foo"
      >
        test-checkbox
      </label>
    </div>
    <input
      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral"
      id="foo"
      type="checkbox"
      value=""
    />
  </div>
</body>
`;
