import { render } from '@testing-library/react';
import ArrowRightIcon from './arrow-right-icon';

describe('ArrowRightIcon', () => {
  it('should render solid icon successfully', () => {
    const { baseElement } = render(<ArrowRightIcon.SOLID />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the solid icon snapshot', () => {
    const { baseElement } = render(<ArrowRightIcon.SOLID />);
    expect(baseElement).toMatchSnapshot();
  });
});
