import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type EllipsisIconProps = React.HTMLProps<SVGElement>;

export const EllipsisIcon = {
  SOLID: (props: EllipsisIconProps) => (
    <IconWrapper viewBox="0 0 36 36" {...props}>
      <circle cx="17.8" cy="18.2" r="3.4"></circle>
      <circle cx="29.5" cy="18.2" r="3.4"></circle>
      <circle cx="6.1" cy="18.2" r="3.4"></circle>
    </IconWrapper>
  ),
};

export default EllipsisIcon;
