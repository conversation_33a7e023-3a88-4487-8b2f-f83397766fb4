import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type TariffUnlinkWithChargerIconProps = React.HTMLProps<SVGElement>;

export const UnlinkWithChargerIcon = {
  LIGHT: (props: TariffUnlinkWithChargerIconProps) => (
    <IconWrapper {...props} viewBox="0 0 24 24">
      <g>
        <path d="m9.59,19.22l-1.58,1.58c-1.49,1.49-3.91,1.49-5.4,0s-1.49-3.91,0-5.4l3.96-3.96c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-3.96,3.96c-2.07,2.07-2.07,5.45,0,7.52,1,1,2.34,1.56,3.76,1.56s2.76-.55,3.76-1.56l1.58-1.58c.29-.29.29-.77,0-1.06-.29-.29-.77-.29-1.06,0Z" />
        <path d="m12.16,5.92l3.31-3.31c1.44-1.44,3.96-1.44,5.4,0,1.49,1.49,1.49,3.91,0,5.4-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22c2.07-2.07,2.07-5.45,0-7.52s-5.45-2.07-7.52,0l-3.31,3.31c-.29.29-.29.77,0,1.06s.77.29,1.06,0Z" />
        <path d="m9.17,4.03c.41,0,.75-.34.75-.75V.87c0-.41-.34-.75-.75-.75s-.75.34-.75.75v2.41c0,.41.34.75.75.75Z" />
        <path d="m4.89,4.89c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-1.81-1.81c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l1.81,1.81Z" />
        <path d="m1.56,8.77h2.22c.41,0,.75-.34.75-.75s-.34-.75-.75-.75H1.56c-.41,0-.75.34-.75.75s.34.75.75.75Z" />
      </g>
      <g transform="scale(0.5) translate(16 16)">
        <path d="M15.76,21a.71.71,0,0,1-.72-.71V16.49h-2.5a.69.69,0,0,1-.39-.12.72.72,0,0,1-.22-1L16.8,7.84a.69.69,0,0,1,.63-.34.72.72,0,0,1,.5.24.7.7,0,0,1,.19.52l0,3.68h2.67a.77.77,0,0,1,.39.11.74.74,0,0,1,.31.46.69.69,0,0,1-.1.54l-5,7.61A.73.73,0,0,1,15.76,21Zm2-12.54ZM17,8.22v0Zm.27-.08h0Z" />
        <path d="M16.65,31.4a1,1,0,0,1-.7-.29c-.44-.43-10.8-10.65-10.8-19a11.5,11.5,0,0,1,23,0c0,8.31-10.35,18.59-10.79,19A1,1,0,0,1,16.65,31.4Zm0-28.8a9.51,9.51,0,0,0-9.5,9.49c0,6.35,7.19,14.44,9.49,16.87,2.31-2.44,9.5-10.56,9.5-16.87A9.5,9.5,0,0,0,16.65,2.6Z" />
      </g>
    </IconWrapper>
  ),
};

export default UnlinkWithChargerIcon;
