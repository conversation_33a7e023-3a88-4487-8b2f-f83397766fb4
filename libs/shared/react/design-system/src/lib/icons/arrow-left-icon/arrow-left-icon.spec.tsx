import { render } from '@testing-library/react';
import ArrowLeftIcon from './arrow-left-icon';

describe('ArrowLeftIcon', () => {
  it.each([
    ['light', ArrowLeftIcon.LIGHT],
    ['solid', ArrowLeftIcon.SOLID],
  ])('should render %s icon successfully', (_, Component) => {
    const { baseElement } = render(<Component />);
    expect(baseElement).toBeTruthy();
  });

  it.each([
    ['light', ArrowLeftIcon.LIGHT],
    ['solid', ArrowLeftIcon.SOLID],
  ])('should match the %s icon snapshot', (_, Component) => {
    const { baseElement } = render(<Component />);
    expect(baseElement).toMatchSnapshot();
  });
});
