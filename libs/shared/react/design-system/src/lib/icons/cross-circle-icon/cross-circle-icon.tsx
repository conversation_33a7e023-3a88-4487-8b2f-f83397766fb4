import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type CrossCircleIconProps = React.HTMLProps<SVGElement>;

export const CrossCircleIcon = {
  LIGHT: (props: CrossCircleIconProps) => (
    <IconWrapper title="Cross Circle" {...props}>
      <g>
        <path d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z" />
        <path d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z" />
      </g>
    </IconWrapper>
  ),
  SMALL: (props: CrossCircleIconProps) => (
    <IconWrapper title="Cross Circle" {...props} viewBox="0 0 104 103">
      <g>
        <path
          d="M52 101.5C79.6142 101.5 102 79.1142 102 51.5C102 23.8858 79.6142 1.5 52 1.5C24.3858 1.5 2 23.8858 2 51.5C2 79.1142 24.3858 101.5 52 101.5Z"
          stroke="#EB4D4B"
          strokeWidth="3"
        />
        <path
          d="M62.338 41.1641L41.664 61.8391"
          stroke="#EB4D4B"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M41.664 41.1641L62.338 61.8381"
          stroke="#EB4D4B"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </IconWrapper>
  ),
};

export default CrossCircleIcon;
