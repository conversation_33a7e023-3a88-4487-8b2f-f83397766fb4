import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type NoTariffsIconProps = React.SVGAttributes<SVGElement>;

export const NoTariffsIcon = {
  SOLID: (props: NoTariffsIconProps) => (
    <IconWrapper
      width="w-[191px]"
      height="h-[197px]"
      viewBox="0 0 191 197"
      fill="none"
      {...props}
    >
      <g transform="translate(5.4, 30.3)">
        <path
          d="M121.063 99.4563C97.0927 106.501 90.997 95.8224 76.8417 90.9033C62.6863 85.9843 47.1537 78.6973 26.3093 91.7446C5.46484 104.792 8.8621 128.994 23.7377 144.917C38.6132 160.84 65.9984 169.582 91.5466 163.466C117.095 157.35 156.194 142.602 158.562 123.199C160.931 103.795 145.031 92.4116 121.063 99.4563Z"
          fill="#7F8083"
          opacity="0.1"
        />
        <path
          d="M182.522 69.7332L24.9475 24.5493L0.000398323 111.55L157.575 156.734L182.522 69.7332Z"
          fill="#7F8083"
          opacity="0.3"
        />
        <path
          d="M40.222 25.9889L47.6609 0.0463867C47.6609 0.0463867 80.6428 18.2215 114.393 31.4738C152.986 46.6212 186.534 55.7406 186.534 55.7406L183.301 67.0162L40.222 25.9889Z"
          fill="#7F8083"
          opacity="0.1"
        />
        <path
          d="M48.9621 104.058C62.2343 107.864 76.0788 100.19 79.8846 86.9177C83.6903 73.6455 76.0162 59.801 62.7439 55.9953C49.4717 52.1895 35.6272 59.8636 31.8215 73.1359C28.0157 86.4081 35.6898 100.253 48.9621 104.058Z"
          fill="#7F8083"
          opacity="0.3"
        />
      </g>
    </IconWrapper>
  ),
};

export default NoTariffsIcon;
