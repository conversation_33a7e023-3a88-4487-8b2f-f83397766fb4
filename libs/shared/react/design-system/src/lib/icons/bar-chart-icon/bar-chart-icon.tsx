import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type BarChartIconProps = React.HTMLProps<SVGElement>;

export const BarChartIcon = {
  LIGHT: (props: BarChartIconProps) => (
    <IconWrapper {...props} viewBox="0 0 24 24">
      <g>
        <path d="m20.96,21.83H3.04c-1.52,0-2.75-1.23-2.75-2.75V4.76c0-1.52,1.23-2.75,2.75-2.75h17.93c1.52,0,2.75,1.23,2.75,2.75v14.32c0,1.52-1.23,2.75-2.75,2.75ZM3.04,3.51c-.69,0-1.25.56-1.25,1.25v14.32c0,.69.56,1.25,1.25,1.25h17.93c.69,0,1.25-.56,1.25-1.25V4.76c0-.69-.56-1.25-1.25-1.25H3.04Z" />
        <path d="m5.39,18.9c-.41,0-.75-.34-.75-.75v-3.69c0-.41.34-.75.75-.75s.75.34.75.75v3.69c0,.41-.34.75-.75.75Z" />
        <path d="m11.75,18.9c-.41,0-.75-.34-.75-.75V6.75c0-.41.34-.75.75-.75s.75.34.75.75v11.4c0,.41-.34.75-.75.75Z" />
        <path d="m18.66,18.9c-.41,0-.75-.34-.75-.75v-7.21c0-.41.34-.75.75-.75s.75.34.75.75v7.21c0,.41-.34.75-.75.75Z" />
      </g>
    </IconWrapper>
  ),
};

export default BarChartIcon;
