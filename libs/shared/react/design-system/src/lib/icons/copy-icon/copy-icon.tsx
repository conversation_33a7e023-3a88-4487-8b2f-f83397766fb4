import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type CopyIconProps = React.HTMLProps<SVGElement>;

export const CopyIcon = {
  LIGHT: (props: CopyIconProps) => (
    <IconWrapper {...props} viewBox="0 0 24 24">
      <defs>
        <style>.cls-1 {}</style>
      </defs>
      <path
        style={{
          fill: 'none',
          stroke: 'currentColor',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          strokeWidth: '1.5px',
        }}
        d="M18.73,14.5h1.83c1.35,0,2.44-1.09,2.44-2.44V3.5c0-1.35-1.09-2.44-2.44-2.44h-8.56c-1.35,0-2.44,1.09-2.44,2.44v1.83M3.45,9.61h8.56c1.35,0,2.44,1.09,2.44,2.44v8.56c0,1.35-1.09,2.44-2.44,2.44H3.45c-1.35,0-2.44-1.09-2.44-2.44v-8.56c0-1.35,1.09-2.44,2.44-2.44Z"
      />
    </IconWrapper>
  ),
};

export default CopyIcon;
