// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`NoDriversIcon should match snapshot 1`] = `
<body>
  <div>
    <svg
      class="fill-current h-[193px] w-[200px]"
      viewBox="0 0 200 193"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>
        No drivers
      </title>
      <g
        transform="translate(30 57)"
      >
        <path
          d="M130.669 64.368C107.106 82.3365 95.8327 73.0856 78.0581 73.642C60.2835 74.1984 40.0038 72.7019 22.4463 95.9884C4.88887 119.275 18.906 144.64 42.1331 155.973C65.3602 167.307 99.3966 165.382 125.102 147.781C150.808 130.181 187.865 97.2718 182.264 74.7766C176.663 52.2814 154.229 46.4091 130.669 64.368Z"
          fill="#7F8083"
          opacity="0.1"
        />
        <path
          d="M117.045 125.475C124.397 125.475 130.357 119.498 130.357 112.126C130.357 104.754 124.397 98.7772 117.045 98.7772C109.693 98.7772 103.733 104.754 103.733 112.126C103.733 119.498 109.693 125.475 117.045 125.475Z"
          fill="#7F8083"
          opacity="0.3"
        />
        <path
          d="M43.6476 125.475C50.9996 125.475 56.9596 119.498 56.9596 112.126C56.9596 104.754 50.9996 98.7772 43.6476 98.7772C36.2957 98.7772 30.3357 104.754 30.3357 112.126C30.3357 119.498 36.2957 125.475 43.6476 125.475Z"
          fill="#7F8083"
          opacity="0.3"
        />
        <path
          d="M140.357 112.126C144.794 112.126 148.441 108.53 148.441 104.019V82.3807C148.441 79.455 146.678 76.773 144.004 75.6759L130.509 70.0681C128.321 69.1538 126.558 67.4471 125.525 65.2528L113.854 39.7742C112.152 35.9951 108.384 33.6179 104.311 33.6179H26C17 33.6179 13.5588 39.5464 13.5588 46.0464V103.654C13.5588 108.347 17.3275 112.126 22.008 112.126H140.357Z"
          fill="#7F8083"
          opacity="0.3"
        />
      </g>
    </svg>
  </div>
</body>
`;
