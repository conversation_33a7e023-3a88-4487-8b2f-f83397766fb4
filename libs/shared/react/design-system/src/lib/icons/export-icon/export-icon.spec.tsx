import { render } from '@testing-library/react';
import ExportIcon from './export-icon';

describe('ExportIcon', () => {
  it('should render light icon successfully', () => {
    const { baseElement } = render(<ExportIcon.LIGHT />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the light icon snapshot', () => {
    const { baseElement } = render(<ExportIcon.LIGHT />);
    expect(baseElement).toMatchSnapshot();
  });
});
