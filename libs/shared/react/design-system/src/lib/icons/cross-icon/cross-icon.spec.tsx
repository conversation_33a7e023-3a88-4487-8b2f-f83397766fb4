import { render } from '@testing-library/react';
import CrossIcon from './cross-icon';

describe('CrossIcon', () => {
  it('should render light icon successfully', () => {
    const { baseElement } = render(<CrossIcon.LIGHT />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the light icon snapshot', () => {
    const { baseElement } = render(<CrossIcon.LIGHT />);
    expect(baseElement).toMatchSnapshot();
  });
});
