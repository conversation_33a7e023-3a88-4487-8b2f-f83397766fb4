import { render, screen } from '@testing-library/react';
import Paragraph, { TextSize, TextWeight } from './paragraph';

describe('Paragraph', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<Paragraph>Hello</Paragraph>);
    expect(baseElement).toBeTruthy();
  });

  it('should contain the paragraph text', () => {
    render(<Paragraph>Hello</Paragraph>);
    expect(screen.getByText('Hello')).toBeTruthy();
  });

  it('should apply additional css styling', () => {
    render(<Paragraph className="another-class">Hello</Paragraph>);
    expect(screen.getByText('Hello')).toHaveClass('another-class');
  });

  it.each([
    TextWeight.Regular,
    TextWeight.Bold,
    TextWeight.Medium,
    TextWeight.Semibold,
  ])(
    'should match the snapshot when text size set to sub-heading and the font weight is %s',
    (textWeight) => {
      const { baseElement } = render(
        <Paragraph textSize={TextSize.SubHeading} textWeight={textWeight}>
          Hello
        </Paragraph>
      );
      expect(baseElement).toMatchSnapshot();
    }
  );

  it.each([
    TextWeight.Regular,
    TextWeight.Bold,
    TextWeight.Medium,
    TextWeight.Semibold,
  ])(
    'should match the snapshot when text size set to body and the font weight is %s',
    (textWeight) => {
      const { baseElement } = render(
        <Paragraph textSize={TextSize.Body} textWeight={textWeight}>
          Hello
        </Paragraph>
      );
      expect(baseElement).toMatchSnapshot();
    }
  );

  it.each([
    TextWeight.Regular,
    TextWeight.Bold,
    TextWeight.Medium,
    TextWeight.Semibold,
  ])(
    'should match the snapshot when text size set to small and the font weight is %s',
    (textWeight) => {
      const { baseElement } = render(
        <Paragraph textSize={TextSize.ExtraSmall} textWeight={textWeight}>
          Hello
        </Paragraph>
      );
      expect(baseElement).toMatchSnapshot();
    }
  );

  it.each([
    TextWeight.Regular,
    TextWeight.Bold,
    TextWeight.Medium,
    TextWeight.Semibold,
  ])(
    'should match the snapshot when text size set to info and the font weight is %s',
    (textWeight) => {
      const { baseElement } = render(
        <Paragraph textSize={TextSize.Info} textWeight={textWeight}>
          Hello
        </Paragraph>
      );
      expect(baseElement).toMatchSnapshot();
    }
  );

  it.each([
    TextWeight.Regular,
    TextWeight.Bold,
    TextWeight.Medium,
    TextWeight.Semibold,
  ])(
    'should match the snapshot when text size set to large and the font weight is %s',
    (textWeight) => {
      const { baseElement } = render(
        <Paragraph textSize={TextSize.Large} textWeight={textWeight}>
          Hello
        </Paragraph>
      );
      expect(baseElement).toMatchSnapshot();
    }
  );
});
