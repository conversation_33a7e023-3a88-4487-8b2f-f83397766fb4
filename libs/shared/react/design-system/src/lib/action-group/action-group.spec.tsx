import { ActionGroup, ActionItem } from './action-group';
import { DeleteIcon } from '../icons';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

const defaultItemProps = {
  'aria-label': 'Item 1',
  onClick: () => jest.fn(),
  text: 'Item 1',
  icon: <DeleteIcon.LIGHT />,
};

describe('ActionGroup', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <ActionGroup>
        <ActionItem {...defaultItemProps} />
        <ActionItem {...defaultItemProps} aria-label="Item 2" text="Item 2" />
      </ActionGroup>
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot when there are 3 items or less', () => {
    const { baseElement } = render(
      <ActionGroup>
        <ActionItem {...defaultItemProps} />
        <ActionItem {...defaultItemProps} aria-label="Item 2" text="Item 2" />
        <ActionItem {...defaultItemProps} aria-label="Item 2" text="Item 3" />
      </ActionGroup>
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot when there are more than 3 items', () => {
    const { baseElement } = render(
      <ActionGroup>
        <ActionItem {...defaultItemProps} />
        <ActionItem
          aria-label="Item 2"
          href="/"
          text="Item 2"
          icon={<DeleteIcon.LIGHT />}
        />
        <ActionItem {...defaultItemProps} aria-label="Item 3" text="Item 3" />
        <ActionItem {...defaultItemProps} aria-label="Item 4" text="Item 4" />
        <ActionItem
          aria-label="Item 5"
          href="/"
          text="Item 5"
          icon={<DeleteIcon.LIGHT />}
        />
      </ActionGroup>
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot when showIconOnly is true', () => {
    const { baseElement } = render(
      <ActionGroup showIconOnly={true}>
        <ActionItem {...defaultItemProps} />
        <ActionItem {...defaultItemProps} aria-label="Item 2" text="Item 2" />
        <ActionItem {...defaultItemProps} aria-label="Item 3" text="Item 3" />
      </ActionGroup>
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should show 3 buttons if more than 3 items are passed', () => {
    render(
      <ActionGroup>
        <ActionItem {...defaultItemProps} />
        <ActionItem {...defaultItemProps} aria-label="Item 2" text="Item 2" />
        <ActionItem {...defaultItemProps} aria-label="Item 3" text="Item 3" />
        <ActionItem {...defaultItemProps} aria-label="Item 4" text="Item 4" />
      </ActionGroup>
    );

    expect(screen.getAllByRole('button')).toHaveLength(3);
    expect(screen.getByRole('button', { name: 'Item 2' })).toBeInTheDocument();
    expect(
      screen.queryByRole('button', { name: 'Item 3' })
    ).not.toBeInTheDocument();
  });

  it('should show all items when menu button is clicked', async () => {
    render(
      <ActionGroup>
        <ActionItem {...defaultItemProps} />
        <ActionItem {...defaultItemProps} aria-label="Item 2" text="Item 2" />
        <ActionItem {...defaultItemProps} aria-label="Item 3" text="Item 3" />
        <ActionItem {...defaultItemProps} aria-label="Item 4" text="Item 4" />
      </ActionGroup>
    );
    expect(
      screen.queryByRole('button', { name: 'Item 3' })
    ).not.toBeInTheDocument();

    await userEvent.click(screen.getByRole('button', { name: 'More actions' }));
    expect(
      screen.getByRole('menuitem', { name: 'Item 3' })
    ).toBeInTheDocument();
  });
});
