// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Compound Percentage Bar should match the snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col gap-y-4"
      role="figure"
    >
      <div
        class="flex flex-row w-full gap-x-1"
      >
        <div
          class="flex items-center justify-center first:rounded-l-full last:rounded-r-full text-white"
          style="background-color: rgb(111, 172, 71); width: 45%;"
        >
          45%
        </div>
        <div
          class="flex items-center justify-center first:rounded-l-full last:rounded-r-full text-black"
          style="background-color: rgb(249, 202, 36); width: 55%;"
        >
          55%
        </div>
      </div>
      <div
        class="flex flex-row gap-4"
      >
        <div
          class="flex flex-row gap-1 items-center justify-center"
        >
          <div
            class="rounded-full w-5 h-5"
            style="background-color: rgb(111, 172, 71);"
          />
          <p>
            Foo
          </p>
        </div>
        <div
          class="flex flex-row gap-1 items-center justify-center"
        >
          <div
            class="rounded-full w-5 h-5"
            style="background-color: rgb(249, 202, 36);"
          />
          <p>
            Bar
          </p>
        </div>
      </div>
    </div>
  </div>
</body>
`;
