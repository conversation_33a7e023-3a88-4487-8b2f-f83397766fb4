import { TextSize, TextWeight } from '../paragraph/paragraph';
import { twMerge } from 'tailwind-merge';
import classNames from 'classnames';

interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  text: string;
  isSrOnly?: boolean;
}

export const Label = ({
  className,
  id,
  isSrOnly,
  text,
  ...props
}: LabelProps) => (
  <label
    id={id}
    className={twMerge(
      classNames(
        TextSize.Body,
        TextWeight.Bold,
        { 'sr-only': isSrOnly },
        className
      )
    )}
    {...props}
  >
    {text}
  </label>
);

export default Label;
