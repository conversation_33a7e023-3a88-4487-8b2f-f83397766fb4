// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`<PERSON><PERSON> should match the snapshot when button type is link 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`But<PERSON> should match the snapshot when button type is link and button is disabled 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75"
      disabled=""
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`<PERSON><PERSON> should match the snapshot when button type is negative 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-error border-error text-white hover:bg-error/80 hover:border-error/80 focus:bg-error/80 focus:border-error/80 active:bg-error/90 active:border-error/90 disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is negative and button is disabled 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-error border-error text-white hover:bg-error/80 hover:border-error/80 focus:bg-error/80 focus:border-error/80 active:bg-error/90 active:border-error/90 disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
      disabled=""
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is negative-link 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-error hover:underline hover:text-error active:font-bold active:text-error disabled:text-neutral disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is negative-link and button is disabled 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-error hover:underline hover:text-error active:font-bold active:text-error disabled:text-neutral disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75"
      disabled=""
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is negative-outline 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is negative-outline and button is disabled 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
      disabled=""
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is primary 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is primary and button is disabled 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
      disabled=""
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is primary-outline 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when button type is primary-outline and button is disabled 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
      disabled=""
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when padding type is py-1.5 px-3.75 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;

exports[`Button should match the snapshot when padding type is py-2.5 px-3.75 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-2.5 px-3.75"
      type="button"
    >
      Hello
    </button>
  </div>
</body>
`;
