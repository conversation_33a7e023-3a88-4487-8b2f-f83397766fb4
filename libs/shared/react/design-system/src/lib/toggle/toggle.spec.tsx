import { fireEvent, render, screen } from '@testing-library/react';
import Toggle from './toggle';

const mockSetEnabled = jest.fn();

const defaultProps = {
  checked: false,
  setEnabled: mockSetEnabled,
};

describe('Toggle', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<Toggle {...defaultProps} />);
    expect(baseElement).toBeTruthy();
  });
  it('should set toggle checked to true', () => {
    render(<Toggle {...defaultProps} />);

    fireEvent.click(screen.getByRole('switch'));

    expect(mockSetEnabled).toHaveBeenCalledWith(true);
  });

  it('should set toggle checked to false', () => {
    render(<Toggle {...defaultProps} checked />);

    fireEvent.click(screen.getByRole('switch'));

    expect(mockSetEnabled).toHaveBeenCalledWith(false);
  });

  it('should show label if sr only label option is false', () => {
    render(
      <Toggle
        {...defaultProps}
        checked
        labelProps={{ text: 'a label', isSrOnly: false }}
        id="a toggle"
      />
    );

    expect(screen.getByText('a label')).not.toHaveClass('sr-only');
  });

  it('should append screen reader only class if sr only label option is true', () => {
    render(
      <Toggle
        {...defaultProps}
        checked
        labelProps={{ text: 'a label', isSrOnly: true }}
        id="a toggle"
      />
    );

    expect(screen.getByText('a label')).toHaveClass('sr-only');
  });

  it('should match the snapshot', () => {
    const { baseElement } = render(<Toggle {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should disable the toggle is disabled is true', () => {
    render(<Toggle {...defaultProps} disabled />);

    expect(screen.getByRole('switch')).toBeDisabled();
  });
});
