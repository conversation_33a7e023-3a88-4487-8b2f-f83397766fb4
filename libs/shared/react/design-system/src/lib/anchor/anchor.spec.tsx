import { render } from '@testing-library/react';
import Anchor from './anchor';

describe('Anchor', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <Anchor href="https://pod-point.com" target="_blank">
        Pod Point
      </Anchor>
    );
    expect(baseElement).toBeTruthy();
  });

  it('should match the snapshot', () => {
    const { baseElement } = render(
      <Anchor href="https://pod-point.com">Pod Point</Anchor>
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should match the snapshot if is a native html link', () => {
    const { baseElement } = render(
      <Anchor href="https://pod-point.com" target="_blank" isNativeLink>
        Pod Point
      </Anchor>
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should match the snapshot with custom class overrides', () => {
    const { baseElement } = render(
      <Anchor
        className="hover:text-error"
        href="https://pod-point.com"
        target="_blank"
        isNativeLink
      >
        Pod Point
      </Anchor>
    );
    expect(baseElement).toMatchSnapshot();
  });
});
