import { twMerge } from 'tailwind-merge';
import React from 'react';
import classNames from 'classnames';

export interface IconWrapperProps extends React.HTMLProps<SVGElement> {
  title?: string;
  viewBox?: string;
}

export const IconWrapper = ({
  'aria-hidden': ariaHidden,
  children,
  className,
  height = 'h-4',
  title,
  width = 'w-4',
  viewBox = '0 0 32 32',
}: IconWrapperProps) => (
  <svg
    className={twMerge(classNames('fill-current', height, width, className))}
    xmlns="http://www.w3.org/2000/svg"
    viewBox={viewBox}
    aria-hidden={ariaHidden}
  >
    {title ? <title>{title}</title> : null}
    {children}
  </svg>
);

export default IconWrapper;
