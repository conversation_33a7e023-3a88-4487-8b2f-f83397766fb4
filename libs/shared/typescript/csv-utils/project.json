{"name": "shared-typescript-csv-utils", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/typescript/csv-utils/src", "projectType": "library", "tags": ["shared"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/typescript/csv-utils/jest.config.ts", "passWithNoTests": false}}}}