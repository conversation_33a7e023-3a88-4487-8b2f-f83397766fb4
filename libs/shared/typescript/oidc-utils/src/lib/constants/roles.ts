export enum OidcRoles {
  CHARGER_COMMISSIONING = 'Charger.Commissioning',
  CHARGER_EDIT_CONFIRM_CHARGE = 'Charger.EditConfirmCharge',
  CHARGER_EDIT_OPERATOR = 'Charger.EditOperator',
  CHARGER_ENABLE_MAINTENANCE_MODE = 'Charger.EnableMaintenanceMode',
  CHARGER_ES_VIEW_ALL = 'Charger.ES.ViewAll',
  CHARGER_FR_VIEW_ALL = 'Charger.FR.ViewAll',
  CHARGER_VIEW_ALL = 'Charger.ViewAll',
  CHARGE_EDIT_REVENUE = 'Charge.EditRevenue',
  GROUP_VIEW_ALL = 'Group.ViewAll',
  PAYOUT_ACCOUNT_REMOVE = 'PayoutAccount.Remove',
  INVOICE_REGENERATE = 'Invoice.Regenerate',
  GROUP_MANAGE_DOCUMENTS = 'Groups.Documents.Manage',
}

export const ALLOWED_ROLES = [
  OidcRoles.CHARGER_COMMISSIONING,
  OidcRoles.CHARGER_EDIT_CONFIRM_CHARGE,
  OidcRoles.CHARGER_EDIT_OPERATOR,
  OidcRoles.CHARGER_ENABLE_MAINTENANCE_MODE,
  OidcRoles.CHARGER_ES_VIEW_ALL,
  OidcRoles.CHARGER_FR_VIEW_ALL,
  OidcRoles.CHARGER_VIEW_ALL,
  OidcRoles.CHARGE_EDIT_REVENUE,
  OidcRoles.GROUP_VIEW_ALL,
  OidcRoles.PAYOUT_ACCOUNT_REMOVE,
  OidcRoles.INVOICE_REGENERATE,
  OidcRoles.GROUP_MANAGE_DOCUMENTS,
];
