# Feature Flag Service

This library was generated with [Nx](https://nx.dev). It exists to wrap an externally hosted feature flag service in
such a way as to abstract application code away from any specific implementation allowing the upstream service to be
swapped out in future if necessary.

## ConfigCat

The current implementation is backed by [ConfigCat](https://configcat.com/). It is necessary to configure an environment
variable named `CONFIGCAT_SDK_KEY` otherwise no calls will be made to the ConfigCat API to fetch feature flag state.
When this happens state will fall back to being provided locally.

This has the benefit of not using any of our ConfigCat quota when developing locally and also provides us with a
fallback option whereby we simply configure feature flag state via environment variables or even in code. This protects
us against accidentally consuming our entire ConfigCat quota and also against a service outage.

## Feature Flags

The following feature flags are currently in use.

| Name                | ConfigCat Key     | Environment Variable Name |
| ------------------- | ----------------- | ------------------------- |
| Enable Pricing Page | enablePricingPage | ENABLE_PRICING_PAGE       |

## Building

Run `nx build shared-typescript-feature-flag-service` to build the library.

## Running unit tests

Run `nx test shared-typescript-feature-flag-service` to execute the unit tests via [Jest](https://jestjs.io).
