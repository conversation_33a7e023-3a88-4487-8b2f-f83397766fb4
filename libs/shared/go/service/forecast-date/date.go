package forecastdate

import (
	"time"
)

const IntervalInMinutes = 30

// From calculates the start time of the forecast.
// It receives the current time and returns a time adjusted to a 30-minute granularity.
// This is then stored against the forecast to facilitate queries.
// The logic is based on actual responses from carbon intensity API.
//
// An alternative approach could be using the first 'from' value from the response.
//
// Unmarshalling the JSON response has breaking potential if the contract changes.
// Making assumptions on the forecast periods can potentially lead to storing an incorrect time.
//
// Since both approaches require making assumptions on the target API behaviour, we decided that this
// approach has less serious consequences if their contract changes.
func From(now time.Time) time.Time {
	// at minute 0 forecast starts at minute 30 of previous hour
	// day, month, year are manipulated as required
	// e.g. requests at 2023-01-01T00:00 have a forecast from 2022-12-31T23:30
	if now.Minute() == 0 {
		return time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-1, 30, 0, 0, time.UTC)
	}
	// between minute 1 and 30 forecast starts at minute 0
	if now.Minute() <= 30 {
		return time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, time.UTC)
	}
	// between minute 31 and 59 forecast starts at minute 30
	return time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 30, 0, 0, time.UTC)
}

func GetPreviousScheduled(date time.Time) time.Time {
	return From(date).Add(-time.Minute * IntervalInMinutes)
}
