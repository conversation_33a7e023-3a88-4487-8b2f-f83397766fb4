package logger

import (
	"context"
	"log"

	"github.com/google/uuid"
)

type traceIDKeyType string

const TraceID traceIDKeyType = "trace_id"

type TraceIDLogger struct {
	logger *log.Logger
}

func NewTraceIDLogger(logger *log.Logger) *TraceIDLogger {
	return &TraceIDLogger{
		logger: logger,
	}
}

func (l *TraceIDLogger) Printf(ctx context.Context, msg string) {
	traceID, ok := ctx.Value(TraceID).(uuid.UUID)
	if !ok {
		l.logger.Print(msg)
	} else {
		l.logger.Printf("%s=%s %s", TraceID, traceID, msg)
	}
}
