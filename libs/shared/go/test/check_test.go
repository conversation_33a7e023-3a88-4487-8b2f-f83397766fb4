package test

import (
	"slices"
	"testing"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/stretchr/testify/require"
)

type heFixture[T1 any] struct {
	name    string
	matcher Matcher[T1, T1]
	slice   []T1
	value   T1
	want    bool
}

func newMatchingHeFixture[T1 any](name string, matcher Matcher[T1, T1], slice []T1) heFixture[T1] {
	return heFixture[T1]{
		name:    name,
		matcher: matcher,
		slice:   slice,
		value:   slice[gofakeit.IntRange(0, len(slice)-1)],
		want:    true,
	}
}

func TestHasElement_Car(t *testing.T) {
	tests := []heFixture[*gofakeit.CarInfo]{
		{
			name:    "no match - Car slice",
			matcher: EqualMatcher[*gofakeit.CarInfo, *gofakeit.CarInfo]{},
			slice:   []*gofakeit.CarInfo{gofakeit.Car(), gofakeit.Car()},
			value:   gofakeit.Car(),
			want:    false,
		},
		newMatchingHeFixture[*gofakeit.CarInfo](
			"match - Car slice",
			EqualMatcher[*gofakeit.CarInfo, *gofakeit.CarInfo]{},
			[]*gofakeit.CarInfo{gofakeit.Car(), gofakeit.Car(), gofakeit.Car()},
		),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := HasElement[*gofakeit.CarInfo](tt.matcher, tt.slice, tt.value)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestHasElement_Int(t *testing.T) {
	tests := []heFixture[int]{
		{
			name:    "no match - int slice",
			matcher: EqualMatcher[int, int]{},
			slice:   []int{1, 3, 7, 9},
			value:   2,
			want:    false,
		},
		newMatchingHeFixture[int](
			"match - int slice",
			EqualMatcher[int, int]{},
			[]int{1, 6, 2, 8, 3},
		),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := HasElement[int, int](tt.matcher, tt.slice, tt.value)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestHasElement_String(t *testing.T) {
	tests := []heFixture[string]{
		{
			name:    "no match - string slice",
			matcher: EqualMatcher[string, string]{},
			slice:   []string{"aidan", "dara"},
			value:   "paul",
			want:    false,
		},
		newMatchingHeFixture[string](
			"match - string slice",
			EqualMatcher[string, string]{},
			[]string{"aidan", "dara", "paul"},
		),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := HasElement[string, string](tt.matcher, tt.slice, tt.value)
			require.Equal(t, tt.want, got)
		})
	}
}

type meFixture[T1 any] struct {
	name    string
	matcher Matcher[T1, T1]
	slice   []T1
	values  []T1
	want    bool
}

func newMatchingMeFixture[T1 any](name string, matcher Matcher[T1, T1], slice []T1) meFixture[T1] {
	values := make([]T1, len(slice))
	copy(values, slice)
	slices.Reverse(values)

	return meFixture[T1]{
		name:    name,
		matcher: matcher,
		slice:   slice,
		values:  values,
		want:    true,
	}
}

func TestMatchingElements_Car(t *testing.T) {
	tests := []meFixture[*gofakeit.CarInfo]{
		{
			name:    "not matching - Car slice - all elements different",
			matcher: EqualMatcher[*gofakeit.CarInfo, *gofakeit.CarInfo]{},
			slice:   []*gofakeit.CarInfo{gofakeit.Car(), gofakeit.Car(), gofakeit.Car()},
			values:  []*gofakeit.CarInfo{gofakeit.Car(), gofakeit.Car(), gofakeit.Car()},
			want:    false,
		},
		newMatchingMeFixture[*gofakeit.CarInfo](
			"matching - Car slice",
			EqualMatcher[*gofakeit.CarInfo, *gofakeit.CarInfo]{},
			[]*gofakeit.CarInfo{gofakeit.Car(), gofakeit.Car(), gofakeit.Car()},
		),
	}

	for i, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := MatchingElements[*gofakeit.CarInfo, *gofakeit.CarInfo](tests[i].matcher, &tests[i].slice, &tests[i].values)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestMatchingElements_String(t *testing.T) {
	tests := []meFixture[string]{
		{
			name:    "matching - both nil",
			matcher: EqualMatcher[string, string]{},
			slice:   nil,
			values:  nil,
			want:    true,
		},
		{
			name:    "matching - both empty slices",
			matcher: EqualMatcher[string, string]{},
			slice:   []string{},
			values:  []string{},
			want:    true,
		},
		{
			name:    "not matching - slice value nil",
			matcher: EqualMatcher[string, string]{},
			slice:   nil,
			values:  []string{"aoife", "fionn"},
			want:    false,
		},
		{
			name:    "not matching - values value nil",
			matcher: EqualMatcher[string, string]{},
			slice:   []string{"aoife", "fionn"},
			values:  nil,
			want:    false,
		},
		{
			name:    "not matching - string slice - one element different",
			matcher: EqualMatcher[string, string]{},
			slice:   []string{"aidan", "dara"},
			values:  []string{"paul", "dara"},
			want:    false,
		},
		newMatchingMeFixture[string](
			"matching - string slice",
			EqualMatcher[string, string]{}, []string{"aidan", "dara", "paul"},
		),
	}

	for i, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := MatchingElements[string, string](tests[i].matcher, &tests[i].slice, &tests[i].values)
			require.Equal(t, tt.want, got)
		})
	}
}
