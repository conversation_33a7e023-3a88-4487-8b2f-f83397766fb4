openapi: 3.0.0
info:
  contact: {}
  description: Billing API service
  title: Billing API
  version: 1.0.0
servers:
  - url: /
paths:
  /health:
    get:
      operationId: HealthController_check
      parameters: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthController_check_200_response'
          description: The Health Check is successful
        '503':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthController_check_503_response'
          description: The Health Check is not successful
      summary: get billing API health
      tags:
        - Healthcheck
  /payments/setup-intent/{uid}:
    post:
      description: Creates setup intent for payment
      operationId: PaymentController_setupIntent
      parameters:
        - explode: false
          in: path
          name: uid
          required: true
          schema:
            type: string
          style: simple
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateIntentResponse'
          description: OK response
        '400':
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
          description: ''
        '500':
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
          description: Internal server error
      summary: create setup intent for payment
      tags:
        - Payments
  /payments/create-payment-intent:
    post:
      description: Creates setup intent for a guest user's payment
      operationId: PaymentController_createGuestPaymentIntent
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateGuestPaymentResponse'
          description: OK response
        '400':
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
          description: ''
        '500':
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
          description: Internal server error
      summary: create setup intent for guest payment
      tags:
        - Payments
  /payments/create-payment-intent/{uid}:
    post:
      description: Creates setup intent for a registered user's payment
      operationId: PaymentController_createRegisteredUserPaymentIntent
      parameters:
        - explode: false
          in: path
          name: uid
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateRegisteredUserPaymentResponse'
          description: OK response
        '400':
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
          description: ''
        '500':
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
          description: Internal server error
      summary: create setup intent for registered user payment
      tags:
        - Payments
  /payments/capture-guest-payment:
    post:
      description: Captures a guest payment
      operationId: PaymentController_captureGuestPayment
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CapturePaymentRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CapturePaymentIntentResponse'
          description: OK response
        '400':
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
          description: ''
        '500':
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
          description: Internal server error
      summary: capture guest payment
      tags:
        - Payments
  /webhook:
    post:
      description: create webhook endpoint for stripe
      operationId: WebhookController_webhook
      parameters:
        - explode: false
          in: header
          name: stripe-signature
          required: true
          schema:
            type: string
          style: simple
      responses:
        '201':
          description: ''
      summary: create webhook endpoint for stripe
      tags:
        - Webhook
  /version:
    get:
      operationId: VersionController_getVersion
      parameters: []
      responses:
        '200':
          description: application version
      summary: get application version
      tags:
        - Version
  /customer/{authId}:
    patch:
      description: Updates a customer record for a user
      operationId: CustomerController_updateCustomer
      parameters:
        - explode: false
          in: path
          name: authId
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerUpdateDTO'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerUpdateResponse'
          description: Successfully updated the customer
        '404':
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
          description: Thrown when a user does not have a payment processor attached
        '500':
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
          description: Internal server error
      summary: update a user's customer record
      tags:
        - Customer
components:
  schemas:
    CreateIntentResponse:
      example:
        ephemeralKey: ephemeralKey
        setupIntent: setupIntent
        customer: customer
      properties:
        customer:
          description: The customer id
          type: string
        setupIntent:
          description: The setup intent secret
          nullable: true
          type: string
        ephemeralKey:
          description: The ephemeral key
          type: string
      required:
        - customer
        - ephemeralKey
        - setupIntent
      type: object
    CreatePaymentRequest:
      example:
        amount: 20
        currency: gbp
      properties:
        amount:
          description: The amount to top up
          example: 20
          type: number
        currency:
          description: Currency of the amount that is to topup
          example: gbp
          type: string
      required:
        - amount
        - currency
      type: object
    CreateGuestPaymentResponse:
      example:
        paymentIntent: paymentIntent
      properties:
        paymentIntent:
          description: The payment intent secret
          nullable: true
          type: string
      required:
        - paymentIntent
      type: object
    CreateRegisteredUserPaymentResponse:
      example:
        ephemeralKey: ephemeralKey
        paymentIntent: paymentIntent
        customer: customer
      properties:
        paymentIntent:
          description: The payment intent secret
          nullable: true
          type: string
        customer:
          description: The customer id
          type: string
        ephemeralKey:
          description: The ephemeral key
          type: string
      required:
        - customer
        - ephemeralKey
        - paymentIntent
      type: object
    CapturePaymentRequest:
      example:
        billingEventId: 123456
        amountToCapture: 20
      properties:
        billingEventId:
          description: The billing event ID
          example: 123456
          format: int64
          minimum: 0
          type: integer
        amountToCapture:
          description: The amount to capture
          example: 20
          format: int64
          minimum: 0
          type: integer
      required:
        - amountToCapture
        - billingEventId
      type: object
    CapturePaymentIntentResponse:
      example:
        paymentIntentStatus: paymentIntentStatus
      properties:
        paymentIntentStatus:
          description: The payment intent status
          type: string
      required:
        - paymentIntentStatus
      type: object
    CustomerUpdateDTO:
      example:
        email: email
      properties:
        email:
          description: The email to update
          type: string
      required:
        - email
      type: object
    CustomerUpdateResponse:
      example:
        email: email
      properties:
        email:
          description: The email to update
          type: string
      required:
        - email
      type: object
    HealthController_check_200_response_info_value:
      additionalProperties: true
      properties:
        status:
          type: string
      required:
        - status
      type: object
    HealthController_check_200_response:
      example:
        details:
          database:
            status: up
        error: {}
        status: ok
        info:
          database:
            status: up
      properties:
        status:
          example: ok
          type: string
        info:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          nullable: true
          type: object
        error:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example: {}
          nullable: true
          type: object
        details:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          type: object
      type: object
    HealthController_check_503_response:
      example:
        details:
          database:
            status: up
          redis:
            status: down
            message: Could not connect
        error:
          redis:
            status: down
            message: Could not connect
        status: error
        info:
          database:
            status: up
      properties:
        status:
          example: error
          type: string
        info:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          nullable: true
          type: object
        error:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            redis:
              status: down
              message: Could not connect
          nullable: true
          type: object
        details:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
            redis:
              status: down
              message: Could not connect
          type: object
      type: object
