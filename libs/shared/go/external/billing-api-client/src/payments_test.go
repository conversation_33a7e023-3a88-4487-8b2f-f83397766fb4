package billingapiclient

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestNewPaymentsAPIClient(t *testing.T) {
	tests := []struct {
		name    string
		host    string
		want    *APIClient
		wantErr bool
	}{
		{
			name:    "success",
			host:    "http://localhost:8080",
			wantErr: false,
		},
		{
			name:    "host is required",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewPaymentsAPIClient(true, tt.host)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewPaymentsAPIClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCanCallPaymentsAPI(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		require.Equal(t, "/payments/capture-guest-payment", req.RequestURI)

		byteBody, _ := io.ReadAll(req.Body)
		requestBody := string(byteBody)
		require.NotNil(t, requestBody)

		res.Header().Set("Content-Type", "application/json")
		res.WriteHeader(http.StatusCreated)

		_, _ = res.Write([]byte(`{"paymentIntentStatus": "succeeded"}`))
	}))
	defer testServer.Close()

	url := testServer.URL
	client, err := NewPaymentsAPIClient(true, url)
	require.NoError(t, err)

	request := client.PaymentControllerCaptureGuestPayment(context.Background())
	request = request.CapturePaymentRequest(*NewCapturePaymentRequestWithDefaults())
	result, httpResponse, err := request.Execute() //nolint:bodyclose // Test code
	require.NoError(t, err)
	require.Equal(t, http.StatusCreated, httpResponse.StatusCode)
	require.Equal(t, "succeeded", result.PaymentIntentStatus)
}

func TestCanCallStubAPI(t *testing.T) {
	client, err := NewPaymentsAPIClient(false, "http://localhost:8080")
	require.NoError(t, err)

	request := client.PaymentControllerCaptureGuestPayment(context.Background())
	request = request.CapturePaymentRequest(*NewCapturePaymentRequestWithDefaults())

	result, httpResponse, err := request.Execute() //nolint:bodyclose // Test code
	require.NoError(t, err)

	require.Equal(t, 201, httpResponse.StatusCode)
	require.Equal(t, "succeeded", result.PaymentIntentStatus)
}
