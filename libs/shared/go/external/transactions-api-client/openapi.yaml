openapi: 3.0.1
info:
  contact:
    email: <EMAIL>
    name: Connectivity Squad (Parrot)
    url: 'https://pod-point.com'
  description: An API for creating charging stations transactions.
  title: Charge Sessions Service Transaction API
  version: '1.0'
servers:
  - url: '//localhost:8000'
paths:
  /transactions:
    post:
      summary: Creates a transaction and return its properties
      description: Creates a transaction and return its properties
      requestBody:
        description: request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.CreateTransactionRequestBody'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CreateTransactionResponseBody'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
  '/transactions/{transactionId}/stop':
    post:
      summary: Asks the charging station to stop a transaction
      description: Triggers a RemoteStopTransaction command to the charging station
      parameters:
        - name: transactionId
          in: path
          description: transaction ID
          required: true
          schema:
            type: integer
          example: 12345
      requestBody:
        description: request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.StopTransactionRequestBody'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.TransactionActionResponseBody'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
        '502':
          description: Bad Gateway
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
  /transactions/start:
    post:
      summary: Asks the charging station to start a transaction
      description: Triggers a RemoteStartTransaction command to the charging station
      requestBody:
        description: request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.StartTransactionRequestBody'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.TransactionActionResponseBody'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
        '502':
          description: Bad Gateway
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
  /transactions/stop:
    post:
      summary: Asks the charging station to stop the transaction with the given properties
      description: Triggers a RemoteStopTransaction command to the charging station
      requestBody:
        description: request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.StopTransactionByParametersRequestBody'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.TransactionActionResponseBody'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
        '502':
          description: Bad Gateway
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.ErrorResponseBody'
components:
  schemas:
    api.ErrorResponseBody:
      type: object
      properties:
        error:
          description: Verbose error message
          type: string
          example: Something went wrong
    handlers.CommandDetails:
      type: object
      properties:
        id:
          type: string
    handlers.CreateTransactionRequestBody:
      type: object
      properties:
        chargingStationId:
          description: Charging Station ID for which to create the transaction
          type: string
          example: PSL-1234567
        connectorId:
          description: The connector ID for which to create the transaction
          type: integer
          example: 1
          maximum: 3
          minimum: 1
        door:
          description: The door name
          type: string
          example: A
          enum:
            - A
            - B
            - C
        idTag:
          description: The authorisation id tag for which to create the transaction
          type: string
          example: '1234567890'
        metadata:
          description: Additional metadata for the ppcp translation
          allOf:
            - $ref: '#/components/schemas/handlers.CreateTransactionRequestBodyMetadata'
        meterStart:
          description: The meter value in Wh for the transaction
          type: integer
          example: 1234
          minimum: 0
        timestamp:
          description: The UTC timestamp of the transaction in the format RFC3339
          type: string
          example: '2021-01-01T00:00:00Z'
      required:
        - chargingStationId
        - connectorId
        - door
        - idTag
        - meterStart
        - timestamp
    handlers.CreateTransactionRequestBodyMetadata:
      type: object
      properties:
        evseId:
          description: The ID of the EVSE
          type: integer
          maximum: 3
          minimum: 1
        firmwareVersion:
          description: The version of the firmware
          type: string
          example: A50P-1.14.1
        pcbMacAddress:
          description: The MAC address of the PCB
          type: string
          example: A81B6A6732FB
        pcbSerialNumber:
          description: The serial number of the PCB
          type: string
          example: '123456789'
        pcbType:
          description: The type of the PCB
          type: string
          example: arch5
    handlers.CreateTransactionResponseBody:
      type: object
      properties:
        id:
          description: The ID of the created transaction
          type: integer
          example: 15
    handlers.StartTransactionRequestBody:
      type: object
      properties:
        authoriserId:
          type: string
        chargingStationId:
          type: string
        clientRef:
          type: string
        door:
          type: string
    handlers.StopTransactionByParametersRequestBody:
      type: object
      properties:
        chargeSessionId:
          type: string
          maxLength: 36
        chargingStationId:
          type: string
          maxLength: 36
        clientRef:
          type: string
          maxLength: 80
        door:
          type: string
          enum:
            - A
            - B
            - C
      required:
        - chargeSessionId
        - chargingStationId
        - clientRef
        - door
    handlers.StopTransactionRequestBody:
      type: object
      properties:
        chargingStationId:
          type: string
          example: PSL-1234567
        clientRef:
          type: string
          maxLength: 80
        door:
          type: string
          example: B
      required:
        - chargingStationId
        - clientRef
        - door
    handlers.TransactionActionResponseBody:
      type: object
      properties:
        command:
          $ref: '#/components/schemas/handlers.CommandDetails'
        message:
          type: string
        status:
          type: string
