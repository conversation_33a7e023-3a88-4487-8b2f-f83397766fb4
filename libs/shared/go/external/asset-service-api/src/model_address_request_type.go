/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
	"fmt"
)

// checks if the AddressRequestType type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &AddressRequestType{}

// AddressRequestType struct for AddressRequestType
type AddressRequestType struct {
	Line1                string   `json:"line1"`
	Line2                *string  `json:"line2,omitempty"`
	PostalTown           string   `json:"postalTown"`
	Postcode             string   `json:"postcode"`
	Country              string   `json:"country"`
	GroupId              *float32 `json:"groupId,omitempty"`
	ContactName          *string  `json:"contactName,omitempty"`
	Email                *string  `json:"email,omitempty"`
	Telephone            *string  `json:"telephone,omitempty"`
	BusinessName         *string  `json:"businessName,omitempty"`
	Description          *string  `json:"description,omitempty"`
	TariffId             *float32 `json:"tariffId,omitempty"`
	CostPerKwh           *float32 `json:"costPerKwh,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _AddressRequestType AddressRequestType

// NewAddressRequestType instantiates a new AddressRequestType object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewAddressRequestType(line1 string, postalTown string, postcode string, country string) *AddressRequestType {
	this := AddressRequestType{}
	this.Line1 = line1
	this.PostalTown = postalTown
	this.Postcode = postcode
	this.Country = country
	return &this
}

// NewAddressRequestTypeWithDefaults instantiates a new AddressRequestType object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewAddressRequestTypeWithDefaults() *AddressRequestType {
	this := AddressRequestType{}
	return &this
}

// GetLine1 returns the Line1 field value
func (o *AddressRequestType) GetLine1() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Line1
}

// GetLine1Ok returns a tuple with the Line1 field value
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetLine1Ok() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Line1, true
}

// SetLine1 sets field value
func (o *AddressRequestType) SetLine1(v string) {
	o.Line1 = v
}

// GetLine2 returns the Line2 field value if set, zero value otherwise.
func (o *AddressRequestType) GetLine2() string {
	if o == nil || IsNil(o.Line2) {
		var ret string
		return ret
	}
	return *o.Line2
}

// GetLine2Ok returns a tuple with the Line2 field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetLine2Ok() (*string, bool) {
	if o == nil || IsNil(o.Line2) {
		return nil, false
	}
	return o.Line2, true
}

// HasLine2 returns a boolean if a field has been set.
func (o *AddressRequestType) HasLine2() bool {
	if o != nil && !IsNil(o.Line2) {
		return true
	}

	return false
}

// SetLine2 gets a reference to the given string and assigns it to the Line2 field.
func (o *AddressRequestType) SetLine2(v string) {
	o.Line2 = &v
}

// GetPostalTown returns the PostalTown field value
func (o *AddressRequestType) GetPostalTown() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.PostalTown
}

// GetPostalTownOk returns a tuple with the PostalTown field value
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetPostalTownOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.PostalTown, true
}

// SetPostalTown sets field value
func (o *AddressRequestType) SetPostalTown(v string) {
	o.PostalTown = v
}

// GetPostcode returns the Postcode field value
func (o *AddressRequestType) GetPostcode() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Postcode
}

// GetPostcodeOk returns a tuple with the Postcode field value
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetPostcodeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Postcode, true
}

// SetPostcode sets field value
func (o *AddressRequestType) SetPostcode(v string) {
	o.Postcode = v
}

// GetCountry returns the Country field value
func (o *AddressRequestType) GetCountry() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Country
}

// GetCountryOk returns a tuple with the Country field value
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetCountryOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Country, true
}

// SetCountry sets field value
func (o *AddressRequestType) SetCountry(v string) {
	o.Country = v
}

// GetGroupId returns the GroupId field value if set, zero value otherwise.
func (o *AddressRequestType) GetGroupId() float32 {
	if o == nil || IsNil(o.GroupId) {
		var ret float32
		return ret
	}
	return *o.GroupId
}

// GetGroupIdOk returns a tuple with the GroupId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetGroupIdOk() (*float32, bool) {
	if o == nil || IsNil(o.GroupId) {
		return nil, false
	}
	return o.GroupId, true
}

// HasGroupId returns a boolean if a field has been set.
func (o *AddressRequestType) HasGroupId() bool {
	if o != nil && !IsNil(o.GroupId) {
		return true
	}

	return false
}

// SetGroupId gets a reference to the given float32 and assigns it to the GroupId field.
func (o *AddressRequestType) SetGroupId(v float32) {
	o.GroupId = &v
}

// GetContactName returns the ContactName field value if set, zero value otherwise.
func (o *AddressRequestType) GetContactName() string {
	if o == nil || IsNil(o.ContactName) {
		var ret string
		return ret
	}
	return *o.ContactName
}

// GetContactNameOk returns a tuple with the ContactName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetContactNameOk() (*string, bool) {
	if o == nil || IsNil(o.ContactName) {
		return nil, false
	}
	return o.ContactName, true
}

// HasContactName returns a boolean if a field has been set.
func (o *AddressRequestType) HasContactName() bool {
	if o != nil && !IsNil(o.ContactName) {
		return true
	}

	return false
}

// SetContactName gets a reference to the given string and assigns it to the ContactName field.
func (o *AddressRequestType) SetContactName(v string) {
	o.ContactName = &v
}

// GetEmail returns the Email field value if set, zero value otherwise.
func (o *AddressRequestType) GetEmail() string {
	if o == nil || IsNil(o.Email) {
		var ret string
		return ret
	}
	return *o.Email
}

// GetEmailOk returns a tuple with the Email field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetEmailOk() (*string, bool) {
	if o == nil || IsNil(o.Email) {
		return nil, false
	}
	return o.Email, true
}

// HasEmail returns a boolean if a field has been set.
func (o *AddressRequestType) HasEmail() bool {
	if o != nil && !IsNil(o.Email) {
		return true
	}

	return false
}

// SetEmail gets a reference to the given string and assigns it to the Email field.
func (o *AddressRequestType) SetEmail(v string) {
	o.Email = &v
}

// GetTelephone returns the Telephone field value if set, zero value otherwise.
func (o *AddressRequestType) GetTelephone() string {
	if o == nil || IsNil(o.Telephone) {
		var ret string
		return ret
	}
	return *o.Telephone
}

// GetTelephoneOk returns a tuple with the Telephone field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetTelephoneOk() (*string, bool) {
	if o == nil || IsNil(o.Telephone) {
		return nil, false
	}
	return o.Telephone, true
}

// HasTelephone returns a boolean if a field has been set.
func (o *AddressRequestType) HasTelephone() bool {
	if o != nil && !IsNil(o.Telephone) {
		return true
	}

	return false
}

// SetTelephone gets a reference to the given string and assigns it to the Telephone field.
func (o *AddressRequestType) SetTelephone(v string) {
	o.Telephone = &v
}

// GetBusinessName returns the BusinessName field value if set, zero value otherwise.
func (o *AddressRequestType) GetBusinessName() string {
	if o == nil || IsNil(o.BusinessName) {
		var ret string
		return ret
	}
	return *o.BusinessName
}

// GetBusinessNameOk returns a tuple with the BusinessName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetBusinessNameOk() (*string, bool) {
	if o == nil || IsNil(o.BusinessName) {
		return nil, false
	}
	return o.BusinessName, true
}

// HasBusinessName returns a boolean if a field has been set.
func (o *AddressRequestType) HasBusinessName() bool {
	if o != nil && !IsNil(o.BusinessName) {
		return true
	}

	return false
}

// SetBusinessName gets a reference to the given string and assigns it to the BusinessName field.
func (o *AddressRequestType) SetBusinessName(v string) {
	o.BusinessName = &v
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *AddressRequestType) GetDescription() string {
	if o == nil || IsNil(o.Description) {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetDescriptionOk() (*string, bool) {
	if o == nil || IsNil(o.Description) {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *AddressRequestType) HasDescription() bool {
	if o != nil && !IsNil(o.Description) {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *AddressRequestType) SetDescription(v string) {
	o.Description = &v
}

// GetTariffId returns the TariffId field value if set, zero value otherwise.
func (o *AddressRequestType) GetTariffId() float32 {
	if o == nil || IsNil(o.TariffId) {
		var ret float32
		return ret
	}
	return *o.TariffId
}

// GetTariffIdOk returns a tuple with the TariffId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetTariffIdOk() (*float32, bool) {
	if o == nil || IsNil(o.TariffId) {
		return nil, false
	}
	return o.TariffId, true
}

// HasTariffId returns a boolean if a field has been set.
func (o *AddressRequestType) HasTariffId() bool {
	if o != nil && !IsNil(o.TariffId) {
		return true
	}

	return false
}

// SetTariffId gets a reference to the given float32 and assigns it to the TariffId field.
func (o *AddressRequestType) SetTariffId(v float32) {
	o.TariffId = &v
}

// GetCostPerKwh returns the CostPerKwh field value if set, zero value otherwise.
func (o *AddressRequestType) GetCostPerKwh() float32 {
	if o == nil || IsNil(o.CostPerKwh) {
		var ret float32
		return ret
	}
	return *o.CostPerKwh
}

// GetCostPerKwhOk returns a tuple with the CostPerKwh field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddressRequestType) GetCostPerKwhOk() (*float32, bool) {
	if o == nil || IsNil(o.CostPerKwh) {
		return nil, false
	}
	return o.CostPerKwh, true
}

// HasCostPerKwh returns a boolean if a field has been set.
func (o *AddressRequestType) HasCostPerKwh() bool {
	if o != nil && !IsNil(o.CostPerKwh) {
		return true
	}

	return false
}

// SetCostPerKwh gets a reference to the given float32 and assigns it to the CostPerKwh field.
func (o *AddressRequestType) SetCostPerKwh(v float32) {
	o.CostPerKwh = &v
}

func (o AddressRequestType) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o AddressRequestType) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["line1"] = o.Line1
	if !IsNil(o.Line2) {
		toSerialize["line2"] = o.Line2
	}
	toSerialize["postalTown"] = o.PostalTown
	toSerialize["postcode"] = o.Postcode
	toSerialize["country"] = o.Country
	if !IsNil(o.GroupId) {
		toSerialize["groupId"] = o.GroupId
	}
	if !IsNil(o.ContactName) {
		toSerialize["contactName"] = o.ContactName
	}
	if !IsNil(o.Email) {
		toSerialize["email"] = o.Email
	}
	if !IsNil(o.Telephone) {
		toSerialize["telephone"] = o.Telephone
	}
	if !IsNil(o.BusinessName) {
		toSerialize["businessName"] = o.BusinessName
	}
	if !IsNil(o.Description) {
		toSerialize["description"] = o.Description
	}
	if !IsNil(o.TariffId) {
		toSerialize["tariffId"] = o.TariffId
	}
	if !IsNil(o.CostPerKwh) {
		toSerialize["costPerKwh"] = o.CostPerKwh
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *AddressRequestType) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"line1",
		"postalTown",
		"postcode",
		"country",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varAddressRequestType := _AddressRequestType{}

	err = json.Unmarshal(data, &varAddressRequestType)

	if err != nil {
		return err
	}

	*o = AddressRequestType(varAddressRequestType)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "line1")
		delete(additionalProperties, "line2")
		delete(additionalProperties, "postalTown")
		delete(additionalProperties, "postcode")
		delete(additionalProperties, "country")
		delete(additionalProperties, "groupId")
		delete(additionalProperties, "contactName")
		delete(additionalProperties, "email")
		delete(additionalProperties, "telephone")
		delete(additionalProperties, "businessName")
		delete(additionalProperties, "description")
		delete(additionalProperties, "tariffId")
		delete(additionalProperties, "costPerKwh")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableAddressRequestType struct {
	value *AddressRequestType
	isSet bool
}

func (v NullableAddressRequestType) Get() *AddressRequestType {
	return v.value
}

func (v *NullableAddressRequestType) Set(val *AddressRequestType) {
	v.value = val
	v.isSet = true
}

func (v NullableAddressRequestType) IsSet() bool {
	return v.isSet
}

func (v *NullableAddressRequestType) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableAddressRequestType(val *AddressRequestType) *NullableAddressRequestType {
	return &NullableAddressRequestType{value: val, isSet: true}
}

func (v NullableAddressRequestType) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableAddressRequestType) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
