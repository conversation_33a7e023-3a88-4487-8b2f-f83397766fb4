/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
)

// checks if the ChargingStationsSummary type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargingStationsSummary{}

// ChargingStationsSummary struct for ChargingStationsSummary
type ChargingStationsSummary struct {
	ChargingStations     *map[string]ChargingStationSummary `json:"chargingStations,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _ChargingStationsSummary ChargingStationsSummary

// NewChargingStationsSummary instantiates a new ChargingStationsSummary object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargingStationsSummary() *ChargingStationsSummary {
	this := ChargingStationsSummary{}
	return &this
}

// NewChargingStationsSummaryWithDefaults instantiates a new ChargingStationsSummary object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargingStationsSummaryWithDefaults() *ChargingStationsSummary {
	this := ChargingStationsSummary{}
	return &this
}

// GetChargingStations returns the ChargingStations field value if set, zero value otherwise.
func (o *ChargingStationsSummary) GetChargingStations() map[string]ChargingStationSummary {
	if o == nil || IsNil(o.ChargingStations) {
		var ret map[string]ChargingStationSummary
		return ret
	}
	return *o.ChargingStations
}

// GetChargingStationsOk returns a tuple with the ChargingStations field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChargingStationsSummary) GetChargingStationsOk() (*map[string]ChargingStationSummary, bool) {
	if o == nil || IsNil(o.ChargingStations) {
		return nil, false
	}
	return o.ChargingStations, true
}

// HasChargingStations returns a boolean if a field has been set.
func (o *ChargingStationsSummary) HasChargingStations() bool {
	if o != nil && !IsNil(o.ChargingStations) {
		return true
	}

	return false
}

// SetChargingStations gets a reference to the given map[string]ChargingStationSummary and assigns it to the ChargingStations field.
func (o *ChargingStationsSummary) SetChargingStations(v map[string]ChargingStationSummary) {
	o.ChargingStations = &v
}

func (o ChargingStationsSummary) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargingStationsSummary) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.ChargingStations) {
		toSerialize["chargingStations"] = o.ChargingStations
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *ChargingStationsSummary) UnmarshalJSON(data []byte) (err error) {
	varChargingStationsSummary := _ChargingStationsSummary{}

	err = json.Unmarshal(data, &varChargingStationsSummary)

	if err != nil {
		return err
	}

	*o = ChargingStationsSummary(varChargingStationsSummary)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "chargingStations")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableChargingStationsSummary struct {
	value *ChargingStationsSummary
	isSet bool
}

func (v NullableChargingStationsSummary) Get() *ChargingStationsSummary {
	return v.value
}

func (v *NullableChargingStationsSummary) Set(val *ChargingStationsSummary) {
	v.value = val
	v.isSet = true
}

func (v NullableChargingStationsSummary) IsSet() bool {
	return v.isSet
}

func (v *NullableChargingStationsSummary) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargingStationsSummary(val *ChargingStationsSummary) *NullableChargingStationsSummary {
	return &NullableChargingStationsSummary{value: val, isSet: true}
}

func (v NullableChargingStationsSummary) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargingStationsSummary) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
