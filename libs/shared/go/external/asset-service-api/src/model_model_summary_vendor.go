/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
	"fmt"
)

// checks if the ModelSummaryVendor type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ModelSummaryVendor{}

// ModelSummaryVendor struct for ModelSummaryVendor
type ModelSummaryVendor struct {
	Name                 string `json:"name"`
	AdditionalProperties map[string]interface{}
}

type _ModelSummaryVendor ModelSummaryVendor

// NewModelSummaryVendor instantiates a new ModelSummaryVendor object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewModelSummaryVendor(name string) *ModelSummaryVendor {
	this := ModelSummaryVendor{}
	this.Name = name
	return &this
}

// NewModelSummaryVendorWithDefaults instantiates a new ModelSummaryVendor object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewModelSummaryVendorWithDefaults() *ModelSummaryVendor {
	this := ModelSummaryVendor{}
	return &this
}

// GetName returns the Name field value
func (o *ModelSummaryVendor) GetName() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Name
}

// GetNameOk returns a tuple with the Name field value
// and a boolean to check if the value has been set.
func (o *ModelSummaryVendor) GetNameOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Name, true
}

// SetName sets field value
func (o *ModelSummaryVendor) SetName(v string) {
	o.Name = v
}

func (o ModelSummaryVendor) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ModelSummaryVendor) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["name"] = o.Name

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *ModelSummaryVendor) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"name",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varModelSummaryVendor := _ModelSummaryVendor{}

	err = json.Unmarshal(data, &varModelSummaryVendor)

	if err != nil {
		return err
	}

	*o = ModelSummaryVendor(varModelSummaryVendor)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "name")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableModelSummaryVendor struct {
	value *ModelSummaryVendor
	isSet bool
}

func (v NullableModelSummaryVendor) Get() *ModelSummaryVendor {
	return v.value
}

func (v *NullableModelSummaryVendor) Set(val *ModelSummaryVendor) {
	v.value = val
	v.isSet = true
}

func (v NullableModelSummaryVendor) IsSet() bool {
	return v.isSet
}

func (v *NullableModelSummaryVendor) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableModelSummaryVendor(val *ModelSummaryVendor) *NullableModelSummaryVendor {
	return &NullableModelSummaryVendor{value: val, isSet: true}
}

func (v NullableModelSummaryVendor) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableModelSummaryVendor) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
