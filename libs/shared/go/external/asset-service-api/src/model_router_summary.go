/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
	"fmt"
)

// checks if the RouterSummary type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &RouterSummary{}

// RouterSummary struct for RouterSummary
type RouterSummary struct {
	SerialNumber         string `json:"serialNumber"`
	MacAddress           string `json:"macAddress"`
	SimNumber            string `json:"simNumber"`
	Model                string `json:"model"`
	AdditionalProperties map[string]interface{}
}

type _RouterSummary RouterSummary

// NewRouterSummary instantiates a new RouterSummary object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRouterSummary(serialNumber string, macAddress string, simNumber string, model string) *RouterSummary {
	this := RouterSummary{}
	this.SerialNumber = serialNumber
	this.MacAddress = macAddress
	this.SimNumber = simNumber
	this.Model = model
	return &this
}

// NewRouterSummaryWithDefaults instantiates a new RouterSummary object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRouterSummaryWithDefaults() *RouterSummary {
	this := RouterSummary{}
	return &this
}

// GetSerialNumber returns the SerialNumber field value
func (o *RouterSummary) GetSerialNumber() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.SerialNumber
}

// GetSerialNumberOk returns a tuple with the SerialNumber field value
// and a boolean to check if the value has been set.
func (o *RouterSummary) GetSerialNumberOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.SerialNumber, true
}

// SetSerialNumber sets field value
func (o *RouterSummary) SetSerialNumber(v string) {
	o.SerialNumber = v
}

// GetMacAddress returns the MacAddress field value
func (o *RouterSummary) GetMacAddress() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.MacAddress
}

// GetMacAddressOk returns a tuple with the MacAddress field value
// and a boolean to check if the value has been set.
func (o *RouterSummary) GetMacAddressOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.MacAddress, true
}

// SetMacAddress sets field value
func (o *RouterSummary) SetMacAddress(v string) {
	o.MacAddress = v
}

// GetSimNumber returns the SimNumber field value
func (o *RouterSummary) GetSimNumber() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.SimNumber
}

// GetSimNumberOk returns a tuple with the SimNumber field value
// and a boolean to check if the value has been set.
func (o *RouterSummary) GetSimNumberOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.SimNumber, true
}

// SetSimNumber sets field value
func (o *RouterSummary) SetSimNumber(v string) {
	o.SimNumber = v
}

// GetModel returns the Model field value
func (o *RouterSummary) GetModel() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Model
}

// GetModelOk returns a tuple with the Model field value
// and a boolean to check if the value has been set.
func (o *RouterSummary) GetModelOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Model, true
}

// SetModel sets field value
func (o *RouterSummary) SetModel(v string) {
	o.Model = v
}

func (o RouterSummary) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o RouterSummary) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["serialNumber"] = o.SerialNumber
	toSerialize["macAddress"] = o.MacAddress
	toSerialize["simNumber"] = o.SimNumber
	toSerialize["model"] = o.Model

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *RouterSummary) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"serialNumber",
		"macAddress",
		"simNumber",
		"model",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varRouterSummary := _RouterSummary{}

	err = json.Unmarshal(data, &varRouterSummary)

	if err != nil {
		return err
	}

	*o = RouterSummary(varRouterSummary)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "serialNumber")
		delete(additionalProperties, "macAddress")
		delete(additionalProperties, "simNumber")
		delete(additionalProperties, "model")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableRouterSummary struct {
	value *RouterSummary
	isSet bool
}

func (v NullableRouterSummary) Get() *RouterSummary {
	return v.value
}

func (v *NullableRouterSummary) Set(val *RouterSummary) {
	v.value = val
	v.isSet = true
}

func (v NullableRouterSummary) IsSet() bool {
	return v.isSet
}

func (v *NullableRouterSummary) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRouterSummary(val *RouterSummary) *NullableRouterSummary {
	return &NullableRouterSummary{value: val, isSet: true}
}

func (v NullableRouterSummary) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRouterSummary) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
