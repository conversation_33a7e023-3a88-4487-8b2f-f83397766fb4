/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
)

// checks if the SerialNumberWithError type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &SerialNumberWithError{}

// SerialNumberWithError struct for SerialNumberWithError
type SerialNumberWithError struct {
	// The serial number of an EVSE (aka PCB)
	SerialNumber         *string `json:"serialNumber,omitempty"`
	Reason               *string `json:"reason,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _SerialNumberWithError SerialNumberWithError

// NewSerialNumberWithError instantiates a new SerialNumberWithError object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewSerialNumberWithError() *SerialNumberWithError {
	this := SerialNumberWithError{}
	return &this
}

// NewSerialNumberWithErrorWithDefaults instantiates a new SerialNumberWithError object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewSerialNumberWithErrorWithDefaults() *SerialNumberWithError {
	this := SerialNumberWithError{}
	return &this
}

// GetSerialNumber returns the SerialNumber field value if set, zero value otherwise.
func (o *SerialNumberWithError) GetSerialNumber() string {
	if o == nil || IsNil(o.SerialNumber) {
		var ret string
		return ret
	}
	return *o.SerialNumber
}

// GetSerialNumberOk returns a tuple with the SerialNumber field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *SerialNumberWithError) GetSerialNumberOk() (*string, bool) {
	if o == nil || IsNil(o.SerialNumber) {
		return nil, false
	}
	return o.SerialNumber, true
}

// HasSerialNumber returns a boolean if a field has been set.
func (o *SerialNumberWithError) HasSerialNumber() bool {
	if o != nil && !IsNil(o.SerialNumber) {
		return true
	}

	return false
}

// SetSerialNumber gets a reference to the given string and assigns it to the SerialNumber field.
func (o *SerialNumberWithError) SetSerialNumber(v string) {
	o.SerialNumber = &v
}

// GetReason returns the Reason field value if set, zero value otherwise.
func (o *SerialNumberWithError) GetReason() string {
	if o == nil || IsNil(o.Reason) {
		var ret string
		return ret
	}
	return *o.Reason
}

// GetReasonOk returns a tuple with the Reason field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *SerialNumberWithError) GetReasonOk() (*string, bool) {
	if o == nil || IsNil(o.Reason) {
		return nil, false
	}
	return o.Reason, true
}

// HasReason returns a boolean if a field has been set.
func (o *SerialNumberWithError) HasReason() bool {
	if o != nil && !IsNil(o.Reason) {
		return true
	}

	return false
}

// SetReason gets a reference to the given string and assigns it to the Reason field.
func (o *SerialNumberWithError) SetReason(v string) {
	o.Reason = &v
}

func (o SerialNumberWithError) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o SerialNumberWithError) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.SerialNumber) {
		toSerialize["serialNumber"] = o.SerialNumber
	}
	if !IsNil(o.Reason) {
		toSerialize["reason"] = o.Reason
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *SerialNumberWithError) UnmarshalJSON(data []byte) (err error) {
	varSerialNumberWithError := _SerialNumberWithError{}

	err = json.Unmarshal(data, &varSerialNumberWithError)

	if err != nil {
		return err
	}

	*o = SerialNumberWithError(varSerialNumberWithError)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "serialNumber")
		delete(additionalProperties, "reason")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableSerialNumberWithError struct {
	value *SerialNumberWithError
	isSet bool
}

func (v NullableSerialNumberWithError) Get() *SerialNumberWithError {
	return v.value
}

func (v *NullableSerialNumberWithError) Set(val *SerialNumberWithError) {
	v.value = val
	v.isSet = true
}

func (v NullableSerialNumberWithError) IsSet() bool {
	return v.isSet
}

func (v *NullableSerialNumberWithError) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableSerialNumberWithError(val *SerialNumberWithError) *NullableSerialNumberWithError {
	return &NullableSerialNumberWithError{value: val, isSet: true}
}

func (v NullableSerialNumberWithError) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableSerialNumberWithError) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
