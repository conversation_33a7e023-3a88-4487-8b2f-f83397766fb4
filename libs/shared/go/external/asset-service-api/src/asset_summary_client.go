package assetserviceapi

import (
	"bytes"
	"context"
	"errors"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-xray-sdk-go/v2/xray"
	"k8s.io/utils/ptr"
)

var userAgent = "xdp-asset-summary-api-client"

type AssetSummaryAPIStub struct {
}

func NewAssetSummaryAPIClient(isProd bool, assetSummaryAPIHost string) (AssetSummaryAPI, error) {
	if assetSummaryAPIHost == "" {
		return nil, errors.New("NewAssetSummaryAPIClient: host is required")
	}

	if isProd {
		cfg := NewConfiguration()
		cfg.UserAgent = userAgent
		cfg.HTTPClient = xray.Client(nil)
		cfg.Servers = ServerConfigurations{{
			URL: assetSummaryAPIHost,
		},
		}
		client := NewAPIClient(cfg)
		return client.AssetSummaryAPI, nil
	}

	return &AssetSummaryAPIStub{}, nil
}

func (a AssetSummaryAPIStub) GetChargingStation(ctx context.Context, ppid string) AssetSummaryAPIGetChargingStationRequest {
	return AssetSummaryAPIGetChargingStationRequest{
		ctx:        ctx,
		ApiService: &a,
		ppid:       ppid,
	}
}

func (a AssetSummaryAPIStub) GetChargingStationExecute(r AssetSummaryAPIGetChargingStationRequest) (*ChargingStationSummary, *http.Response, error) {
	if r.ppid == "" {
		return nil, nil, errors.New("GetChargingStationExecute: ppid is required")
	}

	serial := "PCB12345678"
	macAddress := "00:11:22:33:44:55"
	ocpiEvseID := "OCPI-123456"
	provisionedAt := time.Date(2025, 6, 16, 11, 6, 42, 0, time.UTC)

	ownership := OWNERSHIP_CUSTOMER
	if strings.HasPrefix(r.ppid, "PODPOINT") {
		ownership = OWNERSHIP_POD_POINT
	}

	mockChargingStationSummary := &ChargingStationSummary{
		Ppid: r.ppid,
		Rfid: &RFID{
			SerialNumber: &serial,
			MacAddress:   &macAddress,
		},
		Evses: []EvseSummary{
			{
				EvseId:       1,
				OcpiEvseId:   &ocpiEvseID,
				SerialNumber: &serial,
				MacAddress:   &macAddress,
				Pcb: &PcbSummary{
					Revision:     "RevB",
					Architecture: PcbArchitecture("ARMv7"),
					SerialNumber: serial,
					MacAddress:   macAddress,
					Generation:   Generation("Gen3"),
				},
			},
		},

		Ownership: NullableOwnership{
			value: ptr.To(ownership),
			isSet: true,
		},
		Model: ModelSummary{
			Sku: "SOLO3-UK-7KW",
			Range: ModelSummaryRange{
				Name: "Solo 3",
			},
			Vendor: ModelSummaryVendor{
				Name: "Pod Point",
			},
			Regions: []ModelSummaryRegionsInner{
				{
					Name: ptr.To("United Kingdom"),
				},
			},
		},
		Router: &RouterSummary{
			SerialNumber: "ROUTER123456",
			MacAddress:   "11:22:33:44:55:66",
			SimNumber:    "SIM1234567890",
			Model:        "TP-Link MR600",
		},
		Location: NullableLocationSummary{
			value: &LocationSummary{
				Id:       ptr.To("LOC-001"),
				IsPublic: ptr.To(true),
				Type:     ptr.To(LocationType("workplace")),
			},
			isSet: true,
		},
		Tags: []TagWithId{
			{
				Id:    ptr.To("tag-001"),
				Key:   ptr.To("env"),
				Value: ptr.To("test"),
			},
		},
		ProvisioningInfo: &ProvisioningInfo{
			ProvisionedAt: &provisionedAt,
		},
	}

	response := &http.Response{
		StatusCode: 200,
		Status:     "200 OK",
		Body:       io.NopCloser(bytes.NewBufferString("mock body")),
	}

	return mockChargingStationSummary, response, nil
}

func (a AssetSummaryAPIStub) SearchChargingStations(_ context.Context) AssetSummaryAPISearchChargingStationsRequest {
	// TODO implement me
	panic("implement me")
}

func (a AssetSummaryAPIStub) SearchChargingStationsExecute(_ AssetSummaryAPISearchChargingStationsRequest) (*ChargingStationsSummary, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}
