/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"encoding/json"
	"fmt"
	"time"
)

// checks if the ChargingStationTariffSearchCriteriaDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargingStationTariffSearchCriteriaDto{}

// ChargingStationTariffSearchCriteriaDto struct for ChargingStationTariffSearchCriteriaDto
type ChargingStationTariffSearchCriteriaDto struct {
	// Charging Station PPID
	Ppid string `json:"ppid"`
	// The effective from date
	EffectiveFrom *time.Time `json:"effectiveFrom,omitempty"`
	// The effective to date
	EffectiveTo          *time.Time `json:"effectiveTo,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _ChargingStationTariffSearchCriteriaDto ChargingStationTariffSearchCriteriaDto

// NewChargingStationTariffSearchCriteriaDto instantiates a new ChargingStationTariffSearchCriteriaDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargingStationTariffSearchCriteriaDto(ppid string) *ChargingStationTariffSearchCriteriaDto {
	this := ChargingStationTariffSearchCriteriaDto{}
	this.Ppid = ppid
	return &this
}

// NewChargingStationTariffSearchCriteriaDtoWithDefaults instantiates a new ChargingStationTariffSearchCriteriaDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargingStationTariffSearchCriteriaDtoWithDefaults() *ChargingStationTariffSearchCriteriaDto {
	this := ChargingStationTariffSearchCriteriaDto{}
	return &this
}

// GetPpid returns the Ppid field value
func (o *ChargingStationTariffSearchCriteriaDto) GetPpid() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Ppid
}

// GetPpidOk returns a tuple with the Ppid field value
// and a boolean to check if the value has been set.
func (o *ChargingStationTariffSearchCriteriaDto) GetPpidOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Ppid, true
}

// SetPpid sets field value
func (o *ChargingStationTariffSearchCriteriaDto) SetPpid(v string) {
	o.Ppid = v
}

// GetEffectiveFrom returns the EffectiveFrom field value if set, zero value otherwise.
func (o *ChargingStationTariffSearchCriteriaDto) GetEffectiveFrom() time.Time {
	if o == nil || IsNil(o.EffectiveFrom) {
		var ret time.Time
		return ret
	}
	return *o.EffectiveFrom
}

// GetEffectiveFromOk returns a tuple with the EffectiveFrom field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChargingStationTariffSearchCriteriaDto) GetEffectiveFromOk() (*time.Time, bool) {
	if o == nil || IsNil(o.EffectiveFrom) {
		return nil, false
	}
	return o.EffectiveFrom, true
}

// HasEffectiveFrom returns a boolean if a field has been set.
func (o *ChargingStationTariffSearchCriteriaDto) HasEffectiveFrom() bool {
	if o != nil && !IsNil(o.EffectiveFrom) {
		return true
	}

	return false
}

// SetEffectiveFrom gets a reference to the given time.Time and assigns it to the EffectiveFrom field.
func (o *ChargingStationTariffSearchCriteriaDto) SetEffectiveFrom(v time.Time) {
	o.EffectiveFrom = &v
}

// GetEffectiveTo returns the EffectiveTo field value if set, zero value otherwise.
func (o *ChargingStationTariffSearchCriteriaDto) GetEffectiveTo() time.Time {
	if o == nil || IsNil(o.EffectiveTo) {
		var ret time.Time
		return ret
	}
	return *o.EffectiveTo
}

// GetEffectiveToOk returns a tuple with the EffectiveTo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChargingStationTariffSearchCriteriaDto) GetEffectiveToOk() (*time.Time, bool) {
	if o == nil || IsNil(o.EffectiveTo) {
		return nil, false
	}
	return o.EffectiveTo, true
}

// HasEffectiveTo returns a boolean if a field has been set.
func (o *ChargingStationTariffSearchCriteriaDto) HasEffectiveTo() bool {
	if o != nil && !IsNil(o.EffectiveTo) {
		return true
	}

	return false
}

// SetEffectiveTo gets a reference to the given time.Time and assigns it to the EffectiveTo field.
func (o *ChargingStationTariffSearchCriteriaDto) SetEffectiveTo(v time.Time) {
	o.EffectiveTo = &v
}

func (o ChargingStationTariffSearchCriteriaDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargingStationTariffSearchCriteriaDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["ppid"] = o.Ppid
	if !IsNil(o.EffectiveFrom) {
		toSerialize["effectiveFrom"] = o.EffectiveFrom
	}
	if !IsNil(o.EffectiveTo) {
		toSerialize["effectiveTo"] = o.EffectiveTo
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *ChargingStationTariffSearchCriteriaDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"ppid",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargingStationTariffSearchCriteriaDto := _ChargingStationTariffSearchCriteriaDto{}

	err = json.Unmarshal(data, &varChargingStationTariffSearchCriteriaDto)

	if err != nil {
		return err
	}

	*o = ChargingStationTariffSearchCriteriaDto(varChargingStationTariffSearchCriteriaDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "ppid")
		delete(additionalProperties, "effectiveFrom")
		delete(additionalProperties, "effectiveTo")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableChargingStationTariffSearchCriteriaDto struct {
	value *ChargingStationTariffSearchCriteriaDto
	isSet bool
}

func (v NullableChargingStationTariffSearchCriteriaDto) Get() *ChargingStationTariffSearchCriteriaDto {
	return v.value
}

func (v *NullableChargingStationTariffSearchCriteriaDto) Set(val *ChargingStationTariffSearchCriteriaDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargingStationTariffSearchCriteriaDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargingStationTariffSearchCriteriaDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargingStationTariffSearchCriteriaDto(val *ChargingStationTariffSearchCriteriaDto) *NullableChargingStationTariffSearchCriteriaDto {
	return &NullableChargingStationTariffSearchCriteriaDto{value: val, isSet: true}
}

func (v NullableChargingStationTariffSearchCriteriaDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargingStationTariffSearchCriteriaDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
