/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"encoding/json"
)

// checks if the HealthControllerCheck503Response type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &HealthControllerCheck503Response{}

// HealthControllerCheck503Response struct for HealthControllerCheck503Response
type HealthControllerCheck503Response struct {
	Status               *string                                               `json:"status,omitempty"`
	Info                 map[string]HealthControllerCheck200ResponseInfoValue  `json:"info,omitempty"`
	Error                map[string]HealthControllerCheck200ResponseInfoValue  `json:"error,omitempty"`
	Details              *map[string]HealthControllerCheck200ResponseInfoValue `json:"details,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _HealthControllerCheck503Response HealthControllerCheck503Response

// NewHealthControllerCheck503Response instantiates a new HealthControllerCheck503Response object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHealthControllerCheck503Response() *HealthControllerCheck503Response {
	this := HealthControllerCheck503Response{}
	return &this
}

// NewHealthControllerCheck503ResponseWithDefaults instantiates a new HealthControllerCheck503Response object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHealthControllerCheck503ResponseWithDefaults() *HealthControllerCheck503Response {
	this := HealthControllerCheck503Response{}
	return &this
}

// GetStatus returns the Status field value if set, zero value otherwise.
func (o *HealthControllerCheck503Response) GetStatus() string {
	if o == nil || IsNil(o.Status) {
		var ret string
		return ret
	}
	return *o.Status
}

// GetStatusOk returns a tuple with the Status field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HealthControllerCheck503Response) GetStatusOk() (*string, bool) {
	if o == nil || IsNil(o.Status) {
		return nil, false
	}
	return o.Status, true
}

// HasStatus returns a boolean if a field has been set.
func (o *HealthControllerCheck503Response) HasStatus() bool {
	if o != nil && !IsNil(o.Status) {
		return true
	}

	return false
}

// SetStatus gets a reference to the given string and assigns it to the Status field.
func (o *HealthControllerCheck503Response) SetStatus(v string) {
	o.Status = &v
}

// GetInfo returns the Info field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *HealthControllerCheck503Response) GetInfo() map[string]HealthControllerCheck200ResponseInfoValue {
	if o == nil {
		var ret map[string]HealthControllerCheck200ResponseInfoValue
		return ret
	}
	return o.Info
}

// GetInfoOk returns a tuple with the Info field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *HealthControllerCheck503Response) GetInfoOk() (*map[string]HealthControllerCheck200ResponseInfoValue, bool) {
	if o == nil || IsNil(o.Info) {
		return nil, false
	}
	return &o.Info, true
}

// HasInfo returns a boolean if a field has been set.
func (o *HealthControllerCheck503Response) HasInfo() bool {
	if o != nil && !IsNil(o.Info) {
		return true
	}

	return false
}

// SetInfo gets a reference to the given map[string]HealthControllerCheck200ResponseInfoValue and assigns it to the Info field.
func (o *HealthControllerCheck503Response) SetInfo(v map[string]HealthControllerCheck200ResponseInfoValue) {
	o.Info = v
}

// GetError returns the Error field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *HealthControllerCheck503Response) GetError() map[string]HealthControllerCheck200ResponseInfoValue {
	if o == nil {
		var ret map[string]HealthControllerCheck200ResponseInfoValue
		return ret
	}
	return o.Error
}

// GetErrorOk returns a tuple with the Error field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *HealthControllerCheck503Response) GetErrorOk() (*map[string]HealthControllerCheck200ResponseInfoValue, bool) {
	if o == nil || IsNil(o.Error) {
		return nil, false
	}
	return &o.Error, true
}

// HasError returns a boolean if a field has been set.
func (o *HealthControllerCheck503Response) HasError() bool {
	if o != nil && !IsNil(o.Error) {
		return true
	}

	return false
}

// SetError gets a reference to the given map[string]HealthControllerCheck200ResponseInfoValue and assigns it to the Error field.
func (o *HealthControllerCheck503Response) SetError(v map[string]HealthControllerCheck200ResponseInfoValue) {
	o.Error = v
}

// GetDetails returns the Details field value if set, zero value otherwise.
func (o *HealthControllerCheck503Response) GetDetails() map[string]HealthControllerCheck200ResponseInfoValue {
	if o == nil || IsNil(o.Details) {
		var ret map[string]HealthControllerCheck200ResponseInfoValue
		return ret
	}
	return *o.Details
}

// GetDetailsOk returns a tuple with the Details field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HealthControllerCheck503Response) GetDetailsOk() (*map[string]HealthControllerCheck200ResponseInfoValue, bool) {
	if o == nil || IsNil(o.Details) {
		return nil, false
	}
	return o.Details, true
}

// HasDetails returns a boolean if a field has been set.
func (o *HealthControllerCheck503Response) HasDetails() bool {
	if o != nil && !IsNil(o.Details) {
		return true
	}

	return false
}

// SetDetails gets a reference to the given map[string]HealthControllerCheck200ResponseInfoValue and assigns it to the Details field.
func (o *HealthControllerCheck503Response) SetDetails(v map[string]HealthControllerCheck200ResponseInfoValue) {
	o.Details = &v
}

func (o HealthControllerCheck503Response) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o HealthControllerCheck503Response) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Status) {
		toSerialize["status"] = o.Status
	}
	if o.Info != nil {
		toSerialize["info"] = o.Info
	}
	if o.Error != nil {
		toSerialize["error"] = o.Error
	}
	if !IsNil(o.Details) {
		toSerialize["details"] = o.Details
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *HealthControllerCheck503Response) UnmarshalJSON(data []byte) (err error) {
	varHealthControllerCheck503Response := _HealthControllerCheck503Response{}

	err = json.Unmarshal(data, &varHealthControllerCheck503Response)

	if err != nil {
		return err
	}

	*o = HealthControllerCheck503Response(varHealthControllerCheck503Response)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "status")
		delete(additionalProperties, "info")
		delete(additionalProperties, "error")
		delete(additionalProperties, "details")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableHealthControllerCheck503Response struct {
	value *HealthControllerCheck503Response
	isSet bool
}

func (v NullableHealthControllerCheck503Response) Get() *HealthControllerCheck503Response {
	return v.value
}

func (v *NullableHealthControllerCheck503Response) Set(val *HealthControllerCheck503Response) {
	v.value = val
	v.isSet = true
}

func (v NullableHealthControllerCheck503Response) IsSet() bool {
	return v.isSet
}

func (v *NullableHealthControllerCheck503Response) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHealthControllerCheck503Response(val *HealthControllerCheck503Response) *NullableHealthControllerCheck503Response {
	return &NullableHealthControllerCheck503Response{value: val, isSet: true}
}

func (v NullableHealthControllerCheck503Response) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHealthControllerCheck503Response) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
