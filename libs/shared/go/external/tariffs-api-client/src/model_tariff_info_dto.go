/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"encoding/json"
	"fmt"
)

// checks if the TariffInfoDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &TariffInfoDto{}

// TariffInfoDto struct for TariffInfoDto
type TariffInfoDto struct {
	// Start time in the format HH:mm:ss.
	Start string `json:"start" validate:"regexp=\\\\d{2}:\\\\d{2}:\\\\d{2}"`
	// End time in the format HH:mm:ss.
	End string `json:"end" validate:"regexp=\\\\d{2}:\\\\d{2}:\\\\d{2}"`
	// Price per unit (e.g., £0.30).
	Price float32 `json:"price"`
	// Days of the week this tariff applies to. A tariff should always cover the entire week - all hours of each day.
	Days                 []string `json:"days"`
	AdditionalProperties map[string]interface{}
}

type _TariffInfoDto TariffInfoDto

// NewTariffInfoDto instantiates a new TariffInfoDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTariffInfoDto(start string, end string, price float32, days []string) *TariffInfoDto {
	this := TariffInfoDto{}
	this.Start = start
	this.End = end
	this.Price = price
	this.Days = days
	return &this
}

// NewTariffInfoDtoWithDefaults instantiates a new TariffInfoDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTariffInfoDtoWithDefaults() *TariffInfoDto {
	this := TariffInfoDto{}
	return &this
}

// GetStart returns the Start field value
func (o *TariffInfoDto) GetStart() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Start
}

// GetStartOk returns a tuple with the Start field value
// and a boolean to check if the value has been set.
func (o *TariffInfoDto) GetStartOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Start, true
}

// SetStart sets field value
func (o *TariffInfoDto) SetStart(v string) {
	o.Start = v
}

// GetEnd returns the End field value
func (o *TariffInfoDto) GetEnd() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.End
}

// GetEndOk returns a tuple with the End field value
// and a boolean to check if the value has been set.
func (o *TariffInfoDto) GetEndOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.End, true
}

// SetEnd sets field value
func (o *TariffInfoDto) SetEnd(v string) {
	o.End = v
}

// GetPrice returns the Price field value
func (o *TariffInfoDto) GetPrice() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.Price
}

// GetPriceOk returns a tuple with the Price field value
// and a boolean to check if the value has been set.
func (o *TariffInfoDto) GetPriceOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Price, true
}

// SetPrice sets field value
func (o *TariffInfoDto) SetPrice(v float32) {
	o.Price = v
}

// GetDays returns the Days field value
func (o *TariffInfoDto) GetDays() []string {
	if o == nil {
		var ret []string
		return ret
	}

	return o.Days
}

// GetDaysOk returns a tuple with the Days field value
// and a boolean to check if the value has been set.
func (o *TariffInfoDto) GetDaysOk() ([]string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Days, true
}

// SetDays sets field value
func (o *TariffInfoDto) SetDays(v []string) {
	o.Days = v
}

func (o TariffInfoDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o TariffInfoDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["start"] = o.Start
	toSerialize["end"] = o.End
	toSerialize["price"] = o.Price
	toSerialize["days"] = o.Days

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *TariffInfoDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"start",
		"end",
		"price",
		"days",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varTariffInfoDto := _TariffInfoDto{}

	err = json.Unmarshal(data, &varTariffInfoDto)

	if err != nil {
		return err
	}

	*o = TariffInfoDto(varTariffInfoDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "start")
		delete(additionalProperties, "end")
		delete(additionalProperties, "price")
		delete(additionalProperties, "days")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableTariffInfoDto struct {
	value *TariffInfoDto
	isSet bool
}

func (v NullableTariffInfoDto) Get() *TariffInfoDto {
	return v.value
}

func (v *NullableTariffInfoDto) Set(val *TariffInfoDto) {
	v.value = val
	v.isSet = true
}

func (v NullableTariffInfoDto) IsSet() bool {
	return v.isSet
}

func (v *NullableTariffInfoDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTariffInfoDto(val *TariffInfoDto) *NullableTariffInfoDto {
	return &NullableTariffInfoDto{value: val, isSet: true}
}

func (v NullableTariffInfoDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTariffInfoDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
