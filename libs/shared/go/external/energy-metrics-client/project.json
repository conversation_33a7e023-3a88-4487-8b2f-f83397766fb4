{"name": "shared-go-energy-metrics-client", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/go/external/energy-metrics-client", "projectType": "library", "tags": ["shared"], "targets": {"test": {"executor": "@nx-go/nx-go:test", "options": {"race": true}}, "generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g go -i openapi.json -o src --global-property=apiTests=false,apiDocs=false,modelDocs=false --additional-properties=packageName=energymetricsclient,withGoMod=false,generateInterfaces=true,disallowAdditionalPropertiesIfNotPresent=false", "rm openapitools.json", "npx prettier . --write", "go fmt ./..."]}, "configurations": {"docker": {"cwd": "", "commands": ["docker run --rm -v ${PWD}:/local -w /local openapitools/openapi-generator-cli generate -g go -i libs/shared/go/external/energy-metrics-client/openapi.json -o /local/libs/shared/go/external/energy-metrics-client/src --global-property=apiTests=false,apiDocs=false,modelDocs=false --additional-properties=packageName=energymetricsclient,withGoMod=false,generateInterfaces=true,disallowAdditionalPropertiesIfNotPresent=false", "npx prettier libs/shared/go/external/energy-metrics-client/src --write"]}}}}}