{"openapi": "3.0.0", "paths": {"/health": {"get": {"operationId": "HealthController_check", "parameters": [], "responses": {"200": {"description": "The Health Check is successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}, "503": {"description": "The Health Check is not successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {"redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}, "redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}}, "tags": ["Health"]}}, "/charge-overrides": {"get": {"description": "Search for any overrides based on a PPID. If search criteria is valid but no schedules are in effect then a 200 response is returned with an empty array body `[]`. Also if the services does not find an EVSE for the given criteria then return `[]`", "operationId": "search-active-overrides", "parameters": [{"name": "ppid", "required": true, "in": "query", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"200": {"description": "The list of active overrides", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChargeOverrideResponse"}}}}}, "400": {"description": "The search criteria was invalid"}, "502": {"description": "Unsupported charging station"}}, "summary": "Search for any active overrides in place", "tags": ["Charge Overrides"]}}, "/charging-stations/{ppid}/charge-overrides": {"post": {"description": "Override and schedules on the device (all evses) either indefinitely or until the specified end time", "operationId": "create-charging-station-charge-override", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargeOverrideRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChargeOverrideResponse"}}}}}, "400": {"description": "Request body invalid"}, "404": {"description": "Unit not found"}, "502": {"description": "Unsupported charging station"}}, "summary": "Create an override for a given charger, that should be applied to all EVSEs", "tags": ["Charge Overrides"]}, "get": {"description": "Gets all overrides for a given charging station (All EVSEs)", "operationId": "get-charging-station-charge-overrides", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "includePast", "required": false, "in": "query", "description": "Include past overrides", "schema": {"example": "true", "type": "boolean"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChargeOverrideResponse"}}}}}, "404": {"description": "Unit not found"}, "502": {"description": "Unsupported charging station"}}, "summary": "Get all overrides for a given charging station (All EVSEs)", "tags": ["Charge Overrides"]}, "delete": {"description": "Removes any existing overrides that are in place for a given charging station (aka unit)", "operationId": "clear-charging-station-charge-overrides", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"204": {"description": ""}, "404": {"description": "Unit not found"}, "502": {"description": "Unsupported charging station"}}, "summary": "Removes an override for a given charging station (All EVSEs)", "tags": ["Charge Overrides"]}}, "/charging-profile/{ppid}": {"post": {"description": "Submit charging profiles for the given ppid", "operationId": "submit-charging-profiles", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "description": "Body with list of charging profiles to submit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargingProfilesRequestDto"}}}}, "responses": {"202": {"description": "Charging profiles accepted"}, "400": {"description": "Charging profile not valid"}, "404": {"description": "Charging station not found"}, "422": {"description": "Charging station is not an arch5"}}, "summary": "Submit charging profiles", "tags": ["Charging Profiles"]}}, "/charging-profile/{ppid}/stack-level-range": {"delete": {"description": "Delete profiles in stack level range", "operationId": "delete-profiles-in-stack-level-range", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargingProfileDeleteRangeDto"}}}}, "responses": {"204": {"description": "Stack level range deleted"}, "400": {"description": "Stack level range not valid"}, "404": {"description": "Charging station not found"}, "422": {"description": "Charging station is not an arch5"}}, "summary": "Delete profiles in stack level range", "tags": ["Charging Profiles"]}}, "/delegated-controls": {"get": {"description": "Returns a delegated control charging stations object with the intent. The ppid is used as they key.", "operationId": "get-delegated-control-charging-stations", "parameters": [{"name": "ppid", "required": true, "in": "query", "description": "The PPIDs of the charging stations", "schema": {"example": "PSL-000000,PSL-000001", "type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Delegated control charging stations with linked vehicles and intents, including either generic or connected stateful vehicle data", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DelegatedControlChargingStationResponseDto"}}}}}}, "summary": "Returns delegated control charging stations", "tags": ["Delegated Control Charging Stations"]}}, "/delegated-controls/search": {"get": {"description": "Search criteria can be used to filter charging stations returned, or omitted to return all charging stations", "operationId": "searchDelegatedControlChargingStations", "parameters": [{"name": "hasActiveChargingSession", "required": false, "in": "query", "description": "Does the charging station have a active charging session", "schema": {"example": false, "type": "string"}}, {"name": "providerId", "required": false, "in": "query", "description": "The delegated control provider id", "schema": {"example": "451c6a82-aba9-4596-a33c-248cdd7b1af2", "type": "string"}}, {"name": "itemsPerPage", "required": false, "in": "query", "description": "Pagination option: items per page. Defaults to 10", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Pagination option: page number. Defaults to 1", "schema": {"example": 1, "type": "number"}}, {"name": "hasVehiclePluggedIn", "required": false, "in": "query", "description": "Does the charging station have a vehicle plugged in", "schema": {"example": true, "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "The delegated control status", "schema": {"example": "ACTIVE", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DelegatedControlChargingStationSearchResponseDto"}}}}}, "summary": "Search for delegated control charging stations that match given criteria", "tags": ["Delegated Control Charging Stations"]}}, "/delegated-controls/{ppid}": {"get": {"operationId": "getDelegatedControlChargingStationByPpid", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DelegatedControlChargingStationResponseDto"}}}}, "404": {"description": "Delegated control charging station not found"}}, "summary": "Gets a delegated control charging stations details by its PPID", "tags": ["Delegated Control Charging Stations"]}, "put": {"description": "Create a delegated control charging station and set the intent via providing a charge time", "operationId": "create-delegated-control-charging-station", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnrolmentRequestDto"}}}}, "responses": {"404": {"description": "Charging station not found"}, "422": {"description": "Charging station is unsupported or is missing one of evse, serial number, mac address"}}, "summary": "Create a delegated control charging station and set the intent", "tags": ["Delegated Control Charging Stations"]}, "delete": {"description": "Delete a delegated control charging station by its PPID", "operationId": "delete-delegated-control-charging-station-by-ppid", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"204": {"description": "Delegated control charging station deleted successfully"}, "404": {"description": "Unit not found"}}, "summary": "Delete a delegated control charging station", "tags": ["Delegated Control Charging Stations"]}}, "/delegated-controls/{ppid}/schedules": {"delete": {"description": "This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)", "operationId": "clearTargetScheduleForChargingStation", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"204": {"description": "No-charge schedules set successfully"}, "404": {"description": "Unit not found"}, "422": {"description": "Charging Station architecture info is not available or not supported"}}, "summary": "Clear target schedule(s) for a delegated control charging station", "tags": ["Delegated Control Schedules"]}, "put": {"description": "Request target schedules be set for the likely connected or default vehicle of a delegated control charging station", "operationId": "set-target-schedules-for-delegated-control-charging-station", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "trigger", "required": false, "in": "query", "description": "What triggered this request (if not provided, this is assumed to be PLUGIN)", "schema": {"enum": ["PLUGIN", "INTENT_UPDATE"], "type": "string"}}], "responses": {"202": {"description": "Request accepted"}, "404": {"description": "Unit not found"}, "422": {"description": "Charging Station is not a delegated control charging station"}}, "summary": "Request target schedules be set for connected vehicle connected to a delegated control charging station", "tags": ["Delegated Control Schedules"]}}, "/delegated-controls/{ppid}/plug-in/notify": {"post": {"description": "Notify plug-in", "operationId": "delegate-control-notify-charger-plug-in", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"204": {"description": "Plug-in notified successfully"}}, "summary": "Notify plug-in", "tags": ["Delegated Control Notifications"]}}, "/delegated-controls/{ppid}/un-plug/notify": {"post": {"description": "Notify un-plug", "operationId": "delegate-control-notify-charger-un-plug", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"204": {"description": "Un-<PERSON><PERSON> notified successfully"}}, "summary": "Notify un-plug", "tags": ["Delegated Control Notifications"]}}, "/api3/charging-stations/{ppid}/charge-schedules/resend": {"patch": {"description": "Resend charge schedules to the charging station", "operationId": "resend-charge-schedules", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"204": {"description": "The charge schedules were resent"}}, "summary": "Resend charge schedules", "tags": ["Charge Schedules (API3)"]}}, "/api3/charging-stations/{ppid}/charge-schedules/{scheduleId}": {"patch": {"description": "Update a charge schedule", "operationId": "update-charging-station-charge-schedule", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "scheduleId", "required": true, "in": "path", "description": "The UID of the charge schedule", "schema": {"format": "uuid", "example": "7261db3a-e1e2-4b27-a780-94d3644ae98f", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/API3PatchChargeScheduleBody"}}}}, "responses": {"200": {"description": "The updated charge schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/API3ChargeSchedule"}}}}, "404": {"description": "The charging station or charge schedule was not found"}, "422": {"description": "The charge schedule overlaps with another charge schedule"}}, "summary": "Update a charge schedule", "tags": ["Charge Schedules (API3)"]}}, "/api3/charging-stations/{ppid}/charge-schedules": {"put": {"description": "Set charging station charge schedules", "operationId": "set-charging-station-charge-schedules", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/API3PutChargeScheduleBody"}}}}, "responses": {"201": {"description": "The updated charge schedule", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/API3ChargeSchedule"}}}}}, "404": {"description": "The charging station or charge schedule was not found"}, "422": {"description": "The charge schedule overlaps with another charge schedule"}}, "summary": "Set charge schedules", "tags": ["Charge Schedules (API3)"]}, "get": {"description": "Get charging station charge schedules", "operationId": "get-charging-station-charge-schedules", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"200": {"description": "The charge schedules", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetApi3ChargeSchedules"}}}}, "404": {"description": "The charging station was not found"}}, "summary": "Get charge schedules", "tags": ["Charge Schedules (API3)"]}}, "/charging-stations/{ppid}/flexibility-requests": {"post": {"description": "Create a flexibility request for a given charger", "operationId": "create-charging-station-flexibility-request", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFlexRequestDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlexRequestResponse"}}}}, "400": {"description": "Invalid PPID"}, "404": {"description": "Unit not found"}, "409": {"description": "Conflict found with existing data"}, "422": {"description": "Ends before it starts"}, "502": {"description": "Unsupported charging station"}}, "summary": "Create flexibility request", "tags": ["Flexibility Requests"]}, "get": {"description": "Get the active flexibility requests for a given charger\nResults are limited to the first 1000, either ordered by increasing end times\nOr by decreasing start times if the includePast query param is set to true", "operationId": "get-active-charging-station-flexibility-requests", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "includePast", "required": false, "in": "query", "description": "Include past requests (defaults to false) if \"true\"", "schema": {"example": "true", "type": "boolean"}}, {"name": "includeDeleted", "required": false, "in": "query", "description": "Include deleted (canalled) requests (defaults to false) if \"true\"", "schema": {"example": "false", "type": "boolean"}}, {"name": "from", "required": false, "in": "query", "description": "Return only requests starting after this date", "schema": {"type": "string"}}, {"name": "to", "required": false, "in": "query", "description": "Return only requests starting before this date", "schema": {"type": "string"}}, {"name": "Cache-Control", "required": false, "in": "header", "description": "Set to \"refresh\" to force a refresh", "schema": {"type": "string"}}], "responses": {"200": {"description": "All active flexibility requests for the given charger", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlexRequestResponse"}}}}}, "404": {"description": "Unit not found"}}, "summary": "Get active flexibility requests", "tags": ["Flexibility Requests"]}, "delete": {"description": "Delete all flexibility requests for a given charging station", "operationId": "delete-flexibility-requests-for-charging-station", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}, "summary": "Delete flexibility requests for charging station", "tags": ["Flexibility Requests"]}}, "/flexibility-requests": {"get": {"description": "Get a flexibility request given it's external provider info", "operationId": "get-flexibility-request-by-provider-info", "parameters": [{"name": "providerName", "required": true, "in": "query", "description": "The name of the competition provider", "schema": {"example": "axle", "type": "string"}}, {"name": "providerFlexRequestId", "required": true, "in": "query", "description": "The provider's id for this flex request", "schema": {"example": "d40ff7bccd944e4f", "type": "string"}}, {"name": "ppid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "The flexibility request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlexRequestResponse"}}}}, "404": {"description": "Competition provider not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompetitionProviderNotFoundResponse"}}}}}, "summary": "Get flexibility request by provider info", "tags": ["Flexibility Requests"]}}, "/flexibility-requests/search": {"get": {"description": "Search for flexibility requests given criteria", "operationId": "searchFlexibilityRequests", "parameters": [{"name": "startAt", "required": true, "in": "query", "description": "The flex request start date/time", "schema": {"example": "2024-08-01T00:00:00Z", "type": "string"}}], "responses": {"200": {"description": "Search response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlexRequestSearchResponse"}}}}}, "summary": "Search for flexibility requests given criteria", "tags": ["Flexibility Requests"]}}, "/flexibility-requests/{id}": {"put": {"description": "Update a flexibility request given its id", "operationId": "update-flexibility-request", "parameters": [{"name": "id", "required": true, "in": "path", "description": "The id of the flexibility request", "schema": {"format": "uuid", "example": "571aefe2-a406-4cec-8b35-5466226e598e", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFlexRequestDto"}}}}, "responses": {"201": {"description": "The updated flexibility request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlexRequestResponse"}}}}, "404": {"description": "Flexibility request not found"}}, "summary": "Update flexibility request", "tags": ["Flexibility Requests"]}, "delete": {"description": "Delete a flexibility request given its id", "operationId": "delete-flexibility-request", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}, "summary": "Delete flexibility request", "tags": ["Flexibility Requests"]}}, "/programmes/{programmeId}/flexibility-requests": {"post": {"description": "Create a flexibility request for all charging stations in a given programme", "operationId": "create-flexibility-request-for-programme", "parameters": [{"name": "programmeId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFlexRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"successPpids": {"type": "array", "items": {"type": "string", "example": "PSL-00000"}}, "failedPpids": {"type": "array", "items": {"type": "string", "example": "PSL-00000"}}}}}}}, "403": {"description": "Programme is not active"}, "404": {"description": ""}}, "summary": "Create flexibility request for all charging stations in a given programme", "tags": ["Flexibility Requests"]}}, "/programmes/{programmeId}/flexibility-requests/submit-async": {"post": {"description": "Create a flexibility request for all charging stations in a given programme asynchronously", "operationId": "async-create-flexibility-request-for-programme", "parameters": [{"name": "programmeId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFlexRequestDto"}}}}, "responses": {"202": {"description": "Accepted"}, "403": {"description": "Programme is not active"}, "404": {"description": ""}}, "summary": "Create flexibility request for all charging stations in a given programme asynchronously", "tags": ["Flexibility Requests"]}}, "/delegated-controls/charging-sessions/search": {"get": {"description": "Search for delegated control sessions", "operationId": "searchDelegatedControlSessions", "parameters": [{"name": "plugInFrom", "required": true, "in": "query", "description": "The date and time the vehicle was plugged in. It must at least one hour ago", "schema": {"format": "date-time", "type": "string"}}, {"name": "plugInTo", "required": false, "in": "query", "description": "The date and time the vehicle was plugged out. It must be after plugInFrom", "schema": {"format": "date-time", "nullable": false, "example": "2021-06-01T00:00:00.000Z", "type": "string"}}, {"name": "ppid", "required": false, "in": "query", "description": "A comma separated list of PPIDs", "schema": {"nullable": false, "example": "PSL-00001,PSL-00002", "type": "array", "items": {"type": "string"}}}, {"name": "page", "required": false, "in": "query", "description": "The current page number", "schema": {"nullable": false, "default": 1, "example": 1, "type": "number"}}, {"name": "itemsPerPage", "required": false, "in": "query", "description": "The number of items per page", "schema": {"nullable": false, "default": 10, "example": 10, "type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "Search for delegated control sessions", "tags": ["Delegated Control Sessions"]}}, "/delegated-controls/{ppid}/vehicles/{vehicleId}/intents": {"put": {"description": "Sets delegated control intents. The ppid and vehicleId are required as parameters.", "operationId": "set-delegated-control-intents", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The ppid of the charging station", "schema": {"example": "PSL-123456", "type": "string"}}, {"name": "vehicleId", "required": true, "in": "path", "description": "The vehicleId associated to the charging station", "schema": {"example": "43f7d305-da87-4f80-9f18-b485e5ea3b94", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleIntentsRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetDelegatedControlIntentsResponseDto"}}}}, "404": {"description": "Possible not found scenarios: charging station not found, delegated control charging station not found, vehicle not found, or delegated control charging station vehicles record not found."}, "422": {"description": "Charging station is missing required details"}}, "summary": "Sets delegated control intents", "tags": ["Delegated Control Intents"]}, "get": {"description": "Get vehicle intents", "operationId": "get-vehicle-intents", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "vehicleId", "required": true, "in": "path", "description": "The vehicleId associated to the charging station", "schema": {"example": "43f7d305-da87-4f80-9f18-b485e5ea3b94", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleIntentsResponseDto"}}}}, "400": {"description": "Invalid ppid or vehicleId"}, "404": {"description": "Unit not found"}}, "summary": "Get vehicle intents", "tags": ["Delegated Control Intents"]}}, "/delegated-controls/{ppid}/vehicles/{vehicleId}/intents/current": {"get": {"operationId": "DelegatedControlIntentController_getCurrentIntentDetail", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "vehicleId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Delegated Control Intents"]}}, "/delegated-controls/{ppid}/notify/intent-cannot-be-met": {"post": {"description": "Notify intent cannot be met", "operationId": "notify-intent-cannot-be-met", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CannotMeetSessionIntent"}}}}, "responses": {"200": {"description": "Accepted"}, "404": {"description": "Unit not found"}}, "summary": "Notify that intent cannot be met", "tags": ["Delegated Control Intents"]}}, "/delegated-controls/{ppid}/intent/calculate-charge-info-from-profile": {"post": {"description": "Calculate charge info from profile", "operationId": "calculate-charge-info-from-profile", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxProfileInfoDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleChargeInfoDto"}}}}, "404": {"description": "Unit not found"}}, "summary": "Calculate charge info from ocpp1.6 profile", "tags": ["Delegated Control Intents"]}}, "/delegated-controls/{ppid}/vehicles": {"get": {"description": "Get all vehicles linked to a delegated control charging station", "operationId": "get-delegated-control-charging-station-vehicles", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the delegated charging station", "schema": {"example": "PSL-000000", "type": "string"}}], "responses": {"200": {"description": "The vehicles linked to the delegated control charging station", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendedVehicleLinksResponseDto"}}}}, "400": {"description": "Invalid ppid"}}, "summary": "Get delegated control charging station vehicles", "tags": ["Delegated Control Vehicles"]}, "post": {"description": "Add vehicle to delegated control charging station", "operationId": "add-vehicle-to-delegated-control-charging-station", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVehicleLinkRequestDto"}}}}, "responses": {"201": {"description": "The data of a stateful vehicle", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleLinkResponseDto"}}}}, "400": {"description": "Invalid body"}, "404": {"description": "Unit not found"}, "409": {"description": "Vehicle already linked to the charging station"}, "500": {"description": "Internal server error"}}, "summary": "Add vehicle to delegated control charging station", "tags": ["Delegated Control Vehicles"]}}, "/delegated-controls/{ppid}/vehicles/{vehicleId}": {"get": {"description": "Get vehicle", "operationId": "get-vehicle", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "vehicleId", "required": true, "in": "path", "description": "The vehicleId associated to the charging station", "schema": {"example": "43f7d305-da87-4f80-9f18-b485e5ea3b94", "type": "string"}}], "responses": {"200": {"description": "The data of a stateful vehicle", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendedVehicleLinkResponseDto"}}}}, "404": {"description": "Unit or vehicle not found"}}, "summary": "Get vehicle", "tags": ["Delegated Control Vehicles"]}, "delete": {"description": "Unlink a vehicle from a delegated control charging station", "operationId": "unlink-vehicle-from-delegated-control-charging-station", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "vehicleId", "required": true, "in": "path", "description": "The vehicleId associated to the charging station", "schema": {"example": "43f7d305-da87-4f80-9f18-b485e5ea3b94", "type": "string"}}], "responses": {"204": {"description": "Vehicle unlinked from delegated control charging station"}, "400": {"description": "Invalid ppid or vehicleId"}, "404": {"description": "Unit not found"}}, "summary": "Unlink a vehicle from a delegated control charging station", "tags": ["Delegated Control Vehicles"]}, "patch": {"description": "Given a vehicle ID and new data, updates the vehicle", "operationId": "update-vehicle-by-id", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "vehicleId", "required": true, "in": "path", "description": "The vehicle ID", "schema": {"format": "uuid", "example": "8bca9272-30c9-4075-a035-bf35876eb9ba", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVehicleLinkRequestDto"}}}}, "responses": {"200": {"description": "The data of a stateful vehicle", "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/GenericStatefulVehicleDto", "description": "The data of a stateful generic vehicle"}, {"$ref": "#/components/schemas/ConnectedStatefulVehicleDto", "description": "The data of a stateful connected vehicle"}]}}}}, "400": {"description": "The vehicle ID or body is invalid"}, "404": {"description": "Vehicle with the given ID not found"}}, "summary": "Update vehicle data by ID", "tags": ["Delegated Control Vehicles"]}}, "/delegated-controls/vehicles/{vehicleId}/charging-stations": {"delete": {"description": "Unlink a vehicle from all linked delegated control charging stations", "operationId": "unlink-vehicle-from-delegated-control-charging-stations", "parameters": [{"name": "vehicleId", "required": true, "in": "path", "description": "The vehicleId associated to the charging station", "schema": {"example": "43f7d305-da87-4f80-9f18-b485e5ea3b94", "type": "string"}}], "responses": {"204": {"description": "Vehicle unlinked from all linked charging stations"}, "400": {"description": "Invalid vehicleId"}}, "summary": "Unlink a vehicle from all linked delegated control charging stations", "tags": ["Delegated Control Vehicles"]}, "get": {"description": "Get all charging stations linked to a vehicle", "operationId": "get-charging-stations-linked-to-vehicle", "parameters": [{"name": "vehicleId", "required": true, "in": "path", "description": "The vehicleId linked to the charging stations", "schema": {"example": "43f7d305-da87-4f80-9f18-b485e5ea3b94", "type": "string"}}], "responses": {"200": {"description": "PPIDS of Charging stations linked to a vehicle", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleChargingStationsResponseDto"}}}}, "400": {"description": "Invalid vehicleId"}}, "summary": "Get all charging stations linked to a vehicle", "tags": ["Delegated Control Vehicles"]}}, "/commands/responses": {"post": {"operationId": "postCommandResponse", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommandResponse"}}}}, "responses": {"404": {"description": "The command could not be found"}}, "summary": "Send a response from the connectivity commands API", "tags": ["Commands"]}}, "/commands/trigger": {"post": {"operationId": "triggerCommand", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerCommandRequest"}}}}, "responses": {"201": {"description": ""}}, "summary": "Trigger a command on the charging stations", "tags": ["Commands"]}}, "/charging-stations/{ppid}/evses/{evseId}/energy-offer-status": {"get": {"description": "Returns if a charger should offer energy", "operationId": "get-charging-station-energy-offer-status", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The PPID of the charging station", "schema": {"example": "PSL-000000", "type": "string"}}, {"name": "evseId", "required": true, "in": "path", "description": "The evseId of the door", "schema": {"type": "integer", "minimum": 1, "maximum": 32}}, {"name": "Cache-Control", "required": false, "in": "header", "description": "Set to \"refresh\" to force a refresh", "schema": {"type": "string"}}], "responses": {"200": {"description": "The energy offer status of the charging station", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnergyOfferStatusResponse"}}}}, "400": {"description": "Invalid PPID"}, "404": {"description": "Unit not found"}, "502": {"description": "Unsupported charging station"}}, "summary": "Returns if a charger should offer energy", "tags": ["Energy Offer Status", "energy-offer-status"]}}, "/charge-schedules": {"get": {"description": "Get all active charge schedules for the given ppids", "operationId": "get-charge-schedules", "parameters": [{"name": "ppid", "required": true, "in": "query", "description": "Accepts comma separated ppid(s) to batch fetch their charge schedules", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "The charge schedules for the given ppids", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"required": ["startDay", "endDay", "startTime", "endTime"], "type": "object", "properties": {"startDay": {"type": "integer", "enum": [1, 2, 3, 4, 5, 6, 7]}, "endDay": {"type": "integer", "enum": [1, 2, 3, 4, 5, 6, 7]}, "startTime": {"type": "string", "pattern": "^([01]{1}[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$"}, "endTime": {"type": "string", "pattern": "^([01]{1}[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$"}}}}}}}}}, "summary": "Get all active charge schedules", "tags": ["Charge Schedules"]}}, "/charge-schedules/{ppid}/history": {"get": {"description": "Get historical charge schedules for the given ppid", "operationId": "get-charger-charge-schedule-history", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "Accepts a single ppid to fetch old charge schedules", "schema": {"example": "PSL-123456", "type": "string"}}, {"name": "items", "required": false, "in": "query", "description": "Number of items to return", "schema": {"default": 100, "example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "from", "required": true, "in": "query", "description": "Start date of the charge schedule", "schema": {"example": "2021-01-01", "type": "string"}}, {"name": "to", "required": true, "in": "query", "description": "End date of the charge schedule", "schema": {"example": "2021-01-01", "type": "string"}}], "responses": {"200": {"description": "The historical charge schedules for the given ppid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSchedulesDto"}}}}}, "summary": "Get historical charge schedules", "tags": ["Charge Schedules"]}, "delete": {"description": "Delete old charge schedules for the given ppid", "operationId": "delete-charge-schedule-history", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "Accepts a single ppid to delete old charge schedules", "schema": {"example": "PSL-123456", "type": "string"}}], "responses": {"204": {"description": ""}}, "summary": "Delete old charge schedules", "tags": ["Charge Schedules"]}}, "/charge-schedules/effective": {"post": {"description": "Get effective charge schedules for the given ppids in the time range", "operationId": "get-effective-charge-schedules-in-time-range", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EffectiveChargeSchedulesDto"}}}}, "responses": {"200": {"description": "The effective charge schedules for the given ppids", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EffectiveChargeSchedulesResponseDto"}}}}}, "400": {"description": ""}}, "summary": "Get effective charge schedules in time range", "tags": ["Charge Schedules"]}}, "/charge-schedules/charging-stations": {"get": {"operationId": "ChargeScheduleController_getChargingStationsWithChargeSchedules", "parameters": [{"name": "items", "required": false, "in": "query", "description": "Number of items to return", "schema": {"default": 100, "example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "from", "required": true, "in": "query", "description": "Start date of the charge schedule", "schema": {"example": "2021-01-01", "type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Charge Schedules"]}}, "/charge-schedules/{ppid}/composite": {"get": {"description": "Retrieve the composite charge schedule from the charging by executing GetCompositeSchedule.conf", "operationId": "get-composite-charge-schedule", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "duration", "required": false, "in": "query", "description": "The duration in seconds for which the composite schedule is requested", "schema": {"default": 7200, "example": 3600, "type": "number"}}], "responses": {"200": {"description": "The composite charge schedule for the given ppid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompositeScheduleDto"}}}}, "404": {"description": "The charging station with the given ppid was not found"}, "422": {"description": "Invalid charging station type, only ARCH5 is supported"}, "500": {"description": "Failed to get schedules, an internal error occurred"}}, "summary": "Get composite charge schedule", "tags": ["Charge Schedules"]}}, "/charge-schedules/{ppid}/default": {"put": {"operationId": "setDefaultChargeSchedule", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}, "summary": "Set the default charge schedule for a charging station", "tags": ["Charge Schedules"]}}, "/charging-stations/{ppid}/cache": {"delete": {"description": "This endpoint will clear the cache for a charging station", "operationId": "clearCacheForChargingStation", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The charging station PPID", "schema": {"example": "PSL-12345", "type": "string"}}], "responses": {"204": {"description": ""}}, "summary": "Clear the cache for a charging station", "tags": ["Charging Station Cache"]}}, "/delegated-controls/{ppid}/charging-profiles/validate": {"post": {"description": "Returns a validated charging profile object with a valid flag. The flag indicates if the provided charging profile is valid or not.", "operationId": "validateChargingProfile", "parameters": [{"name": "ppid", "required": true, "in": "path", "description": "The ppid of the charging station", "schema": {"example": "PSL-123456", "type": "string"}}], "requestBody": {"required": true, "description": "The charging profile to validate", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateChargingProfileRequestDto"}}}}, "responses": {"200": {"description": "The validated charging profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateChargingProfileResponseDto"}}}}}, "summary": "Returns the validated charging profile", "tags": ["Delegated Control Charging Profiles"]}}, "/reward-points/{ppid}": {"post": {"description": "Get reward points from charge statistics. Note: this endpoint is to be replaced by POST /reward-info/:ppid and should be considered deprecated.", "operationId": "convert-charge-to-reward-points", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "description": "Charge statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargeStatisticsDto"}}}}, "responses": {"200": {"description": "Reward information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RewardPointsDto"}}}}, "400": {"description": "Invalid charge statistics"}, "404": {"description": "Charging station not found"}}, "summary": "Convert a charge into reward points", "tags": ["Rewards"]}}, "/reward-info/{ppid}": {"post": {"operationId": "getChargingSessionRewardInfo", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "description": "Charge statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargeStatisticsDto"}}}}, "responses": {"200": {"description": "Reward information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RewardInfoDto"}}}}}, "summary": "Get reward information for a charging session", "tags": ["Rewards"]}}}, "info": {"title": "Smart Charging Service Api", "description": "API for managing smart charging service api", "version": "1.0", "contact": {}}, "tags": [{"name": "smart-charging-service-api", "description": ""}], "servers": [], "components": {"schemas": {"EvseResponse": {"type": "object", "properties": {"door": {"type": "string", "description": "The door of the evse", "example": "A"}, "ocppEvseId": {"type": "number", "description": "The ocppEvseId of the evse", "example": 1, "minimum": 1, "maximum": 32}}, "required": ["door", "ocppEvseId"]}, "ChargingStationResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the charging station", "format": "uuid", "example": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b", "nullable": true}, "ppid": {"type": "string", "example": "PSL-000000"}, "mpan": {"type": "string", "nullable": true}, "addressId": {"type": "string", "format": "uuid", "nullable": true}}, "required": ["ppid"]}, "ChargeOverrideResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the charge override", "format": "uuid", "example": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b"}, "requestedAt": {"type": "string", "description": "The date and time the charge override was requested", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "receivedAt": {"type": "string", "description": "The date and time the charge override was received by the api", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "endAt": {"type": "string", "description": "The date and time the charge override should end", "format": "date-time", "example": "2021-08-01T00:00:00.000Z", "nullable": true}, "evse": {"description": "The evse associated with the charge override", "allOf": [{"$ref": "#/components/schemas/EvseResponse"}]}, "chargingStation": {"description": "The charging station associated with the charge override", "allOf": [{"$ref": "#/components/schemas/ChargingStationResponseDto"}]}, "deletedAt": {"type": "string", "description": "The date and time the charge override was deleted", "format": "date-time", "example": "2021-08-01T00:00:00.000Z", "nullable": true}}, "required": ["id", "requestedAt", "receivedAt", "endAt", "evse", "chargingStation", "deletedAt"]}, "ChargeOverrideRequest": {"type": "object", "properties": {"requestedAt": {"type": "string", "description": "The date and time the charge override was requested", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "endAt": {"type": "string", "description": "The date and time the charge override should end", "format": "date-time", "example": "2021-08-01T00:00:00.000Z", "nullable": true}}, "required": ["requestedAt"]}, "ChargingProfileDto": {"type": "object", "properties": {"chargingProfile": {"type": "object", "description": "OCPP 1.6 charging profile, note that chargingProfileId is not required, and if it is provided it will be overwritten by this api"}}, "required": ["chargingProfile"]}, "ChargingProfilesRequestDto": {"type": "object", "properties": {"profiles": {"description": "Charging profiles", "type": "array", "items": {"$ref": "#/components/schemas/ChargingProfileDto"}}}, "required": ["profiles"]}, "ChargingProfileDeleteRangeDto": {"type": "object", "properties": {"start": {"type": "number", "description": "Start of stack level range to delete (inclusive)"}, "end": {"type": "number", "description": "End of stack level range to delete (inclusive)"}}, "required": ["start", "end"]}, "VehicleInformation": {"type": "object", "properties": {"brand": {"type": "string", "description": "The vehicle make", "example": "Polestar", "nullable": true}, "model": {"type": "string", "description": "The vehicle model", "example": "2", "nullable": true}, "modelVariant": {"type": "string", "description": "The vehicle model variant", "example": "Long range", "nullable": true}, "vehicleRegistrationPlate": {"type": "string", "description": "The vehicle registration plate", "example": "ABC123", "nullable": true}, "displayName": {"type": "string", "description": "The vehicle display name", "example": "My car", "nullable": true}, "evDatabaseId": {"type": "string", "description": "The vehicle EV database ID", "example": "1234567890", "nullable": true}}}, "ConnectedChargeState": {"type": "object", "properties": {"batteryCapacity": {"type": "number", "description": "The vehicle battery capacity in kWh", "example": 78}, "batteryLevelPercent": {"type": "number", "description": "The vehicle battery level in percent", "example": 78, "nullable": true}, "chargeLimitPercent": {"type": "number", "description": "The vehicle charge limit in percent", "example": 78, "nullable": true}, "chargeRate": {"type": "number", "description": "The vehicle charge rate in kW", "example": 78, "nullable": true}, "chargeTimeRemaining": {"type": "number", "description": "The vehicle charge time remaining in minutes", "example": 78, "nullable": true}, "isCharging": {"type": "boolean", "description": "The vehicle charging status", "example": true, "nullable": true}, "isFullyCharged": {"type": "boolean", "description": "The vehicle fully charged status", "example": true, "nullable": true}, "isPluggedIn": {"type": "boolean", "description": "The vehicle plugged in status", "example": true, "nullable": true}, "lastUpdated": {"type": "string", "description": "The vehicle last updated timestamp", "example": "2021-08-12T09:00:00Z", "nullable": true}, "maxCurrent": {"type": "number", "description": "The vehicle max current in A", "example": 32, "nullable": true}, "powerDeliveryState": {"type": "string", "description": "The vehicle power delivery state", "enum": ["UNPLUGGED", "PLUGGED_IN:NO_POWER", "PLUGGED_IN:STOPPED", "PLUGGED_IN:COMPLETE", "PLUGGED_IN:CHARGING", "UNKNOWN", "PLUGGED_IN:INITIALIZING", "PLUGGED_IN:FAULT"], "nullable": true}, "range": {"type": "number", "description": "The vehicle range in km", "example": 300, "nullable": true}}, "required": ["batteryCapacity", "batteryLevelPercent", "chargeLimitPercent", "chargeRate", "chargeTimeRemaining", "isCharging", "isFullyCharged", "isPluggedIn", "lastUpdated", "max<PERSON><PERSON><PERSON>", "powerDeliveryState", "range"]}, "InterventionDto": {"type": "object", "properties": {"all": {"type": "string", "description": "The endpoint to extract all interventions"}, "chargeState": {"description": "The individual interventions for charge state", "type": "array", "items": {"type": "string"}}, "information": {"description": "The individual interventions for vehicle information", "type": "array", "items": {"type": "string"}}}, "required": ["all", "chargeState", "information"]}, "ConnectedStatefulVehicleDto": {"type": "object", "properties": {"id": {"type": "string", "description": "The vehicle ID", "format": "uuid"}, "enodeUserId": {"type": "string", "description": "The enode user ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}, "enodeVehicleId": {"type": "string", "description": "The enode vehicle ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}, "vehicleInformation": {"description": "The vehicle information", "allOf": [{"$ref": "#/components/schemas/VehicleInformation"}]}, "chargeState": {"description": "The vehicle charge state", "allOf": [{"$ref": "#/components/schemas/ConnectedChargeState"}]}, "interventions": {"description": "The vehicle interventions", "allOf": [{"$ref": "#/components/schemas/InterventionDto"}]}}, "required": ["id", "vehicleInformation", "chargeState", "interventions"]}, "GenericChargeState": {"type": "object", "properties": {"batteryCapacity": {"type": "number", "description": "The vehicle battery capacity in kWh", "example": 78}}, "required": ["batteryCapacity"]}, "GenericStatefulVehicleDto": {"type": "object", "properties": {"id": {"type": "string", "description": "The vehicle ID", "format": "uuid"}, "enodeUserId": {"type": "string", "description": "The enode user ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}, "enodeVehicleId": {"type": "string", "description": "The vehicle enode ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}, "vehicleInformation": {"description": "The vehicle information", "allOf": [{"$ref": "#/components/schemas/VehicleInformation"}]}, "chargeState": {"description": "The vehicle charge state", "allOf": [{"$ref": "#/components/schemas/GenericChargeState"}]}}, "required": ["id", "vehicleInformation", "chargeState"]}, "VehicleIntentEntryDto": {"type": "object", "properties": {"chargeByTime": {"type": "string", "description": "Time by which the charging should be completed (HH:MM:SS format)", "example": "07:00:00"}, "chargeKWh": {"type": "number", "description": "Number of kWh to charge", "example": 28}, "dayOfWeek": {"type": "string", "description": "The day of the week for the charging intent. Valid values: MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY.", "enum": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "example": "MONDAY"}}, "required": ["chargeByTime", "chargeKWh", "dayOfWeek"]}, "VehicleIntentsResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "The vehicle ID"}, "maxPrice": {"type": "number", "example": 0.5, "description": "The maximum price.", "nullable": true}, "details": {"description": "The vehicle intents", "type": "array", "items": {"$ref": "#/components/schemas/VehicleIntentEntryDto"}}}, "required": ["id", "maxPrice", "details"]}, "VehicleLinkResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "isPluggedInToThisCharger": {"type": "boolean"}, "vehicle": {"oneOf": [{"$ref": "#/components/schemas/ConnectedStatefulVehicleDto"}, {"$ref": "#/components/schemas/GenericStatefulVehicleDto"}]}, "intents": {"$ref": "#/components/schemas/VehicleIntentsResponseDto"}}, "required": ["id", "isPluggedInToThisCharger", "vehicle", "intents"]}, "DelegatedControlChargingStationResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "The charging station ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b"}, "ppid": {"type": "string", "description": "The charging station PPID", "example": "PSL-123456"}, "status": {"type": "string", "description": "The status of the charging station", "enum": ["CANDIDATE", "PENDING", "ACTIVE", "INACTIVE", "UNKNOWN"], "example": "ACTIVE"}, "statusEffectiveFrom": {"format": "date-time", "type": "string", "description": "When the current status became effective"}, "createdAt": {"type": "string", "description": "The charging station creation date", "format": "date-time", "example": "2021-01-01T00:00:00Z"}, "vehicleLinks": {"type": "array", "items": {"$ref": "#/components/schemas/VehicleLinkResponseDto"}}, "thirdPartyManagerProviderId": {"type": "string", "description": "The third party manager provider ID", "nullable": true}}, "required": ["id", "ppid", "createdAt"]}, "DelegatedControlChargingStationSearchCriteriaDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["CANDIDATE", "PENDING", "ACTIVE", "INACTIVE", "UNKNOWN"]}, "hasVehiclePluggedIn": {"type": "boolean"}, "providerId": {"type": "string"}}}, "DelegatedControlChargingStationSearchMetadataDto": {"type": "object", "properties": {"pagination": {"type": "object", "description": "Pagination information", "properties": {"pages": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 1}, "page": {"type": "integer", "example": 1}, "itemsPerPage": {"type": "integer", "example": 1}}}, "criteria": {"$ref": "#/components/schemas/DelegatedControlChargingStationSearchCriteriaDto"}}, "required": ["pagination", "criteria"]}, "DelegatedControlChargingStationSearchResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/DelegatedControlChargingStationResponseDto"}}, "metadata": {"$ref": "#/components/schemas/DelegatedControlChargingStationSearchMetadataDto"}}, "required": ["data", "metadata"]}, "EnrolmentRequestDto": {"type": "object", "properties": {"providerName": {"type": "string", "description": "The provider name", "example": "axle", "nullable": true}, "status": {"type": "string", "description": "The initial status", "example": "CANDIDATE", "enum": ["CANDIDATE", "PENDING", "ACTIVE", "INACTIVE", "UNKNOWN"]}, "mpan": {"type": "string", "description": "The charging station's 13 digit mpan", "example": "1100034567890"}, "postcode": {"type": "string", "description": "The charging station's postcode", "example": "SW1A 1AA"}}}, "ChargeScheduleStatus": {"type": "object", "properties": {"is_active": {"type": "boolean", "title": "Charge schedule status"}}, "required": ["is_active"]}, "API3PatchChargeScheduleBody": {"type": "object", "properties": {"start_day": {"type": "number", "title": "Start day of the week", "enum": [1, 2, 3, 4, 5, 6, 7]}, "start_time": {"type": "object", "title": "Start time of the day", "example": "18:00:00"}, "end_day": {"type": "number", "title": "End day of the week", "enum": [1, 2, 3, 4, 5, 6, 7]}, "end_time": {"type": "object", "title": "End time of the day", "example": "06:00:00"}, "status": {"title": "Charge schedule status", "allOf": [{"$ref": "#/components/schemas/ChargeScheduleStatus"}]}}}, "API3ChargeSchedule": {"type": "object", "properties": {"uid": {"type": "string", "title": "Charge schedule uid"}, "start_day": {"type": "number", "title": "Start day of the week", "enum": [1, 2, 3, 4, 5, 6, 7]}, "start_time": {"type": "string", "title": "Start time of the day", "example": "18:00:00"}, "end_day": {"type": "number", "title": "End day of the week", "enum": [1, 2, 3, 4, 5, 6, 7]}, "end_time": {"type": "string", "title": "End time of the day", "example": "06:00:00"}, "status": {"title": "Charge schedule status", "allOf": [{"$ref": "#/components/schemas/ChargeScheduleStatus"}]}}, "required": ["uid", "start_day", "start_time", "end_day", "end_time", "status"]}, "Data": {"type": "object", "properties": {"uid": {"type": "string", "title": "The ID of the charge schedule, for when updating an existing charge schedule", "format": "uuid"}, "start_day": {"type": "number", "title": "Start day of the week", "enum": [1, 2, 3, 4, 5, 6, 7]}, "start_time": {"type": "string", "title": "Start time of the day", "example": "18:00:00"}, "end_day": {"type": "number", "title": "End day of the week", "enum": [1, 2, 3, 4, 5, 6, 7]}, "end_time": {"type": "string", "title": "End time of the day", "example": "06:00:00"}, "status": {"title": "Charge schedule status", "allOf": [{"$ref": "#/components/schemas/ChargeScheduleStatus"}]}}, "required": ["start_day", "start_time", "end_day", "end_time", "status"]}, "API3PutChargeScheduleBody": {"type": "object", "properties": {"data": {"title": "Charge schedule data", "type": "array", "items": {"$ref": "#/components/schemas/Data"}}}, "required": ["data"]}, "Pagination": {"type": "object", "properties": {}}, "Meta": {"type": "object", "properties": {"pagination": {"title": "Pagination", "allOf": [{"$ref": "#/components/schemas/Pagination"}]}}, "required": ["pagination"]}, "GetApi3ChargeSchedules": {"type": "object", "properties": {"data": {"title": "Charge schedules", "type": "array", "items": {"$ref": "#/components/schemas/API3ChargeSchedule"}}, "meta": {"title": "Meta", "allOf": [{"$ref": "#/components/schemas/Meta"}]}}, "required": ["data", "meta"]}, "FlexRequestLimitDto": {"type": "object", "properties": {"unit": {"type": "string", "description": "The unit of the limit", "enum": ["AMP", "KW"]}, "value": {"type": "number", "description": "The value of the limit"}}, "required": ["unit", "value"]}, "FlexRequestProviderDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the provider"}, "externalFlexRequestId": {"type": "string", "description": "The provider's id for this flex request"}}, "required": ["name", "externalFlexRequestId"]}, "CreateFlexRequestDto": {"type": "object", "properties": {"requestedAt": {"type": "string", "description": "The date and time the flexibility request was requested", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "startAt": {"type": "string", "description": "The date and time the flexibility request will start", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "endAt": {"type": "string", "description": "The date and time the flexibility request will end", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "direction": {"type": "string", "description": "The direction of the flexibility request", "enum": ["INCREASE", "REDUCE"]}, "limit": {"description": "The limit of the flexibility request", "allOf": [{"$ref": "#/components/schemas/FlexRequestLimitDto"}]}, "provider": {"description": "Details of the provider which originated this flex request", "allOf": [{"$ref": "#/components/schemas/FlexRequestProviderDto"}]}, "triggerMessageId": {"type": "string", "description": "For a Programme-based flex request, the id of the message which triggered the request"}}, "required": ["requestedAt", "startAt", "endAt", "direction"]}, "FlexRequestResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the flexibility request", "format": "uuid", "example": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b"}, "chargingStation": {"$ref": "#/components/schemas/ChargingStationResponseDto"}, "requestedAt": {"type": "string", "description": "The date and time the flexibility request was requested", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "startAt": {"type": "string", "description": "The date and time the flexibility request will start", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "endAt": {"type": "string", "description": "The date and time the flexibility request will end", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "direction": {"type": "string", "description": "The direction of the flexibility request", "enum": ["INCREASE", "REDUCE"]}, "limit": {"type": "object", "description": "The limit of the flexibility request", "properties": {"unit": {"type": "string", "enum": ["AMP", "KW"]}, "value": {"type": "number"}}}, "deletedAt": {"type": "string", "description": "The date and time the flexibility request was deleted (cancelled) if applicable", "format": "date-time", "example": "2021-08-01T00:00:00.000Z", "nullable": true}}, "required": ["id", "chargingStation", "requestedAt", "startAt", "direction", "limit"]}, "CompetitionProviderNotFoundResponse": {"type": "object", "properties": {}}, "FlexRequestSearchResponse": {"type": "object", "properties": {"data": {"description": "The list of matching flex requests", "type": "array", "items": {"$ref": "#/components/schemas/FlexRequestResponse"}}}, "required": ["data"]}, "UpdateFlexRequestDto": {"type": "object", "properties": {"startAt": {"type": "string", "description": "The date and time the flexibility request will start", "format": "date-time"}, "endAt": {"type": "string", "description": "The date and time the flexibility request will end", "format": "date-time"}}, "required": ["startAt", "endAt"]}, "VehicleIntentsRequestDto": {"type": "object", "properties": {"intentDetails": {"type": "array", "items": {"$ref": "#/components/schemas/VehicleIntentEntryDto"}}, "maxPrice": {"type": "number", "example": 0.5, "description": "The maximum price."}}, "required": ["intentDetails"]}, "SetDelegatedControlIntentsResponseDto": {"type": "object", "properties": {"id": {"type": "string", "example": "93e7e441-8508-4b79-8cf2-b786b4b46304"}, "delegatedControlChargingStationVehicleId": {"type": "string", "example": "adc6e152-1778-4faa-b71c-194af592f23c"}, "intentDetails": {"type": "array", "items": {"$ref": "#/components/schemas/VehicleIntentEntryDto"}}, "maxPrice": {"type": "number", "example": 0.5, "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "example": "2024-07-31T12:34:56.789Z"}, "updatedAt": {"format": "date-time", "type": "string", "example": "2024-07-31T12:34:56.789Z"}}, "required": ["id", "delegatedControlChargingStationVehicleId", "intentDetails", "maxPrice", "createdAt", "updatedAt"]}, "CannotMeetSessionIntent": {"type": "object", "properties": {"cannotMeetTargetReason": {"type": "string", "enum": ["PRICE", "TIME"]}, "expectedChargeByTarget_kWh": {"type": "number", "nullable": true}, "expectedChargeByTargetPercent": {"type": "number", "nullable": true}, "fullChargeByTime": {"type": "string", "format": "date-time", "nullable": true}}, "required": ["cannotMeetTargetReason", "expectedChargeByTarget_kWh", "expectedChargeByTargetPercent", "fullChargeByTime"]}, "TxProfileInfoDto": {"type": "object", "properties": {"profile": {"type": "object", "description": "ocpp TxProfile charging profile"}}, "required": ["profile"]}, "ChargeDetailDto": {"type": "object", "properties": {"expectedChargeByTarget_kWh": {"type": "number", "nullable": true}, "expectedChargeByTargetPercent": {"type": "number", "nullable": true}, "fullChargeByTime": {"type": "string", "format": "date-time", "nullable": true}}, "required": ["expectedChargeByTarget_kWh", "expectedChargeByTargetPercent", "fullChargeByTime"]}, "VehicleChargeInfoDto": {"type": "object", "properties": {"canMeetTarget": {"type": "boolean"}, "cannotMeetTargetReason": {"type": "string", "enum": ["PRICE", "TIME"], "nullable": true}, "chargeDetail": {"$ref": "#/components/schemas/ChargeDetailDto"}, "chargingStation": {"type": "object", "properties": {"ppid": {"type": "string"}}}}, "required": ["can<PERSON><PERSON>tTarget", "cannotMeetTargetReason", "chargeDetail", "chargingStation"]}, "ExtendedVehicleLinkResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "isPluggedInToThisCharger": {"type": "boolean"}, "vehicle": {"oneOf": [{"$ref": "#/components/schemas/ConnectedStatefulVehicleDto"}, {"$ref": "#/components/schemas/GenericStatefulVehicleDto"}]}, "intents": {"$ref": "#/components/schemas/VehicleIntentsResponseDto"}, "currentIntent": {"nullable": true, "allOf": [{"$ref": "#/components/schemas/VehicleChargeInfoDto"}]}}, "required": ["id", "isPluggedInToThisCharger", "vehicle", "intents", "currentIntent"]}, "ExtendedVehicleLinksResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ExtendedVehicleLinkResponseDto"}}}, "required": ["data"]}, "ExtendedVehicleInformation": {"type": "object", "properties": {"brand": {"type": "string", "description": "The vehicle make", "example": "Polestar", "nullable": true}, "model": {"type": "string", "description": "The vehicle model", "example": "2", "nullable": true}, "modelVariant": {"type": "string", "description": "The vehicle model variant", "example": "Long range", "nullable": true}, "vehicleRegistrationPlate": {"type": "string", "description": "The vehicle registration plate", "example": "ABC123", "nullable": true}, "displayName": {"type": "string", "description": "The vehicle display name", "example": "My car", "nullable": true}, "evDatabaseId": {"type": "string", "description": "The vehicle EV database ID", "example": "1234567890", "nullable": true}, "vin": {"type": "string", "description": "The vehicle VIN", "example": "1234567890", "nullable": true}}}, "UpdateVehicleRequestDto": {"type": "object", "properties": {"vehicleInformation": {"description": "Vehicle information", "allOf": [{"$ref": "#/components/schemas/ExtendedVehicleInformation"}]}, "chargeState": {"description": "The vehicle charge state", "allOf": [{"$ref": "#/components/schemas/GenericChargeState"}]}, "enodeUserId": {"type": "string", "description": "The vehicle user ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}, "enodeVehicleId": {"type": "string", "description": "The vehicle enode ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}}}, "UpdateVehicleLinkRequestDto": {"type": "object", "properties": {"isPluggedInToThisCharger": {"type": "boolean"}, "vehicle": {"$ref": "#/components/schemas/UpdateVehicleRequestDto"}}}, "CreateVehicleRequestDto": {"type": "object", "properties": {"vehicleInformation": {"description": "Vehicle information", "allOf": [{"$ref": "#/components/schemas/ExtendedVehicleInformation"}]}, "chargeState": {"description": "The vehicle charge state", "allOf": [{"$ref": "#/components/schemas/GenericChargeState"}]}, "enodeUserId": {"type": "string", "description": "The vehicle user ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}, "enodeVehicleId": {"type": "string", "description": "The vehicle enode ID", "example": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b", "nullable": true}}, "required": ["chargeState"]}, "CreateVehicleLinkRequestDto": {"type": "object", "properties": {"vehicle": {"$ref": "#/components/schemas/CreateVehicleRequestDto"}, "intents": {"type": "array", "items": {"$ref": "#/components/schemas/VehicleIntentEntryDto"}}}, "required": ["vehicle", "intents"]}, "VehicleChargingStationsResponseDto": {"type": "object", "properties": {"data": {"description": "An array of charging station ppids that the vehicle is linked to", "example": ["PSL-123456", "PSL-654321"], "type": "array", "items": {"type": "string"}}}, "required": ["data"]}, "CommandMetadata": {"type": "object", "properties": {"ppid": {"type": "string", "description": "The charging station PPID"}}, "required": ["ppid"]}, "CommandEvent": {"type": "object", "properties": {"messageId": {"type": "string", "description": "The command message ID"}, "payload": {"type": "string", "description": "The command payload"}, "errorCode": {"type": "string", "description": "The command error code"}, "errorDescription": {"type": "string", "description": "The command error description"}, "errorDetails": {"type": "string", "description": "The command error details"}}, "required": ["messageId"]}, "CommandResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "The command ID"}, "type": {"type": "string", "description": "The command type", "enum": ["ClearChargingProfile", "SetChargingProfile"]}, "responseType": {"type": "string", "description": "The command response type", "enum": ["RES", "ERR"]}, "clientRef": {"type": "string", "description": "The client reference"}, "metadata": {"description": "The metadata", "allOf": [{"$ref": "#/components/schemas/CommandMetadata"}]}, "event": {"description": "The event", "allOf": [{"$ref": "#/components/schemas/CommandEvent"}]}, "protocol": {"type": "string", "description": "The protocol"}, "receivedAt": {"type": "string", "description": "The date and time the command response was received", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}}, "required": ["id", "type", "responseType", "clientRef", "metadata", "event", "protocol", "receivedAt"]}, "TriggerCommandRequest": {"type": "object", "properties": {"ppids": {"description": "A list of PPIDs to send the command to", "type": "array", "items": {"type": "string"}}, "command": {"type": "string", "description": "The command to send to the charging stations", "enum": ["Heartbeat"]}}, "required": ["ppids", "command"]}, "EnergyOfferStatus": {"type": "object", "properties": {"isOfferingEnergy": {"type": "boolean", "description": "Whether the charging station is offering energy", "example": false}, "until": {"type": "string", "description": "The date and time the reason for the offering energy status will change", "format": "date-time", "example": "2021-08-01T00:00:00.000Z", "nullable": true}, "reason": {"type": "string", "description": "The reason the for the offering energy", "enum": ["CHARGE_SCHEDULE", "FLEX_REQUEST", "CHARGE_NOW", "UNKNOWN"], "example": "CHARGE_NOW"}, "randomDelay": {"description": "Whether the charging station is being told to delay enforcing the value of `isOfferingEnergy`: `null` means it's not in a delay period; `true` means it is in a delay period and has been selected to delay, so should not enforce `isOfferingEnergy` yet; and `false` means it is in a delay period but has been selected not to delay.", "oneOf": [{"type": "boolean"}, {"type": "null"}], "example": true}}, "required": ["isOfferingEnergy", "until", "reason"]}, "EnergyOfferStatusResponse": {"type": "object", "properties": {"ppid": {"type": "string", "description": "The PPID of the charging station", "example": "PSL-000000"}, "evseId": {"type": "number", "description": "The ID of the evse", "example": "1"}, "energyOfferStatus": {"description": "The status of the energy offer", "allOf": [{"$ref": "#/components/schemas/EnergyOfferStatus"}]}}, "required": ["ppid", "evseId", "energyOfferStatus"]}, "ChargeScheduleDto": {"type": "object", "properties": {"startDay": {"type": "number", "enum": [1, 2, 3, 4, 5, 6, 7], "description": "The weekday the charge schedule starts on", "example": 1}, "endDay": {"type": "number", "enum": [1, 2, 3, 4, 5, 6, 7], "description": "The weekday the charge schedule ends on", "example": 7}, "startTime": {"type": "string", "description": "The time the charge schedule starts", "example": "00:00:00"}, "endTime": {"type": "string", "description": "The time the charge schedule ends", "example": "23:59:59"}, "createdAt": {"format": "date-time", "type": "string", "description": "The date the charge schedule was created", "example": "2021-01-01T00:00:00Z"}, "deletedAt": {"format": "date-time", "type": "string", "description": "The date the charge schedule was last updated", "example": "2021-01-01T00:00:00Z"}}, "required": ["startDay", "endDay", "startTime", "endTime", "createdAt", "deletedAt"]}, "PaginatedSchedulesDto": {"type": "object", "properties": {"pagination": {"type": "object", "description": "Pagination information", "properties": {"pages": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 1}, "page": {"type": "integer", "example": 1}, "itemsPerPage": {"type": "integer", "example": 1}}}, "schedules": {"description": "The charge schedules for the given ppids", "type": "array", "items": {"$ref": "#/components/schemas/ChargeScheduleDto"}}}, "required": ["pagination", "schedules"]}, "EffectiveChargeSchedulesDto": {"type": "object", "properties": {"from": {"type": "string", "description": "The start date and time of the effective charge schedules", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "to": {"type": "string", "description": "The end date and time of the effective charge schedules", "format": "date-time", "example": "2021-08-01T00:00:00.000Z"}, "ppids": {"description": "The ppids for which the effective charge schedules should be retrieved", "example": ["pp-1", "pp-2"], "type": "array", "items": {"type": "string"}}}, "required": ["from", "to", "ppids"]}, "EffectiveScheduleDto": {"type": "object", "properties": {"period": {"type": "string", "description": "the period ISO string of the effective charge schedule", "example": "2021-08-01T00:00:00.000Z/2021-08-01T01:00:00.000Z"}}, "required": ["period"]}, "EffectiveChargeSchedulesResponseDto": {"type": "object", "properties": {"ppid": {"type": "string", "description": "the ppid that the effective charge schedules are for", "example": "PSL-112334"}, "effectiveSchedules": {"description": "the effective charge schedules for the given ppid", "type": "array", "items": {"$ref": "#/components/schemas/EffectiveScheduleDto"}}}, "required": ["ppid", "effectiveSchedules"]}, "CompositeScheduleDto": {"type": "object", "properties": {"status": {"type": "string", "description": "Status of the request. The Charge Point will indicate if it was able to process the request", "enum": ["Accepted", "Rejected"], "example": "Accepted", "nullable": false}, "connectorId": {"type": "number", "description": "The charging schedule contained in this notification applies to a Connector.", "example": 1, "nullable": false}, "scheduleStart": {"type": "string", "example": "2022-01-01T00:00:00Z", "description": "Time. Periods contained in the charging profile are relative to this point in time. If status is \"Rejected\", this field may be absent.", "nullable": false}, "chargingSchedule": {"description": "Planned Composite Charging Schedule, the energy consumption over time. Always relative to ScheduleStart. If status is \"Rejected\", this field may be absent.", "nullable": false, "allOf": [{"$ref": "#/components/schemas/ChargeScheduleDto"}]}}, "required": ["status", "connectorId"]}, "ChargingSchedulePeriodDto": {"type": "object", "properties": {"startPeriod": {"type": "number", "description": "Start period", "example": 0}, "limit": {"type": "number", "description": "Limit", "example": 0}, "numberPhases": {"type": "number", "description": "Number of phases", "example": 0, "nullable": false}}, "required": ["startPeriod", "limit"]}, "ChargingScheduleDto": {"type": "object", "properties": {"duration": {"type": "number", "description": "Duration", "example": 604800, "nullable": false}, "startSchedule": {"type": "string", "description": "Start schedule", "format": "date-time", "example": "2024-11-13T08:32:36.370Z"}, "chargingRateUnit": {"type": "string", "description": "Charging rate unit", "enum": ["W", "A"], "example": "W"}, "chargingSchedulePeriod": {"description": "Charging schedule period", "type": "array", "items": {"$ref": "#/components/schemas/ChargingSchedulePeriodDto"}}, "minChargingRate": {"type": "number", "description": "Minimum charging rate", "example": 0, "nullable": false}}, "required": ["chargingRateUnit", "chargingSchedulePeriod"]}, "ValidateChargingProfileRequestDto": {"type": "object", "properties": {"transactionId": {"type": "string", "description": "Transaction id", "example": "PPO-NjM0Mzg3OQ=="}, "stackLevel": {"type": "number", "description": "The stack level", "example": 10000}, "chargingProfilePurpose": {"type": "string", "description": "The charging profile purpose", "enum": ["ChargePointMaxProfile", "TxDefaultProfile", "TxProfile"], "example": "TxDefaultProfile"}, "chargingProfileKind": {"type": "string", "description": "The charging profile kind", "enum": ["Absolute", "Recurring", "Relative"], "example": "Recurring"}, "recurrencyKind": {"type": "string", "description": "The recurrency kind", "enum": ["Daily", "Weekly"], "example": "Weekly", "nullable": false}, "validFrom": {"type": "string", "description": "The valid from date", "format": "date-time", "example": "2024-11-13T08:32:36.370Z", "nullable": false}, "validTo": {"type": "string", "description": "The valid to date", "format": "date-time", "nullable": false, "example": "2024-11-13T08:32:36.370Z"}, "chargingSchedule": {"description": "The charging schedule", "allOf": [{"$ref": "#/components/schemas/ChargingScheduleDto"}]}}, "required": ["stackLevel", "chargingProfilePurpose", "chargingProfileKind", "chargingSchedule"]}, "ValidateChargingProfileResponseDto": {"type": "object", "properties": {"valid": {"type": "boolean", "description": "The validity of the charging profile that was presented for validation", "example": "true"}, "reason": {"type": "string", "description": "The reason the charging profile is considered invalid", "example": "exceeds current chargeByTime"}}, "required": ["valid"]}, "ChargeStatisticsDto": {"type": "object", "properties": {"chargeStartTime": {"type": "string", "description": "The start time of the charge (ISO 8601)", "example": "2024-01-01T23:00:00", "format": "date-time"}, "chargeEndTime": {"type": "string", "description": "The end time of the charge (ISO 8601)", "example": "2024-01-01T23:00:00", "format": "date-time"}, "kWhFromGrid": {"type": "number", "description": "The energy (kWh) from the grid used by the charge", "example": 30.5}, "kWhFromGeneration": {"type": "number", "description": "The energy (kWh) from local-generated sources (e.g. by solar) used by the charge", "example": 10.5}}, "required": ["chargeStartTime", "chargeEndTime", "kWhFromGrid", "kWhFromGeneration"]}, "RewardPointsDto": {"type": "object", "properties": {"points": {"type": "number", "description": "The number of points the charge is worth", "example": 10.5}, "reason": {"type": "string", "description": "", "enum": ["ELIGIBLE", "INELIGIBLE_CHARGE_OVERRIDE", "INELIGIBLE_INSUFFICIENT_GRID_IMPORT", "INELIGIBLE_NOT_ENROLLED", "INELIGIBLE_SOLAR_ONLY"], "example": "ELIGIBLE"}}, "required": ["points", "reason"]}, "RewardInfoDto": {"type": "object", "properties": {"rewardableEnergyKwh": {"type": "number", "description": "The energy delivered in the session that is rewardable. If 0 is returned, the session is not eligible for rewards", "example": 20.45}, "vehicleId": {"type": "string", "description": "The vehicle charged during the session", "nullable": true, "example": "2f727f8b-7c0e-43b3-b455-a1d7f07ea036"}}, "required": ["rewardableEnergyKwh", "vehicleId"]}}}}