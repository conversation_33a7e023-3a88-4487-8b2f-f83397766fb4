/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type RewardsAPI interface {

	/*
		ConvertChargeToRewardPoints Convert a charge into reward points

		Get reward points from charge statistics. Note: this endpoint is to be replaced by POST /reward-info/:ppid and should be considered deprecated.

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid
		@return ApiConvertChargeToRewardPointsRequest
	*/
	ConvertChargeToRewardPoints(ctx context.Context, ppid string) ApiConvertChargeToRewardPointsRequest

	// ConvertChargeToRewardPointsExecute executes the request
	//  @return RewardPointsDto
	ConvertChargeToRewardPointsExecute(r ApiConvertChargeToRewardPointsRequest) (*RewardPointsDto, *http.Response, error)

	/*
		GetChargingSessionRewardInfo Get reward information for a charging session

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid
		@return ApiGetChargingSessionRewardInfoRequest
	*/
	GetChargingSessionRewardInfo(ctx context.Context, ppid string) ApiGetChargingSessionRewardInfoRequest

	// GetChargingSessionRewardInfoExecute executes the request
	//  @return RewardInfoDto
	GetChargingSessionRewardInfoExecute(r ApiGetChargingSessionRewardInfoRequest) (*RewardInfoDto, *http.Response, error)
}

// RewardsAPIService RewardsAPI service
type RewardsAPIService service

type ApiConvertChargeToRewardPointsRequest struct {
	ctx                 context.Context
	ApiService          RewardsAPI
	ppid                string
	chargeStatisticsDto *ChargeStatisticsDto
}

// Charge statistics
func (r ApiConvertChargeToRewardPointsRequest) ChargeStatisticsDto(chargeStatisticsDto ChargeStatisticsDto) ApiConvertChargeToRewardPointsRequest {
	r.chargeStatisticsDto = &chargeStatisticsDto
	return r
}

func (r ApiConvertChargeToRewardPointsRequest) Execute() (*RewardPointsDto, *http.Response, error) {
	return r.ApiService.ConvertChargeToRewardPointsExecute(r)
}

/*
ConvertChargeToRewardPoints Convert a charge into reward points

Get reward points from charge statistics. Note: this endpoint is to be replaced by POST /reward-info/:ppid and should be considered deprecated.

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid
	@return ApiConvertChargeToRewardPointsRequest
*/
func (a *RewardsAPIService) ConvertChargeToRewardPoints(ctx context.Context, ppid string) ApiConvertChargeToRewardPointsRequest {
	return ApiConvertChargeToRewardPointsRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return RewardPointsDto
func (a *RewardsAPIService) ConvertChargeToRewardPointsExecute(r ApiConvertChargeToRewardPointsRequest) (*RewardPointsDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPost
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *RewardPointsDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "RewardsAPIService.ConvertChargeToRewardPoints")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/reward-points/{ppid}"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.chargeStatisticsDto == nil {
		return localVarReturnValue, nil, reportError("chargeStatisticsDto is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.chargeStatisticsDto
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetChargingSessionRewardInfoRequest struct {
	ctx                 context.Context
	ApiService          RewardsAPI
	ppid                string
	chargeStatisticsDto *ChargeStatisticsDto
}

// Charge statistics
func (r ApiGetChargingSessionRewardInfoRequest) ChargeStatisticsDto(chargeStatisticsDto ChargeStatisticsDto) ApiGetChargingSessionRewardInfoRequest {
	r.chargeStatisticsDto = &chargeStatisticsDto
	return r
}

func (r ApiGetChargingSessionRewardInfoRequest) Execute() (*RewardInfoDto, *http.Response, error) {
	return r.ApiService.GetChargingSessionRewardInfoExecute(r)
}

/*
GetChargingSessionRewardInfo Get reward information for a charging session

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid
	@return ApiGetChargingSessionRewardInfoRequest
*/
func (a *RewardsAPIService) GetChargingSessionRewardInfo(ctx context.Context, ppid string) ApiGetChargingSessionRewardInfoRequest {
	return ApiGetChargingSessionRewardInfoRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return RewardInfoDto
func (a *RewardsAPIService) GetChargingSessionRewardInfoExecute(r ApiGetChargingSessionRewardInfoRequest) (*RewardInfoDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPost
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *RewardInfoDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "RewardsAPIService.GetChargingSessionRewardInfo")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/reward-info/{ppid}"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.chargeStatisticsDto == nil {
		return localVarReturnValue, nil, reportError("chargeStatisticsDto is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.chargeStatisticsDto
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}
