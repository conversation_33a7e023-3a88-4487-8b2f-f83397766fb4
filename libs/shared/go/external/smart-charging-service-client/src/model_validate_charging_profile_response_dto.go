/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"
)

// checks if the ValidateChargingProfileResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ValidateChargingProfileResponseDto{}

// ValidateChargingProfileResponseDto struct for ValidateChargingProfileResponseDto
type ValidateChargingProfileResponseDto struct {
	// The validity of the charging profile that was presented for validation
	Valid bool `json:"valid"`
	// The reason the charging profile is considered invalid
	Reason               *string `json:"reason,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _ValidateChargingProfileResponseDto ValidateChargingProfileResponseDto

// NewValidateChargingProfileResponseDto instantiates a new ValidateChargingProfileResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewValidateChargingProfileResponseDto(valid bool) *ValidateChargingProfileResponseDto {
	this := ValidateChargingProfileResponseDto{}
	this.Valid = valid
	return &this
}

// NewValidateChargingProfileResponseDtoWithDefaults instantiates a new ValidateChargingProfileResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewValidateChargingProfileResponseDtoWithDefaults() *ValidateChargingProfileResponseDto {
	this := ValidateChargingProfileResponseDto{}
	return &this
}

// GetValid returns the Valid field value
func (o *ValidateChargingProfileResponseDto) GetValid() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.Valid
}

// GetValidOk returns a tuple with the Valid field value
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileResponseDto) GetValidOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Valid, true
}

// SetValid sets field value
func (o *ValidateChargingProfileResponseDto) SetValid(v bool) {
	o.Valid = v
}

// GetReason returns the Reason field value if set, zero value otherwise.
func (o *ValidateChargingProfileResponseDto) GetReason() string {
	if o == nil || IsNil(o.Reason) {
		var ret string
		return ret
	}
	return *o.Reason
}

// GetReasonOk returns a tuple with the Reason field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ValidateChargingProfileResponseDto) GetReasonOk() (*string, bool) {
	if o == nil || IsNil(o.Reason) {
		return nil, false
	}
	return o.Reason, true
}

// HasReason returns a boolean if a field has been set.
func (o *ValidateChargingProfileResponseDto) HasReason() bool {
	if o != nil && !IsNil(o.Reason) {
		return true
	}

	return false
}

// SetReason gets a reference to the given string and assigns it to the Reason field.
func (o *ValidateChargingProfileResponseDto) SetReason(v string) {
	o.Reason = &v
}

func (o ValidateChargingProfileResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ValidateChargingProfileResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["valid"] = o.Valid
	if !IsNil(o.Reason) {
		toSerialize["reason"] = o.Reason
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *ValidateChargingProfileResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"valid",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varValidateChargingProfileResponseDto := _ValidateChargingProfileResponseDto{}

	err = json.Unmarshal(data, &varValidateChargingProfileResponseDto)

	if err != nil {
		return err
	}

	*o = ValidateChargingProfileResponseDto(varValidateChargingProfileResponseDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "valid")
		delete(additionalProperties, "reason")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableValidateChargingProfileResponseDto struct {
	value *ValidateChargingProfileResponseDto
	isSet bool
}

func (v NullableValidateChargingProfileResponseDto) Get() *ValidateChargingProfileResponseDto {
	return v.value
}

func (v *NullableValidateChargingProfileResponseDto) Set(val *ValidateChargingProfileResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableValidateChargingProfileResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableValidateChargingProfileResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableValidateChargingProfileResponseDto(val *ValidateChargingProfileResponseDto) *NullableValidateChargingProfileResponseDto {
	return &NullableValidateChargingProfileResponseDto{value: val, isSet: true}
}

func (v NullableValidateChargingProfileResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableValidateChargingProfileResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
