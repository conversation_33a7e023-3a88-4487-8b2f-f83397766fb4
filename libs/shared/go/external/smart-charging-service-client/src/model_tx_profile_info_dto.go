/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"
)

// checks if the TxProfileInfoDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &TxProfileInfoDto{}

// TxProfileInfoDto struct for TxProfileInfoDto
type TxProfileInfoDto struct {
	// ocpp TxProfile charging profile
	Profile              map[string]interface{} `json:"profile"`
	AdditionalProperties map[string]interface{}
}

type _TxProfileInfoDto TxProfileInfoDto

// NewTxProfileInfoDto instantiates a new TxProfileInfoDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTxProfileInfoDto(profile map[string]interface{}) *TxProfileInfoDto {
	this := TxProfileInfoDto{}
	this.Profile = profile
	return &this
}

// NewTxProfileInfoDtoWithDefaults instantiates a new TxProfileInfoDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTxProfileInfoDtoWithDefaults() *TxProfileInfoDto {
	this := TxProfileInfoDto{}
	return &this
}

// GetProfile returns the Profile field value
func (o *TxProfileInfoDto) GetProfile() map[string]interface{} {
	if o == nil {
		var ret map[string]interface{}
		return ret
	}

	return o.Profile
}

// GetProfileOk returns a tuple with the Profile field value
// and a boolean to check if the value has been set.
func (o *TxProfileInfoDto) GetProfileOk() (map[string]interface{}, bool) {
	if o == nil {
		return map[string]interface{}{}, false
	}
	return o.Profile, true
}

// SetProfile sets field value
func (o *TxProfileInfoDto) SetProfile(v map[string]interface{}) {
	o.Profile = v
}

func (o TxProfileInfoDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o TxProfileInfoDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["profile"] = o.Profile

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *TxProfileInfoDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"profile",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varTxProfileInfoDto := _TxProfileInfoDto{}

	err = json.Unmarshal(data, &varTxProfileInfoDto)

	if err != nil {
		return err
	}

	*o = TxProfileInfoDto(varTxProfileInfoDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "profile")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableTxProfileInfoDto struct {
	value *TxProfileInfoDto
	isSet bool
}

func (v NullableTxProfileInfoDto) Get() *TxProfileInfoDto {
	return v.value
}

func (v *NullableTxProfileInfoDto) Set(val *TxProfileInfoDto) {
	v.value = val
	v.isSet = true
}

func (v NullableTxProfileInfoDto) IsSet() bool {
	return v.isSet
}

func (v *NullableTxProfileInfoDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTxProfileInfoDto(val *TxProfileInfoDto) *NullableTxProfileInfoDto {
	return &NullableTxProfileInfoDto{value: val, isSet: true}
}

func (v NullableTxProfileInfoDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTxProfileInfoDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
