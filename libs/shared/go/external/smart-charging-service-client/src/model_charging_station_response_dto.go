/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"
)

// checks if the ChargingStationResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargingStationResponseDto{}

// ChargingStationResponseDto struct for ChargingStationResponseDto
type ChargingStationResponseDto struct {
	// The ID of the charging station
	Id                   NullableString `json:"id,omitempty"`
	Ppid                 string         `json:"ppid"`
	Mpan                 NullableString `json:"mpan,omitempty"`
	AddressId            NullableString `json:"addressId,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _ChargingStationResponseDto ChargingStationResponseDto

// NewChargingStationResponseDto instantiates a new ChargingStationResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargingStationResponseDto(ppid string) *ChargingStationResponseDto {
	this := ChargingStationResponseDto{}
	this.Ppid = ppid
	return &this
}

// NewChargingStationResponseDtoWithDefaults instantiates a new ChargingStationResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargingStationResponseDtoWithDefaults() *ChargingStationResponseDto {
	this := ChargingStationResponseDto{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *ChargingStationResponseDto) GetId() string {
	if o == nil || IsNil(o.Id.Get()) {
		var ret string
		return ret
	}
	return *o.Id.Get()
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargingStationResponseDto) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Id.Get(), o.Id.IsSet()
}

// HasId returns a boolean if a field has been set.
func (o *ChargingStationResponseDto) HasId() bool {
	if o != nil && o.Id.IsSet() {
		return true
	}

	return false
}

// SetId gets a reference to the given NullableString and assigns it to the Id field.
func (o *ChargingStationResponseDto) SetId(v string) {
	o.Id.Set(&v)
}

// SetIdNil sets the value for Id to be an explicit nil
func (o *ChargingStationResponseDto) SetIdNil() {
	o.Id.Set(nil)
}

// UnsetId ensures that no value is present for Id, not even an explicit nil
func (o *ChargingStationResponseDto) UnsetId() {
	o.Id.Unset()
}

// GetPpid returns the Ppid field value
func (o *ChargingStationResponseDto) GetPpid() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Ppid
}

// GetPpidOk returns a tuple with the Ppid field value
// and a boolean to check if the value has been set.
func (o *ChargingStationResponseDto) GetPpidOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Ppid, true
}

// SetPpid sets field value
func (o *ChargingStationResponseDto) SetPpid(v string) {
	o.Ppid = v
}

// GetMpan returns the Mpan field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *ChargingStationResponseDto) GetMpan() string {
	if o == nil || IsNil(o.Mpan.Get()) {
		var ret string
		return ret
	}
	return *o.Mpan.Get()
}

// GetMpanOk returns a tuple with the Mpan field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargingStationResponseDto) GetMpanOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Mpan.Get(), o.Mpan.IsSet()
}

// HasMpan returns a boolean if a field has been set.
func (o *ChargingStationResponseDto) HasMpan() bool {
	if o != nil && o.Mpan.IsSet() {
		return true
	}

	return false
}

// SetMpan gets a reference to the given NullableString and assigns it to the Mpan field.
func (o *ChargingStationResponseDto) SetMpan(v string) {
	o.Mpan.Set(&v)
}

// SetMpanNil sets the value for Mpan to be an explicit nil
func (o *ChargingStationResponseDto) SetMpanNil() {
	o.Mpan.Set(nil)
}

// UnsetMpan ensures that no value is present for Mpan, not even an explicit nil
func (o *ChargingStationResponseDto) UnsetMpan() {
	o.Mpan.Unset()
}

// GetAddressId returns the AddressId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *ChargingStationResponseDto) GetAddressId() string {
	if o == nil || IsNil(o.AddressId.Get()) {
		var ret string
		return ret
	}
	return *o.AddressId.Get()
}

// GetAddressIdOk returns a tuple with the AddressId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargingStationResponseDto) GetAddressIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.AddressId.Get(), o.AddressId.IsSet()
}

// HasAddressId returns a boolean if a field has been set.
func (o *ChargingStationResponseDto) HasAddressId() bool {
	if o != nil && o.AddressId.IsSet() {
		return true
	}

	return false
}

// SetAddressId gets a reference to the given NullableString and assigns it to the AddressId field.
func (o *ChargingStationResponseDto) SetAddressId(v string) {
	o.AddressId.Set(&v)
}

// SetAddressIdNil sets the value for AddressId to be an explicit nil
func (o *ChargingStationResponseDto) SetAddressIdNil() {
	o.AddressId.Set(nil)
}

// UnsetAddressId ensures that no value is present for AddressId, not even an explicit nil
func (o *ChargingStationResponseDto) UnsetAddressId() {
	o.AddressId.Unset()
}

func (o ChargingStationResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargingStationResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if o.Id.IsSet() {
		toSerialize["id"] = o.Id.Get()
	}
	toSerialize["ppid"] = o.Ppid
	if o.Mpan.IsSet() {
		toSerialize["mpan"] = o.Mpan.Get()
	}
	if o.AddressId.IsSet() {
		toSerialize["addressId"] = o.AddressId.Get()
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *ChargingStationResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"ppid",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargingStationResponseDto := _ChargingStationResponseDto{}

	err = json.Unmarshal(data, &varChargingStationResponseDto)

	if err != nil {
		return err
	}

	*o = ChargingStationResponseDto(varChargingStationResponseDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "id")
		delete(additionalProperties, "ppid")
		delete(additionalProperties, "mpan")
		delete(additionalProperties, "addressId")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableChargingStationResponseDto struct {
	value *ChargingStationResponseDto
	isSet bool
}

func (v NullableChargingStationResponseDto) Get() *ChargingStationResponseDto {
	return v.value
}

func (v *NullableChargingStationResponseDto) Set(val *ChargingStationResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargingStationResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargingStationResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargingStationResponseDto(val *ChargingStationResponseDto) *NullableChargingStationResponseDto {
	return &NullableChargingStationResponseDto{value: val, isSet: true}
}

func (v NullableChargingStationResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargingStationResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
