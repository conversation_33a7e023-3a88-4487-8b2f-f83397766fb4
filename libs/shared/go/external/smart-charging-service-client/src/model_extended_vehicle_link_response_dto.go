/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"
)

// checks if the ExtendedVehicleLinkResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ExtendedVehicleLinkResponseDto{}

// ExtendedVehicleLinkResponseDto struct for ExtendedVehicleLinkResponseDto
type ExtendedVehicleLinkResponseDto struct {
	Id                       string                        `json:"id"`
	IsPluggedInToThisCharger bool                          `json:"isPluggedInToThisCharger"`
	Vehicle                  VehicleLinkResponseDtoVehicle `json:"vehicle"`
	Intents                  VehicleIntentsResponseDto     `json:"intents"`
	CurrentIntent            NullableVehicleChargeInfoDto  `json:"currentIntent"`
	AdditionalProperties     map[string]interface{}
}

type _ExtendedVehicleLinkResponseDto ExtendedVehicleLinkResponseDto

// NewExtendedVehicleLinkResponseDto instantiates a new ExtendedVehicleLinkResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewExtendedVehicleLinkResponseDto(id string, isPluggedInToThisCharger bool, vehicle VehicleLinkResponseDtoVehicle, intents VehicleIntentsResponseDto, currentIntent NullableVehicleChargeInfoDto) *ExtendedVehicleLinkResponseDto {
	this := ExtendedVehicleLinkResponseDto{}
	this.Id = id
	this.IsPluggedInToThisCharger = isPluggedInToThisCharger
	this.Vehicle = vehicle
	this.Intents = intents
	this.CurrentIntent = currentIntent
	return &this
}

// NewExtendedVehicleLinkResponseDtoWithDefaults instantiates a new ExtendedVehicleLinkResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewExtendedVehicleLinkResponseDtoWithDefaults() *ExtendedVehicleLinkResponseDto {
	this := ExtendedVehicleLinkResponseDto{}
	return &this
}

// GetId returns the Id field value
func (o *ExtendedVehicleLinkResponseDto) GetId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Id
}

// GetIdOk returns a tuple with the Id field value
// and a boolean to check if the value has been set.
func (o *ExtendedVehicleLinkResponseDto) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Id, true
}

// SetId sets field value
func (o *ExtendedVehicleLinkResponseDto) SetId(v string) {
	o.Id = v
}

// GetIsPluggedInToThisCharger returns the IsPluggedInToThisCharger field value
func (o *ExtendedVehicleLinkResponseDto) GetIsPluggedInToThisCharger() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.IsPluggedInToThisCharger
}

// GetIsPluggedInToThisChargerOk returns a tuple with the IsPluggedInToThisCharger field value
// and a boolean to check if the value has been set.
func (o *ExtendedVehicleLinkResponseDto) GetIsPluggedInToThisChargerOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.IsPluggedInToThisCharger, true
}

// SetIsPluggedInToThisCharger sets field value
func (o *ExtendedVehicleLinkResponseDto) SetIsPluggedInToThisCharger(v bool) {
	o.IsPluggedInToThisCharger = v
}

// GetVehicle returns the Vehicle field value
func (o *ExtendedVehicleLinkResponseDto) GetVehicle() VehicleLinkResponseDtoVehicle {
	if o == nil {
		var ret VehicleLinkResponseDtoVehicle
		return ret
	}

	return o.Vehicle
}

// GetVehicleOk returns a tuple with the Vehicle field value
// and a boolean to check if the value has been set.
func (o *ExtendedVehicleLinkResponseDto) GetVehicleOk() (*VehicleLinkResponseDtoVehicle, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Vehicle, true
}

// SetVehicle sets field value
func (o *ExtendedVehicleLinkResponseDto) SetVehicle(v VehicleLinkResponseDtoVehicle) {
	o.Vehicle = v
}

// GetIntents returns the Intents field value
func (o *ExtendedVehicleLinkResponseDto) GetIntents() VehicleIntentsResponseDto {
	if o == nil {
		var ret VehicleIntentsResponseDto
		return ret
	}

	return o.Intents
}

// GetIntentsOk returns a tuple with the Intents field value
// and a boolean to check if the value has been set.
func (o *ExtendedVehicleLinkResponseDto) GetIntentsOk() (*VehicleIntentsResponseDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Intents, true
}

// SetIntents sets field value
func (o *ExtendedVehicleLinkResponseDto) SetIntents(v VehicleIntentsResponseDto) {
	o.Intents = v
}

// GetCurrentIntent returns the CurrentIntent field value
// If the value is explicit nil, the zero value for VehicleChargeInfoDto will be returned
func (o *ExtendedVehicleLinkResponseDto) GetCurrentIntent() VehicleChargeInfoDto {
	if o == nil || o.CurrentIntent.Get() == nil {
		var ret VehicleChargeInfoDto
		return ret
	}

	return *o.CurrentIntent.Get()
}

// GetCurrentIntentOk returns a tuple with the CurrentIntent field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ExtendedVehicleLinkResponseDto) GetCurrentIntentOk() (*VehicleChargeInfoDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.CurrentIntent.Get(), o.CurrentIntent.IsSet()
}

// SetCurrentIntent sets field value
func (o *ExtendedVehicleLinkResponseDto) SetCurrentIntent(v VehicleChargeInfoDto) {
	o.CurrentIntent.Set(&v)
}

func (o ExtendedVehicleLinkResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ExtendedVehicleLinkResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["id"] = o.Id
	toSerialize["isPluggedInToThisCharger"] = o.IsPluggedInToThisCharger
	toSerialize["vehicle"] = o.Vehicle
	toSerialize["intents"] = o.Intents
	toSerialize["currentIntent"] = o.CurrentIntent.Get()

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *ExtendedVehicleLinkResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"id",
		"isPluggedInToThisCharger",
		"vehicle",
		"intents",
		"currentIntent",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varExtendedVehicleLinkResponseDto := _ExtendedVehicleLinkResponseDto{}

	err = json.Unmarshal(data, &varExtendedVehicleLinkResponseDto)

	if err != nil {
		return err
	}

	*o = ExtendedVehicleLinkResponseDto(varExtendedVehicleLinkResponseDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "id")
		delete(additionalProperties, "isPluggedInToThisCharger")
		delete(additionalProperties, "vehicle")
		delete(additionalProperties, "intents")
		delete(additionalProperties, "currentIntent")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableExtendedVehicleLinkResponseDto struct {
	value *ExtendedVehicleLinkResponseDto
	isSet bool
}

func (v NullableExtendedVehicleLinkResponseDto) Get() *ExtendedVehicleLinkResponseDto {
	return v.value
}

func (v *NullableExtendedVehicleLinkResponseDto) Set(val *ExtendedVehicleLinkResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableExtendedVehicleLinkResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableExtendedVehicleLinkResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableExtendedVehicleLinkResponseDto(val *ExtendedVehicleLinkResponseDto) *NullableExtendedVehicleLinkResponseDto {
	return &NullableExtendedVehicleLinkResponseDto{value: val, isSet: true}
}

func (v NullableExtendedVehicleLinkResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableExtendedVehicleLinkResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
