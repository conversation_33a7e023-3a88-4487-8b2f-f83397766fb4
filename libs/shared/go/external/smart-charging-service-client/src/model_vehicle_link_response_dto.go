/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"
)

// checks if the VehicleLinkResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VehicleLinkResponseDto{}

// VehicleLinkResponseDto struct for VehicleLinkResponseDto
type VehicleLinkResponseDto struct {
	Id                       string                        `json:"id"`
	IsPluggedInToThisCharger bool                          `json:"isPluggedInToThisCharger"`
	Vehicle                  VehicleLinkResponseDtoVehicle `json:"vehicle"`
	Intents                  VehicleIntentsResponseDto     `json:"intents"`
	AdditionalProperties     map[string]interface{}
}

type _VehicleLinkResponseDto VehicleLinkResponseDto

// NewVehicleLinkResponseDto instantiates a new VehicleLinkResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVehicleLinkResponseDto(id string, isPluggedInToThisCharger bool, vehicle VehicleLinkResponseDtoVehicle, intents VehicleIntentsResponseDto) *VehicleLinkResponseDto {
	this := VehicleLinkResponseDto{}
	this.Id = id
	this.IsPluggedInToThisCharger = isPluggedInToThisCharger
	this.Vehicle = vehicle
	this.Intents = intents
	return &this
}

// NewVehicleLinkResponseDtoWithDefaults instantiates a new VehicleLinkResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVehicleLinkResponseDtoWithDefaults() *VehicleLinkResponseDto {
	this := VehicleLinkResponseDto{}
	return &this
}

// GetId returns the Id field value
func (o *VehicleLinkResponseDto) GetId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Id
}

// GetIdOk returns a tuple with the Id field value
// and a boolean to check if the value has been set.
func (o *VehicleLinkResponseDto) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Id, true
}

// SetId sets field value
func (o *VehicleLinkResponseDto) SetId(v string) {
	o.Id = v
}

// GetIsPluggedInToThisCharger returns the IsPluggedInToThisCharger field value
func (o *VehicleLinkResponseDto) GetIsPluggedInToThisCharger() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.IsPluggedInToThisCharger
}

// GetIsPluggedInToThisChargerOk returns a tuple with the IsPluggedInToThisCharger field value
// and a boolean to check if the value has been set.
func (o *VehicleLinkResponseDto) GetIsPluggedInToThisChargerOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.IsPluggedInToThisCharger, true
}

// SetIsPluggedInToThisCharger sets field value
func (o *VehicleLinkResponseDto) SetIsPluggedInToThisCharger(v bool) {
	o.IsPluggedInToThisCharger = v
}

// GetVehicle returns the Vehicle field value
func (o *VehicleLinkResponseDto) GetVehicle() VehicleLinkResponseDtoVehicle {
	if o == nil {
		var ret VehicleLinkResponseDtoVehicle
		return ret
	}

	return o.Vehicle
}

// GetVehicleOk returns a tuple with the Vehicle field value
// and a boolean to check if the value has been set.
func (o *VehicleLinkResponseDto) GetVehicleOk() (*VehicleLinkResponseDtoVehicle, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Vehicle, true
}

// SetVehicle sets field value
func (o *VehicleLinkResponseDto) SetVehicle(v VehicleLinkResponseDtoVehicle) {
	o.Vehicle = v
}

// GetIntents returns the Intents field value
func (o *VehicleLinkResponseDto) GetIntents() VehicleIntentsResponseDto {
	if o == nil {
		var ret VehicleIntentsResponseDto
		return ret
	}

	return o.Intents
}

// GetIntentsOk returns a tuple with the Intents field value
// and a boolean to check if the value has been set.
func (o *VehicleLinkResponseDto) GetIntentsOk() (*VehicleIntentsResponseDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Intents, true
}

// SetIntents sets field value
func (o *VehicleLinkResponseDto) SetIntents(v VehicleIntentsResponseDto) {
	o.Intents = v
}

func (o VehicleLinkResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VehicleLinkResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["id"] = o.Id
	toSerialize["isPluggedInToThisCharger"] = o.IsPluggedInToThisCharger
	toSerialize["vehicle"] = o.Vehicle
	toSerialize["intents"] = o.Intents

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *VehicleLinkResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"id",
		"isPluggedInToThisCharger",
		"vehicle",
		"intents",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varVehicleLinkResponseDto := _VehicleLinkResponseDto{}

	err = json.Unmarshal(data, &varVehicleLinkResponseDto)

	if err != nil {
		return err
	}

	*o = VehicleLinkResponseDto(varVehicleLinkResponseDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "id")
		delete(additionalProperties, "isPluggedInToThisCharger")
		delete(additionalProperties, "vehicle")
		delete(additionalProperties, "intents")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableVehicleLinkResponseDto struct {
	value *VehicleLinkResponseDto
	isSet bool
}

func (v NullableVehicleLinkResponseDto) Get() *VehicleLinkResponseDto {
	return v.value
}

func (v *NullableVehicleLinkResponseDto) Set(val *VehicleLinkResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableVehicleLinkResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableVehicleLinkResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVehicleLinkResponseDto(val *VehicleLinkResponseDto) *NullableVehicleLinkResponseDto {
	return &NullableVehicleLinkResponseDto{value: val, isSet: true}
}

func (v NullableVehicleLinkResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVehicleLinkResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
