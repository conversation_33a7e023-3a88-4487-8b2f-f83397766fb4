/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"
)

// checks if the Data type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &Data{}

// Data struct for Data
type Data struct {
	Uid                  *string              `json:"uid,omitempty"`
	StartDay             float32              `json:"start_day"`
	StartTime            string               `json:"start_time"`
	EndDay               float32              `json:"end_day"`
	EndTime              string               `json:"end_time"`
	Status               ChargeScheduleStatus `json:"status"`
	AdditionalProperties map[string]interface{}
}

type _Data Data

// NewData instantiates a new Data object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewData(startDay float32, startTime string, endDay float32, endTime string, status ChargeScheduleStatus) *Data {
	this := Data{}
	this.StartDay = startDay
	this.StartTime = startTime
	this.EndDay = endDay
	this.EndTime = endTime
	this.Status = status
	return &this
}

// NewDataWithDefaults instantiates a new Data object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDataWithDefaults() *Data {
	this := Data{}
	return &this
}

// GetUid returns the Uid field value if set, zero value otherwise.
func (o *Data) GetUid() string {
	if o == nil || IsNil(o.Uid) {
		var ret string
		return ret
	}
	return *o.Uid
}

// GetUidOk returns a tuple with the Uid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Data) GetUidOk() (*string, bool) {
	if o == nil || IsNil(o.Uid) {
		return nil, false
	}
	return o.Uid, true
}

// HasUid returns a boolean if a field has been set.
func (o *Data) HasUid() bool {
	if o != nil && !IsNil(o.Uid) {
		return true
	}

	return false
}

// SetUid gets a reference to the given string and assigns it to the Uid field.
func (o *Data) SetUid(v string) {
	o.Uid = &v
}

// GetStartDay returns the StartDay field value
func (o *Data) GetStartDay() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.StartDay
}

// GetStartDayOk returns a tuple with the StartDay field value
// and a boolean to check if the value has been set.
func (o *Data) GetStartDayOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.StartDay, true
}

// SetStartDay sets field value
func (o *Data) SetStartDay(v float32) {
	o.StartDay = v
}

// GetStartTime returns the StartTime field value
func (o *Data) GetStartTime() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.StartTime
}

// GetStartTimeOk returns a tuple with the StartTime field value
// and a boolean to check if the value has been set.
func (o *Data) GetStartTimeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.StartTime, true
}

// SetStartTime sets field value
func (o *Data) SetStartTime(v string) {
	o.StartTime = v
}

// GetEndDay returns the EndDay field value
func (o *Data) GetEndDay() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.EndDay
}

// GetEndDayOk returns a tuple with the EndDay field value
// and a boolean to check if the value has been set.
func (o *Data) GetEndDayOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.EndDay, true
}

// SetEndDay sets field value
func (o *Data) SetEndDay(v float32) {
	o.EndDay = v
}

// GetEndTime returns the EndTime field value
func (o *Data) GetEndTime() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.EndTime
}

// GetEndTimeOk returns a tuple with the EndTime field value
// and a boolean to check if the value has been set.
func (o *Data) GetEndTimeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.EndTime, true
}

// SetEndTime sets field value
func (o *Data) SetEndTime(v string) {
	o.EndTime = v
}

// GetStatus returns the Status field value
func (o *Data) GetStatus() ChargeScheduleStatus {
	if o == nil {
		var ret ChargeScheduleStatus
		return ret
	}

	return o.Status
}

// GetStatusOk returns a tuple with the Status field value
// and a boolean to check if the value has been set.
func (o *Data) GetStatusOk() (*ChargeScheduleStatus, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Status, true
}

// SetStatus sets field value
func (o *Data) SetStatus(v ChargeScheduleStatus) {
	o.Status = v
}

func (o Data) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o Data) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Uid) {
		toSerialize["uid"] = o.Uid
	}
	toSerialize["start_day"] = o.StartDay
	toSerialize["start_time"] = o.StartTime
	toSerialize["end_day"] = o.EndDay
	toSerialize["end_time"] = o.EndTime
	toSerialize["status"] = o.Status

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *Data) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"start_day",
		"start_time",
		"end_day",
		"end_time",
		"status",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varData := _Data{}

	err = json.Unmarshal(data, &varData)

	if err != nil {
		return err
	}

	*o = Data(varData)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "uid")
		delete(additionalProperties, "start_day")
		delete(additionalProperties, "start_time")
		delete(additionalProperties, "end_day")
		delete(additionalProperties, "end_time")
		delete(additionalProperties, "status")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableData struct {
	value *Data
	isSet bool
}

func (v NullableData) Get() *Data {
	return v.value
}

func (v *NullableData) Set(val *Data) {
	v.value = val
	v.isSet = true
}

func (v NullableData) IsSet() bool {
	return v.isSet
}

func (v *NullableData) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableData(val *Data) *NullableData {
	return &NullableData{value: val, isSet: true}
}

func (v NullableData) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableData) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
