/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"
	"time"
)

// checks if the ChargeDetailDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargeDetailDto{}

// ChargeDetailDto struct for ChargeDetailDto
type ChargeDetailDto struct {
	ExpectedChargeByTargetKWh     NullableFloat32 `json:"expectedChargeByTarget_kWh"`
	ExpectedChargeByTargetPercent NullableFloat32 `json:"expectedChargeByTargetPercent"`
	FullChargeByTime              NullableTime    `json:"fullChargeByTime"`
	AdditionalProperties          map[string]interface{}
}

type _ChargeDetailDto ChargeDetailDto

// NewChargeDetailDto instantiates a new ChargeDetailDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargeDetailDto(expectedChargeByTargetKWh NullableFloat32, expectedChargeByTargetPercent NullableFloat32, fullChargeByTime NullableTime) *ChargeDetailDto {
	this := ChargeDetailDto{}
	this.ExpectedChargeByTargetKWh = expectedChargeByTargetKWh
	this.ExpectedChargeByTargetPercent = expectedChargeByTargetPercent
	this.FullChargeByTime = fullChargeByTime
	return &this
}

// NewChargeDetailDtoWithDefaults instantiates a new ChargeDetailDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargeDetailDtoWithDefaults() *ChargeDetailDto {
	this := ChargeDetailDto{}
	return &this
}

// GetExpectedChargeByTargetKWh returns the ExpectedChargeByTargetKWh field value
// If the value is explicit nil, the zero value for float32 will be returned
func (o *ChargeDetailDto) GetExpectedChargeByTargetKWh() float32 {
	if o == nil || o.ExpectedChargeByTargetKWh.Get() == nil {
		var ret float32
		return ret
	}

	return *o.ExpectedChargeByTargetKWh.Get()
}

// GetExpectedChargeByTargetKWhOk returns a tuple with the ExpectedChargeByTargetKWh field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargeDetailDto) GetExpectedChargeByTargetKWhOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return o.ExpectedChargeByTargetKWh.Get(), o.ExpectedChargeByTargetKWh.IsSet()
}

// SetExpectedChargeByTargetKWh sets field value
func (o *ChargeDetailDto) SetExpectedChargeByTargetKWh(v float32) {
	o.ExpectedChargeByTargetKWh.Set(&v)
}

// GetExpectedChargeByTargetPercent returns the ExpectedChargeByTargetPercent field value
// If the value is explicit nil, the zero value for float32 will be returned
func (o *ChargeDetailDto) GetExpectedChargeByTargetPercent() float32 {
	if o == nil || o.ExpectedChargeByTargetPercent.Get() == nil {
		var ret float32
		return ret
	}

	return *o.ExpectedChargeByTargetPercent.Get()
}

// GetExpectedChargeByTargetPercentOk returns a tuple with the ExpectedChargeByTargetPercent field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargeDetailDto) GetExpectedChargeByTargetPercentOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return o.ExpectedChargeByTargetPercent.Get(), o.ExpectedChargeByTargetPercent.IsSet()
}

// SetExpectedChargeByTargetPercent sets field value
func (o *ChargeDetailDto) SetExpectedChargeByTargetPercent(v float32) {
	o.ExpectedChargeByTargetPercent.Set(&v)
}

// GetFullChargeByTime returns the FullChargeByTime field value
// If the value is explicit nil, the zero value for time.Time will be returned
func (o *ChargeDetailDto) GetFullChargeByTime() time.Time {
	if o == nil || o.FullChargeByTime.Get() == nil {
		var ret time.Time
		return ret
	}

	return *o.FullChargeByTime.Get()
}

// GetFullChargeByTimeOk returns a tuple with the FullChargeByTime field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ChargeDetailDto) GetFullChargeByTimeOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return o.FullChargeByTime.Get(), o.FullChargeByTime.IsSet()
}

// SetFullChargeByTime sets field value
func (o *ChargeDetailDto) SetFullChargeByTime(v time.Time) {
	o.FullChargeByTime.Set(&v)
}

func (o ChargeDetailDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargeDetailDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["expectedChargeByTarget_kWh"] = o.ExpectedChargeByTargetKWh.Get()
	toSerialize["expectedChargeByTargetPercent"] = o.ExpectedChargeByTargetPercent.Get()
	toSerialize["fullChargeByTime"] = o.FullChargeByTime.Get()

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *ChargeDetailDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"expectedChargeByTarget_kWh",
		"expectedChargeByTargetPercent",
		"fullChargeByTime",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargeDetailDto := _ChargeDetailDto{}

	err = json.Unmarshal(data, &varChargeDetailDto)

	if err != nil {
		return err
	}

	*o = ChargeDetailDto(varChargeDetailDto)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "expectedChargeByTarget_kWh")
		delete(additionalProperties, "expectedChargeByTargetPercent")
		delete(additionalProperties, "fullChargeByTime")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableChargeDetailDto struct {
	value *ChargeDetailDto
	isSet bool
}

func (v NullableChargeDetailDto) Get() *ChargeDetailDto {
	return v.value
}

func (v *NullableChargeDetailDto) Set(val *ChargeDetailDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargeDetailDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargeDetailDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargeDetailDto(val *ChargeDetailDto) *NullableChargeDetailDto {
	return &NullableChargeDetailDto{value: val, isSet: true}
}

func (v NullableChargeDetailDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargeDetailDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
