package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	eventstore "experience/libs/shared/go/event-store"
)

type SyncEventHandler interface {
	ErrorArbiter

	Execute(ctx context.Context, event eventstore.Event, eventBody json.RawMessage) error
}

type AsyncEvent<PERSON>andler interface {
	Execute(ctx context.Context, tx *sql.Tx, event eventstore.Event, currentTransactionID uint64) error
}

type CommandHandler interface {
	Execute(ctx context.Context, command eventstore.Command) error
}

type ErrorArbiter interface {
	ErrorAllowed(err error) bool
}
