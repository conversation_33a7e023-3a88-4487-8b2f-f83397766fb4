package handlers_test

import (
	"errors"
	"experience/libs/shared/go/event-store/handlers"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestForbidErrors_ErrorAllowed(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want bool
	}{
		{
			name: "allow nil error",
			err:  nil,
			want: true,
		},
		{
			name: "forbid non nil error",
			err:  errors.New("some error"),
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := handlers.ForbidErrors{}
			require.Equal(t, tt.want, s.ErrorAllowed(tt.err))
		})
	}
}
