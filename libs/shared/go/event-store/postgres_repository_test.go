package eventstore_test

import (
	"context"
	"database/sql"
	"encoding/json"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/db/postgres/test"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/sqlc"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/service/test/assertions"
	"fmt"
	"log"
	"sort"
	"sync"
	"testing"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/google/uuid"

	"github.com/jmoiron/sqlx"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type fixtureData struct {
	aggregateID uuid.UUID
	record      eventstore.Record
}

var (
	aggregateID1 = uuid.New()
	aggregateID2 = uuid.New()
	aggregateID3 = uuid.New()
	aggregateID4 = uuid.New()
	aggregateID5 = uuid.New()
	aggregateID6 = uuid.New()
)

type EventStoreTestSuite struct {
	suite.Suite
	testDB    *test.Database
	underTest eventstore.Store
	db        *sql.DB
	sqlc      *sqlc.Queries
}

func TestEventStoreTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping EventStoreTestSuite integration test")
	}
	suite.Run(t, new(EventStoreTestSuite))
}

func (s *EventStoreTestSuite) SetupSuite() {
	t := s.T()
	s.testDB = test.NewDatabase(t, test.WithUser("xdp_events_queue_worker"))
	passwordConfig := s.testDB.PasswordConfig(t)

	_, err := migrate.MigrateUp(
		"file://../../../data-platform/golang-migrate/migration",
		passwordConfig,
		nil,
		true,
	)
	require.NoError(t, err)

	s.db, err = postgres.NewPasswordDB(passwordConfig, true)
	require.NoError(t, err)

	s.sqlc = sqlc.New(s.db)
	newDb := sqlx.NewDb(s.db, "postgres")
	s.underTest = eventstore.NewPGEventStore(newDb)
}

func (s *EventStoreTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *EventStoreTestSuite) AfterTest(_, _ string) {
	prepare, _ := s.db.Prepare("TRUNCATE TABLE event_store.events")
	_, _ = prepare.Exec()
}

func (s *EventStoreTestSuite) TestPostgresSave() {
	storeData := storeDataFixture()

	tests := []struct {
		name            string
		aggregateID     uuid.UUID
		expectedRecords []eventstore.Record
	}{
		{
			name:        "can save 1 record aggregate",
			aggregateID: uuid.New(),
			expectedRecords: []eventstore.Record{
				{
					Version: 1,
					Data:    []byte(`{"test": "value"}`),
				},
			},
		},
		{
			name:        "can save 2 records as different versions on same aggregate",
			aggregateID: uuid.New(),
			expectedRecords: []eventstore.Record{
				{
					Version: 1,
					Data:    []byte(`{"test": "value"}`),
				},
				{
					Version: 2,
					Data:    []byte(`{"test": "value2"}`),
				},
			},
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			ctx := s.T().Context()
			s.seed(ctx, storeData)
			t := s.T()

			for _, record := range tt.expectedRecords {
				tx, _ := s.db.Begin()

				nextVersion, err := s.underTest.GetNextAggregateVersion(ctx, tt.aggregateID)
				require.NoError(t, err)

				err = s.underTest.Save(ctx, tt.aggregateID, nextVersion, record.Data)
				require.NoError(t, err)

				err = tx.Commit()
				require.NoError(t, err)
			}

			events, err := s.sqlc.LoadEventsByAggregateID(ctx, tt.aggregateID)
			require.NoError(t, err)

			for i, event := range events {
				version, conversionErr := numbers.Convert[int, int32](i + 1)
				require.NoError(t, conversionErr)
				require.Equal(t, version, event.Version)
				require.Equal(t, tt.expectedRecords[i].Data, event.Data)
			}
		})
	}
}

func (s *EventStoreTestSuite) TestConcurrentSavesResultInConstrainViolation() {
	aggregateID := uuid.New()

	errChan := make(chan error, 3)
	wg := sync.WaitGroup{}
	t := s.T()
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			tx, _ := s.db.Begin()
			saveErr := s.underTest.Save(s.T().Context(), aggregateID, 1, json.RawMessage(`{"type": "%d"}`))
			if saveErr != nil {
				_ = tx.Rollback()
				errChan <- saveErr
				return
			}

			commitErr := tx.Commit()
			require.NoError(t, commitErr)
		}()
	}
	wg.Wait()
	close(errChan)

	actual, err := s.underTest.Load(s.T().Context(), aggregateID)

	errors := make([]error, 0)
	for err2 := range errChan {
		errors = append(errors, err2)
	}

	require.NoError(t, err)
	require.Len(t, actual, 1)
	require.Len(t, errors, 2)
	require.ErrorIs(t, errors[0], eventstore.ErrEventIsOutOfDate)
	require.ErrorIs(t, errors[1], eventstore.ErrEventIsOutOfDate)
}

func (s *EventStoreTestSuite) seed(ctx context.Context, storeData []fixtureData) {
	for _, sd := range storeData {
		tx, _ := s.db.Begin()
		version, _ := s.underTest.GetNextAggregateVersion(ctx, sd.aggregateID)
		err := s.underTest.Save(ctx, sd.aggregateID, version, sd.record.Data)
		require.NoError(s.T(), err)

		_ = tx.Commit()
		require.NoError(s.T(), err)
	}
}

func (s *EventStoreTestSuite) TestPostgresLoad() {
	storeData := storeDataFixture()

	tests := []struct {
		name            string
		aggregateID     uuid.UUID
		expectedRecords []eventstore.Record
	}{
		{
			name:        "loads saved records for stored aggregate ID 1",
			aggregateID: aggregateID1,
			expectedRecords: []eventstore.Record{
				storeData[0].record,
				storeData[5].record,
			},
		},
		{
			name:        "loads saved records for stored aggregate ID 2",
			aggregateID: aggregateID2,
			expectedRecords: []eventstore.Record{
				storeData[1].record,
			},
		},
		{
			name:        "loads saved records for stored aggregate ID 3",
			aggregateID: aggregateID3,
			expectedRecords: []eventstore.Record{
				storeData[2].record,
				storeData[3].record,
				storeData[4].record,
			},
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			ctx := s.T().Context()
			s.seed(ctx, storeData)

			actualEvents, err := s.underTest.Load(ctx, tt.aggregateID)

			require.NoError(t, err)
			require.Len(t, actualEvents, len(tt.expectedRecords))
			for i := 0; i < len(actualEvents)-1; i++ {
				assertions.AssertEqualsExclude(t, tt.expectedRecords[i], actualEvents[i], []string{"TransactionID", "Version"})
			}
			s.AfterTest("", "")
		})
	}
}

func (s *EventStoreTestSuite) TestPostgresLoadTo() {
	storeData := storeDataFixture()

	tests := []struct {
		name                string
		aggregateID         uuid.UUID
		numOfEventsToLoadTo int
		expectedRecords     []eventstore.Record
	}{
		{
			name:                "loads up to the first saved records for stored aggregate ID 1",
			aggregateID:         aggregateID1,
			numOfEventsToLoadTo: 1,
			expectedRecords: []eventstore.Record{
				storeData[0].record,
			},
		},
		{
			name:                "loads up to the second (all) saved records for stored aggregate ID 1",
			aggregateID:         aggregateID1,
			numOfEventsToLoadTo: 2,
			expectedRecords: []eventstore.Record{
				storeData[0].record,
				storeData[5].record,
			},
		},
		{
			name:                "loads saved records up to second out of three total for stored aggregate ID 3",
			aggregateID:         aggregateID3,
			numOfEventsToLoadTo: 2,
			expectedRecords: []eventstore.Record{
				storeData[2].record,
				storeData[3].record,
			},
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			ctx := s.T().Context()
			s.seed(ctx, storeData)

			allEvents, err := s.underTest.Load(ctx, tt.aggregateID)
			require.NoError(t, err)
			transactionIDToLoadTo := allEvents[tt.numOfEventsToLoadTo-1].TransactionID

			require.NoError(t, err)

			actualEvents, err := s.underTest.LoadTo(ctx, tt.aggregateID, transactionIDToLoadTo)

			require.NoError(t, err)
			require.Len(t, actualEvents, tt.numOfEventsToLoadTo)
			for i := 0; i < len(actualEvents)-1; i++ {
				assertions.AssertEqualsExclude(t, tt.expectedRecords[i], actualEvents[i], []string{"TransactionID", "Version"})
			}
			s.AfterTest("", "")
		})
	}
}

func (s *EventStoreTestSuite) TestPostgresLoadWithTx() {
	storeData := storeDataFixture()
	tests := []struct {
		name            string
		aggregateID     uuid.UUID
		expectedRecords []eventstore.Record
	}{
		{
			name:        "loads saved records for stored aggregate ID 4",
			aggregateID: aggregateID4,
			expectedRecords: []eventstore.Record{
				storeData[6].record,
				storeData[11].record,
			},
		},
		{
			name:        "loads saved records for stored aggregate ID 5",
			aggregateID: aggregateID5,
			expectedRecords: []eventstore.Record{
				storeData[7].record,
			},
		},
		{
			name:        "loads saved records for stored aggregate ID 6",
			aggregateID: aggregateID6,
			expectedRecords: []eventstore.Record{
				storeData[8].record,
				storeData[9].record,
				storeData[10].record,
			},
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			ctx := s.T().Context()
			s.seed(ctx, storeData)

			tx, _ := s.db.Begin()

			actualEvents, err := s.underTest.LoadWithTx(ctx, tx, tt.aggregateID)
			require.NoError(t, err)

			require.Len(t, actualEvents, len(tt.expectedRecords))
			for i := 0; i < len(actualEvents)-1; i++ {
				assertions.AssertEqualsExclude(t, tt.expectedRecords[i], actualEvents[i], []string{"TransactionID", "Version"})
			}
			err = tx.Rollback()
			require.NoError(t, err)
			s.AfterTest("", "")
		})
	}
}

func storeDataFixture() []fixtureData {
	return []fixtureData{
		{
			aggregateID: aggregateID1,
			record: eventstore.Record{
				Version: 1,
				Data:    json.RawMessage(`{"type": "one"}`),
			},
		},
		{
			aggregateID: aggregateID2,
			record: eventstore.Record{
				Version: 1,
				Data:    json.RawMessage(`{"type": "one"}`),
			},
		},
		{
			aggregateID: aggregateID3,
			record: eventstore.Record{
				Version: 1,
				Data:    json.RawMessage(`{"type": "one"}`),
			},
		},
		{
			aggregateID: aggregateID3,
			record: eventstore.Record{
				Version: 2,
				Data:    json.RawMessage(`{"type": "two"}`),
			},
		},
		{
			aggregateID: aggregateID3,
			record: eventstore.Record{
				Version: 3,
				Data:    json.RawMessage(`{"type": "three"}`),
			},
		},
		{
			aggregateID: aggregateID1,
			record: eventstore.Record{
				Version: 2,
				Data:    json.RawMessage(`{"type": "three"}`),
			},
		},
		{
			aggregateID: aggregateID4,
			record: eventstore.Record{
				Version: 1,
				Data:    json.RawMessage(`{"type": "one"}`),
			},
		},
		{
			aggregateID: aggregateID5,
			record: eventstore.Record{
				Version: 1,
				Data:    json.RawMessage(`{"type": "one"}`),
			},
		},
		{
			aggregateID: aggregateID6,
			record: eventstore.Record{
				Version: 1,
				Data:    json.RawMessage(`{"type": "one"}`),
			},
		},
		{
			aggregateID: aggregateID6,
			record: eventstore.Record{
				Version: 2,
				Data:    json.RawMessage(`{"type": "two"}`),
			},
		},
		{
			aggregateID: aggregateID6,
			record: eventstore.Record{
				Version: 3,
				Data:    json.RawMessage(`{"type": "three"}`),
			},
		},
		{
			aggregateID: aggregateID4,
			record: eventstore.Record{
				Version: 2,
				Data:    json.RawMessage(`{"type": "three"}`),
			},
		},
	}
}

func (s *EventStoreTestSuite) TestPostgresLoadInOrder() {
	aggregateID := uuid.New()
	intRange := gofakeit.IntRange(1, 1000)
	want := make([]eventstore.Record, 0, intRange)

	for i := 0; i < intRange; i++ {
		version := intRange - i
		txID, err := numbers.Convert[int, uint64](version)
		require.NoError(s.T(), err)
		versionInt32, err := numbers.Convert[int, int32](version)
		require.NoError(s.T(), err)
		record := aRecord(txID, versionInt32)
		s.insertRecord(s.T(), version, aggregateID, record)
		want = append(want, record)
	}

	got, err := s.underTest.Load(s.T().Context(), aggregateID)
	require.NoError(s.T(), err)

	sort.Slice(want, sortRecords(want))
	require.Equal(s.T(), want, got)
}

func (s *EventStoreTestSuite) TestPostgresSaveMaxXID8ValueUInt64() {
	t := s.T()
	aggregateID := uuid.New()
	s.insertRecordWithMaxXID8(t, "18446744073709551615", aggregateID, eventstore.Record{Version: 1, Data: json.RawMessage(`{"type": "maxVal"}`)})

	got, err := s.underTest.Load(s.T().Context(), aggregateID)

	require.NoError(t, err)

	require.Equal(t, got[0].TransactionID, uint64(18446744073709551615))
}

func (s *EventStoreTestSuite) TestPostgresSaveMaxXID8ValueUInt32() {
	t := s.T()
	aggregateID := uuid.New()
	s.insertRecordWithMaxXID8(t, "4294967295", aggregateID, eventstore.Record{Version: 1, Data: json.RawMessage(`{"type": "maxVal"}`)})

	got, err := s.underTest.Load(s.T().Context(), aggregateID)

	require.NoError(t, err)

	require.Equal(t, got[0].TransactionID, uint64(4294967295))
}

func (s *EventStoreTestSuite) insertRecordWithMaxXID8(t *testing.T, transactionID string, aggregateID uuid.UUID, record eventstore.Record) {
	t.Helper()

	statement, err := s.db.Prepare("INSERT INTO event_store.events (transaction_id, aggregate_id, version, data) VALUES ($1::xid8, $2, $3, $4)")
	require.NoError(t, err)

	_, err = statement.Exec(transactionID, aggregateID, record.Version, record.Data)
	require.NoError(t, err)
}

func (s *EventStoreTestSuite) insertRecord(t *testing.T, transactionID int, aggregateID uuid.UUID, record eventstore.Record) {
	t.Helper()
	statement, err := s.db.Prepare("INSERT INTO event_store.events (transaction_id, aggregate_id, version, data) VALUES ($1, $2, $3, $4)")
	require.NoError(t, err)
	_, err = statement.Exec(transactionID, aggregateID, record.Version, record.Data)
	require.NoError(t, err)
}

func aRecord(id uint64, version int32) eventstore.Record {
	return eventstore.Record{
		TransactionID: id,
		Version:       version,
		Data:          json.RawMessage(fmt.Sprintf(`{"type": "%d"}`, version)),
	}
}

func sortRecords(s []eventstore.Record) func(int, int) bool {
	return func(i int, j int) bool {
		return s[i].TransactionID < s[j].TransactionID
	}
}
