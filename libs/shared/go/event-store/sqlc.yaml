version: 2
sql:
  - schema: '../../../../libs/data-platform/golang-migrate/migration/'
    queries: 'queries/'
    engine: 'postgresql'
    strict_order_by: false
    gen:
      go:
        package: 'sqlc'
        out: './sqlc'
        emit_json_tags: true
        emit_prepared_queries: false
        emit_interface: false
        emit_exact_table_names: false
        overrides:
          - db_type: 'xid8'
            go_type:
              type: uint64
