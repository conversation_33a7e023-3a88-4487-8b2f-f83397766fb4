package aws_test

import (
	"context"
	"experience/libs/shared/go/aws"
	"experience/libs/shared/go/aws/test"
	"log"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestConfig(t *testing.T) {
	t.Run("IsLocal defaults to false", func(t *testing.T) {
		t.Helper()

		require.Equal(t, false, aws.Config{}.IsLocal())
	})
	t.Run("environment defaults to empty string", func(t *testing.T) {
		t.Helper()

		require.Equal(t, "", aws.Config{}.Environment)
	})
	t.Run("environment is local", func(t *testing.T) {
		t.Helper()

		require.True(t, aws.Config{
			Environment: "local",
		}.IsLocal())
	})
	t.Run("environment is not local", func(t *testing.T) {
		t.Helper()

		require.False(t, aws.Config{
			Environment: "dev",
		}.IsLocal())
	})
	t.Run("environment is not prod", func(t *testing.T) {
		t.<PERSON><PERSON>()

		require.False(t, aws.Config{
			Environment: "dev",
		}.IsProd())
	})
	t.Run("environment is prod", func(t *testing.T) {
		t.Helper()

		require.True(t, aws.Config{
			Environment: "prod",
		}.IsProd())
	})
}

func TestLoadConfig(t *testing.T) {
	t.Run("loaded configuration values as expected", func(t *testing.T) {
		t.Helper()

		lsConfig, err := test.NewLocalStackConfig(
			"test/config/localstack/",
			"Secret created: config-secret",
		)
		if err != nil {
			log.Fatalf("unable to create localstack config: %v", err)
		}
		ls := test.NewLocalStack(t, lsConfig)
		defer ls.Close(t)

		expected := ConfigSecret{
			VerySecret:      true,
			FavouriteColour: "green",
			InnerSecret: InnerSecret{
				Type: "sauce",
			},
		}
		actual, _ := aws.LoadConfig(context.Background(), "load-config-test", "config-secret", ConfigSecret{})

		require.Equal(t, expected, actual)
	})
}

type ConfigSecret struct {
	VerySecret      bool   `json:"very_secret"`
	FavouriteColour string `json:"favourite_colour"`
	InnerSecret     `json:"inner_secret"`
}

type InnerSecret struct {
	Type string `json:"type"`
}
