package eventbridge_test

import (
	"encoding/json"
	"experience/libs/shared/go/aws/test"
	"experience/libs/shared/go/eventbridge"
	"experience/libs/shared/go/sqs"

	"fmt"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	sqsSDK "github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func TestEventBridge(t *testing.T) {
	config, err := test.NewLocalStackConfig(
		"./test/config/localstack/",
		"Event rule target created for: test-events")
	require.NoError(t, err)

	localstack := test.NewLocalStack(t, config)
	defer localstack.Close(t)

	input := testInput{
		ID:        uuid.New(),
		Num:       gofakeit.Int(),
		Name:      gofakeit.Name(),
		CreatedAt: gofakeit.Date(),
	}
	marshal, err := json.Marshal(input)
	require.NoError(t, err)

	ebClient, _ := eventbridge.NewClient(t.Context(), eventbridge.WithEventBusARN("my-event-bus"), eventbridge.WithSource("test-file"))

	err = ebClient.PutEvent(t.Context(), "TestEvent", marshal)
	require.NoError(t, err)

	sqsClient, err := sqs.NewSQSClient(t.Context())
	require.NoError(t, err)

	message, err := sqsClient.ReceiveMessage(t.Context(), &sqsSDK.ReceiveMessageInput{
		QueueUrl: aws.String(fmt.Sprintf("http://localhost:%d/000000000000/test-queue", localstack.Port(t))),
	})
	require.NoError(t, err)
	require.Len(t, message.Messages, 1)

	var event eventBridgeEvent[testInput]
	err = json.Unmarshal([]byte(*message.Messages[0].Body), &event)
	require.NoError(t, err)
	require.Equal(t, input, event.Detail)
}

type testInput struct {
	ID        uuid.UUID `json:"id"`
	Num       int       `json:"num"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"createdAt"`
}

type eventBridgeEvent[T any] struct {
	DetailType string `json:"detail-type"`
	Source     string `json:"source"`
	Detail     T      `json:"detail"`
}
