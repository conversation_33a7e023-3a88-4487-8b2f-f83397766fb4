package postgres

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"errors"
	"experience/libs/shared/go/db"
	"fmt"
	"time"

	"github.com/aws/aws-xray-sdk-go/v2/xray"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/sts"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/rds/auth"
	"github.com/lib/pq"

	"github.com/jmoiron/sqlx"
)

const (
	driverName    = "postgres"
	pgxDriverName = "pgx"
)

type ReadWriteDB struct {
	ReadDB  *sqlx.DB
	WriteDB *sqlx.DB
}

func NewReadWriteDB(c *db.ServiceDatasource, local bool, opts ...DriverOption) *ReadWriteDB {
	return &ReadWriteDB{
		ReadDB:  NewIAMDB(&c.ReadConfig.IAMConfig, local, opts...),
		WriteDB: NewIAMDB(&c.WriteConfig.IAMConfig, local, opts...),
	}
}

type DriverOption func(d *ConfigurationAwareDriver)

func WithXray() DriverOption {
	return func(d *ConfigurationAwareDriver) {
		d.XrayEnabled = true
	}
}

// NewIAMDB returns a database handle representing a pool of zero or more underlying connections that authenticates
// via IAM.
//
// Max connection lifetime set on connection pool as IAM auth tokens expire after 15 mins.
//
// * c used to configure database connection
// * local  -  true if application running locally, false if running on AWS
//
// for the case where local is true the connection will be made without a password value on the assumption any local
// postgres container does not require connections to be authenticated
func NewIAMDB(c *db.IAMConfig, local bool, opts ...DriverOption) *sqlx.DB {
	configAwareDriver := ConfigurationAwareDriver{c, local, false}
	for _, opt := range opts {
		opt(&configAwareDriver)
	}

	DB := sql.OpenDB(&configAwareDriver)
	newDb := sqlx.NewDb(DB, pgxDriverName)
	newDb.SetConnMaxLifetime(time.Minute * 14)
	return newDb
}

var (
	ErrDriverNotSupported = errors.New("driver open method not supported")
)

type ConfigurationAwareDriver struct {
	*db.IAMConfig
	Local       bool
	XrayEnabled bool
}

func (c *ConfigurationAwareDriver) Driver() driver.Driver {
	return c
}

func (c *ConfigurationAwareDriver) Open(_ string) (driver.Conn, error) {
	return nil, ErrDriverNotSupported
}

func (c *ConfigurationAwareDriver) Connect(ctx context.Context) (driver.Conn, error) {
	var dsn string
	dbEndpoint := fmt.Sprintf("%s:%d", c.Host, c.Port)
	var authenticationToken = "secret"
	if !c.Local {
		cfg, err := config.LoadDefaultConfig(ctx)
		if err != nil {
			return nil, err
		}
		if c.AssumeRoleArn != "" {
			stsSvc := sts.NewFromConfig(cfg)
			credentials := stscreds.NewAssumeRoleProvider(stsSvc, c.AssumeRoleArn, func(o *stscreds.AssumeRoleOptions) {
				o.RoleSessionName = c.AssumeRoleSessionName
			})
			cfg.Credentials = aws.NewCredentialsCache(credentials)
		}
		authenticationToken, err = auth.BuildAuthToken(ctx, dbEndpoint, cfg.Region, c.Username, cfg.Credentials)
		if err != nil {
			return nil, err
		}
	}

	dsn = createIAMConnectionString(c.IAMConfig, authenticationToken, c.Local)

	var connector driver.Connector
	connector, err := pq.NewConnector(dsn)
	if err != nil {
		return nil, err
	}

	if c.XrayEnabled {
		connector = xray.SQLConnector("postgres", connector)
	}

	connection, err := connector.Connect(ctx)
	if err != nil {
		return nil, err
	}

	return connection, nil
}

// NewPasswordDB returns a database handle representing a pool of zero or more underlying connections that authenticate
// via username and password.
//
// * c      -  used to configure database connection
// * local  -  true if application running locally, false if running on AWS
func NewPasswordDB(c db.PasswordConfig, local bool) (*sql.DB, error) {
	dsn := createConnectionString(c.Host, c.Port, c.Username, c.Password, c.Name, local)

	var DB *sql.DB
	var err error

	DB, err = xray.SQLContext(driverName, dsn)

	if err != nil {
		return nil, err
	}
	return DB, nil
}

func createConnectionString(host string, port int, username, password, name string, local bool) string {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s", host, port, username, password, name)

	if local {
		dsn = fmt.Sprintf("%s sslmode=disable", dsn)
	} else {
		dsn = fmt.Sprintf("%s sslrootcert=/etc/ssl/certs/ca-certificates.crt sslmode=verify-ca", dsn)
	}

	return dsn
}

func createIAMConnectionString(c *db.IAMConfig, password string, local bool) string {
	return createConnectionString(c.Host, c.Port, c.Username, password, c.Name, local)
}
