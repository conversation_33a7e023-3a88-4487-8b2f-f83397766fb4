package sns

import (
	"encoding/json"
	"errors"
)

var (
	ErrNilInput            = errors.New("invalid SNS notification: input string is nil")
	ErrMessageNotPresent   = errors.New("invalid SNS notification: message attribute not present")
	ErrMessageIDNotPresent = errors.New("invalid SNS notification: messageId attribute not present")
	ErrTypeNotPresent      = errors.New("invalid SNS notification: type attribute not present")
)

// Unmarshal string input to SNS Notification, or return error if:
// - not able to unmarshall
// - or type, message, or messageId attributes not present
func Unmarshal(input *string) (*Notification, error) {
	if input == nil {
		return nil, ErrNilInput
	}

	var notification Notification
	err := json.Unmarshal([]byte(*input), &notification)
	if err != nil {
		return nil, err
	}

	if notification.Type == nil {
		return nil, ErrTypeNotPresent
	}

	if notification.Message == nil {
		return nil, ErrMessageNotPresent
	}

	if notification.MessageID == nil {
		return nil, ErrMessageIDNotPresent
	}

	return &notification, nil
}
