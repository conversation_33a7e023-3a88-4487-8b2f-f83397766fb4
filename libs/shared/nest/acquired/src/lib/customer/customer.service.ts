import { CreateCustomer, ExtendedCustomer, Recipient } from './customer.types';
import {
  CustomerCreationFailed,
  CustomerUpdateFailed,
} from './customer.errors';
import { CustomersApi } from '@experience/shared/axios/acquired-api-client';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class AcquiredCustomerService {
  private readonly logger = new Logger(AcquiredCustomerService.name);

  constructor(private readonly customersApi: CustomersApi) {}

  async createRecipientAccount(request: CreateCustomer): Promise<Recipient> {
    this.logger.log({ request }, 'creating acquired customer');

    try {
      const customer = await this.customersApi.createCustomer(undefined, {
        first_name: request.firstName,
      });

      if (!customer.data.customer_id) {
        this.logger.error(
          { status: customer.status, data: customer.data },
          'customer id was not populated'
        );

        throw new CustomerCreationFailed('Customer ID was not populated');
      }

      return {
        id: customer.data.customer_id,
      };
    } catch (error) {
      this.logger.error(
        {
          request,
          error,
        },
        'failed to create acquired customer'
      );

      throw error;
    }
  }

  async disableRecipientAccount(customerId: string) {
    this.logger.log({ customerId }, 'attempting to disable customer');

    try {
      await this.customersApi.updateCustomer(customerId, {
        is_active: false,
      } as ExtendedCustomer);
    } catch (error) {
      this.logger.error({ customerId, error }, 'failed to disable customer');

      throw new CustomerUpdateFailed('failed to disable customer');
    }
  }
}
