import { AcquiredCustomerService } from './customer/customer.service';
import { AcquiredPayeeService } from './payee/payee.service';
import { AcquiredPayoutService } from './payout/payout.service';
import { AcquiredToolsService } from './tools/tools.service';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigurableModuleBuilder, Module } from '@nestjs/common';
import {
  CustomersApi,
  FasterPaymentsApi,
  ToolsApi,
} from '@experience/shared/axios/acquired-api-client';
import { TEST_MODE } from './acquired.constants';
import { provideAcquiredAPI } from './acquired.provider';

export interface AcquiredConfig {
  testMode?: boolean;
  baseURL: string;
  apiKey: string;
  appId: string;
}

export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder<AcquiredConfig>().build();

@Module({
  imports: [CacheModule.register()],
  providers: [
    {
      provide: TEST_MODE,
      inject: [MODULE_OPTIONS_TOKEN],
      useFactory: (config: AcquiredConfig) => config.testMode,
    },
    AcquiredCustomerService,
    AcquiredPayeeService,
    AcquiredPayoutService,
    AcquiredToolsService,
    provideAcquiredAPI(ToolsApi),
    provideAcquiredAPI(CustomersApi),
    provideAcquiredAPI(FasterPaymentsApi),
  ],
  exports: [
    AcquiredCustomerService,
    AcquiredPayeeService,
    AcquiredPayoutService,
    AcquiredToolsService,
  ],
})
export class AcquiredModule extends ConfigurableModuleClass {}
