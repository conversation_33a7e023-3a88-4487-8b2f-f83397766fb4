import { AcquiredPayeeService } from './payee.service';
import { AxiosResponse } from 'axios';
import { CreatePayeeRequest } from './payee.types';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { FasterPaymentsApi } from '@experience/shared/axios/acquired-api-client';
import { PayeeCreationFailed } from './payee.errors';
import { Test } from '@nestjs/testing';

describe(AcquiredPayeeService.name, () => {
  let service: AcquiredPayeeService;
  let fasterPaymentsApi: DeepMocked<FasterPaymentsApi>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        AcquiredPayeeService,
        {
          provide: FasterPaymentsApi,
          useValue: createMock<FasterPaymentsApi>(),
        },
      ],
    }).compile();

    service = module.get(AcquiredPayeeService);
    fasterPaymentsApi = module.get(FasterPaymentsApi);
  });

  afterEach(() => jest.resetAllMocks());

  describe(AcquiredPayeeService.prototype.createPayee, () => {
    const MOCK_CUSTOMER_ID = 'e3aafa55-d352-43e8-8eb4-af542a1ccc3c';
    const MOCK_CREATE_PAYEE_REQUEST: CreatePayeeRequest = {
      name: 'MR E JOHNSON',
      accountNumber: '********',
      sortCode: '010203',
    };

    it('creates the payee, but throws a PayeeCreationFailed if no payee id is populated', async () => {
      fasterPaymentsApi.createPayee.mockResolvedValue({
        data: {
          payee_id: undefined,
        },
      } as AxiosResponse);

      await expect(
        service.createPayee(MOCK_CUSTOMER_ID, MOCK_CREATE_PAYEE_REQUEST)
      ).rejects.toThrow(PayeeCreationFailed);

      expect(fasterPaymentsApi.createPayee).toHaveBeenCalledTimes(1);
      expect(fasterPaymentsApi.createPayee).toHaveBeenCalledWith(
        MOCK_CUSTOMER_ID,
        undefined,
        undefined,
        {
          account_name: MOCK_CREATE_PAYEE_REQUEST.name,
          account_number: MOCK_CREATE_PAYEE_REQUEST.accountNumber,
          sort_code: MOCK_CREATE_PAYEE_REQUEST.sortCode,
        }
      );
    });

    it('successfully creates the payee', async () => {
      fasterPaymentsApi.createPayee.mockResolvedValue({
        data: {
          status: 'success',
          payee_id: '4ebc5489-7b9f-4324-983e-07d4b2b00035',
        },
      } as AxiosResponse);

      const res = await service.createPayee(
        MOCK_CUSTOMER_ID,
        MOCK_CREATE_PAYEE_REQUEST
      );

      expect(res).toStrictEqual({
        id: '4ebc5489-7b9f-4324-983e-07d4b2b00035',
      });

      expect(fasterPaymentsApi.createPayee).toHaveBeenCalledTimes(1);
      expect(fasterPaymentsApi.createPayee).toHaveBeenCalledWith(
        MOCK_CUSTOMER_ID,
        undefined,
        undefined,
        {
          account_name: MOCK_CREATE_PAYEE_REQUEST.name,
          account_number: MOCK_CREATE_PAYEE_REQUEST.accountNumber,
          sort_code: MOCK_CREATE_PAYEE_REQUEST.sortCode,
        }
      );
    });

    it('re-throws any errors', async () => {
      const error = new Error();

      fasterPaymentsApi.createPayee.mockRejectedValue(error);

      await expect(
        service.createPayee(MOCK_CUSTOMER_ID, MOCK_CREATE_PAYEE_REQUEST)
      ).rejects.toThrow(error);
    });
  });
});
