import 'aws-sdk-client-mock-jest';
import {
  SQSClient,
  SendMessageBatchCommand,
  SendMessageCommand,
} from '@aws-sdk/client-sqs';
import { Test, TestingModule } from '@nestjs/testing';
import { mockClient } from 'aws-sdk-client-mock';
import SqsClientService from './sqs.client.service';

describe('SqsClientService', () => {
  let service: SqsClientService;
  const sqsClientMock = mockClient(SQSClient);

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        SqsClientService,
        {
          provide: SQSClient,
          useValue: sqsClientMock,
        },
      ],
    }).compile();

    service = module.get<SqsClientService>(SqsClientService);
  });

  beforeEach(() => {
    sqsClientMock.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should send message to a queue', async () => {
    const expectedResponse = {
      MD5OfMessageAttributes: 'c48838208d2b4e14e3ca0093a8443f09',
      MD5OfMessageBody: 'fafb00f5732ab283681e124bf8747ed1',
      MessageId: '219f8380-5770-4cc2-8c3e-5c715e145f5e',
    };
    sqsClientMock.on(SendMessageCommand).resolves(expectedResponse);

    await service.sendMessage('http://localhost:3001', {
      messageBody: 'test',
      messageAttributes: {
        FOO: {
          StringValue: 'BAR',
          DataType: 'String',
        },
      },
      messageDeduplicationId: '123',
    });

    expect(sqsClientMock).toHaveReceivedNthCommandWith(1, SendMessageCommand, {
      MessageAttributes: {
        FOO: {
          DataType: 'String',
          StringValue: 'BAR',
        },
      },
      MessageBody: 'test',
      MessageDeduplicationId: '123',
      QueueUrl: 'http://localhost:3001',
    });
  });

  it('should send a batch of messages to a queue', async () => {
    const expectedResponse = {
      Failed: [],
      Successful: [
        {
          Id: '1',
          MD5OfMessageBody: 'fafb00f5732ab283681e124bf8747ed1',
          MessageId: '219f8380-5770-4cc2-8c3e-5c715e145f5e',
        },
        {
          Id: '2',
          MD5OfMessageBody: 'fafb00f5732ab283681e124bf8754ed1',
          MessageId: '219f8380-5770-4cc2-8c3e-5c715e161f5e',
        },
      ],
    };
    sqsClientMock.on(SendMessageBatchCommand).resolves(expectedResponse);

    await service.sendBatchMessages('http://localhost:3001', [
      {
        messageId: '1',
        messageBody: 'test',
        messageAttributes: {
          FOO: {
            StringValue: 'BAR',
            DataType: 'String',
          },
        },
      },
      {
        messageId: '2',
        messageBody: 'test2',
        messageAttributes: {
          FOO: {
            StringValue: 'BAZ',
            DataType: 'String',
          },
        },
        messageDeduplicationId: '123',
      },
    ]);

    expect(sqsClientMock).toHaveReceivedNthCommandWith(
      1,
      SendMessageBatchCommand,
      {
        Entries: [
          {
            Id: '1',
            MessageAttributes: {
              FOO: {
                DataType: 'String',
                StringValue: 'BAR',
              },
            },
            MessageBody: 'test',
          },
          {
            Id: '2',
            MessageAttributes: {
              FOO: {
                DataType: 'String',
                StringValue: 'BAZ',
              },
            },
            MessageBody: 'test2',
            MessageDeduplicationId: '123',
          },
        ],
        QueueUrl: 'http://localhost:3001',
      }
    );
  });
});
