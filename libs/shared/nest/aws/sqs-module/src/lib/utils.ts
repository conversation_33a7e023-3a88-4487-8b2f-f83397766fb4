import {
  SqsBatchMessageProps,
  SqsClientService,
  SqsMessageAttribute,
} from './sqs.client.service';
import { v4 as uuid } from 'uuid';

export const batchMessagesForQueueing = (
  items: SqsBatchMessageProps[],
  batchSize = 10
): SqsBatchMessageProps[][] =>
  items.reduce((result, item, index) => {
    const chunkIndex = Math.floor(index / batchSize);

    if (!result[chunkIndex]) {
      result[chunkIndex] = [];
    }
    result[chunkIndex].push(item);

    return result;
  }, [] as SqsBatchMessageProps[][]);

export const mapToBatchMessage = <T>(
  body: T,
  messageAttributes?: Record<string, SqsMessageAttribute>
): SqsBatchMessageProps => ({
  messageId: uuid(),
  messageBody: JSON.stringify(body),
  messageAttributes,
});

export const sendBatchMessages = async (
  queueUrl: string,
  messageBatches: SqsBatchMessageProps[][],
  sqsClientService: SqsClientService,
  onBatchFulfilled?: (batch: SqsBatchMessageProps[]) => Promise<void>
): Promise<void> => {
  const fulfilledMessages = messageBatches.map(async (batch) => {
    await sqsClientService.sendBatchMessages(queueUrl, batch);
    if (onBatchFulfilled) {
      await onBatchFulfilled(batch);
    }
  });
  await Promise.all(fulfilledMessages);
};
