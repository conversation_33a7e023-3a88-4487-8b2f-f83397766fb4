import { App } from 'firebase-admin/lib/app/core';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CHECK_FLAG_TEN_MINUTE_INTERVAL } from './constants';
import { Cache } from 'cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  RemoteConfigParameterValue,
  getRemoteConfig,
} from 'firebase-admin/remote-config';
import { getApp } from '@experience/shared/firebase/admin';

@Injectable()
export class FlagsService {
  private logger = new Logger(FlagsService.name);

  constructor(@Inject(CACHE_MANAGER) private readonly cacheManager: Cache) {}

  public async doCheckForRemoteFlag(
    flagName: string,
    flagValue: string
  ): Promise<boolean> {
    const doCheckFromCache = await this.cacheManager.get(flagName);
    if (typeof doCheckFromCache == 'boolean') {
      return doCheckFromCache;
    }
    const doCheck = await this.getFlagFromRemoteConfig(flagName, flagValue);
    await this.cacheManager.set(
      flagName,
      doCheck,
      CHECK_FLAG_TEN_MINUTE_INTERVAL
    );
    return doCheck;
  }

  async getFlagFromRemoteConfig(
    flagName: string,
    flagValue: string
  ): Promise<boolean> {
    const flag = await this.getRemoteFlag(flagName);
    const flagDoesNotHaveBooleanStringValue =
      flag &&
      'value' in flag &&
      flag.value &&
      flag.value !== 'true' &&
      flag.value !== 'false';
    if (!flag || flagDoesNotHaveBooleanStringValue) {
      this.logger.warn(
        `No feature flag set for from remote config defaulting to false - flag: ${flagName}`
      );
      return false;
    }
    if ('value' in flag) {
      this.logger.log(
        `Retrieved flag do check for ${flagName} from remote config: ${flag.value}`
      );
      return flag.value === flagValue;
    }
    return false;
  }

  async getRemoteFlag(
    flagName: string
  ): Promise<RemoteConfigParameterValue | undefined> {
    const app: App = await getApp();
    const remoteConfig = getRemoteConfig(app);
    const template = await remoteConfig.getTemplate();
    return template.parameters[flagName]?.defaultValue;
  }
}
