import { StripeChargeService } from './charge.service';
import { Test, TestingModule } from '@nestjs/testing';
import <PERSON><PERSON> from 'stripe';

const TEST_CHARGE_ID = 'ch_3Pg3yj4ndFfyXYyp01hE3u8Z';

const mockRetrieve = jest.fn();

jest.mock('stripe', () =>
  jest.fn().mockImplementation(() => ({
    charges: {
      // @ts-expect-error mock
      retrieve: (chargeId) => mockRetrieve(chargeId),
    },
  }))
);

describe('StripeChargeService', () => {
  let service: StripeChargeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StripeChargeService,
        {
          provide: Stripe,
          useValue: new Stripe('test', {}),
        },
      ],
    }).compile();

    service = module.get<StripeChargeService>(StripeChargeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('isPaymentFromWallet', () => {
    it('should return true if wallet type is populated', async () => {
      mockRetrieve.mockResolvedValueOnce({
        id: TEST_CHARGE_ID,
        payment_method_details: {
          card: {
            wallet: {
              type: 'apple_pay',
            },
          },
        },
      } as Stripe.Charge);

      expect(await service.isPaymentFromWallet(TEST_CHARGE_ID)).toEqual(true);
    });

    it('should return false if wallet type is not populated', async () => {
      mockRetrieve.mockResolvedValueOnce(undefined);

      expect(await service.isPaymentFromWallet(TEST_CHARGE_ID)).toEqual(false);
    });
  });
});
