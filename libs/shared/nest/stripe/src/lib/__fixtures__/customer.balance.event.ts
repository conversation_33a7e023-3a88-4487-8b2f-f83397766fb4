import Stripe from 'stripe';

export const TEST_CUSTOMER_BALANCE_EVENT: Stripe.CashBalanceFundsAvailableEvent =
  {
    id: 'evt_1R9A0qFfRqcfiHYMWD8eX4YV',
    object: 'event',
    api_version: '2023-10-16',
    type: 'cash_balance.funds_available',
    data: {
      object: {
        object: 'cash_balance',
        available: { gbp: 5000 },
        customer: 'cus_Na6dX7aXxi11N4',
        livemode: false,
        settings: {
          reconciliation_mode: 'automatic',
          using_merchant_default: false,
        },
      },
    },
    created: 0,
    livemode: false,
    pending_webhooks: 0,
    request: null,
  };
