import { Stripe } from 'stripe';

export const TEST_ACCOUNT_UPDATED_EVENT: Stripe.Event = {
  id: 'evt_1IEAaZG16qv9bVQKQ0Sk0qHg',
  object: 'event',
  api_version: '2020-08-27',
  account: 'acct_1IEAaZG16qv9bVQKQ0Sk0qHg',
  created: **********,
  data: {
    object: {
      id: 'acct_1IEAaZG16qv9bVQKQ0Sk0qHg',
      object: 'account',
      business_profile: {
        annual_revenue: null,
        estimated_worker_count: null,
        mcc: null,
        minority_owned_business_designation: null,
        name: 'Test Business',
        product_description: null,
        support_address: null,
        support_email: null,
        support_phone: null,
        support_url: null,
        url: null,
      },
      business_type: 'individual',
      capabilities: {
        transfers: 'active',
      },
      charges_enabled: true,
      country: 'US',
      default_currency: 'usd',
      details_submitted: true,
      email: '<EMAIL>',
      payouts_enabled: true,
      type: 'standard',
    },
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: 'req_123456789',
    idempotency_key: null,
  },
  type: 'account.updated',
};

export const TEST_CANCEL_TRANSFERS_ACCOUNT_UPDATED_EVENT: Stripe.Event = {
  id: 'evt_1IEAaZG16qv9bVQKQ0Sk0qHg',
  object: 'event',
  api_version: '2020-08-27',
  account: 'acct_1IEAaZG16qv9bVQKQ0Sk0qHg',
  created: **********,
  data: {
    object: {
      id: 'acct_1IEAaZG16qv9bVQKQ0Sk0qHg',
      object: 'account',
      business_profile: {
        annual_revenue: null,
        estimated_worker_count: null,
        mcc: null,
        minority_owned_business_designation: null,
        name: 'Test Business',
        product_description: null,
        support_address: null,
        support_email: null,
        support_phone: null,
        support_url: null,
        url: null,
      },
      business_type: 'individual',
      capabilities: {
        transfers: 'inactive',
      },
      charges_enabled: true,
      country: 'US',
      default_currency: 'usd',
      details_submitted: true,
      email: '<EMAIL>',
      payouts_enabled: true,
      type: 'standard',
    },
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: 'req_123456789',
    idempotency_key: null,
  },
  type: 'account.updated',
};

export const TEST_PAYOUT_PAID_EVENT: Stripe.Event = {
  id: 'evt_1Pq3BlFzEIHhSLgfZ5Dh3Ums',
  object: 'event',
  account: 'acct_1IEAaZG16qv9bVQKQ0Sk0qHg',
  api_version: '2023-10-16',
  created: **********,
  data: {
    object: {
      id: 'po_1Pq36UFzEIHhSLgf764Kk96y',
      object: 'payout',
      amount: 3000,
      application_fee: null,
      application_fee_amount: null,
      arrival_date: **********,
      automatic: true,
      balance_transaction: 'txn_1Pq36VFzEIHhSLgf8WZO5Wyk',
      created: **********,
      currency: 'gbp',
      description: 'STRIPE PAYOUT',
      destination: 'ba_1Po1EKFzEIHhSLgfGMthOWSL',
      failure_balance_transaction: null,
      failure_code: null,
      failure_message: null,
      livemode: false,
      metadata: {},
      method: 'standard',
      original_payout: null,
      payout_method: null,
      reconciliation_status: 'completed',
      reversed_by: null,
      source_type: 'card',
      statement_descriptor: null,
      status: 'paid',
      trace_id: null,
      type: 'bank_account',
    },
  },
  livemode: false,
  pending_webhooks: 2,
  request: {
    id: null,
    idempotency_key: null,
  },
  type: 'payout.paid',
};
