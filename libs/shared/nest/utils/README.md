# Shared Nest Utils

This library was generated with [Nx](https://nx.dev). It provides utilities which can be used when
building [NestJS](https://nestjs.com) applications.

## Running unit tests

Run `npx nx test shared-nest-utils` to execute the unit tests via [Jest](https://jestjs.io).

## Running lint

Run `npx nx lint shared-next-utils` to execute the lint via [ESLint](https://eslint.org).

## Utilities Provided

The following utilities are provided by this library.

### Bootstrap Function

A function which can be used to configure and start a NestJS application. It will configure things
which are common to all NestJS applications such as validation and documentation.

```typescript
import { AppModule } from './app/app.module';
import { bootstrap } from '@experience/shared/nest/utils';

bootstrap(AppModule, 3333);
```

### Pino Logger Options

A constant which can be used to configure consistent logging behaviour across different NestJS applications.

```typescript
import {PINO_LOGGER_OPTIONS} from '@experience/shared/nest/utils';

@Module({
  imports: [
    LoggerModule.forRoot(PINO_LOGGER_OPTIONS),
  ],
})
```
