import {
  CredentialsMiddleware,
  CredentialsModule,
} from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { OCPIPrismaClientModule } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { TariffController } from './tariff.controller';
import { TariffService } from './tariff.service';
import { UniqueMessageIdsMiddleware } from '@experience/commercial/ocpi-service/v221/shared';

@Module({
  imports: [CredentialsModule, OCPIPrismaClientModule],
  controllers: [TariffController],
  providers: [TariffService],
  exports: [TariffService],
})
export class TariffsModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CredentialsMiddleware, UniqueMessageIdsMiddleware)
      .forRoutes(TariffController);
  }
}
