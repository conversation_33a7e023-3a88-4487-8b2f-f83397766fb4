import { ConfigModule } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { OCPIPrismaClientModule } from '@experience/commercial/ocpi-service/v221/prisma/client';
import {
  RevenueProfileTiers,
  RevenueProfiles,
} from '@experience/shared/sequelize/podadmin';
import { SqsClientModule } from '@experience/shared/nest/aws/sqs-module';
import { TariffMigrator } from '../migrators/tariff/tariff-migrator';
import { TariffsModule } from '../tariffs.module';
import MigrateTariffsCommand from './migrate-tariffs/migrate-tariffs.command';
import PushTariffsCommand from './push-tariffs/push-tariffs.command';

@Module({
  imports: [
    ConfigModule,
    OCPIPrismaClientModule,
    TariffsModule,
    SqsClientModule.register(process.env?.['AWS_ENDPOINT_URL']),
  ],
  providers: [
    {
      provide: 'REVENUE_PROFILES_REPOSITORY',
      useValue: RevenueProfiles,
    },
    {
      provide: 'REVENUE_PROFILE_TIERS_REPOSITORY',
      useValue: RevenueProfileTiers,
    },
    MigrateTariffsCommand,
    PushTariffsCommand,
    TariffMigrator,
  ],
})
export class TariffsTaskRunnerModule {}
