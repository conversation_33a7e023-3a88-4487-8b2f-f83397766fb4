import { ConfigModule, ConfigService } from '@nestjs/config';
import { CredentialsModule } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { Module } from '@nestjs/common';
import {
  PushTariffsConsumer,
  PushTariffsQueueConsumerService,
} from './push-tariffs.consumer';
import {
  QueueConsumerService,
  SqsConsumerModule,
} from '@experience/shared/nest/aws/sqs-module';
import { SqsConsumerModuleOptions } from '@experience/shared/nest/aws/sqs-module';
import { TariffsModule } from '../../tariffs.module';

@Module({
  imports: [
    ConfigModule,
    CredentialsModule,
    TariffsModule,
    SqsConsumerModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService): SqsConsumerModuleOptions => ({
        disabled: !config.get<string>('PUSH_TARIFFS_QUEUE_URL'),
        queueUrl: config.get<string>('PUSH_TARIFFS_QUEUE_URL'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    PushTariffsConsumer,
    {
      provide: PushTariffsQueueConsumerService,
      useExisting: QueueConsumerService,
    },
  ],
})
export class PushTariffsConsumerModule {}
