import { Cdr<PERSON>ontroller } from './cdr.controller';
import { CdrService } from './cdr.service';
import {
  DATE_FORMAT,
  TEST_CDR,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { INestApplication } from '@nestjs/common';
import { ModuleGuard } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import {
  OcpiStatusCode,
  OcpiStatusMessage,
} from '@experience/commercial/ocpi-service/v221/shared';
import { PaginationParams, buildPage } from '@experience/shared/nest/utils';
import { Test, TestingModule } from '@nestjs/testing';
import { convertAllDatesToISOString } from '@experience/shared/typescript/utils';
import request from 'supertest';

jest.mock('./cdr.service');

describe('CdrController', () => {
  let app: INestApplication;
  let controller: CdrController;
  let service: CdrService;

  const testPaginationParams: PaginationParams = {
    dateFrom: new Date(0),
    dateTo: new Date(),
    limit: 100,
    offset: 0,
  };

  const mockCanActivate = jest.fn().mockResolvedValue(true);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CdrController],
      providers: [
        { provide: CdrService, useClass: CdrService },
        { provide: ModuleGuard, useClass: ModuleGuard },
      ],
    })
      .overrideProvider(ModuleGuard)
      .useValue({ canActivate: mockCanActivate })
      .compile();

    controller = module.get<CdrController>(CdrController);
    service = module.get<CdrService>(CdrService);

    app = module.createNestApplication();
    app.useGlobalGuards(module.get(ModuleGuard));
    await app.init();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should return a list of cdrs', async () => {
    const mockService = jest
      .spyOn(service, 'findAll')
      .mockResolvedValueOnce(buildPage([TEST_CDR], 1, testPaginationParams));

    const response = await request(app.getHttpServer()).get('/cdrs');

    expect(response.body).toEqual({
      data: [convertAllDatesToISOString(TEST_CDR)],
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(testPaginationParams);
    expect(mockCanActivate).toHaveBeenCalled();
  });

  it('should return a 403 forbidden error if the module is not enabled for that user', async () => {
    mockCanActivate.mockResolvedValueOnce(false);

    const response = await request(app.getHttpServer()).get('/cdrs');

    expect(response.body).toEqual({
      error: 'Forbidden',
      message: 'Forbidden resource',
      statusCode: 403,
    });
    expect(mockCanActivate).toHaveBeenCalled();
  });
});
