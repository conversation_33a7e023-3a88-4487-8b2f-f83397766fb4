import { CommandController } from './command.controller';
import { CommandService } from './command.service';
import { ConfigModule } from '@nestjs/config';
import {
  CredentialsMiddleware,
  CredentialsModule,
} from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { LocationsModule } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { OCPIPrismaClientModule } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { SqsClientModule } from '@experience/shared/nest/aws/sqs-module';
import { UniqueMessageIdsMiddleware } from '@experience/commercial/ocpi-service/v221/shared';

@Module({
  imports: [
    ConfigModule,
    CredentialsModule,
    LocationsModule,
    OCPIPrismaClientModule,
    SqsClientModule.register(process.env?.['AWS_ENDPOINT_URL']),
  ],
  controllers: [CommandController],
  providers: [CommandService],
})
export class CommandsModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CredentialsMiddleware, UniqueMessageIdsMiddleware)
      .forRoutes(CommandController);
  }
}
