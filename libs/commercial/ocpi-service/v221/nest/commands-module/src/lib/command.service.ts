import {
  Capability,
  CommandMessage,
  CommandResponse,
  CommandResponseSchema,
  ConnectorIdRequiredException,
  EvseNotAvailableException,
  EvseStatus,
  EvseUidRequiredException,
  InvalidOrMissingParametersException,
  OcpiStatusMessage,
  SessionNotFoundException,
  SessionStatusInactiveException,
  StartSessionCommand,
  StopSessionCommand,
  UnlockConnectorCommand,
} from '@experience/commercial/ocpi-service/v221/shared';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { LocationService } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';

@Injectable()
export class CommandService {
  private readonly logger = new Logger(CommandService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly database: OCPIPrismaClient,
    private readonly locationService: LocationService,
    private readonly sqsClientService: SqsClientService
  ) {}

  async startSession(command: StartSessionCommand): Promise<CommandResponse> {
    this.logger.log({ command }, 'start session command received');

    if (!command.evse_uid) {
      throw new EvseUidRequiredException();
    }

    const evse = await this.locationService.findEvse(
      command.location_id,
      command.evse_uid
    );

    const availableStatuses: string[] = [
      EvseStatus.enum.AVAILABLE,
      EvseStatus.enum.CHARGING,
    ];

    if (!availableStatuses.includes(evse.status)) {
      throw new EvseNotAvailableException();
    }

    if (evse.connectors.length > 1 && !command.connector_id) {
      throw new ConnectorIdRequiredException();
    }

    const message: CommandMessage = {
      command: {
        ...command,
        connector_id: command.connector_id ?? evse.connectors[0].id,
      },
    };
    this.logger.log({ message, evse }, 'queuing start session command');

    await this.sqsClientService.sendMessage(
      this.configService.get('START_SESSION_COMMANDS_QUEUE_URL') as string,
      { messageBody: JSON.stringify(message) }
    );

    const commandResponse: CommandResponse = {
      result: 'ACCEPTED',
      timeout: 120,
    };
    return CommandResponseSchema.parse(commandResponse);
  }

  async stopSession(command: StopSessionCommand): Promise<CommandResponse> {
    this.logger.log({ command }, 'stop session command received');

    await this.database.session
      .findUnique({ where: { id: command.session_id } })
      .then((entity) => {
        if (!entity) {
          throw new SessionNotFoundException();
        }
        if (entity.status !== 'ACTIVE') {
          throw new SessionStatusInactiveException();
        }
      });

    const message: CommandMessage = {
      command: command,
    };
    this.logger.log({ message }, 'queuing stop session command');

    await this.sqsClientService.sendMessage(
      this.configService.get('STOP_SESSION_COMMANDS_QUEUE_URL') as string,
      {
        messageBody: JSON.stringify(message),
      }
    );

    const commandResponse: CommandResponse = {
      result: 'ACCEPTED',
      timeout: 120,
    };
    return CommandResponseSchema.parse(commandResponse);
  }

  async unlockConnector(
    command: UnlockConnectorCommand
  ): Promise<CommandResponse> {
    this.logger.log({ command }, 'unlock connector command received');

    const evse = await this.locationService.findEvse(
      command.location_id,
      command.evse_uid
    );

    if (!evse.capabilities?.includes(Capability.enum.UNLOCK_CAPABLE)) {
      throw new InvalidOrMissingParametersException(
        OcpiStatusMessage.UNLOCK_CONNECTOR_NOT_SUPPORTED
      );
    }

    const message: CommandMessage = {
      command: command,
    };
    this.logger.log({ message, evse }, 'queuing unlock connector command');

    await this.sqsClientService.sendMessage(
      this.configService.get('UNLOCK_CONNECTOR_COMMANDS_QUEUE_URL') as string,
      {
        messageBody: JSON.stringify(message),
      }
    );

    const commandResponse: CommandResponse = {
      result: 'ACCEPTED',
      timeout: 120,
    };
    return CommandResponseSchema.parse(commandResponse);
  }
}
