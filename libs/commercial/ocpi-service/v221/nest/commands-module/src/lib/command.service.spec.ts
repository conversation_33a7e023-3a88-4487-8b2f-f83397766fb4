import { AsyncLocalStorage } from 'async_hooks';
import { CommandService } from './command.service';
import { ConfigModule } from '@nestjs/config';
import {
  ConnectorIdRequiredException,
  CredentialsStore,
  EvseNotAvailableException,
  EvseStatus,
  EvseUidRequiredException,
  InvalidOrMissingParametersException,
  SessionNotFoundException,
  SessionStatusInactiveException,
} from '@experience/commercial/ocpi-service/v221/shared';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import { LocationService } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import { TEST_ACTIVE_SESSION_ENTITY } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import {
  TEST_COMMAND_RESPONSE_ACCEPTED,
  TEST_EVSE,
  TEST_START_SESSION_COMMAND,
  TEST_START_SESSION_COMMAND_MESSAGE,
  TEST_STOP_SESSION_COMMAND,
  TEST_STOP_SESSION_COMMAND_MESSAGE,
  TEST_UNLOCK_CONNECTOR_COMMAND,
  TEST_UNLOCK_CONNECTOR_COMMAND_MESSAGE,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('@experience/shared/nest/aws/sqs-module');

describe('CommandService', () => {
  let database: OCPIPrismaClient;
  let locationService: LocationService;
  let service: CommandService;
  let sqsClientService: SqsClientService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              START_SESSION_COMMANDS_QUEUE_URL:
                'http://localhost/000000/ocpi-service-start-session-commands',
              STOP_SESSION_COMMANDS_QUEUE_URL:
                'http://localhost/000000/stop-session-commands',
              UNLOCK_CONNECTOR_COMMANDS_QUEUE_URL:
                'http://localhost/000000/ocpi-service-unlock-connector-commands',
            }),
          ],
        }),
      ],
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        CommandService,
        HealthIndicatorService,
        LocationService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        SqsClientService,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
        SqsClientService,
      ],
    }).compile();

    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    locationService = module.get<LocationService>(LocationService);
    service = module.get<CommandService>(CommandService);
    sqsClientService = module.get<SqsClientService>(SqsClientService);
  });

  it('should be defined', () => {
    expect(database).toBeDefined();
    expect(locationService).toBeDefined();
    expect(service).toBeDefined();
    expect(sqsClientService).toBeDefined();
  });

  describe('startSession', () => {
    it.each([EvseStatus.enum.AVAILABLE, EvseStatus.enum.CHARGING])(
      'should queue a start session command if the evse status is %s',
      async (status) => {
        const mockFindEvse = jest
          .spyOn(locationService, 'findEvse')
          .mockResolvedValue({ ...TEST_EVSE, status });
        const mockSendMessage = jest.spyOn(sqsClientService, 'sendMessage');

        const commandResponse = await service.startSession(
          TEST_START_SESSION_COMMAND
        );

        expect(commandResponse).toEqual(TEST_COMMAND_RESPONSE_ACCEPTED);
        expect(mockFindEvse).toHaveBeenCalledWith(
          TEST_START_SESSION_COMMAND.location_id,
          TEST_START_SESSION_COMMAND.evse_uid
        );
        expect(mockSendMessage).toHaveBeenCalledWith(
          'http://localhost/000000/ocpi-service-start-session-commands',
          {
            messageBody: JSON.stringify(TEST_START_SESSION_COMMAND_MESSAGE),
          }
        );
      }
    );

    it('should queue a start session command when no connector id is provided', async () => {
      const mockFindEvse = jest
        .spyOn(locationService, 'findEvse')
        .mockResolvedValue(TEST_EVSE);
      const mockSendMessage = jest.spyOn(sqsClientService, 'sendMessage');

      const commandResponse = await service.startSession({
        ...TEST_START_SESSION_COMMAND,
        connector_id: null,
      });

      expect(commandResponse).toEqual(TEST_COMMAND_RESPONSE_ACCEPTED);
      expect(mockFindEvse).toHaveBeenCalledWith(
        TEST_START_SESSION_COMMAND.location_id,
        TEST_START_SESSION_COMMAND.evse_uid
      );
      expect(mockSendMessage).toHaveBeenCalledWith(
        'http://localhost/000000/ocpi-service-start-session-commands',
        {
          messageBody: JSON.stringify(TEST_START_SESSION_COMMAND_MESSAGE),
        }
      );
    });

    it('should throw an evse uid required exception if evse_uid is not provided', async () => {
      await expect(
        service.startSession({
          ...TEST_START_SESSION_COMMAND,
          evse_uid: undefined,
        })
      ).rejects.toThrow(EvseUidRequiredException);
    });

    it.each([
      EvseStatus.enum.BLOCKED,
      EvseStatus.enum.INOPERATIVE,
      EvseStatus.enum.OUTOFORDER,
      EvseStatus.enum.PLANNED,
      EvseStatus.enum.REMOVED,
      EvseStatus.enum.RESERVED,
      EvseStatus.enum.UNKNOWN,
    ])(
      'should throw an evse not available exception if the evse status is %s',
      async (status) => {
        jest
          .spyOn(locationService, 'findEvse')
          .mockResolvedValue({ ...TEST_EVSE, status });

        await expect(
          service.startSession(TEST_START_SESSION_COMMAND)
        ).rejects.toThrow(EvseNotAvailableException);
      }
    );

    it('should throw a connector id required exception if the evse has multiple connectors and no connector id is provided', async () => {
      jest.spyOn(locationService, 'findEvse').mockResolvedValue({
        ...TEST_EVSE,
        connectors: [TEST_EVSE.connectors[0], TEST_EVSE.connectors[0]],
      });

      await expect(
        service.startSession({
          ...TEST_START_SESSION_COMMAND,
          connector_id: null,
        })
      ).rejects.toThrow(ConnectorIdRequiredException);
    });
  });

  describe('stopSession', () => {
    it('should send a stop session command to the queue', async () => {
      jest
        .spyOn(database.session, 'findUnique')
        .mockResolvedValue({ ...TEST_ACTIVE_SESSION_ENTITY, status: 'ACTIVE' });

      const mockSendMessage = jest.spyOn(sqsClientService, 'sendMessage');

      const commandResponse = await service.stopSession(
        TEST_STOP_SESSION_COMMAND
      );

      expect(commandResponse).toEqual(TEST_COMMAND_RESPONSE_ACCEPTED);
      expect(mockSendMessage).toHaveBeenCalledWith(
        'http://localhost/000000/stop-session-commands',
        {
          messageBody: JSON.stringify(TEST_STOP_SESSION_COMMAND_MESSAGE),
        }
      );
    });

    it('should throw a session not found exception when stopping a session that does not exist', async () => {
      jest.spyOn(database.session, 'findUnique').mockResolvedValue(null);

      await expect(() =>
        service.stopSession(TEST_STOP_SESSION_COMMAND)
      ).rejects.toThrow(SessionNotFoundException);
    });

    it('should throw a session status inactive exception when stopping a session that is not active', async () => {
      jest.spyOn(database.session, 'findUnique').mockResolvedValue({
        ...TEST_ACTIVE_SESSION_ENTITY,
        status: 'COMPLETED',
      });

      await expect(() =>
        service.stopSession(TEST_STOP_SESSION_COMMAND)
      ).rejects.toThrow(SessionStatusInactiveException);
    });
  });

  describe('unlockConnector', () => {
    it('should send an unlock connector command to the queue', async () => {
      const mockFindEvse = jest
        .spyOn(locationService, 'findEvse')
        .mockResolvedValue(TEST_EVSE);
      const mockSendMessage = jest.spyOn(sqsClientService, 'sendMessage');

      const commandResponse = await service.unlockConnector(
        TEST_UNLOCK_CONNECTOR_COMMAND
      );

      expect(commandResponse).toEqual(TEST_COMMAND_RESPONSE_ACCEPTED);
      expect(mockFindEvse).toHaveBeenCalledWith(
        TEST_START_SESSION_COMMAND.location_id,
        TEST_START_SESSION_COMMAND.evse_uid
      );
      expect(mockSendMessage).toHaveBeenCalledWith(
        'http://localhost/000000/ocpi-service-unlock-connector-commands',
        {
          messageBody: JSON.stringify(TEST_UNLOCK_CONNECTOR_COMMAND_MESSAGE),
        }
      );
    });

    it('should throw an unlock connector not available exception if the evse does not support unlocking', async () => {
      jest
        .spyOn(locationService, 'findEvse')
        .mockResolvedValue({ ...TEST_EVSE, capabilities: [] });

      await expect(() =>
        service.unlockConnector(TEST_UNLOCK_CONNECTOR_COMMAND)
      ).rejects.toThrow(InvalidOrMissingParametersException);
    });
  });
});
