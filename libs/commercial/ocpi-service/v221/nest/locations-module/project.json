{"name": "commercial-ocpi-service-v221-nest-locations-module", "$schema": "../../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/ocpi-service/v221/nest/locations-module/src", "projectType": "library", "tags": ["commercial", "ocpi-service"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/ocpi-service/v221/nest/locations-module/jest.config.ts"}, "dependsOn": [{"projects": ["commercial-ocpi-service-v221-prisma-client"], "target": "generate-sources:prisma"}]}}}