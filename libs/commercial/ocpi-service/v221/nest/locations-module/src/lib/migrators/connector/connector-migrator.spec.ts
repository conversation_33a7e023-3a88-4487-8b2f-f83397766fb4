import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import { ConnectorMigrator } from './connector-migrator';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import { Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { Op } from 'sequelize';
import {
  PodUnitConnector,
  TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION,
} from '@experience/shared/sequelize/podadmin';
import { TEST_CONNECTOR_ENTITY_ID } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { includeOptions } from './connector-migrator';
import { mapPodUnitConnectorToConnector } from './connector-mapper';
import { mockDeep } from 'jest-mock-extended';
import MockDate from 'mockdate';

describe('Connector migrator', () => {
  let database: OCPIPrismaClient;
  let podUnitConnectors: typeof PodUnitConnector;
  let migrator: ConnectorMigrator;

  const logger = mockDeep<Logger>();
  const error = new Error('Oops');

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: 'POD_UNIT_CONNECTOR_REPOSITORY',
          useValue: PodUnitConnector,
        },
        {
          provide: 'ALS_PRISMA_OCPI_V221',
          useValue: new AsyncLocalStorage(),
        },
        ConfigService,
        ConnectorMigrator,
        HealthIndicatorService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
      ],
    }).compile();

    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    podUnitConnectors = module.get<typeof PodUnitConnector>(
      'POD_UNIT_CONNECTOR_REPOSITORY'
    );
    migrator = module.get<ConnectorMigrator>(ConnectorMigrator);

    module.useLogger(logger);

    MockDate.set(new Date());
  });

  it('should migrate a pod unit connector to a connector', async () => {
    const mockFindAll = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION]);
    const mockUpsert = (database.connector.upsert = jest.fn());
    const expectedCreate = mapPodUnitConnectorToConnector(
      TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION
    );

    const { id, ...expectedUpdate } = expectedCreate;

    await migrator.migrate(new Date());

    expect(mockFindAll).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: {
        [Op.and]: [
          { '$unit.podLocation.is_public$': true },
          { '$unit.podLocation.updated_at$': { [Op.gt]: new Date() } },
        ],
      },
    });

    expect(mockUpsert).toHaveBeenCalledWith({
      create: expectedCreate,
      update: expectedUpdate,
      where: { id },
    });
  });

  it('should catch errors during the migration task and log a warning', async () => {
    jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValueOnce([TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION]);
    database.connector.upsert = jest.fn().mockRejectedValueOnce(error);

    await migrator.migrate(new Date());

    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      {
        error,
        id: TEST_CONNECTOR_ENTITY_ID,
      },
      'failed to migrate connector',
      'ConnectorMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_CONNECTOR_ENTITY_ID],
      },
      'failed to migrate 1 connectors',
      'ConnectorMigrator'
    );
  });

  it('should continue with the migration task after an error is thrown', async () => {
    jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION,
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION,
      ]);
    database.connector.upsert = jest
      .fn()
      .mockRejectedValueOnce(error)
      .mockResolvedValueOnce(undefined);

    await migrator.migrate(new Date());

    expect(logger.log).toHaveBeenNthCalledWith(
      3,
      'migrated 1 connectors',
      'ConnectorMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      { failedMigrationIds: [TEST_CONNECTOR_ENTITY_ID] },
      'failed to migrate 1 connectors',
      'ConnectorMigrator'
    );
  });

  it('should migrate connectors by ID', async () => {
    const podLocationId = 789;
    const mockFindAll = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION]);
    const mockUpsert = (database.connector.upsert = jest.fn());
    const expectedCreate = mapPodUnitConnectorToConnector(
      TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION
    );
    const { id, ...expectedUpdate } = expectedCreate;

    await migrator.migrateConnectors(podLocationId);

    expect(mockFindAll).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: {
        [Op.and]: [
          { '$unit.pod_location_id$': podLocationId },
          { '$unit.podLocation.is_public$': true },
        ],
      },
    });
    expect(mockUpsert).toHaveBeenCalledWith({
      create: expectedCreate,
      update: expectedUpdate,
      where: { id },
    });
    expect(logger.log).toHaveBeenCalledWith(
      { podLocationId },
      'migrating connectors',
      'ConnectorMigrator'
    );
  });

  it('should handle errors during connector migration by ID', async () => {
    const podLocationId = 789;
    jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION]);
    database.connector.upsert = jest.fn().mockRejectedValueOnce(error);

    await migrator.migrateConnectors(podLocationId);

    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      {
        error,
        id: TEST_CONNECTOR_ENTITY_ID,
      },
      'failed to migrate connector',
      'ConnectorMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_CONNECTOR_ENTITY_ID],
      },
      'failed to migrate 1 connectors',
      'ConnectorMigrator'
    );
  });

  it('should handle empty result when pod location ID has no connectors', async () => {
    const podLocationId = 999;
    jest.spyOn(podUnitConnectors, 'findAll').mockResolvedValue([]);

    await migrator.migrateConnectors(podLocationId);

    expect(logger.log).toHaveBeenCalledWith(
      'found 0 connectors',
      'ConnectorMigrator'
    );
    expect(logger.log).toHaveBeenCalledWith(
      'migrated 0 connectors',
      'ConnectorMigrator'
    );
  });
});
