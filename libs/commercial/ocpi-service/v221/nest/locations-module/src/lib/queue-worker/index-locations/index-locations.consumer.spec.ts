import { AsyncLocalStorage } from 'async_hooks';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CredentialsStore } from '@experience/commercial/ocpi-service/v221/shared';
import { IndexLocationsConsumer } from './index-locations.consumer';
import { IndexLocationsConsumerModule } from './index-locations.consumer.module';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { TEST_EVSE_ENTITY } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';

describe('index locations consumer', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let configService: ConfigService;
  let consumer: IndexLocationsConsumer;
  let database: OCPIPrismaClient;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        IndexLocationsConsumerModule,
        await ConfigModule.forRoot({
          load: [
            () => ({
              SUPPORT_TOOL_API_BASE_URL: 'http://localhost:7102',
            }),
          ],
        }),
      ],
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    configService = module.get<ConfigService>(ConfigService);
    consumer = module.get<IndexLocationsConsumer>(IndexLocationsConsumer);
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(configService).toBeDefined();
    expect(consumer).toBeDefined();
    expect(database).toBeDefined();
  });

  it('should handle message', async () => {
    const mockUpsertSearchIndex = (database.searchIndex.upsert = jest.fn());
    const mockFetchResponse = jest
      .fn()
      .mockResolvedValueOnce({ data: { foo: 'bar' } });
    const mockFetch = jest.spyOn(global, 'fetch');
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: mockFetchResponse,
      status: 200,
    } as unknown as Response);

    await consumer.handle({
      Body: JSON.stringify({ ppid: TEST_EVSE_ENTITY.ppid }),
    });

    expect(mockUpsertSearchIndex).toHaveBeenCalledWith({
      create: {
        indexData: {
          data: {
            foo: 'bar',
          },
        },
        ppid: TEST_EVSE_ENTITY.ppid,
      },
      update: {
        indexData: {
          data: {
            foo: 'bar',
          },
        },
      },
      where: {
        ppid: TEST_EVSE_ENTITY.ppid,
      },
    });
  });
});
