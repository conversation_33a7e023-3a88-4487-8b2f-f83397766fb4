import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  Configuration as ConnectivityServiceClientConfiguration,
  ConnectivityStatusApi as ConnectivityServiceClientConnectivityStatusApi,
} from '@experience/shared/axios/connectivity-service-client';
import { LocationsModule } from '../../locations.module';
import { Module } from '@nestjs/common';
import {
  QueueConsumerService,
  SqsConsumerModule,
} from '@experience/shared/nest/aws/sqs-module';
import {
  RefreshStatusesConsumer,
  RefreshStatusesQueueConsumerService,
} from './refresh-statuses.consumer';
import { SqsConsumerModuleOptions } from '@experience/shared/nest/aws/sqs-module';
import axios from 'axios';

@Module({
  imports: [
    ConfigModule,
    LocationsModule,
    SqsConsumerModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService): SqsConsumerModuleOptions => ({
        disabled: !config.get<string>('REFRESH_STATUSES_QUEUE_URL'),
        queueUrl: config.get<string>('REFRESH_STATUSES_QUEUE_URL'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    RefreshStatusesConsumer,
    {
      provide: RefreshStatusesQueueConsumerService,
      useExisting: QueueConsumerService,
    },
    {
      inject: [ConfigService],
      provide: ConnectivityServiceClientConnectivityStatusApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('CONNECTIVITY_STATUS_API_TIMEOUT', 10000),
        });
        return new ConnectivityServiceClientConnectivityStatusApi(
          new ConnectivityServiceClientConfiguration(),
          configService.get('CONNECTIVITY_STATUS_API_BASE_URL'),
          client
        );
      },
    },
  ],
})
export class RefreshStatusesConsumerModule {}
