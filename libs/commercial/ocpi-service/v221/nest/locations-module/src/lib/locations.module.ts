import { ConfigModule } from '@nestjs/config';
import {
  CredentialsMiddleware,
  CredentialsModule,
} from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { LocationController } from './location.controller';
import { LocationService } from './location.service';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { OCPIPrismaClientModule } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PodadminSequelizeModule } from '@experience/shared/sequelize/podadmin';
import { PushEvseUpdatesProducer } from './producer/push-evse-updates/push-evse-updates.producer';
import { SqsClientModule } from '@experience/shared/nest/aws/sqs-module';
import { UniqueMessageIdsMiddleware } from '@experience/commercial/ocpi-service/v221/shared';

@Module({
  imports: [
    ConfigModule,
    CredentialsModule,
    OCPIPrismaClientModule,
    PodadminSequelizeModule,
    SqsClientModule.register(process.env?.['AWS_ENDPOINT_URL']),
  ],
  controllers: [LocationController],
  providers: [LocationService, PushEvseUpdatesProducer],
  exports: [LocationService, PushEvseUpdatesProducer],
})
export class LocationsModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CredentialsMiddleware, UniqueMessageIdsMiddleware)
      .forRoutes(LocationController);
  }
}
