import { ConfigModule, ConfigService } from '@nestjs/config';
import { CredentialsModule } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { LocationsModule } from '../../locations.module';
import { Module } from '@nestjs/common';
import {
  PushLocationsConsumer,
  PushLocationsQueueConsumerService,
} from './push-locations.consumer';
import {
  QueueConsumerService,
  SqsConsumerModule,
} from '@experience/shared/nest/aws/sqs-module';
import { SqsConsumerModuleOptions } from '@experience/shared/nest/aws/sqs-module';

@Module({
  imports: [
    ConfigModule,
    CredentialsModule,
    LocationsModule,
    SqsConsumerModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService): SqsConsumerModuleOptions => ({
        disabled: !config.get<string>('PUSH_LOCATIONS_QUEUE_URL'),
        queueUrl: config.get<string>('PUSH_LOCATIONS_QUEUE_URL'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    PushLocationsConsumer,
    {
      provide: PushLocationsQueueConsumerService,
      useExisting: QueueConsumerService,
    },
  ],
})
export class PushLocationsConsumerModule {}
