import { AsyncLocalStorage } from 'async_hooks';
import { Authorisers } from '@experience/shared/sequelize/podadmin';
import {
  CdrToken,
  CredentialsNotFoundException,
  CredentialsStore,
  OcpiCredentials,
  OcpiCredentialsSchema,
  Role,
  Token,
} from '@experience/commercial/ocpi-service/v221/shared';
import {
  Credentials as CredentialsEntity,
  CredentialsRole as CredentialsRoleEntity,
  OCPIPrismaClient,
  Prisma,
} from '@experience/commercial/ocpi-service/v221/prisma/client';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { obfuscateToken } from './credentials-utils';
import { v4 as uuid } from 'uuid';

export const credentialsDeepIncludeOptions = {
  include: {
    roles: true,
  },
};

type CredentialsWithRole = Prisma.CredentialsGetPayload<
  typeof credentialsDeepIncludeOptions
>;

@Injectable()
export class CredentialService {
  private readonly logger = new Logger(CredentialService.name);

  constructor(
    private readonly als: AsyncLocalStorage<CredentialsStore>,
    private readonly database: OCPIPrismaClient,
    @Inject('AUTHORISERS_REPOSITORY')
    private readonly authorisers: typeof Authorisers
  ) {}

  /**
   Create flow:
   1. The client sends us credentials to use their system (TOKEN_B) using the initial credentials we provided them (TOKEN_A)
   2. We store the credentials in our database
   3. We generate new credentials for the client to use our system (TOKEN_C)
   4. We link the two sets of credentials together
   5. We delete the initial credentials that the client used to access our system (TOKEN_A)
   6. We return the generated credentials to the client
   **/
  async create(
    baseUrl: string,
    request: OcpiCredentials
  ): Promise<OcpiCredentials> {
    const { serverCredentials } = this.als.getStore() as CredentialsStore;

    this.logger.log(
      {
        credentials: obfuscateToken(serverCredentials),
        request: obfuscateToken(request),
      },
      'creating credentials'
    );

    const clientCredentials = await this.persistCredentials(request);
    const generatedCredentials = this.generateCredentials(baseUrl);
    const newServerCredentials = await this.persistCredentials(
      generatedCredentials,
      clientCredentials
    );
    await this.linkCredentials(newServerCredentials, clientCredentials);
    await this.delete();
    await this.createAuthoriser(newServerCredentials);

    return generatedCredentials;
  }

  /**
   Update flow:
   1. The client sends us updated credentials to use their system (TOKEN_B) using the credentials we provided them (TOKEN_C)
   2. We update the credentials we hold for them in our database
   3. We generate a new token and update the existing credentials for the client to use our system with the new token
   4. We return the updated credentials to the client
   **/
  async update(
    baseUrl: string,
    request: OcpiCredentials
  ): Promise<OcpiCredentials> {
    const { serverCredentials, clientCredentials } =
      this.als.getStore() as CredentialsStore;

    this.logger.log(
      {
        credentials: obfuscateToken(serverCredentials),
        request: obfuscateToken(request),
      },
      'updating credentials'
    );

    if (!clientCredentials) {
      throw new CredentialsNotFoundException();
    }

    await this.updateCredentials(clientCredentials.token, request);
    const generatedCredentials = this.generateCredentials(baseUrl);
    await this.updateCredentials(serverCredentials.token, generatedCredentials);

    return generatedCredentials;
  }

  /**
   Here we retrieve the credentials from async local storage that were retrieved and stored via the middleware
   The retrieved credentials should be the credentials that the client will use to call our system
   **/
  async retrieve(): Promise<OcpiCredentials> {
    const { serverCredentials } = this.als.getStore() as CredentialsStore;
    this.logger.log(
      { credentials: obfuscateToken(serverCredentials) },
      'finding credentials'
    );
    return serverCredentials;
  }

  async retrieveAccessFlags(decodedToken: string): Promise<{
    enableCdrsModule: boolean;
    enableCommandsModule: boolean;
    enableSessionsModule: boolean;
    enableTokensModule: boolean;
  }> {
    return this.database.credentials.findFirstOrThrow({
      select: {
        enableCdrsModule: true,
        enableCommandsModule: true,
        enableSessionsModule: true,
        enableTokensModule: true,
      },
      where: {
        token: decodedToken,
        deletedAt: null,
      },
    });
  }

  async delete(): Promise<void> {
    const { serverCredentials, clientCredentials } =
      this.als.getStore() as CredentialsStore;
    this.logger.log(
      { credentials: obfuscateToken(serverCredentials) },
      'deleting credentials'
    );

    const credentialsEntity = await this.database.credentials.update({
      data: { deletedAt: new Date() },
      where: { token: serverCredentials.token },
    });

    if (clientCredentials) {
      await this.database.credentials.update({
        data: { deletedAt: new Date() },
        where: { token: clientCredentials.token },
      });
    }

    await this.destroyAuthoriserIfExists(credentialsEntity);
  }

  /**
  This retrieves all credentials in the database.
  This function should only be used by scheduled tasks and not exposed via endpoint
  **/
  async retrieveAll(): Promise<OcpiCredentials[]> {
    const credentialsEntities = await this.database.credentials.findMany({
      ...credentialsDeepIncludeOptions,
      where: {
        deletedAt: null,
      },
    });
    return credentialsEntities.map((entity) => this.mapCredentials(entity));
  }

  /**
   This retrieves a set of credentials from the database via token and returns whether they can be used
   to pull tokens from the client
   **/
  async shouldPullTokens(token: string): Promise<boolean> {
    const credentialsEntity = await this.database.credentials.findUnique({
      where: { token, deletedAt: null },
    });
    return credentialsEntity?.pullTokens ?? false;
  }

  /**
   This is used by the middleware to retrieve both client and server credentials
   from the database to store in the async local storage
   **/
  async createStoreFromAuthToken(token: string): Promise<CredentialsStore> {
    const serverCredentialsEntity = await this.database.credentials.findUnique({
      ...credentialsDeepIncludeOptions,
      where: { token, deletedAt: null },
    });

    if (!serverCredentialsEntity) {
      throw new CredentialsNotFoundException();
    }

    const clientCredentialsEntity = serverCredentialsEntity?.clientCredentialsId
      ? await this.database.credentials.findUnique({
          ...credentialsDeepIncludeOptions,
          where: {
            id: serverCredentialsEntity.clientCredentialsId,
            deletedAt: null,
          },
        })
      : null;

    return {
      serverCredentials: this.mapCredentials(serverCredentialsEntity),
      serverCredentialsId: serverCredentialsEntity.id,
      clientCredentials: clientCredentialsEntity
        ? this.mapCredentials(clientCredentialsEntity)
        : null,
      clientCredentialsId: clientCredentialsEntity
        ? clientCredentialsEntity.id
        : null,
    };
  }

  async createStoreFromToken(
    token: Token | CdrToken
  ): Promise<CredentialsStore> {
    const clientCredentialsEntity = await this.database.credentials.findFirst({
      ...credentialsDeepIncludeOptions,
      where: {
        roles: {
          every: {
            partyId: token.party_id,
            countryCode: token.country_code,
            role: Role.enum.EMSP,
          },
        },
        deletedAt: null,
      },
    });

    if (!clientCredentialsEntity) {
      throw new CredentialsNotFoundException();
    }

    const serverCredentialsEntity = clientCredentialsEntity?.serverCredentialsId
      ? await this.database.credentials.findUnique({
          ...credentialsDeepIncludeOptions,
          where: {
            id: clientCredentialsEntity.serverCredentialsId,
            deletedAt: null,
          },
        })
      : null;

    if (!serverCredentialsEntity) {
      throw new CredentialsNotFoundException();
    }

    return {
      serverCredentials: this.mapCredentials(serverCredentialsEntity),
      serverCredentialsId: serverCredentialsEntity.id,
      clientCredentials: this.mapCredentials(clientCredentialsEntity),
      clientCredentialsId: clientCredentialsEntity.id,
      pushCdrsReceiver: clientCredentialsEntity.pushCdrsReceiver,
      pushLocationsReceiver: clientCredentialsEntity.pushLocationsReceiver,
      pushSessionsReceiver: clientCredentialsEntity.pushSessionsReceiver,
      pushTariffsReceiver: clientCredentialsEntity.pushTariffsReceiver,
    };
  }

  async createStoresForPushCdrs(token?: CdrToken): Promise<CredentialsStore[]> {
    const credentials = await this.database.credentials.findMany({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushCdrs: true,
        pushCdrsReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });

    const stores = credentials.map((credential) => {
      const { serverCredentials, ...clientCredentials } = credential;

      if (!clientCredentials || !serverCredentials) {
        throw new CredentialsNotFoundException();
      }

      return {
        serverCredentials: this.mapCredentials(serverCredentials),
        serverCredentialsId: serverCredentials.id,
        clientCredentials: this.mapCredentials(clientCredentials),
        clientCredentialsId: clientCredentials.id,
        pushCdrsReceiver: clientCredentials.pushCdrsReceiver,
      };
    });

    if (token) {
      return stores.filter((store) =>
        store.clientCredentials?.roles.find(
          (role) =>
            role.country_code === token.country_code &&
            role.party_id === token.party_id
        )
      );
    }

    return stores;
  }

  async createStoresForPushLocations(): Promise<CredentialsStore[]> {
    const credentials = await this.database.credentials.findMany({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushLocations: true,
        pushLocationsReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });

    return credentials.map((credential) => {
      const { serverCredentials, ...clientCredentials } = credential;

      if (!clientCredentials || !serverCredentials) {
        throw new CredentialsNotFoundException();
      }

      return {
        serverCredentials: this.mapCredentials(serverCredentials),
        serverCredentialsId: serverCredentials.id,
        clientCredentials: this.mapCredentials(clientCredentials),
        clientCredentialsId: clientCredentials.id,
        pushLocationsReceiver: clientCredentials.pushLocationsReceiver,
      };
    });
  }

  async createStoresForPushEvses(): Promise<CredentialsStore[]> {
    const credentials = await this.database.credentials.findMany({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushEvses: true,
        pushEvsesReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });

    return credentials.map((credential) => {
      const { serverCredentials, ...clientCredentials } = credential;

      if (!clientCredentials || !serverCredentials) {
        throw new CredentialsNotFoundException();
      }

      return {
        serverCredentials: this.mapCredentials(serverCredentials),
        serverCredentialsId: serverCredentials.id,
        clientCredentials: this.mapCredentials(clientCredentials),
        clientCredentialsId: clientCredentials.id,
        pushEvsesReceiver: clientCredentials.pushEvsesReceiver,
      };
    });
  }

  async createStoresForPushTariffs(): Promise<CredentialsStore[]> {
    const credentials = await this.database.credentials.findMany({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushTariffs: true,
        pushTariffsReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });

    return credentials.map((credential) => {
      const { serverCredentials, ...clientCredentials } = credential;

      if (!clientCredentials || !serverCredentials) {
        throw new CredentialsNotFoundException();
      }

      return {
        serverCredentials: this.mapCredentials(serverCredentials),
        serverCredentialsId: serverCredentials.id,
        clientCredentials: this.mapCredentials(clientCredentials),
        clientCredentialsId: clientCredentials.id,
        pushTariffsReceiver: clientCredentials.pushTariffsReceiver,
      };
    });
  }

  private mapCredentials(entity: CredentialsWithRole): OcpiCredentials {
    return OcpiCredentialsSchema.parse({
      roles: entity.roles.map((roleEntity: CredentialsRoleEntity) => ({
        business_details: {
          name: roleEntity.businessDetailsName,
        },
        country_code: roleEntity.countryCode,
        party_id: roleEntity.partyId,
        role: roleEntity.role,
      })),
      token: entity.token,
      url: entity.url,
    });
  }

  private async persistCredentials(
    credentials: OcpiCredentials,
    clientCredentials?: CredentialsEntity
  ): Promise<CredentialsEntity> {
    return this.database.credentials.create({
      data: {
        object: JSON.parse(JSON.stringify(credentials)),
        roles: {
          create: credentials.roles.map((roleRequest) => ({
            businessDetailsName: roleRequest.business_details.name,
            countryCode: roleRequest.country_code,
            partyId: roleRequest.party_id,
            role: roleRequest.role,
          })),
        },
        clientCredentialsId: clientCredentials?.id,
        token: credentials.token,
        url: credentials.url,
      },
    });
  }

  private async updateCredentials(
    token: string,
    credentials: OcpiCredentials
  ): Promise<void> {
    await this.database.credentials.update({
      data: {
        object: JSON.parse(JSON.stringify(credentials)),
        roles: {
          create: credentials.roles.map((roleRequest) => ({
            businessDetailsName: roleRequest.business_details.name,
            countryCode: roleRequest.country_code,
            partyId: roleRequest.party_id,
            role: roleRequest.role,
          })),
        },
        token: credentials.token,
        url: credentials.url,
      },
      where: { token, deletedAt: null },
    });
  }

  private generateCredentials(baseUrl: string): OcpiCredentials {
    return {
      roles: [
        {
          business_details: {
            name: 'Pod Point',
          },
          country_code: 'GB',
          role: 'CPO',
          party_id: 'POD',
        },
      ],
      token: uuid(),
      url: `${baseUrl}/versions`,
    };
  }

  private async linkCredentials(
    serverCredentials: CredentialsEntity,
    clientCredentials: CredentialsEntity
  ): Promise<void> {
    await this.database.credentials.update({
      where: { id: clientCredentials.id },
      data: { serverCredentialsId: serverCredentials.id },
    });
  }

  private async createAuthoriser(credentialsEntity: CredentialsEntity) {
    await this.authorisers.create({
      type: 'ocpi',
      uid: credentialsEntity.id,
    });
  }

  private async destroyAuthoriserIfExists(
    credentialsEntity: CredentialsEntity
  ) {
    const authoriser = await this.authorisers.findOne({
      where: {
        type: 'ocpi',
        uid: credentialsEntity.id,
      },
    });
    if (authoriser) {
      await this.authorisers.destroy({
        where: {
          type: 'ocpi',
          uid: credentialsEntity.id,
        },
      });
    }
  }
}
