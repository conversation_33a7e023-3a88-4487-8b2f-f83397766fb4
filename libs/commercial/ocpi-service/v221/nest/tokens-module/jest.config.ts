export default {
  displayName: 'commercial-ocpi-service-v221-nest-tokens-module',
  preset: '../../../../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory:
    '../../../../../../coverage/libs/commercial/ocpi-service/v221/nest/tokens-module',
  testPathIgnorePatterns: ['index.spec.ts', 'tokens.module.spec.ts'],
};
