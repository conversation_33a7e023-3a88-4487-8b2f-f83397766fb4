import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import {
  CredentialsDoNotMatchParamsException,
  CredentialsStore,
  InvalidOrMissingParametersException,
  TokenNotFoundException,
} from '@experience/commercial/ocpi-service/v221/shared';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import {
  TEST_CREDENTIALS_B,
  TEST_CREDENTIALS_STORE,
  TEST_TOKEN,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { TEST_TOKEN_ENTITY } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { TokenService } from './token.service';

describe('TokenService', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let database: OCPIPrismaClient;
  let service: TokenService;

  const {
    country_code: requestCountryCode,
    party_id: requestPartyId,
    uid: requestUid,
    type: requestType,
    ...TEST_PARTIAL_TOKEN
  } = TEST_TOKEN;

  const {
    id,
    countryCode: entityCountryCode,
    created: entityCreated,
    partyId: entityPartyId,
    uid: entityUid,
    type: entityType,
    ...TEST_PARTIAL_TOKEN_ENTITY
  } = TEST_TOKEN_ENTITY;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        ConfigService,
        HealthIndicatorService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        TokenService,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    service = module.get<TokenService>(TokenService);
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(database).toBeDefined();
    expect(service).toBeDefined();
  });

  it.each([
    ['with credentials store populated', TEST_CREDENTIALS_STORE],
    ['with credentials store not populated', undefined],
  ])(
    'should update a token if token already exists %',
    async (_, credentialsStore) => {
      const mockCredentials = jest
        .spyOn(als, 'getStore')
        .mockReturnValueOnce(credentialsStore);
      const mockFind = (database.token.findFirst = jest
        .fn()
        .mockResolvedValueOnce(TEST_TOKEN_ENTITY));
      const mockUpdate = (database.token.update = jest.fn());
      const mockCreate = (database.token.create = jest.fn());

      await service.createOrUpdateToken(
        TEST_TOKEN.country_code,
        TEST_TOKEN.party_id,
        TEST_TOKEN.uid,
        TEST_TOKEN.type,
        { ...TEST_TOKEN, valid: !TEST_TOKEN.valid }
      );

      expect(mockCredentials).toHaveBeenCalledTimes(1);
      expect(mockFind).toHaveBeenCalledWith({
        where: {
          countryCode: TEST_TOKEN.country_code,
          partyId: TEST_TOKEN.party_id,
          uid: TEST_TOKEN.uid,
          type: TEST_TOKEN.type,
        },
      });
      expect(mockUpdate).toHaveBeenCalledWith({
        where: {
          id: TEST_TOKEN_ENTITY.id,
        },
        data: {
          ...TEST_TOKEN_ENTITY,
          created: undefined,
          id: undefined,
          valid: !TEST_TOKEN_ENTITY.valid,
        },
      });
      expect(mockCreate).not.toHaveBeenCalled();
    }
  );

  it.each([
    ['with credentials store populated', TEST_CREDENTIALS_STORE],
    ['with credentials store not populated', undefined],
  ])(
    'should create a token if token does not exist %s',
    async (_, credentialsStore) => {
      const mockCredentials = jest
        .spyOn(als, 'getStore')
        .mockReturnValueOnce(credentialsStore);
      const mockFind = (database.token.findFirst = jest
        .fn()
        .mockResolvedValueOnce(null));
      const mockUpdate = (database.token.update = jest.fn());
      const mockCreate = (database.token.create = jest.fn());

      await service.createOrUpdateToken(
        TEST_TOKEN.country_code,
        TEST_TOKEN.party_id,
        TEST_TOKEN.uid,
        TEST_TOKEN.type,
        TEST_TOKEN
      );

      expect(mockCredentials).toHaveBeenCalledTimes(1);
      expect(mockFind).toHaveBeenCalledWith({
        where: {
          countryCode: TEST_TOKEN.country_code,
          partyId: TEST_TOKEN.party_id,
          uid: TEST_TOKEN.uid,
          type: TEST_TOKEN.type,
        },
      });
      expect(mockUpdate).not.toHaveBeenCalled();
      expect(mockCreate).toHaveBeenCalledWith({
        data: { ...TEST_TOKEN_ENTITY, created: undefined, id: undefined },
      });
    }
  );

  it('should throw an error if party id or country code in client credentials do not match the URL params when creating/updating token', async () => {
    jest.spyOn(als, 'getStore').mockReturnValueOnce({
      ...TEST_CREDENTIALS_STORE,
      clientCredentials: {
        ...TEST_CREDENTIALS_B,
        roles: [
          {
            role: 'EMSP',
            party_id: 'EMP',
            country_code: 'DE',
            business_details: {
              name: 'Test EMSP',
            },
          },
        ],
      },
    });

    await expect(() =>
      service.createOrUpdateToken(
        TEST_TOKEN.country_code,
        TEST_TOKEN.party_id,
        TEST_TOKEN.uid,
        TEST_TOKEN.type,
        TEST_TOKEN
      )
    ).rejects.toThrow(CredentialsDoNotMatchParamsException);
  });

  it.each([
    [
      'party ids',
      `${TEST_TOKEN.country_code}FOO`,
      TEST_TOKEN.party_id,
      TEST_TOKEN.uid,
      TEST_TOKEN.type,
      [
        'country codes',
        TEST_TOKEN.country_code,
        `${TEST_TOKEN.party_id}FOO`,
        TEST_TOKEN.uid,
        TEST_TOKEN.type,
      ],
    ],
  ])(
    'should throw an error if %s do not match when creating/updating token',
    async (_, countryCode, partyId, uid, type) => {
      await expect(() =>
        service.createOrUpdateToken(countryCode, partyId, uid, type, TEST_TOKEN)
      ).rejects.toThrow(InvalidOrMissingParametersException);
    }
  );

  it('should throw an error if token types do not match when creating/updating token', async () => {
    await expect(() =>
      service.createOrUpdateToken(
        TEST_TOKEN.country_code,
        TEST_TOKEN.party_id,
        TEST_TOKEN.uid,
        TEST_TOKEN.type + 'FOO',
        TEST_TOKEN
      )
    ).rejects.toThrow(InvalidOrMissingParametersException);
  });

  it('should patch existing token', async () => {
    const mockCredentials = jest
      .spyOn(als, 'getStore')
      .mockReturnValueOnce(TEST_CREDENTIALS_STORE);
    const mockFind = jest
      .spyOn(database.token, 'findFirst')
      .mockResolvedValueOnce(TEST_TOKEN_ENTITY);
    const mockUpdate = jest
      .spyOn(database.token, 'update')
      .mockResolvedValueOnce(TEST_TOKEN_ENTITY);

    await service.patchExistingToken(
      TEST_TOKEN_ENTITY.countryCode,
      TEST_TOKEN_ENTITY.partyId,
      TEST_TOKEN_ENTITY.uid,
      TEST_TOKEN_ENTITY.type,
      TEST_PARTIAL_TOKEN
    );

    expect(mockCredentials).toHaveBeenCalledTimes(1);
    expect(mockFind).toHaveBeenCalledWith({
      where: {
        countryCode: TEST_TOKEN_ENTITY.countryCode,
        partyId: TEST_TOKEN_ENTITY.partyId,
        uid: TEST_TOKEN_ENTITY.uid,
        type: TEST_TOKEN_ENTITY.type,
      },
    });
    expect(mockUpdate).toHaveBeenCalledWith({
      where: {
        id: TEST_TOKEN_ENTITY.id,
      },
      data: TEST_PARTIAL_TOKEN_ENTITY,
    });
  });

  it.each([
    [
      'party ids',
      `${TEST_TOKEN.country_code}FOO`,
      TEST_TOKEN.party_id,
      TEST_TOKEN.uid,
      TEST_TOKEN.type,
      [
        'country codes',
        TEST_TOKEN.country_code,
        `${TEST_TOKEN.party_id}FOO`,
        TEST_TOKEN.uid,
        TEST_TOKEN.type,
      ],
    ],
  ])(
    'should throw an error if %s do not match when patching existing token',
    async (_, countryCode, partyId, uid, type) => {
      await expect(() =>
        service.patchExistingToken(countryCode, partyId, uid, type, {
          ...TEST_PARTIAL_TOKEN,
          country_code: requestCountryCode,
          party_id: requestPartyId,
          uid: requestUid,
          type: requestType,
        })
      ).rejects.toThrow(InvalidOrMissingParametersException);
    }
  );

  it('should throw an error if party id or country code in client credentials do not match the URL params when patching token', async () => {
    jest.spyOn(als, 'getStore').mockReturnValueOnce({
      ...TEST_CREDENTIALS_STORE,
      clientCredentials: {
        ...TEST_CREDENTIALS_B,
        roles: [
          {
            role: 'EMSP',
            party_id: 'EMP',
            country_code: 'DE',
            business_details: {
              name: 'Test EMSP',
            },
          },
        ],
      },
    });

    await expect(() =>
      service.patchExistingToken(
        TEST_TOKEN_ENTITY.countryCode,
        TEST_TOKEN_ENTITY.partyId,
        TEST_TOKEN_ENTITY.uid,
        TEST_TOKEN_ENTITY.type,
        TEST_PARTIAL_TOKEN
      )
    ).rejects.toThrow(CredentialsDoNotMatchParamsException);
  });

  it('should throw an error if token is not found when patching token', async () => {
    jest.spyOn(als, 'getStore').mockReturnValueOnce(TEST_CREDENTIALS_STORE);
    jest.spyOn(database.token, 'findFirst').mockResolvedValueOnce(null);

    await expect(() =>
      service.patchExistingToken(
        TEST_TOKEN_ENTITY.countryCode,
        TEST_TOKEN_ENTITY.partyId,
        TEST_TOKEN_ENTITY.uid,
        TEST_TOKEN_ENTITY.type,
        TEST_PARTIAL_TOKEN
      )
    ).rejects.toThrow(TokenNotFoundException);
  });

  it('should find token', async () => {
    const mockCredentials = jest
      .spyOn(als, 'getStore')
      .mockReturnValueOnce(TEST_CREDENTIALS_STORE);
    const mockFind = (database.token.findFirst = jest
      .fn()
      .mockResolvedValueOnce(TEST_TOKEN_ENTITY));

    expect(
      await service.findToken(
        TEST_TOKEN_ENTITY.countryCode,
        TEST_TOKEN_ENTITY.partyId,
        TEST_TOKEN_ENTITY.uid,
        TEST_TOKEN_ENTITY.type
      )
    ).toEqual(TEST_TOKEN);

    expect(mockCredentials).toHaveBeenCalledTimes(1);
    expect(mockFind).toHaveBeenCalledWith({
      where: {
        countryCode: TEST_TOKEN_ENTITY.countryCode,
        partyId: TEST_TOKEN_ENTITY.partyId,
        uid: TEST_TOKEN_ENTITY.uid,
        type: TEST_TOKEN_ENTITY.type,
      },
    });
  });

  it('should throw an error if party id or country code in client credentials do not match the URL params when finding token', async () => {
    jest.spyOn(als, 'getStore').mockReturnValueOnce({
      ...TEST_CREDENTIALS_STORE,
      clientCredentials: {
        ...TEST_CREDENTIALS_B,
        roles: [
          {
            role: 'EMSP',
            party_id: 'EMP',
            country_code: 'DE',
            business_details: {
              name: 'Test EMSP',
            },
          },
        ],
      },
    });

    await expect(() =>
      service.findToken(
        TEST_TOKEN_ENTITY.countryCode,
        TEST_TOKEN_ENTITY.partyId,
        TEST_TOKEN_ENTITY.uid,
        TEST_TOKEN_ENTITY.type
      )
    ).rejects.toThrow(CredentialsDoNotMatchParamsException);
  });

  it('should throw an error if token is not found', async () => {
    jest.spyOn(als, 'getStore').mockReturnValueOnce(TEST_CREDENTIALS_STORE);
    jest.spyOn(database.token, 'findFirst').mockResolvedValueOnce(null);

    await expect(() =>
      service.findToken(
        TEST_TOKEN_ENTITY.countryCode,
        TEST_TOKEN_ENTITY.partyId,
        TEST_TOKEN_ENTITY.uid,
        TEST_TOKEN_ENTITY.type
      )
    ).rejects.toThrow(TokenNotFoundException);
  });
});
