import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const OcpiVersionNumber = z.enum(['2.0', '2.1', '2.1.1', '2.2.1']);

export const OcpiVersionSchema = z.object({
  version: OcpiVersionNumber,
  url: z.url(),
});

export const OcpiVersions = z.array(OcpiVersionSchema);

export const OcpiModuleId = z.enum([
  'cdrs',
  'chargingprofiles',
  'commands',
  'credentials',
  'hubclientinfo',
  'locations',
  'sessions',
  'tariffs',
  'tokens',
]);

export const OcpiInterfaceRole = z.enum(['SENDER', 'RECEIVER']);

export const OcpiEndpointSchema = z.object({
  identifier: OcpiModuleId,
  role: OcpiInterfaceRole,
  url: z.url(),
});

export const OcpiVersionDetailsSchema = z.object({
  version: OcpiVersionNumber,
  endpoints: z.array(OcpiEndpointSchema).nonempty(),
});

export class OcpiEndpointDto extends createZodDto(OcpiEndpointSchema) {}
export class OcpiVersionDto extends createZodDto(OcpiVersionSchema) {}
export class OcpiVersionDetailsDto extends createZodDto(
  OcpiVersionDetailsSchema
) {}

export type OcpiEndpoint = z.infer<typeof OcpiEndpointSchema>;
export type OcpiVersion = z.infer<typeof OcpiVersionSchema>;
export type OcpiVersionDetails = z.infer<typeof OcpiVersionDetailsSchema>;
