import { OcpiVersion, OcpiVersionDetails } from '../versions';

export const TEST_OCPI_VERSIONS: OcpiVersion[] = [
  { url: 'http://localhost:6102/ocpi/cpo/2.2.1', version: '2.2.1' },
];

export const TEST_OCPI_VERSION_DETAILS: OcpiVersionDetails = {
  endpoints: [
    {
      identifier: 'cdrs',
      role: 'SENDER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/cdrs',
    },
    {
      identifier: 'credentials',
      role: 'SENDER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/credentials',
    },
    {
      identifier: 'locations',
      role: 'SENDER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/locations',
    },
    {
      identifier: 'sessions',
      role: 'SENDER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/sessions',
    },
    {
      identifier: 'tariffs',
      role: 'SENDER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/tariffs',
    },
    {
      identifier: 'commands',
      role: 'RECEIVER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/commands',
    },
    {
      identifier: 'credentials',
      role: 'RECEIVER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/credentials',
    },
    {
      identifier: 'tokens',
      role: 'RECEIVER',
      url: 'http://localhost:6102/ocpi/cpo/2.2.1/tokens',
    },
  ],
  version: '2.2.1',
};
