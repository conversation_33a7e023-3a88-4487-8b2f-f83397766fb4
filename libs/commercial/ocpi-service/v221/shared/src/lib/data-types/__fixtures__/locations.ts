import { Connector, Evse, Location } from '../locations';
import { Page } from '@experience/shared/nest/utils';
import { TEST_CONNECTOR_ID, TEST_EVSE_UID, TEST_LOCATION_ID } from './common';
import { TEST_TARIFF } from './tariffs';
import dayjs from 'dayjs';

export const TEST_CONNECTOR: Connector = {
  format: 'SOCKET',
  id: TEST_CONNECTOR_ID,
  last_updated: dayjs().utc().toDate(),
  max_amperage: 200,
  max_electric_power: 300000,
  max_voltage: 400,
  power_type: 'DC',
  standard: 'IEC_62196_T2',
  tariff_ids: [TEST_TARIFF.id],
};

export const TEST_CONNECTORS_PAGE: Page<Connector> = {
  elements: [TEST_CONNECTOR],
  number: 1,
  size: 1,
  totalElements: 1,
  totalPages: 1,
};

export const TEST_EVSE: Evse = {
  capabilities: [
    'CONTACTLESS_CARD_SUPPORT',
    'REMOTE_START_STOP_CAPABLE',
    'START_SESSION_CONNECTOR_REQUIRED',
    'UNLOCK_CAPABLE',
  ],
  connectors: [TEST_CONNECTOR],
  coordinates: { latitude: '56.2340000', longitude: '-56.3420000' },
  evse_id: 'GB*POD*E*PG12345E1',
  last_updated: dayjs().utc().subtract(1, 'minute').toDate(),
  parking_restrictions: ['EV_ONLY'],
  physical_reference: 'test-name',
  status: 'AVAILABLE',
  uid: TEST_EVSE_UID,
};

export const TEST_EVSES_PAGE: Page<Evse> = {
  elements: [TEST_EVSE],
  number: 1,
  size: 1,
  totalElements: 1,
  totalPages: 1,
};

export const TEST_LOCATION: Location = {
  address: '28 - 42 Banner Street Islington',
  city: 'London',
  coordinates: {
    latitude: '16.7269330',
    longitude: '-48.8637200',
  },
  country: 'GBR',
  country_code: 'GB',
  directions: [{ language: 'en', text: 'Parking limited during the day' }],
  evses: [TEST_EVSE],
  facilities: ['SUPERMARKET'],
  id: TEST_LOCATION_ID,
  last_updated: dayjs().utc().toDate(),
  name: 'Discovery House',
  operator: {
    name: 'Pod Point',
    website: 'https://podenergy.com',
  },
  owner: {
    name: 'Registers of Scotland',
  },
  party_id: 'POD',
  postal_code: 'EC1Y 8QE',
  publish: true,
  time_zone: 'Europe/London',
};

export const TEST_LOCATIONS_PAGE: Page<Location> = {
  elements: [TEST_LOCATION],
  number: 1,
  size: 1,
  totalElements: 1,
  totalPages: 1,
};
