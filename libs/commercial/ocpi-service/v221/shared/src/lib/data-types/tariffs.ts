import { DisplayText, Price } from './common';
import { EnergyMix } from './locations';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const TariffDimensionType = z.enum([
  'ENERGY',
  'FLAT',
  'PARKING_TIME',
  'TIME',
]);

export const PriceComponent = z.object({
  type: TariffDimensionType,
  price: z.number().nonnegative(),
  vat: z.number().nonnegative().nullish(),
  step_size: z.number().int().nonnegative(),
});

export const DayOfWeek = z.enum([
  'MONDAY',
  'TUESDAY',
  'WEDNESDAY',
  'THURSDAY',
  'FRIDAY',
  'SATURDAY',
  'SUNDAY',
]);

export const ReservationRestrictionType = z.enum([
  'RESERVATION',
  'RESERVATION_EXPIRES',
]);

export const TariffRestrictions = z.object({
  start_time: z
    .string()
    .length(5)
    .regex(/([0-1][0-9]|2[0-3]):[0-5][0-9]/)
    .nullish(),
  end_time: z
    .string()
    .length(5)
    .regex(/([0-1][0-9]|2[0-3]):[0-5][0-9]/)
    .nullish(),
  start_date: z
    .string()
    .length(10)
    .regex(/([12][0-9]{3})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])/)
    .nullish(),
  end_date: z
    .string()
    .length(10)
    .regex(/([12][0-9]{3})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])/)
    .nullish(),
  min_kwh: z.number().nonnegative().nullish(),
  max_kwh: z.number().nonnegative().nullish(),
  min_current: z.number().nonnegative().nullish(),
  max_current: z.number().nonnegative().nullish(),
  min_power: z.number().nonnegative().nullish(),
  max_power: z.number().nonnegative().nullish(),
  min_duration: z.number().int().nonnegative().nullish(),
  max_duration: z.number().int().nonnegative().nullish(),
  day_of_week: z.array(DayOfWeek).nullish(),
  reservation: ReservationRestrictionType.nullish(),
});

export const TariffElementSchema = z.object({
  price_components: z.array(PriceComponent).nonempty(),
  restrictions: TariffRestrictions.nullish(),
});

export const TariffType = z.enum([
  'AD_HOC_PAYMENT',
  'PROFILE_CHEAP',
  'PROFILE_FAST',
  'PROFILE_GREEN',
  'REGULAR',
]);

export const TariffSchema = z.object({
  id: z.string().max(36),
  country_code: z.string().length(2),
  party_id: z.string().max(3),
  currency: z.string().length(3),
  type: TariffType.nullish(),
  tariff_alt_text: z.array(DisplayText).nullish(),
  tariff_alt_url: z.url().nullish(),
  min_price: Price.nullish(),
  max_price: Price.nullish(),
  elements: z.array(TariffElementSchema).min(1),
  energy_mix: EnergyMix.nullish(),
  start_date_time: z.coerce.date().nullish(),
  end_date_time: z.coerce.date().nullish(),
  last_updated: z.coerce.date(),
});

export class TariffDto extends createZodDto(TariffSchema) {}
export class TariffElementDto extends createZodDto(TariffElementSchema) {}

export type Tariff = z.infer<typeof TariffSchema>;
export type TariffElement = z.infer<typeof TariffElementSchema>;
