import { z } from 'zod';

export const Price = z.object({
  excl_vat: z.number().nonnegative(),
  incl_vat: z.number().nonnegative().nullish(),
});

export const TokenType = z.enum(['AD_HOC_USER', 'APP_USER', 'OTHER', 'RFID']);

export const DisplayText = z.object({
  language: z.string().length(2),
  text: z.string().max(512),
});

enum CommandResultMessage {
  START_SESSION_ACCEPTED = 'Start session command accepted.',
  STOP_SESSION_ACCEPTED = 'Stop session command accepted.',
}

export const DisplayMessage = z.nativeEnum(CommandResultMessage);

export const ImageCategory = z.enum([
  'CHARGER',
  'ENTRANCE',
  'LOCATION',
  'NETWORK',
  'OPERATOR',
  'OTHER',
  'OWNER',
]);

export const Image = z.object({
  url: z.url(),
  thumbnail: z.url().nullish(),
  category: ImageCategory,
  type: z.string().max(4),
  width: z.number().int().max(99999).nullish(),
  height: z.number().int().max(99999).nullish(),
});

export const BusinessDetails = z.object({
  name: z.string().max(100),
  website: z.url().nullish(),
  logo: Image.nullish(),
});
