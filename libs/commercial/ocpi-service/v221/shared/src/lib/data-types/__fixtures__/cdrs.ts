import { Cdr, CdrDimensionType, CdrLocation, ChargingPeriod } from '../cdrs';
import { TEST_CDR_ID, TEST_LOCATION_ID } from './common';
import { TEST_CDR_TOKEN, TEST_COMPLETED_SESSION } from './sessions';
import { TEST_CONNECTOR, TEST_EVSE, TEST_LOCATION } from './locations';
import { TEST_TARIFF } from './tariffs';
import dayjs from 'dayjs';

export const TEST_CDR_LOCATION: CdrLocation = {
  address: TEST_LOCATION.address,
  city: TEST_LOCATION.city,
  connector_format: TEST_CONNECTOR.format,
  connector_id: TEST_CONNECTOR.id,
  connector_power_type: TEST_CONNECTOR.power_type,
  connector_standard: TEST_CONNECTOR.standard,
  coordinates: TEST_LOCATION.coordinates,
  country: TEST_LOCATION.country,
  evse_id: TEST_EVSE.evse_id as string,
  evse_uid: TEST_EVSE.uid,
  id: TEST_LOCATION_ID,
  name: TEST_LOCATION.name,
  postal_code: TEST_LOCATION.postal_code,
};

export const TEST_CHARGING_PERIOD: ChargingPeriod = {
  dimensions: [
    {
      type: CdrDimensionType.enum.ENERGY,
      volume: TEST_COMPLETED_SESSION.kwh,
    },
  ],
  start_date_time: TEST_COMPLETED_SESSION.start_date_time,
  tariff_id: TEST_TARIFF.id,
};

export const TEST_CDR: Cdr = {
  auth_method: TEST_COMPLETED_SESSION.auth_method,
  authorization_reference: TEST_COMPLETED_SESSION.authorization_reference,
  cdr_location: TEST_CDR_LOCATION,
  cdr_token: TEST_CDR_TOKEN,
  charging_periods: [TEST_CHARGING_PERIOD],
  country_code: 'GB',
  currency: TEST_COMPLETED_SESSION.currency,
  end_date_time: TEST_COMPLETED_SESSION.end_date_time as Date,
  id: TEST_CDR_ID,
  last_updated: dayjs().utc().toDate(),
  party_id: 'POD',
  session_id: TEST_COMPLETED_SESSION.id,
  start_date_time: TEST_COMPLETED_SESSION.start_date_time,
  tariffs: [TEST_TARIFF],
  total_cost: {
    excl_vat: TEST_COMPLETED_SESSION.total_cost?.excl_vat as number,
    incl_vat: TEST_COMPLETED_SESSION.total_cost?.incl_vat as number,
  },
  total_energy: TEST_COMPLETED_SESSION.kwh,
  total_time: dayjs(TEST_COMPLETED_SESSION.end_date_time).diff(
    TEST_COMPLETED_SESSION.start_date_time,
    'hours'
  ),
};

export const TEST_CDR_PAGE = {
  elements: [TEST_CDR],
  number: 1,
  size: 1,
  totalElements: 1,
  totalPages: 1,
};
