import { v5 as uuid } from 'uuid';

// https://datatracker.ietf.org/doc/html/rfc4122#section-4.3
const namespace = '1628c735-51d5-48e4-8ca0-f4ad1e2c179a';

interface EvseIdProps {
  alphanumericSeparator?: string;
  countryCode?: string;
  cpoId?: string;
  id: string;
  ppid: string;
}

export const mapFieldsToMigrationUuid = (props: {
  [field: string]: string;
}): string => {
  const name = Object.keys(props)
    .map((key) => `${key}-${props[key]}`)
    .join('-');
  return uuid(name, namespace);
};

export const mapEvseId = ({
  alphanumericSeparator = 'E',
  countryCode = 'GB',
  cpoId = 'POD',
  id,
  ppid,
}: EvseIdProps): string =>
  `${countryCode}*${cpoId}*E*${ppid.replace(
    '-',
    ''
  )}${alphanumericSeparator}${id}`;

export const parseEvseId = (evseId: string): EvseIdProps => {
  const parts = evseId.split('*');
  const moreParts = parts[3].match(/[a-zA-Z]+|[0-9]+/g);

  return {
    alphanumericSeparator: moreParts?.at(2),
    countryCode: parts[0],
    cpoId: parts[1],
    id: moreParts?.at(3) as string,
    ppid: `${moreParts?.at(0)}-${moreParts?.at(1)}`,
  };
};
