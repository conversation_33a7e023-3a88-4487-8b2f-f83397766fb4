import { mapEvseId, mapFieldsToMigrationUuid, parseEvseId } from './utils';

describe('utils', () => {
  describe('map fields to migration uuid', () => {
    it.each([
      ['one field', { location: '1' }, '1715433a-104d-56e0-8e54-6a390aba2046'],
      [
        'two fields',
        { connector: '123', unit: '456' },
        '3fc66c42-e024-557a-9a8c-9f9edbddcec4',
      ],
      [
        'three fields',
        { connector: '123', unit: '456', chargePoint: '789' },
        'cae6867c-2a2f-585c-8ac6-a0754feeb0ee',
      ],
    ])('should map fields to migration id - %s', (_, props, migrationId) => {
      expect(mapFieldsToMigrationUuid(props)).toEqual(migrationId);
    });
  });

  describe('map EVSE ID', () => {
    it('should map EVSE ID', () => {
      expect(
        mapEvseId({
          alphanumericSeparator: 'E',
          countryCode: 'TS',
          cpoId: 'TEST',
          id: '1',
          ppid: 'PG-123456',
        })
      ).toEqual('TS*TEST*E*PG123456E1');
    });
  });

  describe('parse EVSE ID', () => {
    it.each(['PG-123456', 'PG-0000x'])(
      'should parse EVSE ID with ppid %s',
      (ppid) => {
        expect(parseEvseId(`TS*TEST*E*${ppid.replace('-', '')}E1`)).toEqual({
          alphanumericSeparator: 'E',
          countryCode: 'TS',
          cpoId: 'TEST',
          id: '1',
          ppid,
        });
      }
    );
  });
});
