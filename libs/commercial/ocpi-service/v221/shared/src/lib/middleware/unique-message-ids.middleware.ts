import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { OcpiResponse } from '../dto/response.dto';

@Injectable()
export class UniqueMessageIdsMiddleware implements NestMiddleware {
  private readonly logger = new Logger(UniqueMessageIdsMiddleware.name);

  async use(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<OcpiResponse<unknown> | void> {
    if (req.header('x-correlation-id')) {
      res.header('x-correlation-id', req.header('x-correlation-id'));
    }

    if (req.header('x-request-id')) {
      res.header('x-request-id', req.header('x-request-id'));
    }

    next();
  }
}
