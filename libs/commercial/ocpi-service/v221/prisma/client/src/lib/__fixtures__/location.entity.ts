import { Location } from '@prisma/clients/ocpi/v221';
import { Prisma } from '../..';
import { TEST_EVSE_ENTITY_DEEP } from './evse.entity';
import { TEST_LOCATION_ENTITY_ID } from './common';
import dayjs from 'dayjs';

export const TEST_LOCATION_ENTITY: Location = {
  address: '28 - 42 Banner Street Islington',
  chargingWhenClosed: true,
  created: dayjs().utc().toDate(),
  city: 'London',
  country: 'GBR',
  countryCode: 'GB',
  directions: ['Parking limited during the day'],
  facilities: ['SUPERMARKET'],
  id: TEST_LOCATION_ENTITY_ID,
  lastUpdated: dayjs().utc().toDate(),
  lastUpdatedInPodadmin: dayjs().utc().toDate(),
  latitude: new Prisma.Decimal(16.726933),
  longitude: new Prisma.Decimal(-48.86372),
  name: 'Discovery House',
  owner: 'Registers of Scotland',
  partyId: 'POD',
  podadminGroupId: 2,
  podadminSiteId: 1,
  postalCode: 'EC1Y 8QE',
  publish: true,
  timezone: 'Europe/London',
};

export const TEST_LOCATION_ENTITY_DEEP = {
  ...TEST_LOCATION_ENTITY,
  evses: [TEST_EVSE_ENTITY_DEEP],
};
