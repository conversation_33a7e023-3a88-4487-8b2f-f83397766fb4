import { DayOfWeek, Prisma, TariffDimensionType, TariffElement } from '../..';
import { TEST_TARIFF_ELEMENT_ENTITY_ID, TEST_TARIFF_ENTITY_ID } from './common';

export const TEST_TARIFF_ELEMENT_ENTITY: TariffElement = {
  dayOfWeek: [
    DayOfWeek.MONDAY,
    DayOfWeek.TUESDAY,
    DayOfWeek.WEDNESDAY,
    DayOfWeek.THURSDAY,
    DayOfWeek.FRIDAY,
  ],
  endTime: '00:00',
  id: TEST_TARIFF_ELEMENT_ENTITY_ID,
  price: new Prisma.Decimal('0.5'),
  startTime: '00:00',
  stepSize: 1,
  tariffId: TEST_TARIFF_ENTITY_ID,
  type: TariffDimensionType.ENERGY,
};
