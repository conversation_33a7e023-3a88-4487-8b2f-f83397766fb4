import { AsyncLocalStorage } from 'async_hooks';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Inject,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { OCPIPrismaClient } from './ocpi-prisma-client.service';
import { Observable, lastValueFrom, of } from 'rxjs';
import { PrismaClient } from '@prisma/clients/ocpi/v221';

@Injectable()
export class TransactionInterceptor implements NestInterceptor {
  constructor(
    @Inject('ALS_PRISMA_OCPI_V221')
    private readonly als: AsyncLocalStorage<
      Omit<PrismaClient, ITXClientDenyList>
    >,
    private readonly database: OCPIPrismaClient
  ) {}

  async intercept(
    _context: ExecutionContext,
    next: CallHandler
  ): Promise<Observable<unknown>> {
    return of(
      await this.database.client.$transaction((tx) =>
        this.als.run(tx, () => lastValueFrom(next.handle()))
      )
    );
  }
}
