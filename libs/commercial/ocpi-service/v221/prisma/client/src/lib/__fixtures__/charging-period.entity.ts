import { Charging<PERSON>eriod } from '@prisma/clients/ocpi/v221';
import {
  TEST_CDR_ENTITY_ID,
  TEST_CHARGING_PERIOD_ENTITY_ID,
  TEST_TARIFF_ENTITY_ID,
} from './common';
import { TEST_COMPLETED_SESSION_ENTITY } from './session.entity';

export const TEST_CHARGING_PERIOD_ENTITY: ChargingPeriod = {
  cdrId: TEST_CDR_ENTITY_ID,
  dimensionType: 'ENERGY',
  dimensionVolume: TEST_COMPLETED_SESSION_ENTITY.kwh,
  id: TEST_CHARGING_PERIOD_ENTITY_ID,
  startDateTime: TEST_COMPLETED_SESSION_ENTITY.startDateTime,
  tariffId: TEST_TARIFF_ENTITY_ID,
};
