/*
  Warnings:

  - A unique constraint covering the columns `[serverCredentialsId]` on the table `Credentials` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[clientCredentialsId]` on the table `Credentials` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "ocpi/v221"."Credentials"
ADD COLUMN     "clientCredentialsId" TEXT,
ADD COLUMN     "serverCredentialsId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Credentials_serverCredentialsId_key" ON "ocpi/v221"."Credentials"("serverCredentialsId");

-- CreateIndex
CREATE UNIQUE INDEX "Credentials_clientCredentialsId_key" ON "ocpi/v221"."Credentials"("clientCredentialsId");

-- AddForeignKey
ALTER TABLE "ocpi/v221"."Credentials" ADD CONSTRAINT "Credentials_serverCredentialsId_fkey" FOREIGN KEY ("serverCredentialsId") REFERENCES "ocpi/v221"."Credentials"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ocpi/v221"."Credentials" ADD CONSTRAINT "Credentials_clientCredentialsId_fkey" FOREIGN KEY ("clientCredentialsId") REFERENCES "ocpi/v221"."Credentials"("id") ON DELETE SET NULL ON UPDATE CASCADE;
