-- AlterTable
ALTER TABLE "ocpi/v221"."EVSE" ADD COLUMN     "sys_period" tstzrange;

-- CreateTable
CREATE TABLE "ocpi/v221"."EVSEHistory" (
    "pk" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "evseId" TEXT NOT NULL,
    "status" "ocpi/v221"."EVSEStatus" NOT NULL,
    "sys_period" tstzrange,

    CONSTRAINT "EVSEHistory_pkey" PRIMARY KEY ("pk")
);

-- CreateTrigger
CREATE TRIGGER versioning BEFORE INSERT OR UPDATE OR DELETE ON "ocpi/v221"."EVSE"
FOR EACH ROW EXECUTE PROCEDURE "ocpi/v221"."versioning"('sys_period', '"ocpi/v221"."EVSEHistory"', true, true);
