-- CreateEnum
CREATE TYPE "ocpi/v221"."RoleType" AS ENUM ('CPO', 'EMSP', 'HUB', 'NAP', 'NSP', 'OTHER', 'SCSP');

-- AlterTable
ALTER TABLE "ocpi/v221"."Credentials" ADD COLUMN     "object" JSONB NOT NULL;

-- CreateTable
CREATE TABLE "ocpi/v221"."Role" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "credentialsId" TEXT NOT NULL,
    "role" "ocpi/v221"."RoleType" NOT NULL,
    "countryCode" VARCHAR(2) NOT NULL,
    "partyId" VARCHAR(3) NOT NULL,
    "businessDetailsName" TEXT NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Role_partyId_key" ON "ocpi/v221"."Role"("partyId");

-- AddForeignKey
ALTER TABLE "ocpi/v221"."Role" ADD CONSTRAINT "Role_credentialsId_fkey" FOREIGN KEY ("credentialsId") REFERENCES "ocpi/v221"."Credentials"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
