export default {
  displayName: 'commercial-ocpi-service-common-nest-versions-module',
  preset: '../../../../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory:
    '../../../../../../coverage/libs/commercial/ocpi-service/common/nest/versions-module',
  testPathIgnorePatterns: ['index.spec.ts', 'versions.module.spec.ts'],
};
