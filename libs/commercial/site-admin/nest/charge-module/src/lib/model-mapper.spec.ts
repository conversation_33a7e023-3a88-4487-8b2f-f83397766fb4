import {
  Charges,
  TEST_BILLING_ACCOUNT_ENTITY,
  TEST_CHARGE_STATS_ENTITY,
  TEST_USER_ENTITY,
  TEST_VEHICLE_MODEL_ENTITY,
  TEST_VEHICLE_MODEL_USER_ENTITY,
} from '@experience/shared/sequelize/podadmin';
import {
  TEST_ADMINISTRATOR,
  TEST_CHARGE_STATS_INCLUDING_DRIVER_DETAILS,
  TEST_RAW_CHARGE_STATS,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { mapChargeStatsEntity } from './model-mapper';

describe('Charge statistics mapper', () => {
  it('should map an unclaimed charge stats entity to Raw Charge Statistics', () => {
    expect(
      mapChargeStatsEntity(
        {
          ...TEST_CHARGE_STATS_ENTITY,
          claimedChargeId: null,
        } as unknown as Charges,
        []
      )
    ).toEqual({
      ...TEST_RAW_CHARGE_STATS,
      confirmed: false,
      confirmedBy: undefined,
      vehicle: '-',
    });
  });

  it('should map an charge entity including driver details', () => {
    expect(
      mapChargeStatsEntity(
        {
          ...TEST_CHARGE_STATS_ENTITY,
          billingAccount: {
            ...TEST_BILLING_ACCOUNT_ENTITY,
            user: TEST_USER_ENTITY,
          },
        } as Charges,
        [TEST_ADMINISTRATOR.email]
      )
    ).toEqual(TEST_CHARGE_STATS_INCLUDING_DRIVER_DETAILS);
  });

  it('should map a claimed charge stats entity with a user but no vehicle to Raw Charge Statistics', () => {
    const chargeDataForUserWithNoRegisteredVehicle = {
      ...TEST_CHARGE_STATS_ENTITY,
      billingAccount: {
        user: {
          ...TEST_USER_ENTITY,
          vehicleModelUsers: [],
        },
      },
    } as unknown as Charges;

    expect(
      mapChargeStatsEntity(chargeDataForUserWithNoRegisteredVehicle, [
        TEST_ADMINISTRATOR.email,
      ])
    ).toEqual({
      ...TEST_CHARGE_STATS_INCLUDING_DRIVER_DETAILS,
      vehicle: '-',
    });
  });

  it('should map a claimed charge stats entity with a user and vehicle to Raw Charge Statistics', () => {
    const chargeDataForUserWithVehicle = {
      ...TEST_CHARGE_STATS_ENTITY,
      billingAccount: {
        user: {
          ...TEST_USER_ENTITY,
          vehicleModelUsers: [
            {
              ...TEST_VEHICLE_MODEL_USER_ENTITY,
              vehicleModel: TEST_VEHICLE_MODEL_ENTITY,
            },
          ],
        },
      },
    } as Charges;

    expect(
      mapChargeStatsEntity(chargeDataForUserWithVehicle, [
        TEST_ADMINISTRATOR.email,
      ])
    ).toEqual({
      ...TEST_CHARGE_STATS_INCLUDING_DRIVER_DETAILS,
      vehicle: 'Audi A3 e-tron',
    });
  });

  it('should map a claimed charge stats entity with no billing event to Raw Charge Statistics', () => {
    const chargeDataForUserWithVehicle = {
      ...TEST_CHARGE_STATS_ENTITY,
      billingEvent: null,
      billingAccount: {
        user: {
          ...TEST_USER_ENTITY,
          vehicleModelUsers: [
            {
              ...TEST_VEHICLE_MODEL_USER_ENTITY,
              vehicleModel: TEST_VEHICLE_MODEL_ENTITY,
            },
          ],
        },
      },
    } as unknown as Charges;

    expect(mapChargeStatsEntity(chargeDataForUserWithVehicle, [])).toEqual({
      ...TEST_RAW_CHARGE_STATS,
      revenueGenerated: '£0.00',
      vehicle: 'Audi A3 e-tron',
    });
  });
});
