export default {
  displayName: 'commercial-site-admin-nest-support-module',
  preset: '../../../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory:
    '../../../../../coverage/libs/commercial/site-admin/nest/support-module',
  testPathIgnorePatterns: ['index.spec.ts', 'support.module.spec.ts'],
};
