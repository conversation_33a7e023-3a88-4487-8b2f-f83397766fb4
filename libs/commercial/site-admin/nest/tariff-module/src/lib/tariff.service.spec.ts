import {
  DeleteTariffException,
  DuplicateTariffNameException,
  TariffNotFoundException,
} from './tariff.exception';
import {
  RevenueProfileTiers,
  RevenueProfiles,
  TEST_REVENUE_PROFILE_ENTITY,
  TEST_UNASSIGNED_REVENUE_PROFILE_ENTITY,
  Tariffs,
  revenueProfileTiersDeepOptions,
} from '@experience/shared/sequelize/podadmin';
import {
  TEST_CREATE_TARIFF_REQUEST,
  TEST_GROUP,
  TEST_TARIFF,
  TEST_USER,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { TariffService } from './tariff.service';
import { Test, TestingModule } from '@nestjs/testing';

describe('TariffService', () => {
  let service: TariffService;
  let revenueProfilesRepository: typeof RevenueProfiles;
  let revenueProfileTiersRepository: typeof RevenueProfileTiers;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TariffService,
        { provide: 'REVENUE_PROFILES_REPOSITORY', useValue: Tariffs },
        {
          provide: 'REVENUE_PROFILE_TIERS_REPOSITORY',
          useValue: RevenueProfileTiers,
        },
      ],
    }).compile();

    service = module.get<TariffService>(TariffService);
    revenueProfilesRepository = module.get<typeof RevenueProfiles>(
      'REVENUE_PROFILES_REPOSITORY'
    );
    revenueProfileTiersRepository = module.get<typeof RevenueProfileTiers>(
      'REVENUE_PROFILE_TIERS_REPOSITORY'
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find by id', async () => {
    const mockFindOne = jest
      .spyOn(revenueProfilesRepository, 'findOne')
      .mockResolvedValueOnce(TEST_REVENUE_PROFILE_ENTITY);

    const tariff = await service.findById(TEST_TARIFF.id);

    expect(mockFindOne).toHaveBeenCalledWith({
      include: revenueProfileTiersDeepOptions,
      where: { id: TEST_TARIFF.id },
    });
    expect(tariff).toEqual(TEST_TARIFF);
  });

  it('should find by group id and tariff id', async () => {
    const mockFindOne = jest
      .spyOn(revenueProfilesRepository, 'findOne')
      .mockResolvedValueOnce(TEST_REVENUE_PROFILE_ENTITY);

    const tariff = await service.findByGroupIdAndTariffId(
      TEST_GROUP.id,
      TEST_TARIFF.id
    );

    expect(mockFindOne).toHaveBeenCalledWith({
      include: revenueProfileTiersDeepOptions,
      where: { groupId: TEST_GROUP.id, id: TEST_TARIFF.id },
    });
    expect(tariff).toEqual(TEST_TARIFF);
  });

  it('should create by group id', async () => {
    jest.spyOn(revenueProfilesRepository, 'findAll').mockResolvedValueOnce([]);

    const mockCreate = jest
      .spyOn(revenueProfilesRepository, 'create')
      .mockResolvedValueOnce(TEST_REVENUE_PROFILE_ENTITY);

    const tariff = await service.createByGroupId(
      TEST_USER.groupId,
      TEST_CREATE_TARIFF_REQUEST
    );

    expect(mockCreate).toHaveBeenCalledWith({
      name: TEST_CREATE_TARIFF_REQUEST.tariffName,
      groupId: TEST_USER.groupId,
      currency: 'GBP',
    });
    expect(tariff).toEqual(TEST_TARIFF);
  });

  it('should update by group id', async () => {
    jest.spyOn(revenueProfilesRepository, 'findAll').mockResolvedValueOnce([]);

    const mockUpdate = jest
      .spyOn(revenueProfilesRepository, 'update')
      .mockResolvedValueOnce([1]);

    await service.updateByGroupIdAndTariffId(
      TEST_USER.groupId,
      TEST_TARIFF.id,
      TEST_CREATE_TARIFF_REQUEST
    );

    expect(mockUpdate).toHaveBeenCalledWith(
      { name: TEST_CREATE_TARIFF_REQUEST.tariffName },
      {
        where: {
          groupId: TEST_USER.groupId,
          id: TEST_TARIFF.id,
        },
      }
    );
  });

  it('should throw a duplicate tariff name exception on create if the tariff name already exists in the group', async () => {
    jest
      .spyOn(revenueProfilesRepository, 'findAll')
      .mockResolvedValueOnce([TEST_REVENUE_PROFILE_ENTITY]);

    await expect(
      service.createByGroupId(TEST_GROUP.id, {
        ...TEST_CREATE_TARIFF_REQUEST,
        tariffName: '70p per hour',
      })
    ).rejects.toThrow(DuplicateTariffNameException);
  });

  it('should throw a duplicate tariff name exception on update if the tariff name already exists in the group', async () => {
    jest
      .spyOn(revenueProfilesRepository, 'findAll')
      .mockResolvedValueOnce([TEST_REVENUE_PROFILE_ENTITY]);

    await expect(
      service.updateByGroupIdAndTariffId(TEST_GROUP.id, TEST_TARIFF.id, {
        ...TEST_CREATE_TARIFF_REQUEST,
        tariffName: '70p per hour',
      })
    ).rejects.toThrow(DuplicateTariffNameException);
  });

  it('should delete by group id', async () => {
    jest
      .spyOn(revenueProfilesRepository, 'findOne')
      .mockResolvedValueOnce(TEST_UNASSIGNED_REVENUE_PROFILE_ENTITY);
    const mockDeleteRevenueProfiles = jest
      .spyOn(revenueProfilesRepository, 'destroy')
      .mockResolvedValueOnce(1);
    const mockDeleteRevenueProfileTiers = jest
      .spyOn(revenueProfileTiersRepository, 'destroy')
      .mockResolvedValueOnce(1);

    await service.deleteByGroupIdAndTariffId(TEST_USER.groupId, TEST_TARIFF.id);

    expect(mockDeleteRevenueProfiles).toHaveBeenCalledWith({
      where: {
        groupId: TEST_USER.groupId,
        id: TEST_TARIFF.id,
      },
    });
    expect(mockDeleteRevenueProfileTiers).toHaveBeenCalledWith({
      where: {
        revenueProfileId: TEST_TARIFF.id,
      },
    });
  });

  it('should throw a delete tariff exception on delete if the tariff is assigned to a charger', async () => {
    jest
      .spyOn(revenueProfilesRepository, 'findOne')
      .mockResolvedValueOnce(TEST_REVENUE_PROFILE_ENTITY);

    await expect(
      service.deleteByGroupIdAndTariffId(TEST_GROUP.id, TEST_TARIFF.id)
    ).rejects.toThrow(DeleteTariffException);
  });

  it('should throw a tariff not found exception on if the tariff is not found by id', async () => {
    jest
      .spyOn(revenueProfilesRepository, 'findOne')
      .mockResolvedValueOnce(null);

    await expect(service.findById(TEST_TARIFF.id)).rejects.toThrow(
      TariffNotFoundException
    );
  });

  it('should throw a tariff not found exception on if the tariff is not found', async () => {
    jest
      .spyOn(revenueProfilesRepository, 'findOne')
      .mockResolvedValueOnce(null);

    await expect(
      service.findByGroupIdAndTariffId(TEST_GROUP.id, TEST_TARIFF.id)
    ).rejects.toThrow(TariffNotFoundException);
  });
});
