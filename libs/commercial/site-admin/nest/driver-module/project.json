{"name": "commercial-site-admin-nest-driver-module", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/site-admin/nest/driver-module/src", "projectType": "library", "tags": ["commercial", "site-admin"], "implicitDependencies": ["commercial-site-admin-maizzle"], "namedInputs": {"projectSpecificFiles": ["{workspaceRoot}/assets/site-admin-api/email-templates/**/*"]}, "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/commercial/site-admin/nest/driver-module"], "options": {"jestConfig": "libs/commercial/site-admin/nest/driver-module/jest.config.ts", "passWithNoTests": false}}}}