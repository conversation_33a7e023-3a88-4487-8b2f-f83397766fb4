import * as os from 'os';
import { COMMON_INTERNAL_SERVER_ERROR } from '@experience/shared/typescript/validation';
import { CreateDriverRequest } from '@experience/commercial/site-admin/typescript/domain-model';
import { CsvError, parse } from 'csv-parse/sync';
import { DRIVER_INVALID_BULK_UPLOAD_ERROR } from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { FileValidator } from '@nestjs/common';
import { ValidationError } from 'class-validator/types/validation/ValidationError';
import { Validator } from 'class-validator';

export class CreateDriverRequestFileValidator extends FileValidator {
  validator = new Validator();

  buildErrorMessage<TFile>(file?: TFile): string {
    try {
      const records = this.parseRecords(file);
      if (records.length === 0) {
        return DRIVER_INVALID_BULK_UPLOAD_ERROR;
      }

      const errors = records
        .map((request) => Object.assign(new CreateDriverRequest(), request))
        .map((request: CreateDriverRequest) =>
          this.validator.validateSync(request)
        )
        .map((errors: ValidationError[]) =>
          errors.map((error) => {
            const target = error.target as CreateDriverRequest;
            return `${target?.['firstName']},${target?.['lastName']},${target?.['email']},${target?.['tariffTier']},${target?.['canExpense']}`;
          })
        )
        .flat();

      return [DRIVER_INVALID_BULK_UPLOAD_ERROR, '', ...errors].join(os.EOL);
    } catch (error) {
      if (error instanceof CsvError) {
        return DRIVER_INVALID_BULK_UPLOAD_ERROR;
      }
      return COMMON_INTERNAL_SERVER_ERROR;
    }
  }

  isValid<TFile>(file?: TFile): boolean | Promise<boolean> {
    try {
      const records = this.parseRecords(file);
      if (records.length === 0) {
        return false;
      }
      return Promise.all(
        records
          .map((request) => Object.assign(new CreateDriverRequest(), request))
          .map((request: CreateDriverRequest) =>
            this.validator.validateSync(request)
          )
      )
        .then((errors: ValidationError[][]) => errors.flat())
        .then((errors: ValidationError[]) => errors.length === 0);
    } catch (error) {
      if (error instanceof CsvError) {
        return false;
      }
      throw error;
    }
  }

  private parseRecords<TFile>(file?: TFile) {
    const multerFile = file as Express.Multer.File;
    return parse(multerFile.buffer.toString(), {
      columns: ['firstName', 'lastName', 'email', 'tariffTier', 'canExpense'],
      comment: '#',
      skipEmptyLines: true,
      trim: true,
    });
  }
}
