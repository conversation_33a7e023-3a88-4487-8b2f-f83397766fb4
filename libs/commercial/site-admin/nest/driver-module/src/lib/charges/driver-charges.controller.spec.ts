import {
  COMMON_ENUM_STRING_ERROR,
  COMMON_NUMERIC_STRING_ERROR,
  COMMON_UUID_STRING_ERROR,
} from '@experience/shared/typescript/validation';
import {
  DRIVER_NOT_FOUND_ERROR,
  DriverErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { DriverChargesController } from './driver-charges.controller';
import { DriverChargesService } from './driver-charges.service';
import { DriverNotFoundException } from '../driver.exception';
import { INestApplication } from '@nestjs/common';
import {
  TEST_DRIVER,
  TEST_DRIVER_WITH_CHARGING_DETAILS,
  TEST_GROUP,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import request from 'supertest';

jest.mock('./driver-charges.service');

describe('DriverChargesController', () => {
  let app: INestApplication;
  let driverChargesService: DriverChargesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DriverChargesController],
      providers: [
        { provide: DriverChargesService, useClass: DriverChargesService },
      ],
    }).compile();

    driverChargesService =
      module.get<DriverChargesService>(DriverChargesService);

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should find by group id and driver id', async () => {
    jest
      .spyOn(driverChargesService, 'findByGroupIdAndDriverId')
      .mockResolvedValueOnce(TEST_DRIVER_WITH_CHARGING_DETAILS);

    await request(app.getHttpServer())
      .get(
        `/drivers/${TEST_DRIVER.id}/charges?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&tariffTier=${TEST_DRIVER.tariffTier}`
      )
      .expect(200)
      .expect(TEST_DRIVER_WITH_CHARGING_DETAILS);

    expect(driverChargesService.findByGroupIdAndDriverId).toHaveBeenCalledWith(
      TEST_GROUP.id,
      TEST_GROUP.uid,
      TEST_DRIVER.id,
      TEST_DRIVER.tariffTier,
      undefined,
      true
    );
  });

  it('should throw a driver not found exception if no driver is found', () => {
    jest
      .spyOn(driverChargesService, 'findByGroupIdAndDriverId')
      .mockImplementation(() => {
        throw new DriverNotFoundException();
      });

    return request(app.getHttpServer())
      .get(
        `/drivers/${TEST_DRIVER.id}/charges?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&tariffTier=${TEST_DRIVER.tariffTier}`
      )
      .expect(404)
      .expect({
        statusCode: 404,
        message: DRIVER_NOT_FOUND_ERROR,
        error: DriverErrorCodes.DRIVER_NOT_FOUND_ERROR,
      });
  });

  it.each([null, undefined, 'not-a-real-uuid'])(
    'should throw a validation error when group uid is %s when getting by group id and driver id',
    (groupUid) =>
      request(app.getHttpServer())
        .get(
          `/drivers/${TEST_DRIVER.id}/charges?groupId=${TEST_GROUP.id}&groupUid=${groupUid}&tariffTier=${TEST_DRIVER.tariffTier}`
        )
        .expect(400)
        .expect({
          statusCode: 400,
          message: COMMON_UUID_STRING_ERROR,
          error: 'Bad Request',
        })
  );

  it.each([null, undefined, 'one'])(
    'should throw a validation error when group id is %s when getting by group id and driver id',
    (groupId) =>
      request(app.getHttpServer())
        .get(
          `/drivers/${TEST_DRIVER.id}/charges?groupId=${groupId}&groupUid=${TEST_GROUP.uid}&tariffTier=${TEST_DRIVER.tariffTier}`
        )
        .expect(400)
        .expect({
          statusCode: 400,
          message: COMMON_NUMERIC_STRING_ERROR,
          error: 'Bad Request',
        })
  );

  it.each([null, undefined, 'notRealTariffTier'])(
    'should throw a validation error when tariff tier is %s when getting by group id and driver id',
    (tariffTier) =>
      request(app.getHttpServer())
        .get(
          `/drivers/${TEST_DRIVER.id}/charges?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&tariffTier=${tariffTier}`
        )
        .expect(400)
        .expect({
          statusCode: 400,
          message: COMMON_ENUM_STRING_ERROR,
          error: 'Bad Request',
        })
  );

  it('should find charge data and generate csv by group id and driver id', async () => {
    jest
      .spyOn(driverChargesService, 'findByGroupIdAndDriverId')
      .mockResolvedValueOnce(TEST_DRIVER_WITH_CHARGING_DETAILS);

    await request(app.getHttpServer())
      .get(
        `/drivers/${TEST_DRIVER.id}/charges/csv?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&tariffTier=${TEST_DRIVER.tariffTier}`
      )
      .expect(200);

    expect(driverChargesService.findByGroupIdAndDriverId).toHaveBeenCalledWith(
      TEST_GROUP.id,
      TEST_GROUP.uid,
      TEST_DRIVER.id,
      TEST_DRIVER.tariffTier,
      undefined,
      false
    );
  });

  it('should find charge data and generate csv by group id and driver id with date', async () => {
    jest
      .spyOn(driverChargesService, 'findByGroupIdAndDriverId')
      .mockResolvedValueOnce(TEST_DRIVER_WITH_CHARGING_DETAILS);

    await request(app.getHttpServer())
      .get(
        `/drivers/${TEST_DRIVER.id}/charges/csv?groupId=${
          TEST_GROUP.id
        }&groupUid=${TEST_GROUP.uid}&tariffTier=${
          TEST_DRIVER.tariffTier
        }&date=${new Date('2021-01-01').toISOString()}`
      )
      .expect(200);

    expect(driverChargesService.findByGroupIdAndDriverId).toHaveBeenCalledWith(
      TEST_GROUP.id,
      TEST_GROUP.uid,
      TEST_DRIVER.id,
      TEST_DRIVER.tariffTier,
      '2021-01-01T00:00:00.000Z',
      false
    );
  });

  it('should find charge data and generate csv by group id and driver id with all charges requested', async () => {
    jest
      .spyOn(driverChargesService, 'findByGroupIdAndDriverId')
      .mockResolvedValueOnce(TEST_DRIVER_WITH_CHARGING_DETAILS);

    await request(app.getHttpServer())
      .get(
        `/drivers/${TEST_DRIVER.id}/charges/csv?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&tariffTier=${TEST_DRIVER.tariffTier}&all=true`
      )
      .expect(200);

    expect(driverChargesService.findByGroupIdAndDriverId).toHaveBeenCalledWith(
      TEST_GROUP.id,
      TEST_GROUP.uid,
      TEST_DRIVER.id,
      TEST_DRIVER.tariffTier,
      undefined,
      true
    );
  });
});
