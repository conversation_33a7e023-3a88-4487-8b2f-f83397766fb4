import {
  COMMON_REQUIRED_ERROR,
  COMMON_UUID_STRING_ERROR,
} from '@experience/shared/typescript/validation';
import { INestApplication } from '@nestjs/common';
import {
  TEST_UPDATE_USER_REQUEST,
  TEST_USER,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';
import { TransactionInterceptor } from '@experience/shared/sequelize/podadmin';
import {
  USER_FIRST_NAME_MAX_LENGTH,
  USER_FIRST_NAME_MAX_LENGTH_ERROR,
  USER_LAST_NAME_MAX_LENGTH,
  USER_LAST_NAME_MAX_LENGTH_ERROR,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import request from 'supertest';

jest.mock('./user.service');
jest.mock('@experience/shared/sequelize/podadmin');

describe('UserController', () => {
  let app: INestApplication;
  let userService: UserService;
  let transactionInterceptor: TransactionInterceptor;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        { provide: UserService, useClass: UserService },
        { provide: TransactionInterceptor, useClass: TransactionInterceptor },
      ],
    }).compile();

    userService = module.get<UserService>(UserService);
    transactionInterceptor = module.get<TransactionInterceptor>(
      TransactionInterceptor
    );

    jest
      .spyOn(transactionInterceptor, 'intercept')
      .mockImplementation(async (context, next) => next.handle());

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Find', () => {
    it('should find by user Uid', () => {
      jest.spyOn(userService, 'findByUserUid').mockResolvedValueOnce(TEST_USER);

      return request(app.getHttpServer())
        .get(`/user?adminUid=${TEST_USER.authId}`)
        .expect(200)
        .expect({
          ...TEST_USER,
          activatedOn: TEST_USER.activatedOn.toISOString(),
        });
    });

    it.each([null, undefined, 'one'])(
      'should throw a validation error when admin Uid is %s when finding by user Uid',
      (adminUid) => {
        jest
          .spyOn(userService, 'findByUserUid')
          .mockResolvedValueOnce(TEST_USER);

        return request(app.getHttpServer())
          .get(`/user?adminId=${TEST_USER.id}&adminUid=${adminUid}`)
          .expect(400)
          .expect({
            statusCode: 400,
            message: COMMON_UUID_STRING_ERROR,
            error: 'Bad Request',
          });
      }
    );
  });

  describe('Update', () => {
    it('should update by user uid', async () => {
      const mockUpdate = jest.spyOn(userService, 'updateByUserUid');

      const response = await request(app.getHttpServer())
        .put(`/user?&adminUid=${TEST_USER.authId}`)
        .send(TEST_UPDATE_USER_REQUEST);

      expect(mockUpdate).toHaveBeenCalledWith(
        TEST_USER.authId,
        TEST_UPDATE_USER_REQUEST
      );
      expect(response.status).toEqual(204);
    });

    it.each([null, undefined, 1])(
      'should throw a validation error when user uid is %s when updating by user uid',
      (adminUid) =>
        request(app.getHttpServer())
          .put(`/user?adminUid=${adminUid}`)
          .send(TEST_UPDATE_USER_REQUEST)
          .expect(400)
          .expect({
            statusCode: 400,
            message: COMMON_UUID_STRING_ERROR,
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when first name is %s when updating by user uid',
      (firstName) =>
        request(app.getHttpServer())
          .put(`/user?adminUid=${TEST_USER.authId}`)
          .send({ ...TEST_UPDATE_USER_REQUEST, firstName })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when last name is %s when updating by user uid',
      (lastName) =>
        request(app.getHttpServer())
          .put(`/user?adminUid=${TEST_USER.authId}`)
          .send({ ...TEST_UPDATE_USER_REQUEST, lastName })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([
      [
        'first name',
        'firstName',
        USER_FIRST_NAME_MAX_LENGTH,
        USER_FIRST_NAME_MAX_LENGTH_ERROR,
      ],
      [
        'last name',
        'lastName',
        USER_LAST_NAME_MAX_LENGTH,
        USER_LAST_NAME_MAX_LENGTH_ERROR,
      ],
    ])(
      'should throw a validation error when %s is greater than max length when updating by user uid',
      (name, key, length, error) =>
        request(app.getHttpServer())
          .put(`/user?adminUid=${TEST_USER.authId}`)
          .send({
            ...TEST_UPDATE_USER_REQUEST,
            [key]: 't'.repeat(length + 1),
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [error],
            error: 'Bad Request',
          })
    );
  });

  describe('Accept terms and conditions', () => {
    it('should accept latest terms and conditions', async () => {
      await request(app.getHttpServer())
        .put(
          `/user/terms-and-conditions/acceptance?adminUid=${TEST_USER.authId}`
        )
        .expect(204);

      expect(userService.acceptTermsAndConditions).toHaveBeenCalledWith(
        TEST_USER.authId
      );
    });
  });
});
