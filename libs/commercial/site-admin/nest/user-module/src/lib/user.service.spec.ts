import { TERMS_AND_CONDITIONS_VERSION } from './constants';
import {
  TEST_UPDATE_USER_REQUEST,
  TEST_USER,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_USER_ENTITY, Users } from '@experience/shared/sequelize/podadmin';
import { Test, TestingModule } from '@nestjs/testing';
import { UserService, includeOptions } from './user.service';
import { joinStrings } from '@experience/shared/typescript/utils';

const mockFirebaseUser = {
  customClaims: {
    termsVersion: TERMS_AND_CONDITIONS_VERSION,
  },
  displayName: joinStrings([TEST_USER.firstName, TEST_USER.lastName], ' '),
  email: '<EMAIL>',
  emailVerified: true,
  uid: TEST_USER.authId,
};

const mockGetFirebaseUser = jest.fn(() => mockFirebaseUser);
const mockUpdateFirebaseUser = jest.fn();
const mockSetCustomUserClaims = jest.fn();

jest.mock('@experience/shared/firebase/admin', () => ({
  getAuth: () => ({
    getUser: mockGetFirebaseUser,
    updateUser: (...args) => mockUpdateFirebaseUser(...args),
    setCustomUserClaims: (...args) => mockSetCustomUserClaims(...args),
  }),
}));

describe('UserService', () => {
  let service: UserService;
  let repository: typeof Users;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: 'USERS_REPOSITORY',
          useValue: Users,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<typeof Users>('USERS_REPOSITORY');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it.each([
    [
      joinStrings([TEST_USER.firstName, TEST_USER.lastName], ' '),
      TEST_USER.firstName,
      TEST_USER.lastName,
    ],
    [TEST_USER.firstName, '', TEST_USER.firstName],
    [TEST_USER.lastName, '', TEST_USER.lastName],
    ['', '', ''],
    [' ', '', ''],
    [undefined, '', ''],
    [null, '', ''],
  ])(
    'should find by user uid',
    async (
      mockFirebaseUserDisplayName,
      expectedFirstName,
      expectedLastName
    ) => {
      const mockFindOne = jest
        .spyOn(repository, 'findOne')
        .mockResolvedValueOnce(TEST_USER_ENTITY);
      mockFirebaseUser.displayName = mockFirebaseUserDisplayName;

      const user = await service.findByUserUid(TEST_USER.authId);

      expect(user).toEqual({
        ...TEST_USER,
        email: '<EMAIL>',
        emailVerified: true,
        firstName: expectedFirstName,
        lastName: expectedLastName,
      });
      expect(mockFindOne).toHaveBeenCalledWith({
        include: includeOptions,
        paranoid: false,
        where: { authId: TEST_USER.authId },
      });
      expect(mockGetFirebaseUser).toHaveBeenCalledWith(TEST_USER.authId);
    }
  );

  it('should set latest terms accepted when finding a user by uid', async () => {
    const mockFindOne = jest
      .spyOn(repository, 'findOne')
      .mockResolvedValueOnce(TEST_USER_ENTITY);
    mockFirebaseUser.customClaims = {
      termsVersion: TERMS_AND_CONDITIONS_VERSION,
    };

    const user = await service.findByUserUid(TEST_USER.authId);

    expect(user.termsAndConditions.acceptedVersion).toBe(
      TERMS_AND_CONDITIONS_VERSION
    );
    expect(mockFindOne).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: false,
      where: { authId: TEST_USER.authId },
    });
    expect(mockGetFirebaseUser).toHaveBeenCalledWith(TEST_USER.authId);
  });

  it('should update by user uid', async () => {
    const mockFindOne = jest
      .spyOn(repository, 'findOne')
      .mockResolvedValueOnce(TEST_USER_ENTITY);
    const mockUpdate = jest
      .spyOn(repository, 'update')
      .mockResolvedValueOnce([1]);

    await service.updateByUserUid(TEST_USER.authId, TEST_UPDATE_USER_REQUEST);

    expect(mockFindOne).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: false,
      where: { authId: TEST_USER.authId },
    });
    expect(mockUpdate).not.toHaveBeenCalledWith();
    expect(mockUpdateFirebaseUser).toHaveBeenCalledWith(TEST_USER.authId, {
      displayName: joinStrings([
        TEST_UPDATE_USER_REQUEST.firstName,
        TEST_UPDATE_USER_REQUEST.lastName,
      ]),
    });
  });

  it('should accept terms and conditions', async () => {
    const mockFindOne = jest
      .spyOn(repository, 'findOne')
      .mockResolvedValueOnce(TEST_USER_ENTITY);

    await service.acceptTermsAndConditions(TEST_USER.authId);

    expect(mockFindOne).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: false,
      where: { authId: TEST_USER.authId },
    });
    expect(mockGetFirebaseUser).toHaveBeenCalledWith(TEST_USER.authId);
    expect(mockSetCustomUserClaims).toHaveBeenCalledWith(mockFirebaseUser.uid, {
      termsVersion: TERMS_AND_CONDITIONS_VERSION,
    });
  });
});
