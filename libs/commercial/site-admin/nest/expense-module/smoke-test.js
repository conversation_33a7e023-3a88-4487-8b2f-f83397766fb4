import { check } from 'k6';
import http from 'k6/http';

export const options = {
  thresholds: {
    checks: ['rate==1'],
  },
};

export default () => {
  /* global __ENV */
  const { AUTH_ID, BASE_URL } = __ENV;

  check(
    http.get(`${BASE_URL}/expenses/new?authId=${AUTH_ID}&year=2024&month=6`),
    {
      'GET /expenses/new returns 200': (r) => r.status === 200,
    }
  );

  check(
    http.get(
      `${BASE_URL}/expenses/processed?authId=${AUTH_ID}&year=2024&month=6`
    ),
    {
      'GET /expenses/processed returns 200': (r) => r.status === 200,
    }
  );

  check(
    http.get(
      `${BASE_URL}/expenses/stats/monthly?authId=${AUTH_ID}&year=2024&month=6`
    ),
    {
      'GET /expenses/stats/monthly returns 200': (r) => r.status === 200,
    }
  );
};
