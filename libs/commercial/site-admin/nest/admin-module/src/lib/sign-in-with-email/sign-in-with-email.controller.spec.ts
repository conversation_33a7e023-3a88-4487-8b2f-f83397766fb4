import {
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/shared/typescript/validation';
import { INestApplication } from '@nestjs/common';
import { SignInWithEmailController } from './sign-in-with-email.controller';
import { SignInWithEmailService } from './sign-in-with-email.service';
import { TEST_SEND_SIGN_IN_WITH_EMAIL_REQUEST } from '@experience/commercial/site-admin/domain/auth';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 as uuid } from 'uuid';
import request from 'supertest';

jest.mock('./sign-in-with-email.service.ts');

describe('SignInWithEmailController', () => {
  let app: INestApplication;
  let signInWithEmailService: SignInWithEmailService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SignInWithEmailController],
      providers: [
        {
          provide: SignInWithEmailService,
          useClass: SignInWithEmailService,
        },
      ],
    }).compile();

    signInWithEmailService = module.get<SignInWithEmailService>(
      SignInWithEmailService
    );

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should send sign in with email', async () => {
    const mockSendSignInWithEmail = jest
      .spyOn(signInWithEmailService, 'sendSignInWithEmail')
      .mockResolvedValueOnce();

    const response = await request(app.getHttpServer())
      .post('/admins/sign-in-with-email')
      .set('Referer', 'https://sites.podenergy.com')
      .send(TEST_SEND_SIGN_IN_WITH_EMAIL_REQUEST);

    expect(response.status).toEqual(202);
    expect(mockSendSignInWithEmail).toHaveBeenCalledWith(
      TEST_SEND_SIGN_IN_WITH_EMAIL_REQUEST,
      new URL('https://sites.podenergy.com')
    );
  });

  it('should throw a validation error when email address is missing when sending sign in with email', () =>
    request(app.getHttpServer())
      .post('/admins/sign-in-with-email')
      .set('Referer', 'https://sites.podenergy.com')
      .send({ email: undefined })
      .expect(400)
      .expect({
        statusCode: 400,
        message: [
          COMMON_REQUIRED_ERROR,
          COMMON_EMAIL_MAX_LENGTH_ERROR,
          COMMON_INVALID_EMAIL_ERROR,
        ],
        error: 'Bad Request',
      }));

  it('should throw a validation error when email address is invalid when sending sign in with email', () =>
    request(app.getHttpServer())
      .post('/admins/sign-in-with-email')
      .set('Referer', 'https://sites.podenergy.com')
      .send({ email: 'hello' })
      .expect(400)
      .expect({
        statusCode: 400,
        message: [COMMON_INVALID_EMAIL_ERROR],
        error: 'Bad Request',
      }));

  it('should throw a validation error when email address is too long when sending sign in with email', () =>
    request(app.getHttpServer())
      .post('/admins/sign-in-with-email')
      .set('Referer', 'https://sites.podenergy.com')
      .send({ email: uuid().repeat(10) + '@email.com' })
      .expect(400)
      .expect({
        statusCode: 400,
        message: [COMMON_EMAIL_MAX_LENGTH_ERROR, COMMON_INVALID_EMAIL_ERROR],
        error: 'Bad Request',
      }));
});
