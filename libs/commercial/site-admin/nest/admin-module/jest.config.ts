export default {
  displayName: 'commercial-site-admin-nest-admin-module',
  preset: '../../../../../jest.preset.js',
  globals: {},
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
      },
    ],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory:
    '../../../../../coverage/libs/commercial/site-admin/nest/admin-module',
  testPathIgnorePatterns: ['index.spec.ts', 'admin.module.spec.ts'],
};
