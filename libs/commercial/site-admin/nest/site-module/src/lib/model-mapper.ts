import {
  Group,
  OpeningTimes,
  Site,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Groups,
  ParkingOpeningTimeNotes,
  ParkingOpeningTimes,
  PodAddresses,
  TariffTiers,
} from '@experience/shared/sequelize/podadmin';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { mapPodLocationsEntityArray } from '@experience/commercial/site-admin/nest/pod-module';

export const mapGroupsEntity = (entity: Groups): Group =>
  entity
    ? {
        id: entity.id,
        name: entity.name,
        uid: entity.uid,
      }
    : undefined;

export const mapPodAddressesEntity = (entity: PodAddresses): Site =>
  entity
    ? {
        address: {
          country: entity.country,
          line1: entity.line1,
          line2: entity.line2,
          name: entity.businessName,
          postcode: entity.postcode,
          prettyPrint: [
            entity.line1,
            entity.line2,
            entity.postalTown,
            entity.postcode,
            entity.country,
          ]
            .filter((value) => !!value)
            .join(', '),
          town: entity.postalTown,
        },
        contactDetails: {
          email: entity.email,
          name: entity.contactName,
          telephone: entity.telephone,
        },
        description: entity.description,
        energyCost:
          entity.tariff?.tariffTiers?.length === 1
            ? mapTariffTierToEnergyCost(entity.tariff?.tariffTiers[0])
            : entity.costPerKwh,
        group: mapGroupsEntity(entity.group),
        id: entity.id,
        parking: {
          openingTimes: mapParkingOpeningTimes(
            entity.parkingOpeningTimeNotes,
            entity.parkingOpeningTimes
          ),
        },
        pods: mapPodLocationsEntityArray(entity.podLocations),
      }
    : undefined;

export const mapPodAddressesEntityArray = (entities: PodAddresses[]): Site[] =>
  entities ? entities.map(mapPodAddressesEntity) : undefined;

export const mapParkingOpeningTimeNotesArray = (
  entities: ParkingOpeningTimeNotes[]
): string[] =>
  entities ? entities.map(mapParkingOpeningTimeNotes) : undefined;

export const mapParkingOpeningTimes = (
  notes: ParkingOpeningTimeNotes[],
  openingTimes: ParkingOpeningTimes[]
): OpeningTimes =>
  openingTimes
    ? openingTimes.reduce(
        (result, openingTime) => ({
          ...result,
          [openingTime.day]: {
            allDay: !!openingTime.allDay,
            from: openingTime.from,
            to: openingTime.to,
          },
        }),
        { notes: mapParkingOpeningTimeNotesArray(notes) }
      )
    : undefined;

const mapParkingOpeningTimeNotes = (entity: ParkingOpeningTimeNotes): string =>
  entity ? entity.notes : undefined;

export const mapTariffTierToEnergyCost = (entity: TariffTiers): number =>
  entity ? parseFloat((entity.rate / 10000).toFixed(3)) : undefined;
