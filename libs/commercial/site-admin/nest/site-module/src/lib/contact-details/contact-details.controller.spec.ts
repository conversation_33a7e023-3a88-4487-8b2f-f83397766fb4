import {
  COMMON_EMAIL_MAX_LENGTH,
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_REQUIRED_ERROR,
  COMMON_UUID_STRING_ERROR,
} from '@experience/shared/typescript/validation';
import { ContactDetailsController } from './contact-details.controller';
import { ContactDetailsService } from './contact-details.service';
import { INestApplication } from '@nestjs/common';
import {
  TEST_CONTACT_DETAILS,
  TEST_GROUP,
  TEST_SITE,
  TEST_UPDATE_CONTACT_DETAILS_REQUEST,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import request from 'supertest';

jest.mock('./contact-details.service');

describe('ContactDetailsController', () => {
  let app: INestApplication;
  let service: ContactDetailsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContactDetailsController],
      providers: [
        { provide: ContactDetailsService, useClass: ContactDetailsService },
      ],
    }).compile();

    service = module.get<ContactDetailsService>(ContactDetailsService);

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should update by group uid and site id', async () => {
    const mockUpdate = jest.spyOn(service, 'updateByGroupUidAndSiteId');

    const response = await request(app.getHttpServer())
      .put(`/sites/${TEST_SITE.id}/contact-details?groupUid=${TEST_GROUP.uid}`)
      .send(TEST_UPDATE_CONTACT_DETAILS_REQUEST);

    expect(mockUpdate).toHaveBeenCalledWith(
      TEST_SITE.id,
      TEST_GROUP.uid,
      TEST_UPDATE_CONTACT_DETAILS_REQUEST
    );

    expect(response.status).toEqual(204);
  });

  describe('Validation tests', () => {
    it.each([null, undefined, '', ' '])(
      'should throw a validation error when update contact name is %s',
      async (name) => {
        await request(app.getHttpServer())
          .put(
            `/sites/${TEST_SITE.id}/contact-details?groupUid=${TEST_GROUP.uid}`
          )
          .send({
            email: TEST_UPDATE_CONTACT_DETAILS_REQUEST.email,
            name: name,
            telephone: TEST_UPDATE_CONTACT_DETAILS_REQUEST.telephone,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          });
      }
    );

    it('should throw a validation error on create when email is longer than the email field max length', () =>
      request(app.getHttpServer())
        .put(
          `/sites/${TEST_SITE.id}/contact-details?groupUid=${TEST_GROUP.uid}`
        )
        .send({
          ...TEST_CONTACT_DETAILS,
          email: 't'.repeat(COMMON_EMAIL_MAX_LENGTH + 1),
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_EMAIL_MAX_LENGTH_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on create when the email address is not valid', () =>
      request(app.getHttpServer())
        .put(
          `/sites/${TEST_SITE.id}/contact-details?groupUid=${TEST_GROUP.uid}`
        )
        .send({
          email: 'thisisnotanemail',
          name: TEST_CONTACT_DETAILS.name,
          telephone: TEST_CONTACT_DETAILS.telephone,
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_INVALID_EMAIL_ERROR],
          error: 'Bad Request',
        }));

    it.each([null, undefined, 'one'])(
      'should throw a validation error when update group uid is %s',
      async (groupUid) =>
        await request(app.getHttpServer())
          .put(`/sites/${TEST_SITE.id}/contact-details?groupUid=${groupUid}`)
          .send(TEST_UPDATE_CONTACT_DETAILS_REQUEST)
          .expect(400)
          .expect({
            statusCode: 400,
            message: COMMON_UUID_STRING_ERROR,
            error: 'Bad Request',
          })
    );
  });
});
