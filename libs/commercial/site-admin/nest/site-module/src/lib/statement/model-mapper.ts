import {
  AccountingTotals,
  ChargeSummary,
  Pod,
  PodStatement,
  Site,
  SiteStatement,
  Statement,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  extractFirstCharacterOfEachWord,
  formatPenceAsCurrencyNumber,
  formatPoundsAsCurrencyNumber,
} from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';

export const mapChargeSummaryToPodStatement = (
  pod: Pod,
  stats: ChargeSummary,
  feeOverride?: number
): PodStatement => ({
  claimedEnergyDelivered: parseFloat(
    stats.claimedEnergyUsage?.toFixed(2) ?? '0'
  ),
  energyDelivered: parseFloat(stats.energyUsage?.toFixed(2) ?? '0'),
  fees: getFees(stats.revenueGeneratingClaimedUsage, feeOverride),
  numberOfCharges: stats.numberOfCharges,
  paidEnergyDelivered: parseFloat(
    stats.revenueGeneratingClaimedUsage?.toFixed(2) ?? '0'
  ),
  podName: pod.name,
  revenue: getRevenue(stats.revenueGenerated),
});

export const mapPodStatementsToSiteStatement = (
  startDate: dayjs.Dayjs,
  podStatements: PodStatement[],
  site: Site
): SiteStatement => {
  const totalStatement = podStatements.reduce(
    (result, podStatement) => {
      const nestedFields = ['fees', 'revenue'];
      const { podName, ...commonFields } = podStatement;

      Object.entries(commonFields).forEach(([key, value]) => {
        if (nestedFields.includes(key)) {
          result[key] = aggregateAccountingTotals(
            result[key],
            value as AccountingTotals
          );
        } else {
          result[key] += value;
        }
      });

      return result;
    },
    {
      claimedEnergyDelivered: 0,
      energyDelivered: 0,
      fees: {
        net: 0,
        vat: 0,
        gross: 0,
      },
      numberOfCharges: 0,
      paidEnergyDelivered: 0,
      revenue: {
        net: 0,
        vat: 0,
        gross: 0,
      },
    } as Statement
  );

  return {
    groupName: site.group.name,
    siteAddress: site.address.prettyPrint,
    siteName: site.address.name,
    date: startDate.format('YYYY-MM'),
    reference: generateReference(site, startDate),
    ...totalStatement,
    claimedEnergyDelivered: parseFloat(
      totalStatement.claimedEnergyDelivered?.toFixed(2) ?? '0'
    ),
    energyDelivered: parseFloat(
      totalStatement.energyDelivered?.toFixed(2) ?? '0'
    ),
    paidEnergyDelivered: parseFloat(
      totalStatement.paidEnergyDelivered?.toFixed(2) ?? '0'
    ),
  };
};

const aggregateAccountingTotals = (
  result: AccountingTotals,
  toAggregate: AccountingTotals
): AccountingTotals => {
  Object.keys(result).forEach((key) => {
    result[key] += toAggregate[key];
  });

  return result;
};

const getRevenue = (revenue: number): AccountingTotals => {
  const gross = formatPenceAsCurrencyNumber({
    amount: revenue,
  });
  const vat = formatPenceAsCurrencyNumber({
    amount: revenue - revenue / 1.2,
  });
  const net = gross - vat;

  return {
    net,
    vat,
    gross,
  };
};

const getFees = (
  energyUsed: number,
  feeOverride?: number
): AccountingTotals => {
  const feePerKwh = feeOverride ?? 0.01;

  const net = formatPoundsAsCurrencyNumber({
    amount: energyUsed * feePerKwh,
  });
  const vat = formatPoundsAsCurrencyNumber({ amount: net * 0.2 });
  const gross = formatPoundsAsCurrencyNumber({ amount: net + vat });

  return { net, vat, gross };
};

const generateReference = (site: Site, startDate: dayjs.Dayjs): string =>
  extractFirstCharacterOfEachWord(site.group.name) +
  extractFirstCharacterOfEachWord(site.address.name) +
  startDate.format('MMYYYY');
