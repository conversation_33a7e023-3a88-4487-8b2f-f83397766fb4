import {
  AdjustedFee,
  CreateSiteStatementRequest,
  Site,
  SiteStatement,
  StatementPeriod,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { ChargeSummaryService } from '@experience/commercial/site-admin/nest/charge-module';
import { Injectable, Logger } from '@nestjs/common';
import { SiteService } from '../site.service';
import {
  mapChargeSummaryToPodStatement,
  mapPodStatementsToSiteStatement,
} from './model-mapper';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

interface GetStatementProps {
  site: Site;
  startDate: dayjs.Dayjs;
  endDate: dayjs.Dayjs;
  fees?: AdjustedFee[];
}

interface FindStatementProps {
  groupUid: string;
  siteId: number;
  period: StatementPeriod;
  dateOverride?: string;
  request?: CreateSiteStatementRequest;
}

@Injectable()
export class StatementService {
  private readonly logger = new Logger(StatementService.name);

  constructor(
    private chargeSummaryService: ChargeSummaryService,
    private siteService: SiteService
  ) {}

  async findByGroupUidAndSiteId({
    groupUid,
    siteId,
    period,
    dateOverride,
    request,
  }: FindStatementProps): Promise<SiteStatement> {
    this.logger.log(
      { groupUid, siteId, period, request },
      'generating statement'
    );

    const site = await this.siteService.findByGroupUidAndSiteId({
      groupUid,
      siteId,
    });

    const endDate = dateOverride
      ? dayjs(dateOverride).endOf('month')
      : dayjs().utc().subtract(1, 'month').endOf('month');
    const startDate = endDate.subtract(period - 1, 'month').startOf('month');

    const statement = await this.getStatement({
      site,
      startDate,
      endDate,
      fees: request?.adjustedFees,
    });

    if (period > 1) {
      const monthlyBreakdown = await this.getMonthlyBreakdown({
        startDate,
        endDate,
        site,
        fees: request?.adjustedFees,
      });
      return { ...statement, breakdown: monthlyBreakdown };
    }
    return statement;
  }

  async createByGroupUidAndSiteId({
    groupUid,
    siteId,
    period,
    dateOverride,
    request,
  }: FindStatementProps): Promise<SiteStatement> {
    return this.findByGroupUidAndSiteId({
      groupUid,
      siteId,
      period,
      dateOverride,
      request,
    });
  }

  private async getMonthlyBreakdown({
    startDate,
    endDate,
    site,
    fees,
  }: GetStatementProps): Promise<SiteStatement[]> {
    return Promise.all(
      [...Array(endDate.diff(startDate, 'month') + 1).keys()]
        .map((num) => startDate.add(num, 'month'))
        .map((startDate) =>
          this.getStatement({
            site,
            startDate,
            endDate: startDate.endOf('month'),
            fees,
          })
        )
    );
  }

  private async getStatement({
    site,
    startDate,
    endDate,
    fees = [],
  }: GetStatementProps): Promise<SiteStatement> {
    const chargeSummaries =
      await this.chargeSummaryService.findChargeSummaryByPodIds(
        site.pods.map((pod) => pod.ppid),
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD')
      );

    const podStatements = site.pods.map((pod) => {
      const adjustedFee = fees.find((fee) => fee.ppid === pod.ppid);
      return mapChargeSummaryToPodStatement(
        pod,
        chargeSummaries[pod.ppid],
        adjustedFee?.fee
      );
    });

    return mapPodStatementsToSiteStatement(startDate, podStatements, site);
  }
}
