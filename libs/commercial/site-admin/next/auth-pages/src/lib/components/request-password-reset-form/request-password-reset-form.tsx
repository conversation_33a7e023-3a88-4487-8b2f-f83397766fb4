import { AUTH_EMAIL_REQUIRED_ERROR } from '@experience/commercial/site-admin/domain/auth';
import {
  Anchor,
  Button,
  Input,
  InputWidth,
  Paragraph,
} from '@experience/shared/react/design-system';
import { COMMON_INVALID_EMAIL_ERROR } from '@experience/shared/typescript/validation';
import { Controller, useForm } from 'react-hook-form';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { isEmail } from 'class-validator';
import { useErrorHandler } from '@experience/shared/react/hooks';
import axios from 'axios';

interface RequestPasswordResetFormValues {
  email: string;
}

interface RequestPasswordResetFormProps {
  setEmailAddress: (email: string) => void;
}

export const RequestPasswordResetForm = ({
  setEmailAddress,
}: RequestPasswordResetFormProps) => {
  const handleError = useErrorHandler();

  const {
    control,
    formState: { errors: formErrors, isSubmitting },
    handleSubmit,
  } = useForm<RequestPasswordResetFormValues>({
    defaultValues: { email: '' },
    reValidateMode: 'onSubmit',
  });

  const resetPassword = async ({ email }: RequestPasswordResetFormValues) => {
    try {
      await axios.post('/api/auth/password-reset', {
        email,
      });
      setEmailAddress(email);
    } catch (error) {
      handleError(error);
    }
  };

  const formId = 'request-password-reset-form';

  return (
    <form
      className="text-left"
      id={formId}
      onSubmit={handleSubmit((data, event) => {
        event?.preventDefault();
        return resetPassword(data);
      })}
      noValidate
    >
      <Controller
        control={control}
        name="email"
        rules={{
          required: AUTH_EMAIL_REQUIRED_ERROR,
          validate: (email) => isEmail(email) || COMMON_INVALID_EMAIL_ERROR,
        }}
        render={({ field: { name, onChange, value } }) => (
          <Input
            errorMessage={formErrors[name]?.message as string}
            label="Email address"
            id={name}
            isFocused={true}
            name={name}
            onChange={onChange}
            type="email"
            value={value}
            width={InputWidth.FULL}
          />
        )}
      />
      <VerticalSpacer />
      <Paragraph>
        Please enter your registered email to reset your password.
      </Paragraph>
      <VerticalSpacer />
      <Paragraph>
        <Anchor href="/auth/login-with-password">Back to login</Anchor>
      </Paragraph>
      <VerticalSpacer />
      <Button
        disabled={isSubmitting}
        form={formId}
        className="w-full"
        type="submit"
      >
        Request password reset
      </Button>
    </form>
  );
};

export default RequestPasswordResetForm;
