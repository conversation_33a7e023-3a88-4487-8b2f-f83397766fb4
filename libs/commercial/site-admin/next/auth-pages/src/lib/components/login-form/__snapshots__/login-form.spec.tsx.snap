// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`LoginForm should match snapshot 1`] = `
<body>
  <div>
    <form
      class="text-left"
      id="login-form"
      novalidate=""
    >
      <div
        class=""
      >
        <label
          class="text-md font-bold block mb-2"
          for="username"
        >
          Email address
        </label>
      </div>
      <input
        class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
        id="username"
        name="username"
        value=""
      />
      <div
        class="pb-4"
      />
      <div
        class=""
      >
        <label
          class="text-md font-bold block mb-2"
          for="password"
        >
          Password
        </label>
      </div>
      <input
        class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
        id="password"
        name="password"
        type="password"
        value=""
      />
      <div
        class="pb-2"
      />
      <a
        class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
        href="/auth/reset-password"
      >
        Forgot password?
      </a>
      <div
        class="pb-4"
      />
      <button
        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 w-full"
        form="login-form"
        type="submit"
      >
        Log in
      </button>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal break-words"
      >
        This site is protected by reCAPTCHA Enterprise and the Google
         
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
          href="https://policies.google.com/privacy"
        >
          Privacy Policy
        </a>
         
        and
         
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
          href="https://policies.google.com/terms"
        >
          Terms of Service
        </a>
         
        apply.
      </p>
    </form>
  </div>
</body>
`;
