import 'whatwg-fetch';
import {
  AUTH_ACCOUNT_DISABLED_ERROR,
  AUTH_EMAIL_LINK_REQUIRED_ERROR,
  AUTH_EMAIL_REQUIRED_ERROR,
  AUTH_INVALID_CREDENTIALS_ERROR,
  AUTH_INVALID_SIGN_IN_CODE,
  AUTH_PASSWORD_REQUIRED_ERROR,
  AUTH_TOO_MANY_REQUESTS_ERROR,
} from '@experience/commercial/site-admin/domain/auth';
import {
  COMMON_INTERNAL_SERVER_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
} from '@experience/shared/typescript/validation';
import { ErrorBoundary } from '@sentry/nextjs';
import {
  UserCredential,
  signInWithEmailAndPassword,
  signInWithEmailLink,
} from 'firebase/auth';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { getAuth } from '@experience/shared/next/firebase';
import { mutate } from 'swr';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import LoginForm from './login-form';

(signIn as jest.Mock).mockImplementation(() => ({ ok: true, status: 200 }));

const mockMutate = jest.mocked(mutate);
const mockRouter = { push: jest.fn() };

jest.mock('@experience/shared/next/firebase', () => ({ getAuth: jest.fn() }));
jest.mock('firebase/auth');
jest.mock('next-auth/react');
jest.mock('next/navigation', () => ({ useRouter: jest.fn() }));
jest.mock('swr');

const fillOutLoginForm = (
  username?: boolean | string,
  password?: boolean | string
) => {
  if (username) {
    fireEvent.change(screen.getByLabelText('Email address'), {
      target: {
        value: typeof username === 'string' ? username : '<EMAIL>',
      },
    });
  }
  if (password) {
    fireEvent.change(screen.getByLabelText('Password'), {
      target: { value: typeof password === 'string' ? password : 'password' },
    });
  }
  fireEvent.click(screen.getByRole('button', { name: 'Log in' }));
};

describe('LoginForm', () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  it('should be defined', () => {
    const { baseElement } = render(<LoginForm />);
    expect(baseElement).toBeDefined();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<LoginForm />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should show validation error if email address is missing', async () => {
    render(<LoginForm />);

    fillOutLoginForm(false, true);

    expect(
      await screen.findByText(AUTH_EMAIL_REQUIRED_ERROR)
    ).toBeInTheDocument();

    await waitFor(() => {
      expect(signIn).not.toHaveBeenCalled();
    });
  });

  it('should show validation error if password is missing', async () => {
    render(<LoginForm />);

    fillOutLoginForm(true, false);

    expect(
      await screen.findByText(AUTH_PASSWORD_REQUIRED_ERROR)
    ).toBeInTheDocument();

    await waitFor(() => {
      expect(signIn).not.toHaveBeenCalled();
    });
  });

  it('should login if form is valid', async () => {
    render(<LoginForm />);
    const mockedSignIn = jest.mocked(signInWithEmailAndPassword);
    mockedSignIn.mockResolvedValueOnce({
      user: { getIdToken: () => 'foo' },
    } as unknown as UserCredential);

    fillOutLoginForm(true, true);

    await waitFor(async () => {
      expect(mockedSignIn).toHaveBeenCalledWith(
        await getAuth(),
        '<EMAIL>',
        'password'
      );
      expect(signIn).toHaveBeenCalledWith('firebase-credentials', {
        idToken: 'foo',
        redirect: false,
        signInMethod: 'password',
        username: '<EMAIL>',
      });
      expect(mockMutate).toHaveBeenCalledWith(expect.any(Function), null, {});
    });
  });

  it('should login if form is valid and email address is in local storage', async () => {
    const mockedSignIn = jest.mocked(signInWithEmailAndPassword);
    mockedSignIn.mockResolvedValueOnce({
      user: { getIdToken: () => 'foo' },
    } as unknown as UserCredential);
    window.localStorage.setItem('emailForSignIn', '"<EMAIL>"');
    render(<LoginForm />);

    fillOutLoginForm(false, true);

    await waitFor(async () => {
      expect(mockedSignIn).toHaveBeenCalledWith(
        await getAuth(),
        '<EMAIL>',
        'password'
      );
      expect(signIn).toHaveBeenCalledWith('firebase-credentials', {
        idToken: 'foo',
        redirect: false,
        signInMethod: 'password',
        username: '<EMAIL>',
      });
      expect(window.localStorage.getItem('emailForSignIn')).toBeNull();
    });
  });

  it.each(['<EMAIL>', '<EMAIL>'])(
    'should automatically login if oob code is present and email address %s is in local storage',
    async (emailAddress) => {
      const mockedSignIn = jest.mocked(signInWithEmailLink);
      mockedSignIn.mockResolvedValueOnce({
        user: { getIdToken: () => 'foo' },
      } as unknown as UserCredential);
      window.localStorage.setItem('emailForSignIn', `"${emailAddress}"`);

      render(<LoginForm oobCode="oobCode" />);

      await waitFor(async () => {
        expect(mockedSignIn).toHaveBeenCalledWith(
          await getAuth(),
          emailAddress
        );
        expect(signIn).toHaveBeenCalledWith('firebase-credentials', {
          idToken: 'foo',
          redirect: false,
          signInMethod: 'emailLink',
          username: emailAddress,
        });
        expect(window.localStorage.getItem('emailForSignIn')).toBeNull();
      });
    }
  );

  it.each(['<EMAIL>', '<EMAIL>'])(
    'should not automatically login if oob code is present and email address %s is not in local storage',
    async (emailAddress) => {
      const mockedSignIn = jest.mocked(signInWithEmailLink);
      mockedSignIn.mockResolvedValueOnce({
        user: { getIdToken: () => 'foo' },
      } as unknown as UserCredential);
      render(<LoginForm oobCode="oobCode" />);

      fillOutLoginForm(emailAddress);

      await waitFor(async () => {
        expect(mockedSignIn).toHaveBeenCalledWith(
          await getAuth(),
          emailAddress
        );
        expect(signIn).toHaveBeenCalledWith('firebase-credentials', {
          idToken: 'foo',
          redirect: false,
          signInMethod: 'emailLink',
          username: emailAddress,
        });
        expect(window.localStorage.getItem('emailForSignIn')).toBeNull();
      });
    }
  );

  it.each([
    ['auth/invalid-credential', AUTH_INVALID_CREDENTIALS_ERROR],
    ['auth/invalid-login-credentials', AUTH_INVALID_CREDENTIALS_ERROR],
    ['auth/wrong-password', AUTH_INVALID_CREDENTIALS_ERROR],
    ['auth/user-not-found', AUTH_INVALID_CREDENTIALS_ERROR],
    ['auth/user-disabled', AUTH_ACCOUNT_DISABLED_ERROR],
    ['auth/too-many-requests', AUTH_TOO_MANY_REQUESTS_ERROR],
    ['auth/invalid-email', COMMON_INVALID_EMAIL_ERROR],
    ['auth/network-request-failed', COMMON_INTERNAL_SERVER_ERROR],
    ['auth/expired-action-code', AUTH_INVALID_SIGN_IN_CODE],
    ['auth/invalid-action-code', AUTH_INVALID_SIGN_IN_CODE],
  ])('should handle firebase authentication error: %s', async (code, error) => {
    render(<LoginForm />);

    const mockedSignIn = jest.mocked(signInWithEmailAndPassword);
    mockedSignIn.mockRejectedValueOnce({ code: code });

    fillOutLoginForm(true, true);

    await waitFor(() => {
      expect(screen.getByText(error)).toBeInTheDocument();
    });
  });

  it.each(['invalid', 'invalid.com', 'invalid:<EMAIL>'])(
    'should display an invalid email address error message if the email address is: %s',
    async (emailAddress) => {
      render(<LoginForm />);

      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: emailAddress },
      });

      fireEvent.click(screen.getByRole('button', { name: 'Log in' }));

      expect(
        await screen.findByText(COMMON_INVALID_EMAIL_ERROR)
      ).toBeInTheDocument();
    }
  );

  it.each(['<EMAIL>'])(
    'should display an error message if the email address is: %s',
    async (emailAddress) => {
      render(<LoginForm />);

      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: emailAddress },
      });

      fireEvent.click(screen.getByRole('button', { name: 'Log in' }));

      expect(
        await screen.findByText(AUTH_EMAIL_LINK_REQUIRED_ERROR)
      ).toBeInTheDocument();
    }
  );

  it.each(['AccessDenied', 'OAuthCallback'])(
    'should redirect to auth error page if an %s error is thrown',
    async (error) => {
      render(<LoginForm />);
      const mockedSignIn = jest.mocked(signInWithEmailAndPassword);
      mockedSignIn.mockResolvedValueOnce({
        user: { getIdToken: () => 'foo' },
      } as unknown as UserCredential);

      (signIn as jest.Mock).mockImplementationOnce(() => ({
        ok: false,
        status: 401,
        error,
      }));

      fillOutLoginForm(true, true);

      await waitFor(() => {
        expect(signIn).toHaveBeenCalledWith('firebase-credentials', {
          idToken: 'foo',
          redirect: false,
          signInMethod: 'password',
          username: '<EMAIL>',
        });
      });

      expect(mockRouter.push).toHaveBeenCalledWith('/auth/error');
    }
  );

  it('should redirect to generic error page if another error is thrown', async () => {
    const mockedSignIn = jest.mocked(signInWithEmailAndPassword);
    mockedSignIn.mockRejectedValueOnce({
      ok: false,
      status: 500,
      error: 'Configuration',
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <LoginForm />
      </ErrorBoundary>
    );

    fireEvent.change(screen.getByLabelText('Email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText('Password'), {
      target: { value: 'password' },
    });

    try {
      fireEvent.click(screen.getByRole('button', { name: 'Log in' }));
    } catch (error) {
      expect(
        await screen.findByRole('heading', {
          level: 1,
          name: 'Error detected',
        })
      ).toBeInTheDocument();
    }
  });

  it('should redirect to our reset password page', async () => {
    render(<LoginForm />);

    expect(
      screen.getByRole('link', { name: 'Forgot password?' })
    ).toHaveAttribute('href', '/auth/reset-password');
  });

  it('should have a back to access link log in button if prop is true', async () => {
    render(<LoginForm backToEmailLinkLogin />);

    fireEvent.click(
      screen.getByRole('button', { name: 'Back to access link log in' })
    );
    expect(mockRouter.push).toHaveBeenCalledWith('/auth/login');
  });
});
