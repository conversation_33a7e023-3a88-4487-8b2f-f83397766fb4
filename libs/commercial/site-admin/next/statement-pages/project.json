{"name": "commercial-site-admin-next-statement-pages", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/site-admin/next/statement-pages/src", "projectType": "library", "tags": ["commercial", "site-admin"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/site-admin/next/statement-pages/jest.config.ts", "passWithNoTests": false}}}}