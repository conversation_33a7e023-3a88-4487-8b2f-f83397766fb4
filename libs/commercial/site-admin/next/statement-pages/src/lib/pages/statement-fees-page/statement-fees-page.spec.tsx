import { COMMON_INTERNAL_SERVER_ERROR } from '@experience/shared/typescript/validation';
import {
  TEST_POD,
  TEST_SITE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_STATEMENT } from '@experience/commercial/statement-service/shared';
import { Toaster } from '@experience/shared/react/design-system';
import { render, screen } from '@testing-library/react';
import { useSWRWithErrorHandling } from '@experience/shared/react/hooks';
import StatementFeesPage from './statement-fees-page';
import axios from 'axios';
import userEvent from '@testing-library/user-event';

jest.mock('@experience/shared/react/hooks');
const mockedUseSWRWithErrorHandling = jest.mocked(useSWRWithErrorHandling);

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: mockPush }),
  useParams: () => ({
    id: 1,
  }),
}));

describe('StatementFeesPage', () => {
  beforeEach(() => {
    mockedUseSWRWithErrorHandling.mockReturnValue({
      data: {
        ...TEST_SITE,
        pods: [
          TEST_POD,
          {
            ...TEST_POD,
            ageYears: 0,
            installDate: '2023-04-12T08:38:12.000Z',
            id: '2',
            name: 'ZETA',
            ppid: 'NOVEMBER',
          },
        ],
      },
      fallback: undefined,
    });
  });

  it('should be defined', () => {
    const { baseElement } = render(<StatementFeesPage />);
    expect(baseElement).toBeDefined();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<StatementFeesPage />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should call axios post and navigate to the statement page on confirm', async () => {
    mockAxiosPost.mockResolvedValueOnce({ data: TEST_STATEMENT });
    render(<StatementFeesPage />);

    await userEvent.click(
      screen.getByRole('button', {
        name: 'Generate statement',
      })
    );
    await userEvent.click(screen.getByRole('menuitem', { name: 'Last month' }));

    expect(mockAxiosPost).toHaveBeenCalledWith(
      `/api/sites/${TEST_SITE.id}/statement?period=1`,
      {
        adjustedFees: [
          {
            fee: undefined,
            ppid: 'BAR',
          },
          {
            fee: undefined,
            ppid: 'NOVEMBER',
          },
        ],
      }
    );
    expect(mockPush).toHaveBeenCalledWith(
      `/sites/${TEST_SITE.id}/statement?period=1`
    );
  });

  it('should display an toast message when there is an axios error', async () => {
    mockAxiosPost.mockRejectedValueOnce(new Error('oops'));
    render(
      <>
        <Toaster />
        <StatementFeesPage />
      </>
    );

    await userEvent.click(
      screen.getByRole('button', {
        name: 'Generate statement',
      })
    );
    await userEvent.click(screen.getByRole('menuitem', { name: 'Last month' }));

    expect(
      await screen.findByText(COMMON_INTERNAL_SERVER_ERROR)
    ).toBeInTheDocument();
  });
});
