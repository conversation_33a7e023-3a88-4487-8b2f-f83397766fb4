import { MenuButton } from '@experience/shared/react/design-system';
import { SelectMonthModal } from '@experience/shared/react/modals';
import { useState } from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

export const DownloadGroupSitesStatsMenu = () => {
  const [open, setOpen] = useState(false);
  const apiEndpoint = '/api/sites/stats/csv';
  const description =
    'Download statistics for a given month. At group level it is currently only possible to download statistics from January 2023 onwards.';
  return (
    <>
      <MenuButton title="Download aggregated CSV">
        <MenuButton.Item
          className="text-xs"
          onClick={() => window.open(apiEndpoint, '_blank')}
        >
          Current month
        </MenuButton.Item>

        <MenuButton.Item
          className="text-xs"
          onClick={() =>
            window.open(
              `${apiEndpoint}?date=${dayjs
                .utc()
                .subtract(1, 'month')
                .startOf('month')}`,
              '_blank'
            )
          }
        >
          Previous month
        </MenuButton.Item>
        <MenuButton.Item className="text-xs" onClick={() => setOpen(true)}>
          Select month
        </MenuButton.Item>
      </MenuButton>
      <SelectMonthModal
        apiEndpoint={apiEndpoint}
        open={open}
        description={description}
        setOpen={setOpen}
        startYear={2023}
      />
    </>
  );
};

export default DownloadGroupSitesStatsMenu;
