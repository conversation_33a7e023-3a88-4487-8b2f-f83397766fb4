import {
  DRIVER_DUPLICATE_BULK_UPLOAD_ERROR,
  DRIVER_INVALID_BULK_UPLOAD_ERROR,
  DRIVER_SUCCESS_BULK_UPLOAD,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { Toaster } from '@experience/shared/react/design-system';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import AddMultipleDriversModal from './add-multiple-drivers-modal';
import axios from 'axios';
import toast from 'react-hot-toast';

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockAppend = jest.fn();
const mockFormData = {
  append: mockAppend,
};
jest.mock('form-data', () => jest.fn().mockImplementation(() => mockFormData));

const mockSetOpen = jest.fn();

const defaultProps = {
  open: true,
  setOpen: mockSetOpen,
};

describe('AddMultipleDriverModal', () => {
  afterEach(async () => {
    await waitFor(() => {
      toast.remove();
    });
  });

  it('should render correctly', () => {
    const { baseElement } = render(
      <AddMultipleDriversModal {...defaultProps} />,
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <AddMultipleDriversModal {...defaultProps} />,
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should disable confirm button when no file is selected', () => {
    render(<AddMultipleDriversModal {...defaultProps} />);

    expect(screen.getByRole('button', { name: /Confirm/i })).toBeDisabled();
  });

  it('should enable the confirm button if a file is selected', () => {
    render(<AddMultipleDriversModal {...defaultProps} />);

    const fileInput = screen.getByLabelText('File');
    const file = new File(['hello'], 'hello.csv', { type: 'text/csv' });

    fireEvent.change(fileInput, { target: { files: [file] } });

    expect(screen.getByRole('button', { name: 'Confirm' })).toBeEnabled();
  });

  it('should call API when file input is changed and uploaded', async () => {
    render(
      <>
        <Toaster />
        <AddMultipleDriversModal {...defaultProps} />
      </>,
    );

    const file = new File(['hello'], 'hello.csv', { type: 'text/csv' });

    fireEvent.click(screen.getByRole('button', { name: 'Select file' }));
    fireEvent.change(screen.getByLabelText('File'), {
      target: { files: [file] },
    });

    const confirmButton = screen.getByRole('button', { name: 'Confirm' });

    fireEvent.click(confirmButton);

    expect(confirmButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAppend).toHaveBeenCalledWith('file', file, {
        header: { 'Content-Type': 'multipart/form-data' },
      });
      expect(mockAxiosPost).toHaveBeenCalledWith(
        '/api/drivers/upload',
        mockFormData,
      );
      expect(mockMutate).toHaveBeenCalledWith(
        '/api/drivers?includeDeleted=true',
      );
      expect(confirmButton).not.toBeDisabled();
      expect(screen.getByText(DRIVER_SUCCESS_BULK_UPLOAD)).toBeInTheDocument();
    });
  });

  it.each([400, 413, 500])(
    'should return the error toast if a %s error is triggered',
    async (errorStatus) => {
      render(
        <>
          <Toaster />
          <AddMultipleDriversModal {...defaultProps} />
        </>,
      );

      mockAxiosPost.mockRejectedValueOnce({
        response: {
          status: errorStatus,
        },
      });

      const file = new File(['hello'], 'hello.csv', { type: 'text/csv' });

      fireEvent.click(screen.getByRole('button', { name: 'Select file' }));
      fireEvent.change(screen.getByLabelText('File'), {
        target: { files: [file] },
      });
      fireEvent.click(screen.getByRole('button', { name: 'Confirm' }));

      await waitFor(() => {
        expect(
          screen.getByText(DRIVER_INVALID_BULK_UPLOAD_ERROR),
        ).toBeInTheDocument();
      });
    },
  );

  it('should return the error toast if a 409 status is triggered', async () => {
    render(
      <>
        <Toaster />
        <AddMultipleDriversModal {...defaultProps} />
      </>,
    );

    mockAxiosPost.mockRejectedValueOnce({
      response: {
        status: 409,
      },
    });

    const file = new File(['hello'], 'hello.csv', { type: 'text/csv' });

    fireEvent.click(screen.getByRole('button', { name: 'Select file' }));
    fireEvent.change(screen.getByLabelText('File'), {
      target: { files: [file] },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Confirm' }));

    await waitFor(() => {
      expect(
        screen.getByText(DRIVER_DUPLICATE_BULK_UPLOAD_ERROR),
      ).toBeInTheDocument();
    });
  });

  it('should return the error boundary if a 500 error is triggered', async () => {
    render(
      <>
        <Toaster />
        <AddMultipleDriversModal {...defaultProps} />
      </>,
    );

    mockAxiosPost.mockRejectedValueOnce({
      response: {
        status: 504,
      },
    });

    const file = new File(['hello'], 'hello.csv', { type: 'text/csv' });

    fireEvent.click(screen.getByRole('button', { name: 'Select file' }));
    fireEvent.change(screen.getByLabelText('File'), {
      target: { files: [file] },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Confirm' }));

    await waitFor(() => {
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });
  });

  it('should have a link to download template file', () => {
    render(<AddMultipleDriversModal {...defaultProps} />);

    const downloadLink = screen.getByRole('link', {
      name: 'Download this CSV template',
    });

    expect(downloadLink).toHaveAttribute(
      'href',
      '/files/upload-drivers-template.csv',
    );
    expect(downloadLink).toHaveProperty('download');
  });
});
