import { DriverTariffTier } from '@experience/commercial/site-admin/typescript/domain-model';
import { MenuButton } from '@experience/shared/react/design-system';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

interface DownloadChargingDataMenuProps {
  driverId: number;
  tariffTier: DriverTariffTier;
}

export const DownloadDriverChargesMenu = ({
  driverId,
  tariffTier,
}: DownloadChargingDataMenuProps) => (
  <MenuButton title="Download CSV">
    <MenuButton.Item
      className="text-xs"
      onClick={() =>
        window.open(
          `/api/drivers/${driverId}/charges/csv?tariffTier=${tariffTier}`,
          '_blank'
        )
      }
    >
      Current month
    </MenuButton.Item>
    <MenuButton.Item
      className="text-xs"
      onClick={() =>
        window.open(
          `/api/drivers/${driverId}/charges/csv?tariffTier=${tariffTier}&date=${dayjs
            .utc()
            .subtract(1, 'month')
            .startOf('month')}`,
          '_blank'
        )
      }
    >
      Previous month
    </MenuButton.Item>
    <MenuButton.Item
      className="text-xs"
      onClick={() =>
        window.open(
          `/api/drivers/${driverId}/charges/csv?tariffTier=${tariffTier}&all=true`,
          '_blank'
        )
      }
    >
      All charges
    </MenuButton.Item>
  </MenuButton>
);

export default DownloadDriverChargesMenu;
