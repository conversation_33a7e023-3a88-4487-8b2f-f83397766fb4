import { COMMON_REQUIRED_ERROR } from '@experience/shared/typescript/validation';
import {
  DENIED_DOMAIN_ERROR,
  DOMAIN_ALPHANUMERIC_ERROR,
  DOMAIN_MAX_LENGTH,
  DOMAIN_MAX_LENGTH_ERROR,
  DOMAIN_PREFIX_ERROR,
  DUPLICATE_DOMAIN_ERROR,
  DomainErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { ErrorBoundary } from '@sentry/react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import AddDomainModal from './add-domain-modal';
import axios from 'axios';

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockPush = jest.fn();
const mockPathname = jest.fn(() => '/domains');
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: (url: string) => mockPush(url),
  }),
  usePathname: () => mockPathname(),
}));

const mockSetOpen = jest.fn();

const defaultProps = {
  open: true,
  setOpen: mockSetOpen,
};

describe('AddDomainModal', () => {
  beforeEach(() => {
    mockPathname.mockReturnValue('/drivers/domains');
  });

  it('should render correctly', () => {
    const { baseElement } = render(<AddDomainModal {...defaultProps} />);

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<AddDomainModal {...defaultProps} />);

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal if cancel button is clicked', () => {
    render(<AddDomainModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should call API and route to domain page when form is submitted if input is valid and user is on drivers page', async () => {
    mockPathname.mockReturnValue('/drivers');

    render(<AddDomainModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 'testing.com' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    await waitFor(() => {
      expect(mockAxiosPost).toHaveBeenCalledWith('/api/domains', {
        domainName: 'testing.com',
      });
      expect(mockMutate).toHaveBeenCalledWith('/api/domains');
      expect(mockPush).toHaveBeenCalledWith('/drivers/domains');
    });
  });

  it('should call API and close modal when form is submitted if input is valid and user is on domains page', async () => {
    render(<AddDomainModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 'testing.com' },
    });

    const submitButton = screen.getByRole('button', { name: 'Add domain' });

    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosPost).toHaveBeenCalledWith('/api/domains', {
        domainName: 'testing.com',
      });
      expect(mockMutate).toHaveBeenCalledWith('/api/domains');
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should show validation message on form submission if the input field is empty', async () => {
    render(<AddDomainModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    expect(await screen.findByText(COMMON_REQUIRED_ERROR)).toBeInTheDocument();
  });

  it('should show validation message on form submission if the input field exceeds max length', async () => {
    render(<AddDomainModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 't'.repeat(DOMAIN_MAX_LENGTH + 1) },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    expect(
      await screen.findByText(DOMAIN_MAX_LENGTH_ERROR)
    ).toBeInTheDocument();
  });

  it('should show validation message on form submission if the input field contains special characters', async () => {
    render(<AddDomainModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 'e&^5%$£.com' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    expect(
      await screen.findByText(DOMAIN_ALPHANUMERIC_ERROR)
    ).toBeInTheDocument();
  });

  it('should show validation message on form submission if the input field contains "www." at the start of the input', async () => {
    render(<AddDomainModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 'www.testing.com' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    expect(await screen.findByText(DOMAIN_PREFIX_ERROR)).toBeInTheDocument();
  });

  it('should show validation message on form submission if the domain name already exists', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        data: {
          error: DomainErrorCodes.DUPLICATE_DOMAIN_ERROR,
          message: DUPLICATE_DOMAIN_ERROR,
          statusCode: 409,
        },
        status: 409,
      },
    });

    render(<AddDomainModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 'testing.com' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    expect(await screen.findByText(DUPLICATE_DOMAIN_ERROR)).toBeInTheDocument();
  });

  it('should show validation message on form submission if the domain name is on the blacklist', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        data: {
          error: DomainErrorCodes.DENIED_DOMAIN_ERROR,
          message: DENIED_DOMAIN_ERROR,
          statusCode: 403,
        },
        status: 403,
      },
    });

    render(<AddDomainModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 'testing.com' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    expect(await screen.findByText(DENIED_DOMAIN_ERROR)).toBeInTheDocument();
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <AddDomainModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.change(screen.getByLabelText('Domain name'), {
      target: { value: 'testing.com' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Add domain' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should autofocus the first input', () => {
    render(<AddDomainModal {...defaultProps} />);
    expect(screen.getByLabelText('Domain name')).toHaveFocus();
  });
});
