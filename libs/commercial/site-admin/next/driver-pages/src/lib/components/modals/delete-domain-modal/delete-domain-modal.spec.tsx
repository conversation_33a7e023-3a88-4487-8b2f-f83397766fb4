import { ErrorBoundary } from '@sentry/react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import DeleteDomainModal from './delete-domain-modal';
import axios from 'axios';
import dayjs from 'dayjs';

jest.mock('axios');
const mockAxiosDelete = jest.mocked(axios.delete);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockRouter = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
}));

const mockSetOpen = jest.fn();

const defaultProps = {
  open: true,
  setOpen: mockSetOpen,
  domain: {
    domainName: 'pod-point.com',
    groupId: 1,
    id: 1,
    activatedOn: dayjs().toDate(),
  },
};

describe('DeleteDomainModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(<DeleteDomainModal {...defaultProps} />);

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<DeleteDomainModal {...defaultProps} />);

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal if cancel button is clicked', () => {
    render(<DeleteDomainModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should pre-populate the domain name', () => {
    render(<DeleteDomainModal {...defaultProps} />);

    expect(
      screen.getByText(
        'Are you sure you want to delete the domain pod-point.com?'
      )
    ).toBeInTheDocument();
  });

  it('should call API and close modal when delete is clicked', async () => {
    render(<DeleteDomainModal {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: 'Delete' });

    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosDelete).toHaveBeenCalledWith('/api/domains/1');
      expect(mockMutate).toHaveBeenCalledWith('/api/domains');
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosDelete.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <DeleteDomainModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Delete' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });
});
