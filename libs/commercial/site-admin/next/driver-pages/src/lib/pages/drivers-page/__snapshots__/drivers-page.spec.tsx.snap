// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`DriversPage should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Drivers
        </h1>
      </div>
      <div
        class="flex items-center"
      >
        <p
          class="text-md font-normal break-words"
        >
          List of 2 drivers permitted to use your network.
        </p>
      </div>
      <div
        class="flex justify-end items-center print:hidden"
      >
        <div
          class="relative inline-block text-left"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed ml-20"
            data-headlessui-state=""
            id="headlessui-menu-button-:test-id-2"
            type="button"
          >
            <span>
              Add drivers
            </span>
            <svg
              class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </button>
        </div>
      </div>
    </header>
    <div
      class="flex space-x-4"
    >
      <span
        aria-label="Drivers"
        class="px-2 font-semibold text-primary border-b border-b-primary"
      >
        Drivers
      </span>
      <a
        class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden"
        href="/drivers/domains"
      >
        Domains
      </a>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-36"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="status"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="status"
              id="headlessui-label-:test-id-7"
            >
              Status
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-7 status"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="status"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-28"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="tariffTier"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="tariffTier"
              id="headlessui-label-:test-id-15"
            >
              Tariff tier
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-15 tariffTier"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="tariffTier"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div>
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="canExpense"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="canExpense"
              id="headlessui-label-:test-id-23"
            >
              Can expense
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-23 canExpense"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="canExpense"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="items-center print:hidden self-end"
        >
          <div
            class="relative"
          >
            <span
              class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                  />
                </g>
              </svg>
            </span>
            <div
              class=""
            >
              <label
                class="text-md font-bold sr-only block mb-2"
                for="table-search"
              >
                Search
              </label>
            </div>
            <input
              aria-label="Search table"
              class="border-none focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
              id="table-search"
              placeholder="Search"
              role="searchbox"
              value=""
            />
            <button
              aria-label="Clear search"
              class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral focus:outline-hidden"
              name="Clear search"
            >
              <svg
                class="h-3 w-3 stroke-1 stroke-current fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <g>
                    <line
                      x1="5.16"
                      x2="18.94"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                  <g>
                    <line
                      x1="18.94"
                      x2="5.16"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                </g>
              </svg>
            </button>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="infinite-scroll-component__outerdiv"
    >
      <div
        class="infinite-scroll-component "
        style="height: auto; overflow: auto; -webkit-overflow-scrolling: touch;"
      >
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of drivers
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Name
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      >
                        <svg
                          class="fill-current h-4 w-4 stroke-current stroke-2"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            transform="translate(4 4)"
                          >
                            <path
                              d="m20.9,8.67L12.81.58c-.14-.16-.34-.27-.56-.27,0,0,0,0,0,0-.19,0-.39.07-.54.22L3.1,9.13c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l7.34-7.34v20.09c0,.41.34.75.75.75s.75-.34.75-.75V2.89l6.85,6.85c.29.29.77.29,1.06,0s.29-.77,0-1.06Z"
                            />
                          </g>
                        </svg>
                      </span>
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Email
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Date registered
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Status
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Tariff tier
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer justify-center"
                      type="button"
                    >
                      Can expense
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Homer Simpson
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 break-all"
                  >
                    <EMAIL>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    13th June 2021
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Pending"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/10 border border-neutral text-neutral"
                      role="status"
                    >
                      Pending
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Member
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-primary"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          Yes
                        </title>
                        <g>
                          <path
                            d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                          />
                          <path
                            d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-1"
                    >
                      <button
                        aria-label="Edit driver Homer Simpson"
                        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-driver-1"
                        id="edit-driver-1"
                        name="edit-driver-1"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                      <button
                        aria-label="Delete driver Homer Simpson"
                        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="delete-driver-1"
                        id="delete-driver-1"
                        name="delete-driver-1"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                          />
                          <path
                            d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                          />
                          <path
                            d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                          />
                        </svg>
                      </button>
                      <button
                        aria-label="Invite driver Homer Simpson"
                        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="invite-driver-1"
                        id="invite-driver-1"
                        name="invite-driver-1"
                        type="button"
                      >
                        <div
                          role="tooltip"
                          tabindex="0"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 512 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M64 112c-8.8 0-16 7.2-16 16v22.1L220.5 291.7c20.7 17 50.4 17 71.1 0L464 150.1V128c0-8.8-7.2-16-16-16H64zM48 212.2V384c0 8.8 7.2 16 16 16H448c8.8 0 16-7.2 16-16V212.2L322 328.8c-38.4 31.5-93.7 31.5-132 0L48 212.2zM0 128C0 92.7 28.7 64 64 64H448c35.3 0 64 28.7 64 64V384c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128z"
                            />
                          </svg>
                        </div>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                      href="/drivers/1?tariffTier=Driver"
                    >
                      Jane Bloggs
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 break-all"
                  >
                    <EMAIL>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    13th June 2021
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Registered"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success"
                      role="status"
                    >
                      Registered
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Driver
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-primary"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          Yes
                        </title>
                        <g>
                          <path
                            d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                          />
                          <path
                            d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-1"
                    >
                      <button
                        aria-label="Edit driver Jane Bloggs"
                        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-driver-1"
                        id="edit-driver-1"
                        name="edit-driver-1"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                      <button
                        aria-label="Delete driver Jane Bloggs"
                        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="delete-driver-1"
                        id="delete-driver-1"
                        name="delete-driver-1"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                          />
                          <path
                            d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                          />
                          <path
                            d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                          />
                        </svg>
                      </button>
                      <button
                        aria-label="Invite driver Jane Bloggs"
                        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="invite-driver-1"
                        id="invite-driver-1"
                        name="invite-driver-1"
                        type="button"
                      >
                        <div
                          role="tooltip"
                          tabindex="0"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 512 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M64 112c-8.8 0-16 7.2-16 16v22.1L220.5 291.7c20.7 17 50.4 17 71.1 0L464 150.1V128c0-8.8-7.2-16-16-16H64zM48 212.2V384c0 8.8 7.2 16 16 16H448c8.8 0 16-7.2 16-16V212.2L322 328.8c-38.4 31.5-93.7 31.5-132 0L48 212.2zM0 128C0 92.7 28.7 64 64 64H448c35.3 0 64 28.7 64 64V384c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128z"
                            />
                          </svg>
                        </div>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;
