import {
  Group,
  GroupType,
  User,
  canSwitchGroup,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { LoadingOverlay } from '@experience/shared/react/design-system';
import { NotFoundPage } from '@experience/shared/react/error-pages';
import { PageHeader } from '@experience/shared/react/headings';
import {
  useGroup,
  useGroups,
  useUser,
} from '@experience/commercial/site-admin/next/shared';
import GroupsTable from '../../components/groups-table/groups-table';
import pluralize from 'pluralize';

export const GroupsPage = () => {
  const group = useGroup() as Group;
  const groups = useGroups() as Group[];
  const user = useUser() as User;

  if (!group || !groups || !user) {
    return <LoadingOverlay />;
  }

  if (!canSwitchGroup(user.groupName as string, user.groupType as GroupType)) {
    return <NotFoundPage />;
  }

  return (
    <>
      <div className="flex flex-col space-y-2">
        <PageHeader
          heading="Groups"
          subHeading={pluralize(
            `List of ${groups.length} ${pluralize('groups', groups.length)}`
          )}
        />
      </div>
      <GroupsTable group={group} groups={groups} />
    </>
  );
};

export default GroupsPage;
