import {
  ActionGroup,
  ActionItem,
  AdvancedTable,
  CurrencyPoundIcon,
  DownloadIcon,
  statusBadgeMapper,
} from '@experience/shared/react/design-system';
import { ColumnDef } from '@tanstack/react-table';
import { SUBSCRIPTION_INVOICE_STATUS_MAP } from '@experience/commercial/statement-service/next/shared';
import { SubscriptionInvoice } from '@experience/commercial/site-admin/typescript/domain-model';
import { formatPenceAsCurrencyString } from '@experience/shared/typescript/utils';

interface SubscriptionPaymentsTableProps {
  subscriptionsPayments: SubscriptionInvoice[];
}

const columns: ColumnDef<SubscriptionInvoice>[] = [
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row: { original: payment } }) =>
      statusBadgeMapper({
        status: payment.status,
        statusMap: SUBSCRIPTION_INVOICE_STATUS_MAP,
      }),
  },
  {
    accessorKey: 'created',
    header: 'Created',
  },
  {
    accessorKey: 'amount',
    header: 'Amount',
    cell: ({
      row: {
        original: { amount },
      },
    }) => formatPenceAsCurrencyString({ amount }),
  },
  {
    accessorKey: 'due',
    header: 'Due',
  },
  {
    accessorKey: 'invoiceNumber',
    header: 'Invoice number',
    cell: ({ row: { original: payment } }) =>
      payment.status === 'draft' ? 'ADF-DRAFT' : payment.invoiceNumber ?? '-',
  },
  {
    accessorKey: 'customerEmail',
    header: 'Customer email',
  },
  {
    header: 'Actions',
    cell: ({ row: { original: payment } }) => (
      <ActionGroup>
        {[
          payment.status !== 'paid' ? (
            <ActionItem
              key={`pay-online-${payment.invoiceNumber}`}
              aria-label={`Pay online for invoice ${payment.invoiceNumber}`}
              href={payment.hostedInvoiceUrl}
              icon={<CurrencyPoundIcon.LIGHT />}
              text="Pay online"
            />
          ) : null,
          <ActionItem
            key={`download-${payment.invoiceNumber}`}
            aria-label={`Download invoice ${payment.invoiceNumber}`}
            href={payment.invoicePdfUrl}
            icon={<DownloadIcon.LIGHT />}
            text="Invoice"
          />,
        ]
          .filter((item) => item !== null)
          .map((item) => item)}
      </ActionGroup>
    ),
  },
];

export const SubscriptionPaymentsTable = ({
  subscriptionsPayments,
}: SubscriptionPaymentsTableProps) => (
  <AdvancedTable
    caption="Table of subscription payments"
    columns={columns}
    data={subscriptionsPayments}
    id="subscription-payments-table"
    initialSort={[{ id: 'created', desc: true }]}
    showSearchField
  />
);

export default SubscriptionPaymentsTable;
