import { AdvancedTable, Spinner } from '@experience/shared/react/design-system';
import { ColumnDef } from '@tanstack/react-table';
import {
  Pod,
  SubscriptionCharger,
} from '@experience/commercial/site-admin/typescript/domain-model';
import useSWR from 'swr';

export interface SubscriptionChargersTableProps {
  subscriptionChargers: SubscriptionCharger[];
}

type SubscriptionChargerWithAdditionalInfo = SubscriptionCharger & {
  name?: string;
  siteName?: string;
};

const columns: ColumnDef<SubscriptionChargerWithAdditionalInfo>[] = [
  {
    accessorKey: 'ppid',
    header: 'PPID',
  },
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'siteName',
    header: 'Site',
  },
  {
    accessorKey: 'socket',
    header: 'Socket',
  },
];

export const SubscriptionChargersTable = ({
  subscriptionChargers,
}: SubscriptionChargersTableProps) => {
  const { data: allChargers, isLoading } = useSWR<Pod[]>('/api/pods');

  if (isLoading) {
    return (
      <div className="flex justify-center mt-2">
        <Spinner />
      </div>
    );
  }

  const chargersWithAdditionalInfo: SubscriptionChargerWithAdditionalInfo[] =
    subscriptionChargers.map((charger) => {
      const matchingCharger = allChargers?.find(
        (pod) => pod.ppid === charger.ppid
      );
      return {
        ...charger,
        name: matchingCharger?.name,
        siteName: matchingCharger?.site?.address?.name,
      };
    });

  return (
    <AdvancedTable
      caption="Table of subscription chargers"
      columns={columns}
      data={chargersWithAdditionalInfo}
      id="subscription-chargers-table"
      showSearchField
    />
  );
};
