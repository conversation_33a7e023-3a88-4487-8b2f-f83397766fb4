// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Business details should match snapshot 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-center"
      >
        <h3
          class="text-lg font-bold pb-1"
        >
          Business name
        </h3>
      </div>
      <p
        class="text-md font-normal break-words"
      >
        Test Group Inc Invoice office
      </p>
      <h3
        class="text-lg font-bold pb-2 pt-2"
      >
        Business email address
      </h3>
      <p
        class="text-md font-normal break-words"
      >
        <EMAIL>
      </p>
      <h3
        class="text-lg font-bold pb-2 pt-2"
      >
        Account reference
      </h3>
      <p
        class="text-md font-normal break-words"
      >
        *********
      </p>
      <h3
        class="text-lg font-bold pb-2 pt-2"
      >
        PO number
      </h3>
      <p
        class="text-md font-normal break-words"
      >
        UK10010001
      </p>
    </section>
  </div>
</body>
`;
