import {
  Card,
  Heading,
  HeadingSizes,
  Paragraph,
} from '@experience/shared/react/design-system';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import FaqsSection, { faqs } from '../../components/faqs-section/faqs-section';
import PagesSection, {
  pages,
} from '../../components/pages-section/pages-section';

export const HelpPage = () => {
  const router = useRouter();
  const currentHash = window.location.hash.slice(1);
  const [hash, setHash] = useState(currentHash);
  const scrolledRef = useRef(false);

  useEffect(() => {
    if (currentHash && !scrolledRef.current) {
      const id = currentHash.replace('#', '');
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        scrolledRef.current = true;
      }
    }
  }, [currentHash]);

  const handleLink = (value: string) => {
    setHash(value);
    router.push({
      hash: value,
    });
  };

  const sections = ['Demo videos', 'FAQs', 'Pages', 'Contacting us'];

  return (
    <div className="space-y-4">
      <header className="w-full h-96 bg-navy bg-center bg-contain bg-no-repeat  bg-[url('/hero-background.png')]">
        <div className="flex text-center justify-center items-end w-full h-full">
          <Heading.H1 className="text-white text-4xl align-bottom pb-6">
            How can we help you?
          </Heading.H1>
        </div>
      </header>

      <section>
        <Heading.H2 fontSize={HeadingSizes.M}>Section links</Heading.H2>
        <VerticalSpacer />

        <div className="grid grid-cols-6 gap-4 py-2">
          {sections.map((value) => (
            <a
              href={`#${encodeURI(value)}`}
              key={value}
              className="flex h-24 p-2 rounded-sm bg-white font-bold text-center justify-center items-center hover:underline"
            >
              {value}
            </a>
          ))}
        </div>
      </section>

      <section>
        <Heading.H2 fontSize={HeadingSizes.M}>FAQ links</Heading.H2>
        <VerticalSpacer />

        <div className="grid grid-cols-6 gap-4 py-2">
          {faqs.map((faq) => (
            <button
              onClick={() => handleLink(`FAQs-${encodeURI(faq.title)}`)}
              key={faq.title}
              className="flex h-24 p-2 rounded-sm bg-white font-bold text-center justify-center items-center hover:underline"
            >
              {faq.title}
            </button>
          ))}
        </div>
      </section>

      <section>
        <Heading.H2 fontSize={HeadingSizes.M}>Pages</Heading.H2>
        <VerticalSpacer />

        <div className="grid grid-cols-6 gap-4 py-2">
          {pages.map((page) => (
            <button
              onClick={() => handleLink(`Pages-${encodeURI(page.title)}`)}
              key={page.title}
              className="flex h-24 p-2 rounded-sm bg-white font-bold text-center justify-center items-center hover:underline"
            >
              {page.title}
            </button>
          ))}
        </div>
      </section>

      <section id="Demo%20videos">
        <Heading.H3>Demo videos</Heading.H3>
        <VerticalSpacer />
        <Card>
          <Paragraph>
            There are no demo videos here yet. We are hard at work to improve
            this help page and will be adding these soon.
          </Paragraph>
        </Card>
      </section>

      <FaqsSection hash={hash} />

      <PagesSection hash={hash} />

      <section id="Contacting%20us">
        <Heading.H3>Contacting us</Heading.H3>
        <VerticalSpacer />
        <Card>
          <Paragraph>
            You can get in touch with us by phoning our Support team on 020 7247
            4114 or using the &quot;Report an issue&quot; button in the service.
            EV drivers can report problems with chargers themselves via the app,
            too.
          </Paragraph>
          <Paragraph>
            Take a look below to be sure you&apos;re reaching out to the right
            team for your query.
          </Paragraph>
          <ul className="list-disc list-inside">
            <li>
              Faulty charger queries & reports, Maintenance update requests -
              Customer Support & Technical Team via the &quot;Report an
              issue&quot; button
            </li>
            <li>
              Warranty, Servicing & Maintenance - Aftersales Account Manager
            </li>
            <li>
              Site Management Service related support - Customer Success
              Management via the &quot;Report an issue&quot; button
            </li>
            <li>
              Invoice & Statement Queries - Accounts team via
              <EMAIL>
            </li>
            <li>
              New or existing sales & passive bay activation - Commercial Sales
              team
            </li>
          </ul>
        </Card>
      </section>
      <VerticalSpacer />
    </div>
  );
};

export default HelpPage;
