// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`SitePage should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Discovery House, EC1Y 8QE
        </h1>
      </div>
      <div
        class="flex items-center col-span-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
        </p>
      </div>
    </header>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-center mb-3"
        >
          <h3
            class="text-lg font-bold pb-0"
          >
            Opening times
          </h3>
          <button
            aria-label="Edit opening times"
            class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-opening-times"
            id="edit-opening-times"
            name="edit-opening-times"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Opening times table
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3 sr-only"
                    scope="col"
                  >
                    Day
                  </th>
                  <th
                    class="text-left p-3 sr-only"
                    scope="col"
                  >
                    Time
                  </th>
                </tr>
              </thead>
              <tbody
                class=""
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal font-bold px-2"
                  >
                    Monday
                  </td>
                  <td
                    class="whitespace-normal"
                  >
                    10:30 - 12:30
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal font-bold px-2"
                  >
                    Tuesday
                  </td>
                  <td
                    class="whitespace-normal"
                  >
                    All day
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal font-bold px-2"
                  >
                    Wednesday
                  </td>
                  <td
                    class="whitespace-normal"
                  >
                    10:30 - 12:30
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal font-bold px-2"
                  >
                    Thursday
                  </td>
                  <td
                    class="whitespace-normal"
                  >
                    10:30 - 12:30
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal font-bold px-2"
                  >
                    Friday
                  </td>
                  <td
                    class="whitespace-normal"
                  >
                    10:30 - 12:30
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal font-bold px-2"
                  >
                    Saturday
                  </td>
                  <td
                    class="whitespace-normal"
                  >
                    10:30 - 21:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal font-bold px-2"
                  >
                    Sunday
                  </td>
                  <td
                    class="whitespace-normal"
                  >
                    10:30 - 21:00
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-center mb-3"
        >
          <h3
            class="text-lg font-bold pb-0"
          >
            Additional information
          </h3>
          <button
            aria-label="Edit additional info"
            class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-additional-info"
            id="edit-additional-info"
            name="edit-additional-info"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div
          class="line-clamp-7"
        >
          <p
            class="text-md font-normal break-words"
          >
            Parking limited during the day
          </p>
        </div>
      </section>
      <div
        class="rounded-sm overflow-hidden"
      >
        <div
          data-testid="map"
          style="width: 100%; height: 100%; position: relative; z-index: 0; min-height: 12rem;"
        />
      </div>
      <section
        class="p-3 rounded-sm bg-white h-full"
      >
        <div
          class="flex justify-between items-center mb-3"
        >
          <h3
            class="text-lg font-bold pb-0"
          >
            Contact details
          </h3>
          <button
            aria-label="Edit contact details"
            class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-contact-details"
            id="edit-contact-details"
            name="edit-contact-details"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <p
          class="text-lg font-normal break-words"
        >
          Joe Blogs
        </p>
        <p
          class="text-lg font-normal break-words"
        >
          0123456789
        </p>
        <p
          class="text-lg font-normal break-words"
        >
          <EMAIL>
        </p>
      </section>
      <section
        class="p-3 rounded-sm bg-white h-full"
      >
        <div
          class="flex justify-between items-center mb-3"
        >
          <h3
            class="text-lg font-bold pb-0"
          >
            Energy cost
          </h3>
          <button
            aria-label="Edit energy cost"
            class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-energy-cost"
            id="edit-energy-cost"
            name="edit-energy-cost"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <p
          class="text-lg font-normal break-words"
        >
          16.7p
           per kWh
        </p>
        <div
          class="pb-4"
        />
        <p
          class="text-xs font-normal break-words"
        >
          The amount you pay your energy supplier for electricity. If energy cost is not set the price will default to 14p per kWh.
        </p>
      </section>
      <section
        class="p-3 rounded-sm bg-primary/20"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charging information
        </h3>
        <p
          class="text-lg font-normal break-words"
        >
          Display and export charging data
        </p>
        <div
          class="pb-2"
        />
        <div
          class="relative inline-block text-left"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
            data-headlessui-state=""
            id="headlessui-menu-button-:test-id-2"
            type="button"
          >
            <span>
              Download CSV
            </span>
            <svg
              class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div
          class="pb-2"
        />
      </section>
    </div>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Charging stats - this month so far
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      So far this month you have generated
            £0.00
            in revenue from 1 charger
            and delivered undefined kWh of
            energy
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy delivered
        </h3>
        <div>
          <p
            class="text-5xl leading-normal font-medium"
          >
            Unknown
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          CO
          <sub
            class="sub"
          >
            2
          </sub>
           avoided*
        </h3>
        <div>
          <p
            class="text-5xl leading-normal font-medium"
          >
            Unknown
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Revenue
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £0.00
        </span>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy cost
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £0.00
        </span>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Estimated based on calculations. We are working hard to improve the accuracy of CO
      <sub
        class="sub"
      >
        2
      </sub>
       data by improving data modelling. Please contact us if you have any questions regarding this information.
    </p>
    <div
      class="pb-4"
    />
    <div
      class="flex justify-between"
    >
      <h2
        class="text-2xl"
      >
        Chargers
      </h2>
      <button
        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
        type="button"
      >
        Report an issue
      </button>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of chargers
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Name
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,8.67L12.81.58c-.14-.16-.34-.27-.56-.27,0,0,0,0,0,0-.19,0-.39.07-.54.22L3.1,9.13c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l7.34-7.34v20.09c0,.41.34.75.75.75s.75-.34.75-.75V2.89l6.85,6.85c.29.29.77.29,1.06,0s.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  PPID
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Model
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Last contact
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Last complete charge
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Last known status
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <a
                  class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                  href="/pods/BAR"
                >
                  FOO
                </a>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                BAR
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                T7-S
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex flex-col"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="p-1.5"
                    >
                      12/08/2022 - 08:38:12
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex flex-row"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="p-1.5"
                    >
                      <div
                        class="flex items-center"
                      >
                        <svg
                          aria-hidden="true"
                          class="fill-current h-4 w-4 mr-2 text-sky"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="16"
                            cy="16"
                            r="12"
                          />
                        </svg>
                        Available
                      </div>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
