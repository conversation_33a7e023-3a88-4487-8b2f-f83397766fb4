import { ADDITIONAL_INFO_MAX_LENGTH_ERROR } from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  TEST_OPENING_TIMES,
  TEST_UPSERT_ADDITIONAL_INFO_REQUEST,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import EditSiteAdditionalInfoModal from './edit-site-additional-info-modal';
import axios from 'axios';
import userEvent from '@testing-library/user-event';

const mockSetOpen = jest.fn();

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const additionalInfoModalDefaultProps = {
  additionalInfo: TEST_OPENING_TIMES.notes[0],
  open: true,
  setOpen: mockSetOpen,
  siteId: 1,
};

describe('EditSiteAdditionalInfoModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render successfully', () => {
    const { baseElement } = render(
      <EditSiteAdditionalInfoModal {...additionalInfoModalDefaultProps} />
    );

    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <EditSiteAdditionalInfoModal {...additionalInfoModalDefaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal if cancel button is clicked', async () => {
    render(
      <EditSiteAdditionalInfoModal {...additionalInfoModalDefaultProps} />
    );

    await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should not persist the data if the modal is closed', async () => {
    render(
      <EditSiteAdditionalInfoModal {...additionalInfoModalDefaultProps} />
    );

    expect(screen.getByRole('textbox')).toHaveValue(
      'Parking limited during the day'
    );

    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'new value' },
    });

    await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(screen.getByRole('textbox')).toHaveValue(
      'Parking limited during the day'
    );
  });

  it('should pre-populate the text area', () => {
    render(
      <EditSiteAdditionalInfoModal {...additionalInfoModalDefaultProps} />
    );

    expect(screen.getByRole('textbox')).toHaveValue(
      'Parking limited during the day'
    );
  });

  it('should call API and close modal when form is submitted', async () => {
    render(
      <EditSiteAdditionalInfoModal {...additionalInfoModalDefaultProps} />
    );

    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'Parking limited during the day' },
    });

    const submitButton = screen.getByRole('button', { name: 'Save changes' });

    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosPost).toHaveBeenCalledWith(
        '/api/sites/1/additional-information',
        TEST_UPSERT_ADDITIONAL_INFO_REQUEST
      );
      expect(mockMutate).toHaveBeenCalledWith('/api/sites/1');
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should fail validation and display an error if there are more than 1000 characters', async () => {
    render(
      <EditSiteAdditionalInfoModal {...additionalInfoModalDefaultProps} />
    );

    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: `${'x'.repeat(1001)}` },
    });

    screen.getByRole('button', { name: 'Save changes' }).click();

    expect(
      await screen.findByText(ADDITIONAL_INFO_MAX_LENGTH_ERROR)
    ).toBeInTheDocument();
  });
});
