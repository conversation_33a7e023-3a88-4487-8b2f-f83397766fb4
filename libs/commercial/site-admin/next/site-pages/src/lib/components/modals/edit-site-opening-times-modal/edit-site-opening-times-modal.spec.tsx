import {
  Days,
  TEST_OPENING_TIMES,
  TEST_UPSERT_OPENING_TIMES_REQUEST,
  TEST_UPSERT_OPENING_TIMES_REQUEST_WITH_CLOSED_DAYS,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  OPENING_TIME_END_GREATER_THAN_START_ERROR,
  OPENING_TIME_INVALID_TIME_ERROR,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import EditSiteOpeningTimesModal from './edit-site-opening-times-modal';
import axios from 'axios';
import userEvent from '@testing-library/user-event';

const mockSetOpen = jest.fn();

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const openingTimesModalDefaultProps = {
  data: TEST_OPENING_TIMES,
  open: true,
  setOpen: mockSetOpen,
  siteId: 1,
};

describe('EditSiteOpeningTimesModal', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />
    );

    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal if cancel button is clicked', () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should not persist the data if the modal is closed', () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    expect(screen.getByLabelText('Monday to')).toHaveValue('12:30');

    fireEvent.change(screen.getByLabelText('Monday to'), {
      target: { value: '10:00' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(screen.getByLabelText('Monday to')).toHaveValue('12:30');
  });

  it('should pre-populate the fields', () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);
    expect(screen.getByLabelText('Monday from')).toHaveValue('10:30');
    expect(screen.getByLabelText('Monday to')).toHaveValue('12:30');
    expect(
      screen.getByRole('switch', { name: 'Monday all day' })
    ).toHaveAttribute('aria-checked', 'false');
    expect(
      screen.getByRole('switch', { name: 'Tuesday all day' })
    ).toHaveAttribute('aria-checked', 'true');
  });

  it('should call API and close modal when form is submitted if input is valid', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    fireEvent.change(screen.getByLabelText('Monday to'), {
      target: { value: '11:00' },
    });

    const submitButton = screen.getByRole('button', { name: 'Save changes' });

    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosPost).toHaveBeenCalledWith(
        '/api/sites/1/opening-times',
        TEST_UPSERT_OPENING_TIMES_REQUEST
      );
      expect(mockMutate).toHaveBeenCalledWith('/api/sites/1');
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should disable the input elements if all day is selected', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    expect(screen.getByLabelText('Monday to')).not.toBeDisabled();

    await userEvent.click(
      screen.getByRole('switch', { name: 'Monday all day' })
    );

    expect(screen.getByLabelText('Monday to')).toBeDisabled();
  });

  it('should disable the input elements if closed is selected', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    expect(screen.getByLabelText('Monday to')).not.toBeDisabled();

    await userEvent.click(screen.getByRole('switch', { name: 'Monday open' }));

    expect(screen.getByLabelText('Monday to')).toBeDisabled();
    expect(screen.getByLabelText('Monday to')).toHaveValue('Closed');
  });

  it('should disable the all day toggle when the open toggle is switched off', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    expect(
      screen.getByRole('switch', { name: 'Monday all day' })
    ).not.toBeDisabled();

    await userEvent.click(screen.getByRole('switch', { name: 'Monday open' }));

    expect(
      screen.getByRole('switch', { name: 'Monday all day' })
    ).toBeDisabled();
  });

  it('should display All day when all day is selected', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    expect(screen.getByLabelText('Monday from')).toHaveValue('10:30');
    expect(screen.getByLabelText('Monday to')).toHaveValue('12:30');

    await userEvent.click(
      screen.getByRole('switch', { name: 'Monday all day' })
    );

    expect(screen.getByLabelText('Monday from')).toHaveValue('All day');
    expect(screen.getByLabelText('Monday to')).toHaveValue('All day');
  });

  it('should display closed when closed is selected', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    expect(screen.getByLabelText('Monday from')).toHaveValue('10:30');
    expect(screen.getByLabelText('Monday to')).toHaveValue('12:30');

    await userEvent.click(screen.getByRole('switch', { name: 'Monday open' }));

    expect(screen.getByLabelText('Monday from')).toHaveValue('Closed');
    expect(screen.getByLabelText('Monday to')).toHaveValue('Closed');
  });

  it('should populate the from, to and allDay fields when all day is selected', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    await userEvent.click(
      screen.getByRole('switch', { name: 'Monday all day' })
    );

    await userEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    await waitFor(() => {
      expect(mockAxiosPost).toHaveBeenCalledWith(
        '/api/sites/1/opening-times',
        expect.objectContaining({
          openingTimes: expect.arrayContaining([
            {
              allDay: true,
              day: Days.MONDAY,
              from: '00:00',
              to: '23:59',
            },
          ]),
        })
      );
    });
  });

  it('should populate the from and to fields when closed is selected to avoid validation fails', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    fireEvent.change(screen.getByLabelText('Monday to'), {
      target: { value: 'Blah' },
    });

    fireEvent.change(screen.getByLabelText('Monday from'), {
      target: { value: 'Blah' },
    });

    await userEvent.click(screen.getByRole('switch', { name: 'Monday open' }));
    await userEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(mockAxiosPost).toHaveBeenCalledWith('/api/sites/1/opening-times', {
      openingTimes: TEST_UPSERT_OPENING_TIMES_REQUEST.openingTimes.filter(
        (item) => item.day !== Days.MONDAY
      ),
    });
  });

  it('should filter out closed days from the api request', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    await userEvent.click(screen.getByRole('switch', { name: 'Monday open' }));
    await userEvent.click(screen.getByRole('switch', { name: 'Tuesday open' }));
    await userEvent.click(
      screen.getByRole('switch', { name: 'Wednesday open' })
    );

    await userEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(mockAxiosPost).toHaveBeenCalledWith(
      '/api/sites/1/opening-times',
      TEST_UPSERT_OPENING_TIMES_REQUEST_WITH_CLOSED_DAYS
    );
    expect(mockMutate).toHaveBeenCalledWith('/api/sites/1');
    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it.each([
    [OPENING_TIME_INVALID_TIME_ERROR, 'thisIsNotaTime'],
    [OPENING_TIME_END_GREATER_THAN_START_ERROR, '09:00'],
  ])(
    'should fail with error message %s for the relevant validation scenario',
    async (errorMessage, value) => {
      render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

      expect(screen.getByLabelText('Monday to')).toHaveValue('12:30');

      fireEvent.change(screen.getByLabelText('Monday to'), {
        target: { value },
      });

      fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

      expect(await screen.findByText(errorMessage)).toBeInTheDocument();
    }
  );

  it('should not display the end before start error when the fields are submitted blank', async () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    fireEvent.change(screen.getByLabelText('Monday to'), {
      target: { value: '' },
    });

    fireEvent.change(screen.getByLabelText('Monday from'), {
      target: { value: '' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findAllByText(OPENING_TIME_INVALID_TIME_ERROR)
    ).toHaveLength(2);
    expect(
      await screen.queryAllByText(OPENING_TIME_END_GREATER_THAN_START_ERROR)
    ).toHaveLength(0);
  });

  it('should autofocus the first input', () => {
    render(<EditSiteOpeningTimesModal {...openingTimesModalDefaultProps} />);

    expect(screen.getByLabelText('Monday from')).toHaveFocus();
  });
});
