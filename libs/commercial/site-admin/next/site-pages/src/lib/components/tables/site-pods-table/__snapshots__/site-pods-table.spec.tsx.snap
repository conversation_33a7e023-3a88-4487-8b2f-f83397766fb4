// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`SitePodsTable should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of chargers
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Name
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,8.67L12.81.58c-.14-.16-.34-.27-.56-.27,0,0,0,0,0,0-.19,0-.39.07-.54.22L3.1,9.13c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l7.34-7.34v20.09c0,.41.34.75.75.75s.75-.34.75-.75V2.89l6.85,6.85c.29.29.77.29,1.06,0s.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  PPID
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Model
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Last contact
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Last complete charge
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Last known status
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <a
                  class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                  href="/pods/BAR"
                >
                  FOO
                </a>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                BAR
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                T7-S
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex flex-col"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="p-1.5"
                    >
                      12/08/2022 - 08:38:12
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex flex-row"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="p-1.5"
                    >
                      <div
                        class="flex items-center"
                      >
                        <svg
                          aria-hidden="true"
                          class="fill-current h-4 w-4 mr-2 text-sky"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="16"
                            cy="16"
                            r="12"
                          />
                        </svg>
                        Available
                      </div>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
