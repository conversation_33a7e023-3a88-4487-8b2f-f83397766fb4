import {
  AlertType,
  Anchor,
  Paragraph,
} from '@experience/shared/react/design-system';
import { InformationBanner } from '@experience/shared/next/components';
import { useSubscriptionStatus } from '../providers/subscription-provider/subscription-provider';
import { useReadOnly } from '../providers/group-provider/group-provider';

export const InvoiceOverdueBanner = () => {
  const subscriptionStatus = useSubscriptionStatus();
  const readOnly = useReadOnly();

  if (readOnly) {
    return (
      <InformationBanner
        heading="Read only mode"
        bannerType={AlertType.ERROR}
        message={
          <Paragraph>
            Your Site Management Service is in read only mode as you have
            overdue invoices in the <Anchor href="/billing">Billing</Anchor>{' '}
            tab.
          </Paragraph>
        }
      />
    );
  }

  return subscriptionStatus === 'past_due' ? (
    <InformationBanner
      heading="Subscription overdue"
      bannerType={AlertType.WARNING}
      message={
        <Paragraph>
          Your Site Management Service will switch to read-only mode if overdue
          invoices in the <Anchor href="/billing">Billing</Anchor> tab are
          unpaid.
        </Paragraph>
      }
    />
  ) : null;
};
