{"compilerOptions": {"outDir": "../../../../../dist/out-tsc", "types": ["node"]}, "exclude": ["jest.config.ts", "**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx"], "extends": "./tsconfig.json", "files": ["../../../../../node_modules/@nx/react/typings/cssmodule.d.ts", "../../../../../node_modules/@nx/react/typings/image.d.ts"], "include": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"]}