import { Button } from '@experience/shared/react/design-system';
import { useReadOnly } from '@experience/commercial/site-admin/next/shared';

export interface AddTariffMenuProps {
  setOpenAddTariff: (open: boolean) => void;
}

export const AddTariffMenu = ({ setOpenAddTariff }: AddTariffMenuProps) => {
  const readOnly = useReadOnly();

  return (
    <Button
      onClick={() => setOpenAddTariff(true)}
      className="ml-20"
      disabled={readOnly}
    >
      Add tariff
    </Button>
  );
};

export default AddTariffMenu;
