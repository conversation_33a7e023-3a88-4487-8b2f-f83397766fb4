import { Cookies, CookiesProvider } from 'react-cookie';
import { GroupProvider } from '@experience/commercial/site-admin/next/shared';
import {
  TEST_POD_WITH_TARIFF,
  TEST_TARIFF,
  TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
  TariffPricingModel,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import TariffSummary from './tariff-summary';
import useSWR, { SWRResponse } from 'swr';

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({ status: 'authenticated' })),
}));

jest.mock('swr');

const mockedUseSWR = jest.mocked(useSWR);
const mockSetState = jest.fn();

const cookies = new Cookies();

describe('TariffSummary', () => {
  afterEach(async () => {
    await waitFor(() => {
      cookies.remove('pod-point.tariff-summary-tour-seen');
    });
  });

  beforeEach(() => {
    jest.mock('react', () => ({
      useState: (schedule: unknown) => [schedule, mockSetState],
    }));

    mockedUseSWR.mockReturnValue({
      data: [TEST_POD_WITH_TARIFF],
      error: null,
    } as SWRResponse);

    cookies.set('pod-point.tariff-summary-tour-seen', true);
  });

  it('should render successfully', () => {
    const { baseElement } = render(
      <CookiesProvider>
        <TariffSummary tariff={TEST_TARIFF} />
      </CookiesProvider>
    );
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <CookiesProvider cookies={cookies}>
        <TariffSummary tariff={TEST_TARIFF} />
      </CookiesProvider>
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should show edit flat rate modal if pricing model is energy and start/end time are the same', async () => {
    render(
      <CookiesProvider>
        <TariffSummary
          tariff={{
            ...TEST_TARIFF,
            schedule: {
              ...TEST_TARIFF.schedule,
              drivers: [
                {
                  ...TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
                  endDay: 1,
                  endTime: '00:00',
                  startDay: 1,
                  startTime: '00:00',
                },
              ],
            },
          }}
        />
      </CookiesProvider>
    );

    const editScheduleButton = screen.getByRole('button', {
      name: /Edit schedule drivers/i,
    });
    fireEvent.click(editScheduleButton);

    expect(
      await screen.findByRole('heading', {
        level: 2,
        name: 'Edit flat rate schedule',
      })
    ).toBeInTheDocument();
  });

  it.each([
    [2, '23:30:00', 'energy'],
    [1, '13:00:00', 'energy'],
    [1, '23:30:00', 'duration'],
  ])(
    'should show the custom edit schedule modal when start day is %s and end day is 1, start time is %s and end time is 23:30:00 and pricing model is %s',
    (startDay, startTime, pricingModel) => {
      render(
        <CookiesProvider>
          <TariffSummary
            tariff={{
              ...TEST_TARIFF,
              schedule: {
                ...TEST_TARIFF.schedule,
                drivers: [
                  {
                    ...TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
                    pricingModel: pricingModel as TariffPricingModel,
                    startDay,
                    startTime,
                  },
                ],
              },
            }}
          />
        </CookiesProvider>
      );

      const editScheduleButton = screen.getByRole('button', {
        name: /Edit schedule drivers/i,
      });
      fireEvent.click(editScheduleButton);

      expect(
        screen.getByRole('heading', {
          level: 2,
          name: 'Edit schedule',
        })
      ).toBeInTheDocument();
    }
  );

  it('should disable the button if the group is in read-only mode', () => {
    mockedUseSWR.mockReturnValueOnce({
      data: { readOnly: true },
    } as SWRResponse);

    render(
      <GroupProvider>
        <CookiesProvider>
          <TariffSummary tariff={TEST_TARIFF} />
        </CookiesProvider>
      </GroupProvider>
    );

    expect(screen.getByRole('button', { name: /Manage/ })).toBeDisabled();
  });
});
