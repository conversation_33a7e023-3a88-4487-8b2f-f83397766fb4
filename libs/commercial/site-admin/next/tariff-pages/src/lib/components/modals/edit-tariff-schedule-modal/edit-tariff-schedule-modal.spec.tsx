import { COMMON_CURRENCY_PENCE_ERROR } from '@experience/shared/typescript/validation';
import { ErrorBoundary } from '@sentry/react';
import {
  TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
  TARIFF_PER_KWH_PRICE_ERROR,
  TARIFF_SCHEDULE_LIMIT_ERROR,
  TARIFF_SCHEDULE_OVERLAP_ERROR,
  TariffErrorCodes,
  TariffScheduleErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  TEST_TARIFF,
  TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
  TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
  TariffPricingModel,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import EditTariffScheduleModal from './edit-tariff-schedule-modal';
import axios from 'axios';
import userEvent from '@testing-library/user-event';

jest.mock('axios');
const mockAxiosPut = jest.mocked(axios.put);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockSetOpen = jest.fn();

const defaultProps = {
  open: true,
  schedule: TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
  setOpen: mockSetOpen,
  tariff: TEST_TARIFF,
};

describe('EditTariffScheduleModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <EditTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <EditTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should disable tariff tier and pricing model select fields', () => {
    render(<EditTariffScheduleModal {...defaultProps} />);

    expect(screen.getByLabelText('Tariff tier')).toBeDisabled();
    expect(screen.getByLabelText('Pricing model')).toBeDisabled();
  });

  it('should render prepopulated default values', async () => {
    render(<EditTariffScheduleModal {...defaultProps} />);

    await userEvent.click(screen.getByLabelText('Start day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();
    await userEvent.click(screen.getByRole('option', { name: 'Tuesday' }));

    expect(screen.getByLabelText('Start time')).toHaveValue('10:30:00');

    await userEvent.click(screen.getByLabelText('End day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();

    expect(screen.getByLabelText('End time')).toHaveValue('23:30:00');
  });

  it('should close modal and reset inputs if cancel button is clicked', async () => {
    render(<EditTariffScheduleModal {...defaultProps} />);

    await userEvent.click(screen.getByLabelText('Start day'));
    await userEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });

    await userEvent.click(screen.getByLabelText('End day'));
    await userEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(screen.getByLabelText('Start time')).toHaveValue(
      TEST_TARIFF_DRIVER_DURATION_SCHEDULE.startTime
    );
    expect(screen.getByLabelText('End time')).toHaveValue(
      TEST_TARIFF_DRIVER_DURATION_SCHEDULE.endTime
    );
    expect(screen.getByLabelText('Price (in pence)')).toHaveValue(
      TEST_TARIFF_DRIVER_DURATION_SCHEDULE.bands[0].cost
    );

    await userEvent.click(screen.getByLabelText('Start day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();
    await userEvent.click(screen.getByRole('option', { name: 'Tuesday' }));

    await userEvent.click(screen.getByLabelText('End day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();
  });

  it('should call the API when the form is submitted', async () => {
    render(<EditTariffScheduleModal {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: 'Save changes' });

    await userEvent.click(screen.getByLabelText('Start day'));
    await userEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });

    await userEvent.click(screen.getByLabelText('End day'));
    await userEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '10' },
    });

    fireEvent.click(submitButton);
    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosPut).toHaveBeenCalledWith(
        `/api/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}`,
        {
          endDay: 5,
          endTime: '17:00',
          price: '10',
          pricingModel: 'duration',
          startDay: 3,
          startTime: '09:00',
          tariffTier: 'drivers',
        }
      );
      expect(mockMutate).toHaveBeenCalledWith(`/api/tariffs/${TEST_TARIFF.id}`);
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it.each([
    ['fixed', '10.1', COMMON_CURRENCY_PENCE_ERROR],
    ['duration', '10.1', COMMON_CURRENCY_PENCE_ERROR],
    ['energy', '10.1234', TARIFF_PER_KWH_PRICE_ERROR],
  ])(
    'should throw a validation error if the price is invalid for the price %s',
    async (pricingModel, value, error) => {
      render(
        <EditTariffScheduleModal
          {...defaultProps}
          schedule={{
            ...TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
            pricingModel: pricingModel as TariffPricingModel,
          }}
        />
      );

      fireEvent.change(screen.getByLabelText('Price (in pence)'), {
        target: { value },
      });

      fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

      expect(await screen.findByText(error)).toBeInTheDocument();
    }
  );

  it.each([
    ['duration', '1000', '£10.00'],
    ['energy', '2001', '£2.00'],
    ['fixed', '3001', '£30.00'],
  ])(
    'should throw a validation error if the pricing model is %s and price is %p',
    async (pricingModel, price, limit) => {
      render(
        <EditTariffScheduleModal
          {...defaultProps}
          schedule={{
            ...TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
            pricingModel: pricingModel as TariffPricingModel,
          }}
        />
      );

      fireEvent.change(screen.getByLabelText('Price (in pence)'), {
        target: { value: price },
      });

      fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));
      expect(
        await screen.findByText(TARIFF_SCHEDULE_LIMIT_ERROR(limit))
      ).toBeInTheDocument();
    }
  );

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPut.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <EditTariffScheduleModal {...defaultProps} />
      </ErrorBoundary>
    );

    await userEvent.click(screen.getByLabelText('Start day'));
    await userEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });

    await userEvent.click(screen.getByLabelText('End day'));
    await userEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should show validation error if incompatible pricing model error is thrown', async () => {
    mockAxiosPut.mockRejectedValueOnce({
      response: {
        data: {
          error: TariffErrorCodes.TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          message: TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          statusCode: 400,
        },
        status: 400,
      },
    });

    render(<EditTariffScheduleModal {...defaultProps} />);

    await userEvent.click(screen.getByLabelText('Start day'));
    await userEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });

    await userEvent.click(screen.getByLabelText('End day'));
    await userEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findByText(TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR)
    ).toBeInTheDocument();
  });

  it('should show validation message on form submission when the tariff overlaps with an existing one', async () => {
    mockAxiosPut.mockRejectedValueOnce({
      response: {
        data: {
          error: TariffScheduleErrorCodes.SCHEDULE_OVERLAP_ERROR,
          message: TARIFF_SCHEDULE_OVERLAP_ERROR,
          statusCode: 403,
        },
        status: 403,
      },
    });

    render(
      <EditTariffScheduleModal
        {...defaultProps}
        tariff={{
          ...TEST_TARIFF,
          schedule: {
            drivers: [
              TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
              {
                ...TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
                id: 2,
                startDay: 2,
                endDay: 2,
              },
            ],
            public: [],
            members: [],
          },
        }}
      />
    );
    await userEvent.click(screen.getByLabelText('Start day'));
    await userEvent.click(screen.getByRole('option', { name: 'Tuesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '06:00' },
    });

    await userEvent.click(screen.getByLabelText('End day'));
    await userEvent.click(screen.getByRole('option', { name: 'Tuesday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findByText(TARIFF_SCHEDULE_OVERLAP_ERROR)
    ).toBeInTheDocument();
  });

  it('should autofocus the first available input', () => {
    render(<EditTariffScheduleModal {...defaultProps} />);
    expect(screen.getByLabelText('Start day')).toHaveFocus();
  });
});
