import { Controller, useForm } from 'react-hook-form';
import {
  CreateTariffScheduleRequest,
  Tariff,
  TariffPricingModel,
  TariffTier,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Input,
  InputWidth,
  Modal,
  ModalProps,
  Select,
} from '@experience/shared/react/design-system';
import {
  TARIFF_SCHEDULE_FIELD_PRICE,
  TARIFF_SCHEDULE_FIELD_TARIFF_TIER,
  TARIFF_SCHEDULE_OVERLAP_ERROR,
  TARIFF_SCHEDULE_TARIFF_TIER_OPTIONS,
  TariffScheduleErrorCodes,
  ValidationError,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { classValidatorResolver } from '@hookform/resolvers/class-validator';
import { mutate } from 'swr';
import { useErrorHandler } from '@experience/shared/react/hooks';
import React, { useEffect, useState } from 'react';
import axios, { AxiosError } from 'axios';

interface AddFlatRateTariffScheduleModalProps {
  tariff: Tariff;
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const AddFlatRateTariffScheduleModal = ({
  tariff,
  open,
  setOpen,
}: AddFlatRateTariffScheduleModalProps) => {
  const handleError = useErrorHandler();
  const [tariffTier, setTariffTier] = useState<TariffTier>();
  const resolver = classValidatorResolver(CreateTariffScheduleRequest);

  const formId = 'add-flat-rate-tariff-schedule-form';

  const getDefaultValues = (): Partial<CreateTariffScheduleRequest> => ({
    pricingModel: TariffPricingModel.energy,
    startDay: 0,
    startTime: '00:00',
    endDay: 0,
    endTime: '00:00',
    price: undefined,
  });

  const {
    clearErrors,
    control,
    formState: { errors: formErrors, isSubmitting },
    handleSubmit,
    reset,
    setError,
  } = useForm<CreateTariffScheduleRequest>({
    defaultValues: getDefaultValues(),
    resolver,
    reValidateMode: 'onBlur',
  });

  useEffect(() => {
    reset(getDefaultValues());
  }, [reset]);

  const hasTariffSchedule =
    !!tariff.schedule?.[tariffTier as TariffTier]?.length;
  const doesNotSupportTariffs = !!tariff.pods.find(
    (pod) => !pod.supportsTariffs
  );
  const isDisabled = hasTariffSchedule || doesNotSupportTariffs;

  const handleCancel = () => {
    setOpen(false);
    clearErrors();
    reset(getDefaultValues());
  };

  const handleConfirmError = (error: unknown): void => {
    const axiosError = (error as AxiosError).response?.data as ValidationError;

    switch (axiosError?.error) {
      case TariffScheduleErrorCodes.SCHEDULE_OVERLAP_ERROR:
        setError(TARIFF_SCHEDULE_FIELD_PRICE, {
          message: TARIFF_SCHEDULE_OVERLAP_ERROR,
        });
        break;
      default:
        handleError(error);
    }
  };

  const handleCurrency = (
    value: string,
    onChange: (value: string | null) => void
  ): void => {
    if (!value) {
      onChange(null);
      return;
    }
    // We have to do this to strip out trailing zeros on the input
    onChange(parseFloat(value).toString());
  };

  const handleConfirm = async (
    request: CreateTariffScheduleRequest
  ): Promise<void> => {
    try {
      await axios.post(`/api/tariffs/${tariff.id}/schedules`, request);
      await mutate(`/api/tariffs/${tariff.id}`);

      setOpen(false);
      reset(getDefaultValues());
    } catch (error) {
      handleConfirmError(error);
    }
  };

  const content = (
    <form id={formId} noValidate>
      <Controller
        control={control}
        name={TARIFF_SCHEDULE_FIELD_TARIFF_TIER}
        render={({ field: { name, onChange, value } }) => {
          setTariffTier(value);
          return (
            <Select
              errorMessage={
                formErrors[TARIFF_SCHEDULE_FIELD_TARIFF_TIER]?.message as string
              }
              id={name}
              isFocused={true}
              label="Tariff tier"
              name={name}
              onChange={onChange}
              options={TARIFF_SCHEDULE_TARIFF_TIER_OPTIONS}
              placeholder="Tariff tier"
              selected={TARIFF_SCHEDULE_TARIFF_TIER_OPTIONS.find(
                (option) => option.id === value
              )}
              width={InputWidth.FULL}
            />
          );
        }}
      />
      <VerticalSpacer />
      <Controller
        control={control}
        name={TARIFF_SCHEDULE_FIELD_PRICE}
        render={({ field: { name, onChange, value } }) => (
          <Input
            errorMessage={
              formErrors[TARIFF_SCHEDULE_FIELD_PRICE]?.message as string
            }
            id={name}
            label="Price per kWh (in pence)"
            min="0"
            max="999.999"
            name={name}
            onChange={onChange}
            onBlur={(event: React.ChangeEvent<HTMLInputElement>) => {
              handleCurrency(event.target.value, onChange);
            }}
            placeholder="e.g. 40"
            value={value}
            step={'0.001'}
            type="number"
            width={InputWidth.FULL}
          />
        )}
      />
    </form>
  );

  const modalProps: ModalProps = {
    cancelButtonText: 'Cancel',
    confirmButtonText: 'Add schedule',
    confirmButtonTooltipText: hasTariffSchedule
      ? 'Any existing schedules for this tariff tier must be deleted before this schedule can be added'
      : undefined,
    content,
    formId,
    handleCancel,
    handleConfirm: handleSubmit(
      async (data) => await handleConfirm(data as CreateTariffScheduleRequest)
    ),
    isDisabled,
    isSubmitting,
    open,
    setOpen,
    title: 'Add flat rate schedule',
  };

  return <Modal {...modalProps} />;
};

export default AddFlatRateTariffScheduleModal;
