// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`AddDayNightTariffScheduleModal should match snapshot 1`] = `
<body>
  <div />
  <div
    id="headlessui-portal-root"
  >
    <div
      data-headlessui-portal=""
    >
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
      <div>
        <div
          aria-label="Add day/night schedule"
          aria-labelledby="headlessui-dialog-title-:test-id-7"
          aria-modal="true"
          class="relative z-10"
          data-headlessui-state="open"
          data-open=""
          id="headlessui-dialog-:test-id-0"
          role="dialog"
          tabindex="-1"
        >
          <div
            aria-hidden="true"
            class="fixed inset-0 bg-neutral/75 duration-300 ease-out data-[closed]:opacity-0"
            data-headlessui-state="open"
            data-open=""
          />
          <div
            class="fixed inset-0 z-10 overflow-y-auto"
          >
            <div
              class="flex min-h-full items-end justify-center p-4 text-center sm:items-center"
            >
              <div
                class="relative rounded-sm bg-white p-4 text-left shadow-xl duration-300 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 transition-all w-[35rem]"
                data-headlessui-state="open"
                data-open=""
                id="headlessui-dialog-panel-:test-id-6"
              >
                <div
                  class="absolute top-0 right-0 pt-4 pr-4"
                >
                  <button
                    class="flex w-7 h-7 justify-center items-center rounded-full bg-neutral/20 hover:bg-neutral/30 text-neutral outline-hidden focus:ring-2 focus:ring-neutral/50"
                    type="button"
                  >
                    <svg
                      class="h-4 w-4 stroke-1 stroke-current fill-current"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Close
                      </title>
                      <g>
                        <g>
                          <line
                            x1="5.16"
                            x2="18.94"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                        <g>
                          <line
                            x1="18.94"
                            x2="5.16"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
                <h2
                  class="pb-4 text-xxl"
                  data-headlessui-state="open"
                  data-open=""
                  id="headlessui-dialog-title-:test-id-7"
                >
                  Add day/night schedule
                </h2>
                <div
                  class="pb-6"
                >
                  <form
                    id="add-day-night-tariff-schedule-form"
                    novalidate=""
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="tariffTier"
                        class="block mb-2 text-md font-bold"
                        data-headlessui-state=""
                        for="tariffTier"
                        id="headlessui-label-:test-id-9"
                      >
                        Tariff tier
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-9 tariffTier"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-full"
                          data-focus=""
                          data-headlessui-state="focus"
                          id="tariffTier"
                          type="button"
                        >
                          <span
                            class="text-neutral block truncate"
                          >
                            Tariff tier
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      />
                    </div>
                    <div
                      class="pb-4"
                    />
                    <div
                      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
                    >
                      <div>
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold block mb-2"
                            for="dayStartTime"
                          >
                            Day start time
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="dayStartTime"
                          name="dayStartTime"
                          type="time"
                          value="00:00:00"
                        />
                      </div>
                      <div>
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold block mb-2"
                            for="dayPrice"
                          >
                            Day price per kWh (in pence)
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="dayPrice"
                          max="199.999"
                          min="0"
                          name="dayPrice"
                          placeholder="e.g. 40"
                          step="0.001"
                          type="number"
                          value=""
                        />
                      </div>
                    </div>
                    <div
                      class="pb-4"
                    />
                    <div
                      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
                    >
                      <div>
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold block mb-2"
                            for="nightStartTime"
                          >
                            Night start time
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="nightStartTime"
                          name="nightStartTime"
                          type="time"
                          value="00:00:00"
                        />
                      </div>
                      <div>
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold block mb-2"
                            for="nightPrice"
                          >
                            Night price per kWh (in pence)
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="nightPrice"
                          max="199.999"
                          min="0"
                          name="nightPrice"
                          placeholder="e.g. 40"
                          step="0.001"
                          type="number"
                          value=""
                        />
                      </div>
                    </div>
                  </form>
                </div>
                <div
                  class="flex justify-between"
                >
                  <button
                    class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 mr-4"
                    type="button"
                  >
                    Cancel
                  </button>
                  <button
                    class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
                    form="add-day-night-tariff-schedule-form"
                    type="submit"
                  >
                    Add schedule
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
    </div>
  </div>
</body>
`;
