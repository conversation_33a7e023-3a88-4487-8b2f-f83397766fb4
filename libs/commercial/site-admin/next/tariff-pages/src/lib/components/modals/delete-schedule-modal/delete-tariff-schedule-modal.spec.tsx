import { ErrorBoundary } from '@sentry/react';
import {
  TEST_DELETE_TARIFF_SCHEDULE_REQUEST,
  TEST_POD_WITH_OCPP_SUPPORT,
  TEST_TARIFF,
  TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import DeleteTariffScheduleModal from './delete-tariff-schedule-modal';
import axios from 'axios';

jest.mock('axios');
const mockAxiosDelete = jest.mocked(axios.delete);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockRouter = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
}));

const mockSetOpen = jest.fn();

const defaultProps = {
  tariff: TEST_TARIFF,
  schedule: TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
  open: true,
  setOpen: mockSetOpen,
};

describe('DeleteTariffScheduleModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <DeleteTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <DeleteTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal if cancel button is clicked', () => {
    render(<DeleteTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(mockAxiosDelete).not.toHaveBeenCalled();
  });

  it('should call API and close modal when delete is clicked', async () => {
    render(<DeleteTariffScheduleModal {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: 'Delete' });

    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosDelete).toHaveBeenCalledWith(
        `/api/tariffs/${TEST_TARIFF.id}/schedules`,
        { data: TEST_DELETE_TARIFF_SCHEDULE_REQUEST }
      );
      expect(mockMutate).toHaveBeenCalledWith(
        `/api/tariffs/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}`
      );
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosDelete.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <DeleteTariffScheduleModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Delete' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should disable confirm button if charger supports ocpp', () => {
    render(
      <DeleteTariffScheduleModal
        {...defaultProps}
        tariff={{ ...TEST_TARIFF, pods: [TEST_POD_WITH_OCPP_SUPPORT] }}
      />
    );

    expect(screen.getByRole('button', { name: 'Delete' })).toBeDisabled();
  });
});
