{"name": "commercial-site-admin-next-tariff-pages", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/site-admin/next/tariff-pages/src", "projectType": "library", "tags": ["commercial", "site-admin"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/commercial/site-admin/next/tariff-pages"], "options": {"jestConfig": "libs/commercial/site-admin/next/tariff-pages/jest.config.ts", "passWithNoTests": false}}}}