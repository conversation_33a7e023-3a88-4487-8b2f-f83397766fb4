import { COMMON_REQUIRED_ERROR } from '@experience/shared/typescript/validation';
import { ErrorBoundary } from '@sentry/react';
import {
  TEST_POD,
  TEST_POD_WITH_OCPP_SUPPORT,
  TEST_POD_WITH_TARIFF,
  TEST_TARIFF,
  TEST_TARIFF_DRIVER_ENERGY_SCHEDULE,
  TEST_TARIFF_DRIVER_FIXED_SCHEDULE,
  TEST_TARIFF_WITH_ENERGY_SCHEDULE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import { mutate } from 'swr';
import AssignTariffModal from './assign-tariff-modal';
import axios from 'axios';
import userEvent from '@testing-library/user-event';

jest.mock('axios');
const mockAxiosPut = jest.mocked(axios.put);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockRouter = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
}));

const mockSetOpen = jest.fn();

const defaultProps = {
  open: true,
  setOpen: mockSetOpen,
  pod: TEST_POD_WITH_TARIFF,
  tariffs: [
    TEST_TARIFF_WITH_ENERGY_SCHEDULE,
    { ...TEST_TARIFF_WITH_ENERGY_SCHEDULE, id: 2, name: 'another tariff' },
  ],
};

describe('AssignTariffModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(<AssignTariffModal {...defaultProps} />);

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<AssignTariffModal {...defaultProps} />);

    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot if no tariffs available', () => {
    const { baseElement } = render(
      <AssignTariffModal {...defaultProps} tariffs={[]} />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should autofocus first input', () => {
    render(<AssignTariffModal {...defaultProps} />);
    expect(screen.getByLabelText('Select tariff')).toHaveFocus();
  });

  it('should close modal if cancel button is clicked', () => {
    render(<AssignTariffModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should not persist changes if the cancel button is clicked', async () => {
    render(<AssignTariffModal {...defaultProps} />);

    await userEvent.click(screen.getByLabelText('Select tariff'));
    await userEvent.click(
      screen.getByRole('option', { name: 'another tariff' })
    );
    await userEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(mockAxiosPut).not.toHaveBeenCalled();

    await userEvent.click(screen.getByLabelText('Select tariff'));

    expect(
      screen.getByRole('option', { name: '70p per hour Checkmark' })
    ).toBeInTheDocument();
  });

  it('should call API and close modal when form is submitted if input is valid', async () => {
    render(<AssignTariffModal {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: 'Save changes' });

    await userEvent.click(screen.getByLabelText('Select tariff'));
    await userEvent.click(
      screen.getByRole('option', { name: '70p per hour Checkmark' })
    );
    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosPut).toHaveBeenCalledWith('/api/pods/1/tariff', {
        id: 1,
      });
      expect(mockMutate).toHaveBeenCalledWith('/api/pods/BAR');
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should show validation message on form submission if no tariff selected', async () => {
    const noTariffsOnPod = { ...defaultProps, pod: TEST_POD };
    render(<AssignTariffModal {...noTariffsOnPod} />);

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(await screen.findByText(COMMON_REQUIRED_ERROR)).toBeInTheDocument();
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPut.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <AssignTariffModal {...defaultProps} />
      </ErrorBoundary>
    );

    await userEvent.click(screen.getByLabelText('Select tariff'));
    await userEvent.click(
      screen.getByRole('option', { name: '70p per hour Checkmark' })
    );
    await userEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should pre-populate the select if there is a tariff present on the charger', async () => {
    render(<AssignTariffModal {...defaultProps} />);
    await userEvent.click(screen.getByLabelText('Select tariff'));
    expect(
      screen.getByRole('option', { name: '70p per hour Checkmark' })
    ).toBeInTheDocument();
  });

  it('should disable select field option if tariff is using energy pricing model and pod does not support energy pricing', async () => {
    render(
      <AssignTariffModal
        {...defaultProps}
        pod={{ ...TEST_POD, supportsPerKwh: false }}
        tariffs={[
          {
            ...TEST_TARIFF,
            schedule: { drivers: [TEST_TARIFF_DRIVER_ENERGY_SCHEDULE] },
          },
        ]}
      />
    );
    await userEvent.click(screen.getByLabelText('Select tariff'));

    expect(
      screen.getByRole('option', {
        name: '70p per hour - incompatible with this charger',
      })
    ).toHaveAttribute('aria-disabled', 'true');
  });

  it('should disable select field option if tariff is using non-energy pricing model and pod is public', async () => {
    render(
      <AssignTariffModal
        {...defaultProps}
        pod={{ ...TEST_POD, isPublic: true }}
        tariffs={[
          {
            ...TEST_TARIFF,
            schedule: { drivers: [TEST_TARIFF_DRIVER_FIXED_SCHEDULE] },
          },
        ]}
      />
    );
    await userEvent.click(screen.getByLabelText('Select tariff'));

    expect(
      screen.getByRole('option', {
        name: '70p per hour - incompatible with this charger',
      })
    ).toHaveAttribute('aria-disabled', 'true');
  });

  it('should disable save button if existing tariff is incompatible with charger', () => {
    const incompatibleTariff = {
      ...TEST_TARIFF,
      schedule: { drivers: [TEST_TARIFF_DRIVER_ENERGY_SCHEDULE] },
    };

    render(
      <AssignTariffModal
        {...defaultProps}
        pod={{ ...TEST_POD, supportsPerKwh: false, tariff: incompatibleTariff }}
        tariffs={[incompatibleTariff]}
      />
    );

    expect(screen.getByRole('button', { name: 'Save changes' })).toBeDisabled();
  });

  it('should enable save button if compatible tariff is selected', async () => {
    const incompatibleTariff = {
      ...TEST_TARIFF,
      schedule: { drivers: [TEST_TARIFF_DRIVER_ENERGY_SCHEDULE] },
    };

    render(
      <AssignTariffModal
        {...defaultProps}
        pod={{
          ...TEST_POD,
          isPublic: false,
          supportsPerKwh: false,
          tariff: incompatibleTariff,
        }}
        tariffs={[
          incompatibleTariff,
          {
            ...TEST_TARIFF,
            id: 2,
            name: '99p',
            schedule: { drivers: [TEST_TARIFF_DRIVER_FIXED_SCHEDULE] },
          },
        ]}
      />
    );

    expect(screen.getByRole('button', { name: 'Save changes' })).toBeDisabled();

    await userEvent.click(screen.getByLabelText('Select tariff'));
    await userEvent.click(screen.getByRole('option', { name: '99p' }));

    expect(screen.getByRole('button', { name: 'Save changes' })).toBeEnabled();
  });

  it('should disable select field and button if no tariffs available', () => {
    render(<AssignTariffModal {...defaultProps} tariffs={[]} />);
    const selectComponent = screen.getByRole('button', {
      name: /select tariff/i,
    });

    expect(within(selectComponent).getByText("You don't have any tariffs"));
    expect(selectComponent).toBeDisabled();
    expect(screen.getByRole('button', { name: 'Save changes' })).toBeDisabled();
  });

  it('should disable confirm button if charger supports ocpp', () => {
    render(
      <AssignTariffModal {...defaultProps} pod={TEST_POD_WITH_OCPP_SUPPORT} />
    );

    expect(screen.getByRole('button', { name: 'Save changes' })).toBeDisabled();
  });

  it('should not display information alert if pod has confirm charge enabled', () => {
    render(
      <AssignTariffModal
        {...defaultProps}
        pod={{
          ...TEST_POD_WITH_TARIFF,
          confirmChargeEnabled: true,
        }}
      />
    );

    expect(
      screen.queryByText(
        /assigning a tariff to this charger will update it's configuration so drivers are required to confirm their charge./i
      )
    ).not.toBeInTheDocument();
  });

  it('should display information alert if a pod has confirm charge disabled', () => {
    render(
      <AssignTariffModal
        {...defaultProps}
        pod={{
          ...TEST_POD_WITH_TARIFF,
          confirmChargeEnabled: false,
        }}
      />
    );

    expect(
      screen.getByText(
        /assigning a tariff to this charger will update it's configuration so drivers are required to confirm their charge./i
      )
    ).toBeInTheDocument();
  });
});
