import { Card } from '@experience/shared/react/design-system';
import { Pod } from '@experience/commercial/site-admin/typescript/domain-model';

export interface PodEnergyUsageProps {
  pod: Pod;
}

export const PodEnergyUsage = ({ pod }: PodEnergyUsageProps) => (
  <Card>
    <Card.Header>Energy delivered</Card.Header>
    <Card.Metric
      value={pod.chargeSummary?.energyUsage.toLocaleString()}
      unitSuffix="kWh"
    />
  </Card>
);

export default PodEnergyUsage;
