import { DotIcon } from '@experience/shared/react/design-system';
import { SocketStatus } from '@experience/commercial/site-admin/typescript/domain-model';
import classNames from 'classnames';

export interface PodStatusProps {
  status: SocketStatus;
}

export const PodStatus = ({ status }: PodStatusProps) => (
  <div className="flex items-center">
    <DotIcon.SOLID
      aria-hidden={true}
      className={classNames(
        ['mr-2'],
        { 'text-sky': status === 'Available' },
        { 'text-forest': status === 'Charging' },
        { 'text-brick': status === 'Offline' },
        { 'text-black': status === 'Unavailable' }
      )}
    />
    {status}
  </div>
);

export default PodStatus;
