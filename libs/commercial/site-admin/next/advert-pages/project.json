{"name": "commercial-site-admin-next-advert-pages", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "/src", "projectType": "library", "tags": ["commercial", "site-admin"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/commercial/site-admin/next/advert-pages"], "options": {"jestConfig": "libs/commercial/site-admin/next/advert-pages/jest.config.ts", "passWithNoTests": false}}}}