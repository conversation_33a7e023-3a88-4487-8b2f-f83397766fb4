import { Admin } from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Modal,
  ModalProps,
  Paragraph,
} from '@experience/shared/react/design-system';
import { mutate } from 'swr';
import { useErrorHandler } from '@experience/shared/react/hooks';
import { useState } from 'react';
import axios from 'axios';

interface ReactivateAdminModalProps {
  admin: Admin;
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const ReactivateAdminModal = ({
  admin,
  open,
  setOpen,
}: ReactivateAdminModalProps) => {
  const handleError = useErrorHandler();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async (): Promise<void> => {
    setIsSubmitting(true);
    try {
      await axios.post(`/api/admins/${admin.id}/status`);
      await mutate('/api/admins');
      setOpen(false);
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const modalProps: ModalProps = {
    cancelButtonText: 'Cancel',
    confirmButtonText: 'Reactivate',
    content: (
      <Paragraph>
        {`Are you sure you want to reactivate the admin ${admin?.firstName}
          ${admin?.lastName}?`}
      </Paragraph>
    ),
    handleConfirm,
    isSubmitting,
    open,
    setOpen,
    title: 'Reactivate admin',
  };

  return <Modal {...modalProps} />;
};

export default ReactivateAdminModal;
