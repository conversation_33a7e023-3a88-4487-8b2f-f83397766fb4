import { ErrorBoundary } from '@sentry/react';
import { TEST_ADMINISTRATOR } from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import DeleteAdminModal from './delete-admin-modal';
import axios from 'axios';

jest.mock('axios');
const mockAxiosDelete = jest.mocked(axios.delete);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockRouter = jest.fn();

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
}));

const mockSetOpen = jest.fn();

const defaultProps = {
  open: true,
  setOpen: mockSetOpen,
  admin: TEST_ADMINISTRATOR,
};

describe('DeleteAdminModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(<DeleteAdminModal {...defaultProps} />);

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<DeleteAdminModal {...defaultProps} />);

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal if cancel button is clicked', () => {
    render(<DeleteAdminModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should pre-populate the admin name', () => {
    render(<DeleteAdminModal {...defaultProps} />);

    expect(
      screen.getByText('Are you sure you want to delete the admin Joe Bloggs?')
    ).toBeInTheDocument();
  });

  it('should call API and close modal when delete is clicked', async () => {
    render(<DeleteAdminModal {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: 'Delete' });

    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosDelete).toHaveBeenCalledWith('/api/admins/1');
      expect(mockMutate).toHaveBeenCalledWith('/api/admins');
      expect(mockSetOpen).toHaveBeenCalledWith(false);
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosDelete.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <DeleteAdminModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Delete' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });
});
