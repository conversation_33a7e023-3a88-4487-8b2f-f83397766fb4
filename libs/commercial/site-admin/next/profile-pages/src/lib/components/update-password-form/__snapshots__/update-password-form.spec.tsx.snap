// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`UpdatePasswordForm should match snapshot 1`] = `
<body>
  <div>
    <form
      id="update-password-form"
      novalidate=""
    >
      <div
        class=""
      >
        <label
          class="text-md font-bold block mb-2"
          for="currentPassword"
        >
          Current password
        </label>
      </div>
      <input
        class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
        id="currentPassword"
        name="currentPassword"
        type="password"
        value=""
      />
      <div
        class="pb-4"
      />
      <div
        class=""
      >
        <label
          class="text-md font-bold block mb-2"
          for="newPassword"
        >
          New password
        </label>
      </div>
      <input
        class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
        id="newPassword"
        name="newPassword"
        type="password"
        value=""
      />
      <div
        class="pb-4"
      />
      <div
        class=""
      >
        <label
          class="text-md font-bold block mb-2"
          for="confirmNewPassword"
        >
          Confirm new password
        </label>
      </div>
      <input
        class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
        id="confirmNewPassword"
        name="confirmNewPassword"
        type="password"
        value=""
      />
      <div
        class="pb-4"
      />
      <button
        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 sm:w-auto w-full"
        form="update-password-form"
        type="submit"
      >
        Update password
      </button>
    </form>
  </div>
</body>
`;
