import { formatPoundsAsCurrencyString } from '@experience/shared/typescript/utils';

export const DELETE_TARIFF_ERROR =
  'Cannot delete a tariff that is assigned to a charger';
export const DUPLICATE_TARIFF_NAME_ERROR =
  'A tariff with this name already exists';
export const TARIFF_FIELD_NAME = 'tariffName';
export const TARIFF_INCOMPATIBLE_POD_ERROR =
  'Selected charger is incompatible with this tariff';
export const TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR =
  'Pricing model must be either per kWh, per hour or per charge.';
export const TARIFF_INVALID_TARIFF_TIER_ERROR =
  'Tariff tier must be either Driver, Member or Public.';
export const TARIFF_NOT_FOUND_ERROR = 'Tariff not found';
export const TARIFF_PER_KWH_PRICE_ERROR =
  'Price for per kWh tariffs must be within 3 decimal places';
export const TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR =
  'Price must not be less than 1p (unless free)';
export const TARIFF_SCHEDULE_FIELD_END_DAY = 'endDay';
export const TARIFF_SCHEDULE_FIELD_END_TIME = 'endTime';
export const TARIFF_SCHEDULE_FIELD_PRICE = 'price';
export const TARIFF_SCHEDULE_FIELD_WEEKDAY_PRICE = 'weekdayPrice';
export const TARIFF_SCHEDULE_FIELD_WEEKEND_PRICE = 'weekendPrice';
export const TARIFF_SCHEDULE_FIELD_DAY_PRICE = 'dayPrice';
export const TARIFF_SCHEDULE_FIELD_NIGHT_PRICE = 'nightPrice';
export const TARIFF_SCHEDULE_FIELD_DAY_START_TIME = 'dayStartTime';
export const TARIFF_SCHEDULE_FIELD_NIGHT_START_TIME = 'nightStartTime';
export const TARIFF_SCHEDULE_FIELD_PRICING_MODEL = 'pricingModel';
export const TARIFF_SCHEDULE_FIELD_START_DAY = 'startDay';
export const TARIFF_SCHEDULE_FIELD_START_TIME = 'startTime';
export const TARIFF_SCHEDULE_FIELD_TARIFF_TIER = 'tariffTier';
export const TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR =
  'This pricing model is incompatible with the chargers assigned to this tariff';
export const TARIFF_SCHEDULE_OVERLAP_ERROR =
  'The pricing schedule you are trying to set overlaps with an existing one. Please edit the start/end day and time so it does not overlap with any others before saving.';
export const TARIFF_SCHEDULE_PRICE_BAND_OVERLAP_ERROR =
  'Price band start time already exists';
export const TARIFF_SCHEDULE_START_SECOND_FIELD = 'startSecond';
export const TARIFF_SCHEDULE_ENERGY_PRICE_LIMIT = 200;
export const TARIFF_SCHEDULE_DURATION_PRICE_LIMIT = 1000;
export const TARIFF_SCHEDULE_FIXED_PRICE_LIMIT = 3000;
export const TARIFF_SCHEDULE_LIMIT_ERROR = (limit: string) =>
  `Price must be less than ${limit} for this Pricing model`;
export const DAY_NIGHT_TARIFF_SCHEDULE_MATCHING_START_TIMES_ERROR = `Night start time must not be the same as day start time`;

export const TARIFF_SCHEDULE_HOUR_OPTIONS = [
  { id: 3600, name: '1 hour' },
  { id: 7200, name: '2 hours' },
  { id: 10800, name: '3 hours' },
  { id: 14400, name: '4 hours' },
  { id: 18000, name: '5 hours' },
  { id: 21600, name: '6 hours' },
  { id: 25200, name: '7 hours' },
  { id: 28800, name: '8 hours' },
  { id: 32400, name: '9 hours' },
  { id: 36000, name: '10 hours' },
  { id: 39600, name: '11 hours' },
  { id: 43200, name: '12 hours' },
  { id: 46800, name: '13 hours' },
  { id: 50400, name: '14 hours' },
  { id: 54000, name: '15 hours' },
  { id: 57600, name: '16 hours' },
  { id: 61200, name: '17 hours' },
  { id: 64800, name: '18 hours' },
  { id: 68400, name: '19 hours' },
  { id: 72000, name: '20 hours' },
  { id: 75600, name: '21 hours' },
  { id: 79200, name: '22 hours' },
  { id: 82800, name: '23 hours' },
  { id: 86400, name: '24 hours' },
];

export const TARIFF_SCHEDULE_TARIFF_TIER_OPTIONS = [
  { id: 'drivers', name: 'Driver' },
  { id: 'members', name: 'Member' },
  { id: 'public', name: 'Public' },
];
export const TARIFF_SCHEDULE_DAYS_OPTIONS = [
  { id: 0, name: 'Sunday' },
  { id: 1, name: 'Monday' },
  { id: 2, name: 'Tuesday' },
  { id: 3, name: 'Wednesday' },
  { id: 4, name: 'Thursday' },
  { id: 5, name: 'Friday' },
  { id: 6, name: 'Saturday' },
];

export const TARIFF_SCHEDULE_PRICE_LIMITS = [
  {
    id: 'energy',
    limit: TARIFF_SCHEDULE_ENERGY_PRICE_LIMIT,
    prettyPrint: formatPoundsAsCurrencyString({
      amount: TARIFF_SCHEDULE_ENERGY_PRICE_LIMIT,
    }),
  },
  {
    id: 'duration',
    limit: TARIFF_SCHEDULE_DURATION_PRICE_LIMIT,
    prettyPrint: formatPoundsAsCurrencyString({
      amount: TARIFF_SCHEDULE_DURATION_PRICE_LIMIT,
    }),
  },
  {
    id: 'fixed',
    limit: TARIFF_SCHEDULE_FIXED_PRICE_LIMIT,
    prettyPrint: formatPoundsAsCurrencyString({
      amount: TARIFF_SCHEDULE_FIXED_PRICE_LIMIT,
    }),
  },
];
