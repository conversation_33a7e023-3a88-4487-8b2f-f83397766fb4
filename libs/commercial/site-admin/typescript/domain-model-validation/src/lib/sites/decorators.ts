import {
  OPENING_TIME_END_GREATER_THAN_START_ERROR,
  OPENING_TIME_INVALID_TIME_ERROR,
} from './constants';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { OpeningTimeWithDay } from '@experience/commercial/site-admin/typescript/domain-model';
import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { isStartBeforeEnd, isValidTime } from './rules';

@ValidatorConstraint({ name: 'isValidTime', async: false })
export class IsValidTime implements ValidatorConstraintInterface {
  validate(value: string) {
    return isValidTime.validator?.(value) ?? false;
  }

  defaultMessage() {
    return OPENING_TIME_INVALID_TIME_ERROR;
  }
}

@ValidatorConstraint({ name: 'isStartBeforeEnd', async: false })
export class IsStartBeforeEnd implements ValidatorConstraintInterface {
  validate(value: string, { object }: ValidationArguments) {
    return (
      isStartBeforeEnd.validator?.(value, (object as OpeningTimeWithDay).to) ??
      false
    );
  }

  defaultMessage() {
    return OPENING_TIME_END_GREATER_THAN_START_ERROR;
  }
}
