import {
  Days,
  OpeningTimes,
  UpsertOpeningTimesRequest,
} from '../opening-times.dto';

export const TEST_OPENING_TIMES: OpeningTimes = {
  notes: ['Parking limited during the day'],
  [Days.MONDAY]: {
    allDay: false,
    from: '10:30:00',
    to: '12:30:00',
  },
  [Days.TUESDAY]: {
    allDay: true,
    from: '00:00:00',
    to: '23:59:59',
  },
  [Days.WEDNESDAY]: {
    allDay: false,
    from: '10:30:00',
    to: '12:30:00',
  },
  [Days.THURSDAY]: {
    allDay: false,
    from: '10:30:00',
    to: '12:30:00',
  },
  [Days.FRIDAY]: {
    allDay: false,
    from: '10:30:00',
    to: '12:30:00',
  },
  [Days.SATURDAY]: {
    allDay: false,
    from: '10:30:00',
    to: '21:00:00',
  },
  [Days.SUNDAY]: {
    allDay: false,
    from: '10:30:00',
    to: '21:00:00',
  },
};

export const TEST_UPSERT_OPENING_TIMES_REQUEST: UpsertOpeningTimesRequest = {
  openingTimes: [
    {
      allDay: false,
      day: Days.MONDAY,
      from: '10:30',
      to: '11:00',
    },
    {
      allDay: true,
      day: Days.TUESDAY,
      from: '00:00',
      to: '23:59',
    },
    {
      allDay: false,
      day: Days.WEDNESDAY,
      from: '10:30',
      to: '12:30',
    },
    {
      allDay: false,
      day: Days.THURSDAY,
      from: '10:30',
      to: '12:30',
    },
    {
      allDay: false,
      day: Days.FRIDAY,
      from: '10:30',
      to: '12:30',
    },
    {
      allDay: false,
      day: Days.SATURDAY,
      from: '10:30',
      to: '21:00',
    },
    {
      allDay: false,
      day: Days.SUNDAY,
      from: '10:30',
      to: '21:00',
    },
  ],
};

export const TEST_UPSERT_OPENING_TIMES_REQUEST_WITH_CLOSED_DAYS: UpsertOpeningTimesRequest =
  {
    openingTimes: [
      {
        allDay: false,
        day: Days.THURSDAY,
        from: '10:30',
        to: '12:30',
      },
      {
        allDay: false,
        day: Days.FRIDAY,
        from: '10:30',
        to: '12:30',
      },
      {
        allDay: false,
        day: Days.SATURDAY,
        from: '10:30',
        to: '21:00',
      },
      {
        allDay: false,
        day: Days.SUNDAY,
        from: '10:30',
        to: '21:00',
      },
    ],
  };
