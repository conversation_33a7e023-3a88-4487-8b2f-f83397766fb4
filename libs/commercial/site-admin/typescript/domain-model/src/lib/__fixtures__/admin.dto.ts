import { Admin, AdminStatus, CreateAdminRequest } from '../admin.dto';

export const TEST_ADMINISTRATOR: Admin = {
  activatedOn: new Date('2020-01-01'),
  authId: '123e4567-e89b-12d3-a456-************',
  email: '<EMAIL>',
  emailBounced: false,
  firstName: 'Joe',
  groupId: 1,
  groupName: 'Registers of Scotland',
  groupUid: 'afe96d8c-b1bb-44ec-b7e7-8d370a7fab52',
  id: 1,
  lastName: 'Bloggs',
  status: AdminStatus.REGISTERED,
};

export const TEST_PENDING_ADMINISTRATOR: Admin = {
  ...TEST_ADMINISTRATOR,
  email: '<EMAIL>',
  firstName: 'Mike',
  status: AdminStatus.PENDING,
};

export const TEST_DEACTIVATED_ADMINISTRATOR: Admin = {
  ...TEST_ADMINISTRATOR,
  email: '<EMAIL>',
  firstName: '<PERSON>',
  status: AdminStatus.DEACTIVATED,
};

export const ANOTHER_TEST_ADMINISTRATOR: Admin = {
  activatedOn: new Date('2020-02-02'),
  authId: 'e076fe67-561c-481d-b95e-f1988ab2c3fc',
  email: '<EMAIL>',
  emailBounced: false,
  firstName: 'Jane',
  groupId: 1,
  groupName: 'Registers of Scotland',
  groupUid: 'afe96d8c-b1bb-44ec-b7e7-8d370a7fab52',
  id: 2,
  lastName: 'Bloggs',
  status: AdminStatus.REGISTERED,
};

export const TEST_CREATE_ADMIN_REQUEST: CreateAdminRequest = {
  email: '<EMAIL>',
};
