import * as MockDate from 'mockdate';
import {
  generateChargesCsvFileName,
  generateInsightsCsvFileName,
} from './csv-utils';
import dayjs from 'dayjs';

describe('generateCsvFilenames', () => {
  MockDate.set(new Date(2023, 3, 15));

  describe('generateChargesCsvFileName', () => {
    it('should generate a csv filename for this month', () => {
      expect(generateChargesCsvFileName('Foo Bar')).toEqual(
        'Foo Bar_Apr-01-Apr-15-2023.csv'
      );
    });

    it('should generate a csv filename for last month', () => {
      const date = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
      expect(generateChargesCsvFileName('Foo Bar', date)).toEqual(
        'Foo Bar_Mar-2023.csv'
      );
    });

    it('should generate a csv filename for this month last year', () => {
      const date = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
      expect(generateChargesCsvFileName('Foo Bar', date)).toEqual(
        'Foo Bar_Apr-2022.csv'
      );
    });

    it('should generate a csv filename for all time', () => {
      expect(generateChargesCsvFileName('Foo Bar', undefined, true)).toEqual(
        'Foo Bar_all-charges_Apr-15-2023.csv'
      );
    });

    it('should replace invalid characters with an empty string', () => {
      expect(
        generateChargesCsvFileName(
          'Foo Bar’.,#!$%^&*;:{}=-–_`~()',
          undefined,
          true
        )
      ).toEqual('Foo Bar_all-charges_Apr-15-2023.csv');
    });

    it.each([
      [true, 'Foo Bar_all-charges_Apr-15-2023_suffix.csv'],
      [false, 'Foo Bar_Apr-01-Apr-15-2023_suffix.csv'],
    ])('should append a suffix if provided', (all, filename) => {
      expect(
        generateChargesCsvFileName('Foo Bar', undefined, all, 'suffix')
      ).toEqual(filename);
    });
  });

  describe('generateInsightsCsvFileName', () => {
    it.each(['Group', 'Site', 'Charger'])(
      `should generate a csv filename with start/end date if no year is passed and prefix %s`,
      async (prefix) => {
        const filename = generateInsightsCsvFileName(prefix, '2021-01-01');
        expect(filename).toEqual(
          `${encodeURIComponent(prefix)}_Jan-01-2021-Apr-15-2023.csv`
        );
      }
    );

    it.each(['Group', 'Site', 'Charger'])(
      `should generate a csv filename if a year is passed in and prefix %s`,
      async (prefix) => {
        const filename = generateInsightsCsvFileName(
          prefix,
          '2021-01-01',
          2022
        );
        expect(filename).toEqual(`${encodeURIComponent(prefix)}_2022.csv`);
      }
    );
  });
});
