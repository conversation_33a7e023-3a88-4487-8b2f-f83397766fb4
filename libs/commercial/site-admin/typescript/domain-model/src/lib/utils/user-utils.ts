import { User } from '../user.dto';
import { sort } from 'fast-sort';

export const sortUsers = (users: User[]): User[] =>
  sort(users).by([
    { asc: (admin) => admin.status },
    { desc: (admin) => admin.activatedOn },
  ]);

export const hasAcceptedLatestTerms = (user: User): boolean => {
  const { termsAndConditions } = user;

  if (!termsAndConditions.acceptedVersion) {
    return false;
  }

  return (
    termsAndConditions.acceptedVersion === termsAndConditions.currentVersion
  );
};
