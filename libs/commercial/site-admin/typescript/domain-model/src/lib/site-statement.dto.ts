import { COMMON_REQUIRED_ERROR } from '@experience/shared/typescript/validation';
import {
  IsNotEmpty,
  IsOptional,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  SITE_STATEMENT_FEES_MAX_VALUE_ERROR,
  SITE_STATEMENT_FEES_MIN_VALUE_ERROR,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { Transform, Type } from 'class-transformer';

export class AccountingTotals {
  gross: number;
  net: number;
  vat: number;
}

export class Statement {
  claimedEnergyDelivered: number;
  energyDelivered: number;
  fees: AccountingTotals;
  numberOfCharges: number;
  paidEnergyDelivered: number;
  revenue: AccountingTotals;
}

export class PodStatement extends Statement {
  podName: string;
}

export class SiteStatement extends Statement {
  breakdown?: SiteSubStatement[];
  date: string;
  groupName: string;
  reference: string;
  siteAddress: string;
  siteName: string;
}

export class SiteSubStatement extends Statement {
  date: string;
  groupName: string;
  reference: string;
  siteAddress: string;
  siteName: string;
}

export class AdjustedFee {
  @IsNotEmpty({ message: COMMON_REQUIRED_ERROR })
  ppid: string;

  @Max(0.99, { message: SITE_STATEMENT_FEES_MAX_VALUE_ERROR })
  @Min(0, { message: SITE_STATEMENT_FEES_MIN_VALUE_ERROR })
  @IsOptional()
  @Transform(({ value }) =>
    value === undefined || value === null || value === ''
      ? undefined
      : Number(value)
  )
  fee?: number;
}

export class CreateSiteStatementRequest {
  @ValidateNested()
  @Type(() => AdjustedFee)
  adjustedFees: AdjustedFee[];
}

export enum StatementPeriod {
  MONTHLY = 1,
  QUARTERLY = 3,
}
