// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  ADDITIONAL_INFO_MAX_LENGTH,
  ADDITIONAL_INFO_MAX_LENGTH_ERROR,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { MaxLength } from 'class-validator';

export class AdditionalInfoInterface {
  additionalInfo: string;
}

export class UpdateAdditionalInfoRequest {
  @MaxLength(ADDITIONAL_INFO_MAX_LENGTH, {
    message: ADDITIONAL_INFO_MAX_LENGTH_ERROR,
  })
  additionalInfo?: string;
}
