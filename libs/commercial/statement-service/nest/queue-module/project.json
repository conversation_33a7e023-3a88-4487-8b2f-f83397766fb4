{"name": "commercial-statement-service-nest-queue-module", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/statement-service/nest/queue-module/src", "projectType": "library", "tags": ["commercial", "statement-service"], "namedInputs": {"projectSpecificFiles": ["{workspaceRoot}/assets/statement-service-api/email-templates/**/*"]}, "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/statement-service/nest/queue-module/jest.config.ts"}, "dependsOn": [{"projects": ["commercial-statement-service-prisma-statements-client"], "target": "generate-sources:prisma"}]}}}