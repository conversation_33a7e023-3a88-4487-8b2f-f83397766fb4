{"name": "commercial-statement-service-nest-webhook-module", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/statement-service/nest/webhook-module/src", "projectType": "library", "tags": ["commercial", "statement-service"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/statement-service/nest/webhook-module/jest.config.ts"}}}}