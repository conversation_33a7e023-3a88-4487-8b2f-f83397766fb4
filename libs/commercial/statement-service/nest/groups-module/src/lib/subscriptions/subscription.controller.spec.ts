import { INestApplication } from '@nestjs/common';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionService } from './subscription.service';
import {
  TEST_GROUP,
  TEST_SUBSCRIPTION_CHARGER,
} from '@experience/commercial/statement-service/shared';
import { Test } from '@nestjs/testing';
import request from 'supertest';

jest.mock('./subscription.service');

describe('SubscriptionController', () => {
  let app: INestApplication;
  let controller: SubscriptionController;
  let service: SubscriptionService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      controllers: [SubscriptionController],
      providers: [SubscriptionService],
    }).compile();

    controller = module.get(SubscriptionController);
    service = module.get(SubscriptionService);

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should find subscription chargers for a group', async () => {
    const mockFindSubscriptionChargersByGroupId = jest
      .spyOn(service, 'findSubscriptionChargersByGroupId')
      .mockResolvedValueOnce([TEST_SUBSCRIPTION_CHARGER]);

    const response = await request(app.getHttpServer())
      .get(`/groups/${TEST_GROUP.groupId}/subscription/chargers`)
      .expect(200);

    expect(response.body).toEqual([TEST_SUBSCRIPTION_CHARGER]);
    expect(mockFindSubscriptionChargersByGroupId).toHaveBeenCalledWith(
      TEST_GROUP.groupId
    );
  });

  it('should return a 400 error when groupId is not a valid UUID', async () => {
    const response = await request(app.getHttpServer())
      .get(`/groups/not-a-uuid/subscription/chargers`)
      .expect(400);

    expect(response.body).toEqual({
      statusCode: 400,
      message: 'Validation failed (uuid is expected)',
      error: 'Bad Request',
    });
  });

  it('should return an empty array when no subscription chargers found', async () => {
    jest
      .spyOn(service, 'findSubscriptionChargersByGroupId')
      .mockResolvedValueOnce([]);

    const response = await request(app.getHttpServer())
      .get(`/groups/${TEST_GROUP.groupId}/subscription/chargers`)
      .expect(200);

    expect(response.body).toEqual([]);
  });
});
