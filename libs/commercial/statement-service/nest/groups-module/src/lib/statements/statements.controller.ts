import { <PERSON>, Get, Param, <PERSON><PERSON>U<PERSON><PERSON>ip<PERSON> } from '@nestjs/common';
import { GroupsStatementsService } from './statements.service';
import { Statement } from '@experience/commercial/statement-service/shared';

@Controller('groups/:groupId/statements')
export class GroupsStatementsController {
  constructor(
    private readonly groupStatementsService: GroupsStatementsService
  ) {}

  @Get()
  async findStatementsByGroupId(
    @Param('groupId', ParseUUIDPipe) groupId: string
  ): Promise<Statement[]> {
    return this.groupStatementsService.findStatementsByGroupId(groupId);
  }
}
