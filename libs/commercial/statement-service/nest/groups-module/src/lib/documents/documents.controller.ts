import 'multer';
import {
  Body,
  Controller,
  FileTypeValidator,
  Get,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  ParseUUIDPipe,
  Post,
  Res,
  StreamableFile,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  CreateDocumentRequest,
  Document,
} from '@experience/commercial/statement-service/shared';
import { DocumentsInterceptor } from './document.interceptor';
import { DocumentsService } from './documents.service';
import { Express } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { HasRoles } from '@experience/shared/nest/utils';
import { OidcRoles } from '@experience/shared/typescript/oidc-utils';
import { Response } from 'express';

const TEN_MEGABYTES = 10_000_000;

@Controller('groups/:groupId/documents')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Get()
  @HasRoles(OidcRoles.GROUP_MANAGE_DOCUMENTS)
  async findDocumentsByGroupId(
    @Param('groupId', ParseUUIDPipe) groupId: string
  ): Promise<Document[]> {
    return this.documentsService.findDocumentsByGroupId(groupId);
  }

  @Post()
  @HasRoles(OidcRoles.GROUP_MANAGE_DOCUMENTS)
  @UseInterceptors(FileInterceptor('file'), DocumentsInterceptor)
  async uploadDocument(
    @Param('groupId', ParseUUIDPipe) groupId: string,
    @Body() request: CreateDocumentRequest,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType: /pdf|word/,
            skipMagicNumbersValidation: true,
          }),
          new MaxFileSizeValidator({ maxSize: TEN_MEGABYTES }),
        ],
      })
    )
    file: Express.Multer.File
  ): Promise<void> {
    await this.documentsService.uploadDocument(
      request,
      file.buffer,
      file.mimetype,
      groupId
    );
  }

  @Get(':documentId/download')
  @UseInterceptors(DocumentsInterceptor)
  @HasRoles(OidcRoles.GROUP_MANAGE_DOCUMENTS)
  async downloadDocument(
    @Param('documentId', ParseUUIDPipe) documentId: string,
    @Param('groupId', ParseUUIDPipe) groupId: string,
    @Res({ passthrough: true }) res: Response
  ): Promise<StreamableFile> {
    const { filename, mimetype, buffer } =
      await this.documentsService.downloadDocument(documentId, groupId);

    res.set({
      'Content-Type': mimetype,
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    return new StreamableFile(buffer);
  }
}
