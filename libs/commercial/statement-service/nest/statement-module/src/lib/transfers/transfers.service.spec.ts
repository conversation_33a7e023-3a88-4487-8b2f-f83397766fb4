import { ConfigModule } from '@nestjs/config';
import { GroupsService } from '@experience/commercial/statement-service/nest/groups-module';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import { InvoiceService } from '@experience/commercial/statement-service/nest/invoice-module';
import { Map } from 'immutable';
import { S3Service } from '@experience/shared/nest/aws/s3-module';
import {
  Statement,
  TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
  TEST_STATEMENT,
} from '@experience/commercial/statement-service/shared';
import { StatementService } from '../statement.service';
import {
  StatementsPrismaClient,
  TEST_WORK_ITEM_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { StripeConnectService } from '@experience/shared/nest/stripe';
import { Test, TestingModule } from '@nestjs/testing';
import { TransfersService } from './transfers.service';
import MockDate from 'mockdate';

jest.mock('@experience/commercial/statement-service/nest/groups-module');
jest.mock('@experience/commercial/statement-service/nest/invoice-module');
jest.mock('@experience/shared/nest/aws/s3-module');
jest.mock('@experience/shared/nest/stripe');

describe('TransfersService', () => {
  let service: TransfersService;
  let statementService: StatementService;
  let stripeConnectService: StripeConnectService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule],
      providers: [
        GroupsService,
        HealthIndicatorService,
        PrismaHealthIndicator,
        S3Service,
        StatementService,
        StatementsPrismaClient,
        StripeConnectService,
        InvoiceService,
        TransfersService,
      ],
    }).compile();

    service = module.get<TransfersService>(TransfersService);
    statementService = module.get<StatementService>(StatementService);
    stripeConnectService =
      module.get<StripeConnectService>(StripeConnectService);
    MockDate.set(new Date(2022, 0, 15, 12, 0, 0, 0));
  });

  it('should generate stripe transfers based on generated revenue', async () => {
    const mockFindConnectedGroups = jest
      .spyOn(statementService, 'getStripeConnectedAccountGroups')
      .mockResolvedValueOnce([
        {
          ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
          groupId: TEST_WORK_ITEM_ENTITY.groupId,
        },
        {
          ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
          groupId: '576ad142-7d3e-41e6-b96e-cb151fdcbb01',
          stripeConnectedAccountId: 'acct_123456788',
        },
      ]);
    const statement1 = Map(TEST_STATEMENT)
      .setIn(['id'], '0a69aa8f-fa98-4488-a113-4ea8aca29e5e')
      .setIn(['workItem', 'groupId'], '576ad142-7d3e-41e6-b96e-cb151fdcbb01')
      .toObject() as unknown as Statement;
    const statement2 = Map(TEST_STATEMENT)
      .setIn(['id'], '0a69aa8f-fa98-4488-a113-4ea8aca29e5f')
      .setIn(['workItem', 'groupId'], '576ad142-7d3e-41e6-b96e-cb151fdcbb01')
      .toObject() as unknown as Statement;
    const mockFindConnectedStatements = jest
      .spyOn(statementService, 'getStatementsToPayout')
      .mockResolvedValueOnce([TEST_STATEMENT])
      .mockResolvedValueOnce([statement1, statement2]);
    const mockCreateTransfer = jest
      .spyOn(stripeConnectService, 'createTransfer')
      .mockResolvedValueOnce('tr_1MiN3gLkdIwHu7ixNCZvFdgA')
      .mockResolvedValueOnce('tr_1MiN3gLkdIwHu7ixNCZvFdgB');
    const mockUpdateStatements = jest
      .spyOn(statementService, 'updateStatementPayoutStatus')
      .mockResolvedValue();

    await service.createStripeTransfers();

    expect(mockFindConnectedGroups).toHaveBeenCalled();
    expect(mockFindConnectedStatements).toHaveBeenNthCalledWith(
      1,
      '725fead1-a43b-468b-a500-a279d8a47f95'
    );
    expect(mockFindConnectedStatements).toHaveBeenNthCalledWith(
      2,
      '576ad142-7d3e-41e6-b96e-cb151fdcbb01'
    );
    expect(mockCreateTransfer).toHaveBeenNthCalledWith(1, {
      amount: 7370,
      description: 'Test Group Inc. - 15th January 2022',
      destination: 'acct_123456789',
    });
    expect(mockCreateTransfer).toHaveBeenNthCalledWith(2, {
      amount: 14740,
      description: 'Test Group Inc. - 15th January 2022',
      destination: 'acct_123456788',
    });
    expect(mockUpdateStatements).toHaveBeenNthCalledWith(
      1,
      ['0a69aa8f-fa98-4488-a113-4ea8aca29e5d'],
      'TRANSFERRED',
      'tr_1MiN3gLkdIwHu7ixNCZvFdgA'
    );
    expect(mockUpdateStatements).toHaveBeenNthCalledWith(
      2,
      [
        '0a69aa8f-fa98-4488-a113-4ea8aca29e5e',
        '0a69aa8f-fa98-4488-a113-4ea8aca29e5f',
      ],
      'TRANSFERRED',
      'tr_1MiN3gLkdIwHu7ixNCZvFdgB'
    );
  });

  it('should handle decimal places when generating stripe transfers', async () => {
    const mockFindConnectedGroups = jest
      .spyOn(statementService, 'getStripeConnectedAccountGroups')
      .mockResolvedValueOnce([
        {
          ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
          groupId: TEST_WORK_ITEM_ENTITY.groupId,
        },
      ]);

    jest
      .spyOn(statementService, 'getStatementsToPayout')
      .mockResolvedValueOnce([
        {
          ...TEST_STATEMENT,
          revenue: {
            gross: 76.76,
            net: 97.5,
            vat: 34.7,
          },
        },
      ]);
    const mockCreateTransfer = jest
      .spyOn(stripeConnectService, 'createTransfer')
      .mockResolvedValueOnce('tr_1MiN3gLkdIwHu7ixNCZvFdgA');
    jest
      .spyOn(statementService, 'updateStatementPayoutStatus')
      .mockResolvedValue();

    await service.createStripeTransfers();

    expect(mockFindConnectedGroups).toHaveBeenCalled();
    expect(mockCreateTransfer).toHaveBeenNthCalledWith(1, {
      amount: 7676,
      description: 'Test Group Inc. - 15th January 2022',
      destination: 'acct_123456789',
    });
  });

  it('should set payout status to deferred and not generate stripe transfers if the group has transferEnabled set to false', async () => {
    jest
      .spyOn(statementService, 'getStripeConnectedAccountGroups')
      .mockResolvedValueOnce([
        {
          ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
          groupId: TEST_WORK_ITEM_ENTITY.groupId,
          transfersEnabled: false,
        },
      ]);
    const mockUpdateStatements = jest
      .spyOn(
        statementService,
        'updateCurrentStatementsPayoutStatusByConnectedAccountId'
      )
      .mockResolvedValueOnce();
    const mockCreateTransfer = jest
      .spyOn(stripeConnectService, 'createTransfer')
      .mockResolvedValueOnce(null);

    await service.createStripeTransfers();

    expect(mockCreateTransfer).not.toHaveBeenCalled();
    expect(mockUpdateStatements).toHaveBeenCalledWith(
      TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT.stripeConnectedAccountId,
      'DEFERRED'
    );
  });

  it('should not generate stripe transfers if there are no connected statements', async () => {
    jest
      .spyOn(statementService, 'getStripeConnectedAccountGroups')
      .mockResolvedValueOnce([TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT]);
    jest.spyOn(statementService, 'getStatementsToPayout').mockResolvedValue([]);
    const mockCreateTransfer = jest
      .spyOn(stripeConnectService, 'createTransfer')
      .mockResolvedValueOnce(null);

    await service.createStripeTransfers();

    expect(mockCreateTransfer).not.toHaveBeenCalled();
  });

  it('should not generate stripe transfers if there is no stripe transfer id', async () => {
    const group = {
      ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
      groupId: TEST_WORK_ITEM_ENTITY.groupId,
    };
    jest
      .spyOn(statementService, 'getStripeConnectedAccountGroups')
      .mockResolvedValueOnce([group]);
    jest
      .spyOn(statementService, 'getStatementsToPayout')
      .mockResolvedValueOnce([TEST_STATEMENT]);
    const mockCreateTransfer = jest
      .spyOn(stripeConnectService, 'createTransfer')
      .mockResolvedValueOnce(null);
    const mockUpdateStatements = jest
      .spyOn(statementService, 'updateStatementPayoutStatus')
      .mockResolvedValueOnce();

    await service.createStripeTransfers();

    expect(mockCreateTransfer).toHaveBeenCalledWith({
      amount: 7370,
      description: 'Test Group Inc. - 15th January 2022',
      destination: 'acct_123456789',
    });
    expect(mockUpdateStatements).toHaveBeenCalledWith(
      ['0a69aa8f-fa98-4488-a113-4ea8aca29e5d'],
      'WARNING'
    );
  });

  it('should update payout status if generating transfers fails', async () => {
    const group = {
      ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
      groupId: TEST_WORK_ITEM_ENTITY.groupId,
    };
    jest
      .spyOn(statementService, 'getStripeConnectedAccountGroups')
      .mockResolvedValueOnce([group]);
    jest
      .spyOn(statementService, 'getStatementsToPayout')
      .mockResolvedValueOnce([TEST_STATEMENT]);
    jest
      .spyOn(stripeConnectService, 'createTransfer')
      .mockResolvedValueOnce(null);
    const mockUpdateStatements = jest
      .spyOn(statementService, 'updateStatementPayoutStatus')
      .mockResolvedValueOnce();

    await service.createStripeTransfers();

    expect(mockUpdateStatements).toHaveBeenCalledWith(
      ['0a69aa8f-fa98-4488-a113-4ea8aca29e5d'],
      'WARNING'
    );
  });
});
