import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { MessageBody, findAutomatedWorkItems } from './producer-utils';
import {
  SqsBatchMessageProps,
  SqsClientService,
  batchMessagesForQueueing,
  mapToBatchMessage,
  sendBatchMessages,
} from '@experience/shared/nest/aws/sqs-module';
import {
  StatementsPrismaClient,
  WorkItemEntity,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { Status } from '@experience/commercial/statement-service/prisma/statements/client';

@Injectable()
export class StatementsToGenerateProducerService {
  private readonly logger = new Logger(
    StatementsToGenerateProducerService.name
  );

  constructor(
    private readonly configService: ConfigService,
    private readonly statementsDatabase: StatementsPrismaClient,
    private readonly sqsClientService: SqsClientService
  ) {
    // we have to bind 'this' so that we can pass this class method as a callback to send batch messages function
    this.updateStatusToGenerating = this.updateStatusToGenerating.bind(this);
  }

  async queueStatementsToGenerate(): Promise<void> {
    this.logger.log('queueing automated work items for statement generation');

    const workItems = (await this.statementsDatabase.workItem.findMany({
      where: {
        status: Status.READY,
        deletedAt: null,
      },
    })) as WorkItemEntity[];

    const automatedWorkItems = await findAutomatedWorkItems(
      workItems,
      this.statementsDatabase
    );

    const messages = batchMessagesForQueueing(
      automatedWorkItems.map((item) =>
        mapToBatchMessage<MessageBody>({
          workItemId: item.id,
        })
      )
    );

    await sendBatchMessages(
      this.configService.get('STATEMENTS_TO_GENERATE_QUEUE_URL') as string,
      messages,
      this.sqsClientService,
      this.updateStatusToGenerating
    );
  }

  private async updateStatusToGenerating(
    batch: SqsBatchMessageProps[]
  ): Promise<void> {
    const workItemIds = batch.map(
      (message) => JSON.parse(message.messageBody).workItemId
    );

    await this.statementsDatabase.workItem.updateMany({
      where: {
        id: {
          in: workItemIds,
        },
      },
      data: {
        status: Status.GENERATING,
      },
    });
  }
}
