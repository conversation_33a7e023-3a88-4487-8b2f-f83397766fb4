export enum StatementErrorCodes {
  STATEMENT_NOT_FOUND_ERROR = 'STATEMENT_NOT_FOUND_ERROR',
  STATEMENT_WORK_ITEM_NOT_FOUND_ERROR = 'STATEMENT_WORK_ITEM_NOT_FOUND_ERROR',
  INVOICE_PRICE_TOO_LOW_ERROR = 'INVOICE_PRICE_TOO_LOW_ERROR',
}

export const STATEMENT_NOT_FOUND_ERROR_MESSAGE = 'No statement found';

export const STATEMENT_WORK_ITEM_NOT_FOUND_ERROR_MESSAGE =
  'No work item found for this statement';
