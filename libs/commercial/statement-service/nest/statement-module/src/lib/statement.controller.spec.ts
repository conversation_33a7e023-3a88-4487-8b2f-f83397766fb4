import {
  CommonErrorCodes,
  InvalidStatusTransitionException,
} from '@experience/commercial/statement-service/nest/shared';
import { INVOICE_PRICE_TOO_LOW_ERROR_MESSAGE } from '@experience/shared/nest/stripe';
import { INestApplication } from '@nestjs/common';
import { InvoicePriceTooLowException } from '@experience/shared/nest/stripe';
import {
  STATEMENT_NOT_FOUND_ERROR_MESSAGE,
  STATEMENT_WORK_ITEM_NOT_FOUND_ERROR_MESSAGE,
  StatementErrorCodes,
} from './constants';
import { StatementController } from './statement.controller';
import {
  StatementNotFoundException,
  StatementWorkItemNotFoundException,
} from './statement.exception';
import { StatementService } from './statement.service';
import { Status } from '@experience/commercial/statement-service/prisma/statements/client';
import {
  TEST_CREATE_STATEMENT_REQUEST,
  TEST_CREATE_STATEMENT_RESPONSE,
  TEST_STATEMENT,
  TEST_STATEMENTS,
  WorkItemStatus,
} from '@experience/commercial/statement-service/shared';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

jest.mock('./statement.service');

describe('Statement controller', () => {
  let app: INestApplication;
  let statementService: StatementService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StatementController],
      providers: [{ provide: StatementService, useClass: StatementService }],
    }).compile();

    statementService = module.get<StatementService>(StatementService);

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Read', () => {
    it('should find statements', async () => {
      const mockFindAll = jest.spyOn(statementService, 'findAllStatements');
      mockFindAll.mockResolvedValueOnce(TEST_STATEMENTS);

      await request(app.getHttpServer())
        .get(`/statements`)
        .expect(200)
        .expect(TEST_STATEMENTS);

      expect(mockFindAll).toHaveBeenCalled();
    });

    it('should find statements by status', async () => {
      const mockFindAll = jest.spyOn(statementService, 'findAllStatements');
      mockFindAll.mockResolvedValueOnce(TEST_STATEMENTS);

      await request(app.getHttpServer())
        .get(`/statements?status=${WorkItemStatus.SENT}`)
        .expect(200)
        .expect(TEST_STATEMENTS);

      expect(mockFindAll).toHaveBeenCalled();
    });

    it('should find statements by date', async () => {
      const mockFindAll = jest.spyOn(statementService, 'findAllStatements');
      mockFindAll.mockResolvedValueOnce(TEST_STATEMENTS);

      await request(app.getHttpServer())
        .get(`/statements?date=2023-01-01`)
        .expect(200)
        .expect(TEST_STATEMENTS);

      expect(mockFindAll).toHaveBeenCalled();
    });

    it('should find statements by group id', async () => {
      const mockFindAll = jest.spyOn(statementService, 'findAllStatements');
      mockFindAll.mockResolvedValueOnce(TEST_STATEMENTS);

      await request(app.getHttpServer())
        .get(`/statements?groupId=123`)
        .expect(200)
        .expect(TEST_STATEMENTS);

      expect(mockFindAll).toHaveBeenCalledWith(undefined, '123', undefined);
    });

    it('should find a statement', async () => {
      const mockFindAll = jest.spyOn(statementService, 'findStatement');
      mockFindAll.mockResolvedValueOnce(TEST_STATEMENT);

      const { invoice, ...TEST_STATEMENT_RESPONSE } = TEST_STATEMENT;

      await request(app.getHttpServer())
        .get(`/statements/${TEST_STATEMENT.id}`)
        .expect(200)
        .expect(TEST_STATEMENT_RESPONSE);

      expect(mockFindAll).toHaveBeenCalled();
    });

    it('should thrown an exception if the statement is not found', async () => {
      jest
        .spyOn(statementService, 'findStatement')
        .mockRejectedValueOnce(new StatementNotFoundException());

      await request(app.getHttpServer())
        .get(`/statements/${TEST_STATEMENT.id}`)
        .expect(404)
        .expect({
          error: StatementErrorCodes.STATEMENT_NOT_FOUND_ERROR,
          message: STATEMENT_NOT_FOUND_ERROR_MESSAGE,
          statusCode: 404,
        });
    });
  });

  describe('Create', () => {
    it('should create a statement', async () => {
      const mockCreateStatement = jest
        .spyOn(statementService, 'createStatement')
        .mockResolvedValueOnce(TEST_CREATE_STATEMENT_RESPONSE);

      await request(app.getHttpServer())
        .post(`/statements`)
        .send(TEST_CREATE_STATEMENT_REQUEST)
        .expect(201)
        .expect(TEST_CREATE_STATEMENT_RESPONSE);

      expect(mockCreateStatement).toHaveBeenCalledWith(
        TEST_CREATE_STATEMENT_REQUEST
      );
    });

    it('should return 400 if the status transition is invalid', async () => {
      const mockCreateStatement = jest
        .spyOn(statementService, 'createStatement')
        .mockRejectedValueOnce(
          new InvalidStatusTransitionException(Status.SENT, Status.GENERATED)
        );

      await request(app.getHttpServer())
        .post(`/statements`)
        .send(TEST_CREATE_STATEMENT_REQUEST)
        .expect(400)
        .expect({
          statusCode: 400,
          message: `Invalid status transition: ${Status.SENT} to ${Status.GENERATED}`,
          error: CommonErrorCodes.INVALID_STATUS_TRANSITION,
        });

      expect(mockCreateStatement).toHaveBeenCalledWith(
        TEST_CREATE_STATEMENT_REQUEST
      );
    });

    it('should return 404 if no associated work item found', async () => {
      const mockCreateStatement = jest
        .spyOn(statementService, 'createStatement')
        .mockRejectedValueOnce(new StatementWorkItemNotFoundException());

      await request(app.getHttpServer())
        .post(`/statements`)
        .send(TEST_CREATE_STATEMENT_REQUEST)
        .expect(404)
        .expect({
          statusCode: 404,
          message: STATEMENT_WORK_ITEM_NOT_FOUND_ERROR_MESSAGE,
          error: StatementErrorCodes.STATEMENT_WORK_ITEM_NOT_FOUND_ERROR,
        });

      expect(mockCreateStatement).toHaveBeenCalledWith(
        TEST_CREATE_STATEMENT_REQUEST
      );
    });

    it('should throw an exception if the invoice price is too low', async () => {
      jest
        .spyOn(statementService, 'createStatement')
        .mockRejectedValueOnce(new InvoicePriceTooLowException());

      await request(app.getHttpServer())
        .post(`/statements`)
        .send(TEST_CREATE_STATEMENT_REQUEST)
        .expect(400)
        .expect({
          statusCode: 400,
          message: INVOICE_PRICE_TOO_LOW_ERROR_MESSAGE,
          error: StatementErrorCodes.INVOICE_PRICE_TOO_LOW_ERROR,
        });
    });
  });

  describe('Download', () => {
    it('should download a statement pdf', async () => {
      const buffer = Buffer.from('pdf');
      const filename = 'test.pdf';

      const mockGetStatementPdf = jest
        .spyOn(statementService, 'getStatementPdf')
        .mockResolvedValueOnce({ buffer, filename });

      await request(app.getHttpServer())
        .get(`/statements/${TEST_STATEMENT.id}/pdf`)
        .expect(200)
        .expect('Content-Type', 'application/pdf')
        .expect('Content-Disposition', 'attachment; filename=test.pdf');

      expect(mockGetStatementPdf).toHaveBeenCalledWith(TEST_STATEMENT.id);
    });
  });
});
