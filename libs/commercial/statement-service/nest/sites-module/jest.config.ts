export default {
  displayName: 'commercial-statement-service-nest-sites-module',
  preset: '../../../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory:
    '../../../../../coverage/libs/commercial/statement-service/nest/configs-module',
  testPathIgnorePatterns: ['index.spec.ts', 'sites.module.spec.ts'],
};
