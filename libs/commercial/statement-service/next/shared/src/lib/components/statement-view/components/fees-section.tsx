import {
  Heading,
  Paragraph,
  TextWeight,
} from '@experience/shared/react/design-system';
import { SiteStatement } from '@experience/commercial/site-admin/typescript/domain-model';
import {
  ThreeColumnLayout,
  VerticalSpacer,
} from '@experience/shared/react/layouts';
import { formatPoundsAsCurrencyString } from '@experience/shared/typescript/utils';

const FeesSection = ({ fees }: Pick<SiteStatement, 'fees'>) => (
  <>
    <Heading.H2>Invoice from Pod Point</Heading.H2>
    <VerticalSpacer />
    <div className="border-primary border-solid border-4 px-4 py-4 text-center">
      <Paragraph
        className="text-primary text-xl text-left"
        textWeight={TextWeight.Bold}
      >
        Our fees
      </Paragraph>
      <VerticalSpacer />
      <ThreeColumnLayout>
        <div>
          <Paragraph textWeight={TextWeight.Bold}>Gross fees</Paragraph>
          <VerticalSpacer />
          <Paragraph>
            £
            {formatPoundsAsCurrencyString({
              amount: fees.gross,
              options: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              },
            })}
          </Paragraph>
        </div>
        <div>
          <Paragraph textWeight={TextWeight.Bold}>Net</Paragraph>
          <VerticalSpacer />
          <Paragraph>
            £
            {formatPoundsAsCurrencyString({
              amount: fees.net,
              options: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              },
            })}
          </Paragraph>
        </div>
        <div>
          <Paragraph textWeight={TextWeight.Bold}>VAT at 20%</Paragraph>
          <VerticalSpacer />
          <Paragraph>
            £
            {formatPoundsAsCurrencyString({
              amount: fees.vat,
              options: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              },
            })}
          </Paragraph>
        </div>
      </ThreeColumnLayout>
      <VerticalSpacer />
      <Paragraph>
        We have sent an invoice for our fees enclosed with this statement.
      </Paragraph>
    </div>
    <VerticalSpacer />
  </>
);

export default FeesSection;
