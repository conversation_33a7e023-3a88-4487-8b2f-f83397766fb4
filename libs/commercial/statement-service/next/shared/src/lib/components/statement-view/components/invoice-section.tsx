import { AccountingTotals } from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Anchor,
  Heading,
  Paragraph,
  TextWeight,
} from '@experience/shared/react/design-system';
import {
  ThreeColumnLayout,
  VerticalSpacer,
} from '@experience/shared/react/layouts';
import { formatPoundsAsCurrencyString } from '@experience/shared/typescript/utils';

export interface InvoiceSectionProps {
  hasStripeConnectedAccount: boolean;
  revenue: AccountingTotals;
}

const InvoiceSection = ({
  hasStripeConnectedAccount,
  revenue,
}: InvoiceSectionProps) => (
  <>
    <Heading.H2>Owed to you</Heading.H2>
    <VerticalSpacer />
    <div className="border-primary border-solid border-4 px-4 py-4 text-center">
      <Paragraph
        className="text-primary text-xl text-left"
        textWeight={TextWeight.Bold}
      >
        Your revenue
      </Paragraph>
      <VerticalSpacer />
      <ThreeColumnLayout>
        <div>
          <Paragraph textWeight={TextWeight.Bold}>Gross revenue</Paragraph>
          <VerticalSpacer />
          <Paragraph>
            £
            {formatPoundsAsCurrencyString({
              amount: revenue.gross,
              options: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              },
            })}
          </Paragraph>
        </div>
        <div>
          <Paragraph textWeight={TextWeight.Bold}>Net</Paragraph>
          <VerticalSpacer />
          <Paragraph>
            £
            {formatPoundsAsCurrencyString({
              amount: revenue.net,
              options: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              },
            })}
          </Paragraph>
        </div>
        <div>
          <Paragraph textWeight={TextWeight.Bold}>VAT at 20%</Paragraph>
          <VerticalSpacer />
          <Paragraph>
            £
            {formatPoundsAsCurrencyString({
              amount: revenue.vat,
              options: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              },
            })}
          </Paragraph>
        </div>
      </ThreeColumnLayout>
      <VerticalSpacer />
      {hasStripeConnectedAccount ? (
        <>
          <Paragraph>
            This is a self billed VAT invoice. The VAT shown is your output Tax
            due to HMRC.
          </Paragraph>
          <VerticalSpacer />
          <Paragraph>
            Revenue will be automatically transferred to your Stripe connected
            account on the 22nd of the month. The account balance will then be
            paid into your bank account according to your payout schedule.
          </Paragraph>
        </>
      ) : (
        <>
          <Paragraph className="content-center">
            Please produce an invoice for &apos;Your Revenue&apos; and send it
            to us at:
          </Paragraph>
          <Anchor href="mailto:<EMAIL>">
            <EMAIL>
          </Anchor>
        </>
      )}
    </div>
    <VerticalSpacer />
  </>
);

export default InvoiceSection;
