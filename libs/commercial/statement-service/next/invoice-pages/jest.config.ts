export default {
  displayName: 'commercial-statement-service-next-invoice-pages',
  preset: '../../../../../jest.preset.js',
  transform: {
    '^(?!.*\\.(js|jsx|ts|tsx|css|json)$)': '@nx/react/plugins/jest',
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nx/react/babel'] }],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  coverageDirectory:
    '../../../../../coverage/libs/commercial/statement-service/next/invoice-pages',
  setupFilesAfterEnv: ['@testing-library/jest-dom'],
};
