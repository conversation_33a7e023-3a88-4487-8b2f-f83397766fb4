import {
  Anchor,
  Button,
  ButtonTypes,
  Heading,
  HeadingSizes,
  Paragraph,
  TextWeight,
} from '@experience/shared/react/design-system';
import { InvoiceDto } from '@experience/commercial/statement-service/shared';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { formatPoundsAsCurrencyString } from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';

export interface AdminFeesInvoicePageProps {
  invoice: InvoiceDto;
}

export const AdminFeesInvoicePage = ({
  invoice: {
    id: invoiceId,
    group,
    site,
    statement,
    invoiceDate,
    invoiceNumber,
    quoteNumber,
  },
}: AdminFeesInvoicePageProps) => {
  const handleDownloadInvoice = () => {
    window.open(`/api/invoices/${invoiceId}/pdf`, '_blank');
  };

  return (
    <>
      <div className="flex items-baseline">
        <Heading.H1 fontSize={HeadingSizes.L} className="font-semibold">
          Admin fee invoice
        </Heading.H1>
        <Button
          className="print:hidden ml-auto"
          buttonType={ButtonTypes.PRIMARY}
          onClick={handleDownloadInvoice}
        >
          Download invoice
        </Button>
      </div>
      <Heading.H2 className="print:text-xxl">
        {group.groupName} - {site.siteName}
      </Heading.H2>
      <VerticalSpacer />
      <div className="flex space-x-12">
        <address className="not-italic">
          Pod Point Ltd
          <br />
          222 Grays Inn Road
          <br />
          London
          <br />
          WC1X 8HB
        </address>
        <div className="leading-8">
          <address>
            <Paragraph>
              <span className="font-bold">Email:</span>{' '}
              <EMAIL>
            </Paragraph>
          </address>

          <Paragraph>
            <span className="font-bold">VAT Reg No:</span> 278743455
          </Paragraph>

          <Paragraph>
            <span className="font-bold">UTR No:</span> 1959823584
          </Paragraph>
        </div>
      </div>
      <VerticalSpacer />

      <section>
        <Heading.H2 className="print:text-xxl">Invoice</Heading.H2>
        <VerticalSpacer />
        <div className="border-primary border-solid border-4 px-4 py-4 text-center">
          <div className="flex">
            <div className="flex-1">
              <Paragraph textWeight={TextWeight.Bold}>Invoice No</Paragraph>
              <Paragraph>{invoiceNumber}</Paragraph>
            </div>
            <div className="flex-1">
              <Paragraph textWeight={TextWeight.Bold}>Invoice Date</Paragraph>
              <Paragraph>{invoiceDate}</Paragraph>
            </div>
            <div className="flex-1">
              <Paragraph textWeight={TextWeight.Bold}>Quote No</Paragraph>
              <Paragraph>{quoteNumber}</Paragraph>
            </div>
            <div className="flex-1">
              <Paragraph textWeight={TextWeight.Bold}>Account Ref</Paragraph>
              <Paragraph>{group.accountRef}</Paragraph>
            </div>
          </div>
          <VerticalSpacer />
          <Paragraph>
            Invoice is payable within 30 days from document date.
          </Paragraph>
        </div>
        <VerticalSpacer />
        <div className="flex space-x-4 border-primary border-solid border-4 px-4 py-4 text-center">
          <div>
            <Paragraph textWeight={TextWeight.Bold}>Item Code</Paragraph>
            <Paragraph>ADFEE</Paragraph>
          </div>
          <div className="flex-1">
            <Paragraph textWeight={TextWeight.Bold}>Description</Paragraph>
            <Paragraph>
              {`Admin Fee | ${statement.reference} | ${dayjs(
                statement.workItem?.month
              ).format('MMMM YYYY')}`}
            </Paragraph>
          </div>
          <div>
            <Paragraph textWeight={TextWeight.Bold}>VAT</Paragraph>
            <Paragraph>20%</Paragraph>
          </div>
          <div>
            <Paragraph textWeight={TextWeight.Bold}>Net</Paragraph>
            <Paragraph>
              £
              {formatPoundsAsCurrencyString({
                amount: statement.fees.net,
                options: {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                },
              })}
            </Paragraph>
          </div>
        </div>
      </section>
      <VerticalSpacer />

      <Paragraph textWeight={TextWeight.Bold}>Site and Address:</Paragraph>
      <Paragraph>
        {site.siteName}, {statement.siteAddress}
      </Paragraph>
      <VerticalSpacer />

      <Paragraph>
        Where required, your charger will comply with The Electric Vehicles
        (Smart Charge Points) Regulations 2021. Our statement of Compliance can
        be found at{' '}
        <Anchor href="https://pod-point.com/technical/hardware" isNativeLink>
          https://pod-point.com/technical/hardware
        </Anchor>
      </Paragraph>
      <VerticalSpacer />

      <section>
        <Heading.H2 className="print:text-xxl">Payment details</Heading.H2>
        <VerticalSpacer />
        <div className="flex">
          <div className="flex w-2/3 justify-center">
            <table className="lg:w-4/5">
              <tbody>
                <tr>
                  <td className="font-bold">Business name:</td>
                  <td>Barclays Bank plc</td>
                </tr>
                <tr>
                  <td className="font-bold">Account name:</td>
                  <td>Pod Point Limited</td>
                </tr>
                <tr>
                  <td className="font-bold">Account currency:</td>
                  <td>GBP</td>
                </tr>
                <tr>
                  <td className="font-bold">Sort code:</td>
                  <td>20-02-83</td>
                </tr>
                <tr>
                  <td className="font-bold">Account number:</td>
                  <td>********</td>
                </tr>
                <tr>
                  <td className="font-bold">IBAN:</td>
                  <td>GB89 BARC 2002 8383 9172 15</td>
                </tr>
                <tr>
                  <td className="font-bold">SWIFT:</td>
                  <td>BARCGB22</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div className="flex-1 border-primary border-solid border-4 px-4 py-4 text-center">
            <div>
              <Paragraph textWeight={TextWeight.Bold}>Net</Paragraph>
              <Paragraph>
                £
                {formatPoundsAsCurrencyString({
                  amount: statement.fees.net,
                  options: {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  },
                })}
              </Paragraph>
              <VerticalSpacer />
              <Paragraph textWeight={TextWeight.Bold}>VAT</Paragraph>
              <Paragraph>
                £
                {formatPoundsAsCurrencyString({
                  amount: statement.fees.vat,
                  options: {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  },
                })}
              </Paragraph>
              <VerticalSpacer />
              <Paragraph textWeight={TextWeight.Bold}>Gross</Paragraph>
              <Paragraph>
                £
                {formatPoundsAsCurrencyString({
                  amount: statement.fees.gross,
                  options: {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  },
                })}
              </Paragraph>
            </div>
          </div>
        </div>
      </section>
      <VerticalSpacer />
    </>
  );
};

export default AdminFeesInvoicePage;
