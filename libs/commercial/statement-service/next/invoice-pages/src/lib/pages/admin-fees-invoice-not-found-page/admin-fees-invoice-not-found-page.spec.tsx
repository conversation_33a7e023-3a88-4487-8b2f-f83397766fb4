import { render } from '@testing-library/react';
import AdminFeesInvoiceNotFoundPage from './admin-fees-invoice-not-found-page';

describe('Admin fees invoice not found page', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<AdminFeesInvoiceNotFoundPage />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<AdminFeesInvoiceNotFoundPage />);
    expect(baseElement).toMatchSnapshot();
  });
});
