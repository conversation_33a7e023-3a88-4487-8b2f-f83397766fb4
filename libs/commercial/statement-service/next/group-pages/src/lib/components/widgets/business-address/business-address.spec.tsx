import { BusinessAddress } from './business-address';
import {
  TEST_GROUP,
  TEST_GROUP_WITH_ADDRESS,
} from '@experience/commercial/statement-service/shared';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

jest.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

const mockOpenEditGroup = jest.fn();
const defaultProps = {
  group: TEST_GROUP_WITH_ADDRESS,
  setOpenEditGroup: mockOpenEditGroup,
};

describe('Business address', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<BusinessAddress {...defaultProps} />);
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<BusinessAddress {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should default to - if no address is provided', () => {
    const { getByText } = render(
      <BusinessAddress {...defaultProps} group={TEST_GROUP} />
    );
    expect(getByText('-')).toBeInTheDocument();
  });

  it('should open the edit group modal when the edit address button is clicked', async () => {
    render(<BusinessAddress {...defaultProps} />);

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();

    await userEvent.click(
      screen.getByRole('button', {
        name: 'Edit business address',
      })
    );

    expect(mockOpenEditGroup).toHaveBeenCalledWith(true);
  });
});
