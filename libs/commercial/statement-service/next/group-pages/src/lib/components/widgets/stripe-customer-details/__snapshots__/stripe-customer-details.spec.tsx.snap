// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`StripeCustomerDetails should match snapshot with a stripe customer 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-1"
      >
        Stripe customer ID
      </h3>
      <button
        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75"
        type="button"
      >
        cus_123456789
      </button>
    </section>
  </div>
</body>
`;

exports[`StripeCustomerDetails should match snapshot with no stripe customer 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="h-full flex flex-col"
      >
        <p
          class="text-md font-normal h-full text-center break-words"
        >
          Complete the business details for this group then create a Stripe customer for invoicing
        </p>
        <div
          class="pb-4"
        />
        <button
          class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 mx-auto"
          data-testid="create-stripe-customer"
          id="create-stripe-customer"
          type="button"
        >
          1. Create Stripe customer
        </button>
      </div>
    </section>
  </div>
</body>
`;
