import {
  Anchor,
  CheckmarkCircleIcon,
  CrossCircleIcon,
  IconCell,
  InfiniteTable,
  Placement,
  Tooltip,
  WarningIcon,
} from '@experience/shared/react/design-system';
import { ColumnDef } from '@tanstack/react-table';
import { Group } from '@experience/commercial/statement-service/shared';

export interface GroupsTableProps {
  groups: Group[];
}

export const GroupsTable = ({ groups }: GroupsTableProps) => {
  const getStripeRevenuePayoutStatus = (group: Group): string => {
    switch (true) {
      case !!group.stripeConnectedAccountId && group.transfersEnabled:
        return 'Yes';
      case !!group.stripeConnectedAccountId && !group.transfersEnabled:
        return 'Warning';
      case !group.stripeConnectedAccountId:
        return 'No';
      default:
        return 'No';
    }
  };

  const getStripeSubscriptionStatus = ({
    stripeSubscriptionStatus,
  }: Group): string[] => {
    switch (stripeSubscriptionStatus) {
      case 'active':
      case 'trialing':
        return ['Yes'];
      case 'unpaid':
      case 'past_due':
        return ['Yes', 'Warning'];
      default:
        return ['No'];
    }
  };

  const columns: ColumnDef<Group>[] = [
    {
      accessorKey: 'groupName',
      header: () => 'Name',
      cell: ({ row: { original: group } }) => (
        <Anchor href={`/groups/${group.groupId}`}>{group.groupName}</Anchor>
      ),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'accountRef',
      header: () => 'Account reference',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'stripeCustomerId',
      filterFn: (row, columnId, filterValue) => {
        const cellValue = !!row.getValue(columnId);
        if (!filterValue || filterValue === '') return true;
        return filterValue === 'true' ? cellValue : !cellValue;
      },
      cell: ({ row: { original: group } }) => (
        <IconCell
          icon={
            group.stripeCustomerId ? (
              <CheckmarkCircleIcon.LIGHT
                height="h-6"
                className="text-primary"
                width="w-6"
                title="Yes"
              />
            ) : (
              <CrossCircleIcon.LIGHT
                className="text-error"
                height="h-6"
                width="w-6"
                title="No"
              />
            )
          }
        />
      ),
      header: () => 'Stripe customer',
      id: 'stripeCustomerId',
      meta: {
        sortButtonClassName: 'justify-center',
      },
      sortingFn: 'basic',
    },
    {
      accessorFn: (group) => getStripeSubscriptionStatus(group),
      cell: (cell) => {
        const stripeSubscriptionStatus = cell.getValue() as string[];
        const isYesStatus =
          stripeSubscriptionStatus.length === 1 &&
          stripeSubscriptionStatus.includes('Yes');
        const isWarningStatus = stripeSubscriptionStatus.includes('Warning');

        return (
          <IconCell
            icon={
              isYesStatus ? (
                <CheckmarkCircleIcon.LIGHT
                  height="h-6"
                  className="text-primary"
                  width="w-6"
                  title="Yes"
                />
              ) : isWarningStatus ? (
                <Tooltip
                  message="This group has one or more unpaid subscription invoices"
                  placement={Placement.TOP}
                >
                  <WarningIcon.LIGHT
                    className="text-warning"
                    height="h-6"
                    width="w-6"
                    title="Warning"
                  />
                </Tooltip>
              ) : (
                <CrossCircleIcon.LIGHT
                  className="text-error"
                  height="h-6"
                  width="w-6"
                  title="No"
                />
              )
            }
          />
        );
      },
      header: () => 'Subscription',
      id: 'stripeSubscriptionId',
      meta: {
        sortButtonClassName: 'justify-center',
      },
      sortingFn: 'basic',
      filterFn: 'arrIncludes',
    },
    {
      accessorFn: (group) => getStripeRevenuePayoutStatus(group),
      cell: ({ getValue: stripeRevenuePayoutStatus }) => (
        <IconCell
          icon={
            stripeRevenuePayoutStatus() === 'Yes' ? (
              <Tooltip
                message="This group has a Stripe connected account and revenue payouts are enabled"
                placement={Placement.TOP}
              >
                <CheckmarkCircleIcon.LIGHT
                  height="h-6"
                  className="text-primary"
                  width="w-6"
                  title="Yes"
                />
              </Tooltip>
            ) : stripeRevenuePayoutStatus() === 'Warning' ? (
              <Tooltip
                message="This group has a Stripe connected account but revenue payouts are not enabled"
                placement={Placement.TOP}
              >
                <WarningIcon.LIGHT
                  className="text-warning"
                  height="h-6"
                  width="w-6"
                  title="Warning"
                />
              </Tooltip>
            ) : (
              <Tooltip
                message="This group does not have a Stripe connected account"
                placement={Placement.TOP}
              >
                <CrossCircleIcon.LIGHT
                  className="text-error"
                  height="h-6"
                  width="w-6"
                  title="No"
                />
              </Tooltip>
            )
          }
        />
      ),
      header: () => 'Payout account',
      id: 'stripeRevenuePayoutStatus',
      meta: {
        sortButtonClassName: 'justify-center',
      },
    },
  ];

  return (
    <InfiniteTable
      id="groups-table"
      caption="Table of groups"
      columns={columns}
      data={groups}
      filters={{
        clientSide: [
          {
            columnId: 'stripeCustomerId',
            label: 'Stripe customer',
            options: [
              { id: '', name: 'All' },
              { id: 'true', name: 'Yes' },
              { id: 'false', name: 'No' },
            ],
          },
          {
            columnId: 'stripeSubscriptionId',
            label: 'Subscription',
            options: [
              { id: '', name: 'All' },
              { id: 'Yes', name: 'Yes' },
              { id: 'No', name: 'No' },
              { id: 'Warning', name: 'Warning' },
            ],
          },

          {
            columnId: 'stripeRevenuePayoutStatus',
            label: 'Payout account',
            options: [
              { id: '', name: 'All' },
              { id: 'Yes', name: 'Yes' },
              { id: 'No', name: 'No' },
              { id: 'Warning', name: 'Warning' },
            ],
          },
        ],
      }}
      showSearchField
    />
  );
};
