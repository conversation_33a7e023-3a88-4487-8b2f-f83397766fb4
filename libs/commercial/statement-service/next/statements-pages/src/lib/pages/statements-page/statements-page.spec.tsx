import * as MockDate from 'mockdate';
import {
  TEST_GROUP,
  TEST_STATEMENTS,
} from '@experience/commercial/statement-service/shared';
import { render } from '@testing-library/react';
import StatementsPage from './statements-page';

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

describe('StatementsPage', () => {
  beforeEach(() => {
    MockDate.set(new Date(2023, 11, 21));
  });

  afterEach(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    const { baseElement } = render(
      <StatementsPage
        statements={TEST_STATEMENTS}
        filteredDate={'2023-10-01'}
        groups={[TEST_GROUP]}
      />
    );
    expect(baseElement).toBeDefined();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <StatementsPage
        statements={TEST_STATEMENTS}
        filteredDate={'2023-10-01'}
        groups={[TEST_GROUP]}
      />
    );
    expect(baseElement).toMatchSnapshot();
  });
});
