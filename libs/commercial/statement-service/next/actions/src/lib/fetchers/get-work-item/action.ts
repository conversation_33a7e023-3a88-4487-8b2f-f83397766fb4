'use server';

import {
  STATEMENT_SERVICE_API_URL,
  WorkItem,
} from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

export const getWorkItem = async (workItemId: string): Promise<WorkItem> =>
  appRequestHandler<WorkItem>(
    `${STATEMENT_SERVICE_API_URL}/work-items/${workItemId}`,
    {
      method: 'GET',
      headers: {
        'content-type': 'application/json',
      },
    }
  );
