import {
  TEST_GROUP,
  TEST_UPDATE_GROUP_REQUEST,
} from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { updateGroupConfig } from './action';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Update group config', () => {
  it('it should update a group config', async () => {
    mockRequestHandler.mockResolvedValueOnce({ statusCode: 200 });
    const updateGroupBody = JSON.stringify(TEST_UPDATE_GROUP_REQUEST);

    await updateGroupConfig(TEST_GROUP.groupId, updateGroupBody);

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:5102/groups/${TEST_GROUP.groupId}`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'PUT',
        body: updateGroupBody,
      }
    );
  });
});
