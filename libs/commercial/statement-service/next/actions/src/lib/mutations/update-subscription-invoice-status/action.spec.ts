import {
  TEST_INVOICE_WITH_STRIPE_DATA,
  TEST_SUBSCRIPTION,
} from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { revalidatePath } from 'next/cache';
import { updateSubscriptionInvoiceStatus } from './action';

jest.mock('next/cache');
const mockRevalidatePath = jest.mocked(revalidatePath);

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Update subscription invoice status action', () => {
  it('should update the status of an invoice', async () => {
    await updateSubscriptionInvoiceStatus(
      TEST_SUBSCRIPTION.id,
      TEST_INVOICE_WITH_STRIPE_DATA.id,
      'paid'
    );

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:5102/stripe/subscriptions/${TEST_SUBSCRIPTION.id}/invoices/${TEST_INVOICE_WITH_STRIPE_DATA.id}/status`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'PUT',
        body: JSON.stringify({ status: 'paid' }),
      }
    );

    expect(mockRevalidatePath).toHaveBeenNthCalledWith(1, '/statements');
    expect(mockRevalidatePath).toHaveBeenNthCalledWith(2, '/groups/[slug]');
  });
});
