import { TEST_UPDATE_SITE_REQUEST } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { updateSiteConfig } from './action';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Update site config', () => {
  it('it should update a site config', async () => {
    mockRequestHandler.mockResolvedValueOnce({ statusCode: 200 });

    const body = JSON.stringify(TEST_UPDATE_SITE_REQUEST);

    await updateSiteConfig('963', body);

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:5102/sites/963`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'PUT',
        body,
      }
    );
  });
});
