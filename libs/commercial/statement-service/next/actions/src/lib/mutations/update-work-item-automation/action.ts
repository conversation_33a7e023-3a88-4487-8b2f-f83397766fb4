'use server';

import { STATEMENT_SERVICE_API_URL } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

export const updateWorkItemAutomation = async (
  workItemId: string,
  data: string
): Promise<void> => {
  await appRequestHandler(
    `${STATEMENT_SERVICE_API_URL}/work-items/${workItemId}/automated`,
    {
      headers: {
        'content-type': 'application/json',
      },
      method: 'PUT',
      body: data,
    }
  );
};
