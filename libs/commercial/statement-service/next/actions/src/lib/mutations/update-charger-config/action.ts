'use server';

import { STATEMENT_SERVICE_API_URL } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { revalidatePath } from 'next/cache';

export const updateChargerConfig = async (
  siteId: string,
  data: string
): Promise<void> => {
  await appRequestHandler(
    `${STATEMENT_SERVICE_API_URL}/sites/${siteId}/chargers`,
    {
      headers: {
        'content-type': 'application/json',
      },
      method: 'PUT',
      body: data,
    }
  );
  revalidatePath(`/groups/[groupId]/sites/[siteId]/chargers`, 'page');
};
