import { TEST_CREATE_GROUP_REQUEST } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { createGroupConfig } from './action';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Create group config', () => {
  it('it should create a group config', async () => {
    mockRequestHandler.mockResolvedValueOnce({ statusCode: 201 });

    await createGroupConfig(JSON.stringify(TEST_CREATE_GROUP_REQUEST));

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:5102/groups`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify(TEST_CREATE_GROUP_REQUEST),
      }
    );
  });
});
