import { TEST_INVOICE_WITH_STRIPE_DATA } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { revalidatePath } from 'next/cache';
import { updateInvoiceStatus } from './action';

jest.mock('next/cache');
const mockRevalidatePath = jest.mocked(revalidatePath);

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Update invoice status action', () => {
  it('should update the status of an invoice', async () => {
    await updateInvoiceStatus(TEST_INVOICE_WITH_STRIPE_DATA.id, 'paid');

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:5102/invoices/${TEST_INVOICE_WITH_STRIPE_DATA.id}/status`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'PUT',
        body: JSON.stringify({ status: 'paid' }),
      }
    );

    expect(mockRevalidatePath).toHaveBeenNthCalledWith(1, '/statements');
    expect(mockRevalidatePath).toHaveBeenNthCalledWith(2, '/groups/[slug]');
  });
});
