# Email

## Properties

| Name      | Type       | Description | Notes                  |
| --------- | ---------- | ----------- | ---------------------- |
| **email** | **string** |             | [default to undefined] |

## Example

```typescript
import { Email } from './api';

const instance: Email = {
  email,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
