# InvoiceDto

## Properties

| Name                    | Type                          | Description | Notes                             |
| ----------------------- | ----------------------------- | ----------- | --------------------------------- |
| **id**                  | **string**                    |             | [default to undefined]            |
| **invoiceNumber**       | **string**                    |             | [default to undefined]            |
| **quoteNumber**         | **string**                    |             | [default to undefined]            |
| **invoiceDate**         | **string**                    |             | [default to undefined]            |
| **group**               | [**Group**](Group.md)         |             | [default to undefined]            |
| **site**                | [**Site**](Site.md)           |             | [default to undefined]            |
| **statement**           | [**Statement**](Statement.md) |             | [default to undefined]            |
| **stripeInvoiceId**     | **string**                    |             | [optional] [default to undefined] |
| **stripeInvoiceStatus** | **string**                    |             | [optional] [default to undefined] |
| **stripeInvoiceNumber** | **string**                    |             | [optional] [default to undefined] |
| **hostedInvoiceUrl**    | **string**                    |             | [optional] [default to undefined] |

## Example

```typescript
import { InvoiceDto } from './api';

const instance: InvoiceDto = {
  id,
  invoiceNumber,
  quoteNumber,
  invoiceDate,
  group,
  site,
  statement,
  stripeInvoiceId,
  stripeInvoiceStatus,
  stripeInvoiceNumber,
  hostedInvoiceUrl,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
