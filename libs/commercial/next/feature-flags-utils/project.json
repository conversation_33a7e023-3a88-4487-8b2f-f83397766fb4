{"name": "feature-flags-utils", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/next/feature-flags-utils/src", "projectType": "library", "tags": ["commercial"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commercial/next/feature-flags-utils/jest.config.ts"}}}}