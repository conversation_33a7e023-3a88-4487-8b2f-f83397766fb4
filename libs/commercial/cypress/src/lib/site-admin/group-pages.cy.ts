export const describeGroupPages = (): void => {
  describe('group pages', () => {
    beforeEach(() => {
      cy.reload();
      cy.clickLink('Groups');
    });

    it('should render correctly', () => {
      cy.shouldHaveHeading(1, 'Groups');
      cy.findByRole('searchbox', { name: 'Search table' }).should('be.visible');
      cy.shouldHaveText(/List of \d+ groups?./);
      cy.shouldHaveTable('Table of groups');
    });

    it('should join a group', () => {
      cy.clickFirstButtonWithinTable('Join', 'Table of groups');
      cy.shouldHaveText(/You have successfully joined/);
    });
  });
};
