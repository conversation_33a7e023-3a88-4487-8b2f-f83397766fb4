// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/charge-authorisation/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/charge-authorisation/mock/ports.go -source=libs/data-platform/charge-authorisation/ports.go
//

// Package mock_chargeauthorisation is a generated GoMock package.
package mock_chargeauthorisation

import (
	context "context"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetAuthoriserForOCPIToken mocks base method.
func (m *MockRepository) GetAuthoriserForOCPIToken(ctx context.Context, ocpiToken, ppid string) (*chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthoriserForOCPIToken", ctx, ocpiToken, ppid)
	ret0, _ := ret[0].(*chargeauthorisation.ClaimedCharge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthoriserForOCPIToken indicates an expected call of GetAuthoriserForOCPIToken.
func (mr *MockRepositoryMockRecorder) GetAuthoriserForOCPIToken(ctx, ocpiToken, ppid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthoriserForOCPIToken", reflect.TypeOf((*MockRepository)(nil).GetAuthoriserForOCPIToken), ctx, ocpiToken, ppid)
}

// GetBillingEventByID mocks base method.
func (m *MockRepository) GetBillingEventByID(ctx context.Context, billingEventID int64) (*chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBillingEventByID", ctx, billingEventID)
	ret0, _ := ret[0].(*chargeauthorisation.ClaimedCharge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBillingEventByID indicates an expected call of GetBillingEventByID.
func (mr *MockRepositoryMockRecorder) GetBillingEventByID(ctx, billingEventID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillingEventByID", reflect.TypeOf((*MockRepository)(nil).GetBillingEventByID), ctx, billingEventID)
}

// GetGroupForRFID mocks base method.
func (m *MockRepository) GetGroupForRFID(ctx context.Context, rfidToken string) (*chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupForRFID", ctx, rfidToken)
	ret0, _ := ret[0].(*chargeauthorisation.ClaimedCharge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupForRFID indicates an expected call of GetGroupForRFID.
func (mr *MockRepositoryMockRecorder) GetGroupForRFID(ctx, rfidToken any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupForRFID", reflect.TypeOf((*MockRepository)(nil).GetGroupForRFID), ctx, rfidToken)
}

// GetGroupForUnitPpid mocks base method.
func (m *MockRepository) GetGroupForUnitPpid(ctx context.Context, chargerID string) (*chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupForUnitPpid", ctx, chargerID)
	ret0, _ := ret[0].(*chargeauthorisation.ClaimedCharge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupForUnitPpid indicates an expected call of GetGroupForUnitPpid.
func (mr *MockRepositoryMockRecorder) GetGroupForUnitPpid(ctx, chargerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupForUnitPpid", reflect.TypeOf((*MockRepository)(nil).GetGroupForUnitPpid), ctx, chargerID)
}

// GetGuestAuthoriserID mocks base method.
func (m *MockRepository) GetGuestAuthoriserID(ctx context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuestAuthoriserID", ctx)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuestAuthoriserID indicates an expected call of GetGuestAuthoriserID.
func (mr *MockRepositoryMockRecorder) GetGuestAuthoriserID(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuestAuthoriserID", reflect.TypeOf((*MockRepository)(nil).GetGuestAuthoriserID), ctx)
}

// GetLocationForUnitPpid mocks base method.
func (m *MockRepository) GetLocationForUnitPpid(ctx context.Context, chargerID string) (*chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocationForUnitPpid", ctx, chargerID)
	ret0, _ := ret[0].(*chargeauthorisation.ClaimedCharge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocationForUnitPpid indicates an expected call of GetLocationForUnitPpid.
func (mr *MockRepositoryMockRecorder) GetLocationForUnitPpid(ctx, chargerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocationForUnitPpid", reflect.TypeOf((*MockRepository)(nil).GetLocationForUnitPpid), ctx, chargerID)
}

// GetUserBalance mocks base method.
func (m *MockRepository) GetUserBalance(ctx context.Context, userToken string) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBalance", ctx, userToken)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserBalance indicates an expected call of GetUserBalance.
func (mr *MockRepositoryMockRecorder) GetUserBalance(ctx, userToken any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBalance", reflect.TypeOf((*MockRepository)(nil).GetUserBalance), ctx, userToken)
}

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
	isgomock struct{}
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// StopTransaction mocks base method.
func (m *MockService) StopTransaction(ctx context.Context, ppid, door string) (*chargeauthorisation.StopTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopTransaction", ctx, ppid, door)
	ret0, _ := ret[0].(*chargeauthorisation.StopTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopTransaction indicates an expected call of StopTransaction.
func (mr *MockServiceMockRecorder) StopTransaction(ctx, ppid, door any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopTransaction", reflect.TypeOf((*MockService)(nil).StopTransaction), ctx, ppid, door)
}

// ValidateGuestClaim mocks base method.
func (m *MockService) ValidateGuestClaim(ctx context.Context, guestToken, chargerID string, doorID uint32) (bool, *chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateGuestClaim", ctx, guestToken, chargerID, doorID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*chargeauthorisation.ClaimedCharge)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ValidateGuestClaim indicates an expected call of ValidateGuestClaim.
func (mr *MockServiceMockRecorder) ValidateGuestClaim(ctx, guestToken, chargerID, doorID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateGuestClaim", reflect.TypeOf((*MockService)(nil).ValidateGuestClaim), ctx, guestToken, chargerID, doorID)
}

// ValidateOcpiClaim mocks base method.
func (m *MockService) ValidateOcpiClaim(ctx context.Context, ocpiToken, chargerID string, doorID uint32) (bool, *chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateOcpiClaim", ctx, ocpiToken, chargerID, doorID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*chargeauthorisation.ClaimedCharge)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ValidateOcpiClaim indicates an expected call of ValidateOcpiClaim.
func (mr *MockServiceMockRecorder) ValidateOcpiClaim(ctx, ocpiToken, chargerID, doorID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateOcpiClaim", reflect.TypeOf((*MockService)(nil).ValidateOcpiClaim), ctx, ocpiToken, chargerID, doorID)
}

// ValidateRfidClaim mocks base method.
func (m *MockService) ValidateRfidClaim(ctx context.Context, rfidToken, chargerID string, doorID uint32) (bool, *chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateRfidClaim", ctx, rfidToken, chargerID, doorID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*chargeauthorisation.ClaimedCharge)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ValidateRfidClaim indicates an expected call of ValidateRfidClaim.
func (mr *MockServiceMockRecorder) ValidateRfidClaim(ctx, rfidToken, chargerID, doorID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateRfidClaim", reflect.TypeOf((*MockService)(nil).ValidateRfidClaim), ctx, rfidToken, chargerID, doorID)
}

// ValidateUserClaim mocks base method.
func (m *MockService) ValidateUserClaim(ctx context.Context, userToken, chargerID string, doorID uint32) (bool, *chargeauthorisation.ClaimedCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateUserClaim", ctx, userToken, chargerID, doorID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*chargeauthorisation.ClaimedCharge)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ValidateUserClaim indicates an expected call of ValidateUserClaim.
func (mr *MockServiceMockRecorder) ValidateUserClaim(ctx, userToken, chargerID, doorID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUserClaim", reflect.TypeOf((*MockService)(nil).ValidateUserClaim), ctx, userToken, chargerID, doorID)
}

// MockPodadminRepository is a mock of PodadminRepository interface.
type MockPodadminRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPodadminRepositoryMockRecorder
	isgomock struct{}
}

// MockPodadminRepositoryMockRecorder is the mock recorder for MockPodadminRepository.
type MockPodadminRepositoryMockRecorder struct {
	mock *MockPodadminRepository
}

// NewMockPodadminRepository creates a new mock instance.
func NewMockPodadminRepository(ctrl *gomock.Controller) *MockPodadminRepository {
	mock := &MockPodadminRepository{ctrl: ctrl}
	mock.recorder = &MockPodadminRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPodadminRepository) EXPECT() *MockPodadminRepositoryMockRecorder {
	return m.recorder
}

// InsertClaimedCharge mocks base method.
func (m *MockPodadminRepository) InsertClaimedCharge(ctx context.Context, claimedCharge *chargeauthorisation.ClaimedCharge) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertClaimedCharge", ctx, claimedCharge)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertClaimedCharge indicates an expected call of InsertClaimedCharge.
func (mr *MockPodadminRepositoryMockRecorder) InsertClaimedCharge(ctx, claimedCharge any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertClaimedCharge", reflect.TypeOf((*MockPodadminRepository)(nil).InsertClaimedCharge), ctx, claimedCharge)
}

// MockCache is a mock of Cache interface.
type MockCache struct {
	ctrl     *gomock.Controller
	recorder *MockCacheMockRecorder
	isgomock struct{}
}

// MockCacheMockRecorder is the mock recorder for MockCache.
type MockCacheMockRecorder struct {
	mock *MockCache
}

// NewMockCache creates a new mock instance.
func NewMockCache(ctrl *gomock.Controller) *MockCache {
	mock := &MockCache{ctrl: ctrl}
	mock.recorder = &MockCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCache) EXPECT() *MockCacheMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockCache) Get(arg0 string) (any, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0)
	ret0, _ := ret[0].(any)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockCacheMockRecorder) Get(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockCache)(nil).Get), arg0)
}

// Set mocks base method.
func (m *MockCache) Set(arg0 string, arg1 any, arg2 time.Duration) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Set", arg0, arg1, arg2)
}

// Set indicates an expected call of Set.
func (mr *MockCacheMockRecorder) Set(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockCache)(nil).Set), arg0, arg1, arg2)
}
