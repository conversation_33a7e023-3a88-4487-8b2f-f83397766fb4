// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.30.0
// source: billing.sql

package sqlc

import (
	"context"
)

const getBillingAccountBalanceByUserAuthID = `-- name: GetBillingAccountBalanceByUserAuthID :one
SELECT balance FROM podpoint.billing_accounts ba
INNER JOIN podpoint.users u ON ba.user_id = u.id
WHERE u.auth_id = $1
  AND u.deleted_at IS NULL
  AND ba.deleted_at IS NULL
`

func (q *Queries) GetBillingAccountBalanceByUserAuthID(ctx context.Context, authID string) (int32, error) {
	row := q.db.QueryRowContext(ctx, getBillingAccountBalanceByUserAuthID, authID)
	var balance int32
	err := row.Scan(&balance)
	return balance, err
}
