{"compileOnSave": false, "compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": ".", "declaration": false, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "importHelpers": true, "lib": ["es2023", "dom"], "module": "esnext", "moduleResolution": "node", "paths": {"@experience/billing-api/api-client": ["libs/mobile/billing/api/axios/src/index.ts"], "@experience/billing-api/msw": ["libs/mobile/billing/api/msw/src/handlers.ts"], "@experience/commercial/cypress": ["libs/commercial/cypress/src/index.ts"], "@experience/commercial/loyalty-card-service/axios": ["libs/commercial/loyalty-card-service/axios/src/index.ts"], "@experience/commercial/loyalty-card-service/nest/tesco-clubcard-module": ["libs/commercial/loyalty-card-service/nest/tesco-clubcard-module/src/index.ts"], "@experience/commercial/loyalty-card-service/nest/tesco-clubcard-module/specs": ["libs/commercial/loyalty-card-service/nest/tesco-clubcard-module/src/index.spec.ts"], "@experience/commercial/loyalty-card-service/prisma/loyalty-card/client": ["libs/commercial/loyalty-card-service/prisma/loyalty-card/client/src/index.ts"], "@experience/commercial/loyalty-card-service/prisma/loyalty-card/client/specs": ["libs/commercial/loyalty-card-service/prisma/loyalty-card/client/src/index.spec.ts"], "@experience/commercial/loyalty-card-service/prisma/loyalty-card/schema": ["libs/commercial/loyalty-card-service/prisma/loyalty-card/schema/src/index.ts"], "@experience/commercial/loyalty-card-service/shared": ["libs/commercial/loyalty-card-service/shared/src/index.ts"], "@experience/commercial/loyalty-card-service/shared/specs": ["libs/commercial/loyalty-card-service/shared/src/index.spec.ts"], "@experience/commercial/next/api-request-utils": ["libs/commercial/next/api-request-utils/src/index.ts"], "@experience/commercial/next/app-request-utils": ["libs/commercial/next/app-request-utils/src/index.ts"], "@experience/commercial/next/auth": ["libs/commercial/next/auth/src/index.ts"], "@experience/commercial/next/feature-flags-utils": ["libs/commercial/next/feature-flags-utils/src/index.ts"], "@experience/commercial/next/route-request-utils": ["libs/commercial/next/route-request-utils/src/index.ts"], "@experience/commercial/ocpi-service/nest/common/versions-module": ["libs/commercial/ocpi-service/common/nest/versions-module/src/index.ts"], "@experience/commercial/ocpi-service/nest/common/versions-module/specs": ["libs/commercial/ocpi-service/common/nest/versions-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/axios/emsp-api-client": ["libs/commercial/ocpi-service/v221/axios/emsp-api-client/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/cdrs-module": ["libs/commercial/ocpi-service/v221/nest/cdrs-module/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/cdrs-module/specs": ["libs/commercial/ocpi-service/v221/nest/cdrs-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/nest/commands-module": ["libs/commercial/ocpi-service/v221/nest/commands-module/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/commands-module/specs": ["libs/commercial/ocpi-service/v221/nest/commands-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/nest/credentials-module": ["libs/commercial/ocpi-service/v221/nest/credentials-module/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/credentials-module/specs": ["libs/commercial/ocpi-service/v221/nest/credentials-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/nest/locations-module": ["libs/commercial/ocpi-service/v221/nest/locations-module/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/locations-module/specs": ["libs/commercial/ocpi-service/v221/nest/locations-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/nest/sessions-module": ["libs/commercial/ocpi-service/v221/nest/sessions-module/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/sessions-module/specs": ["libs/commercial/ocpi-service/v221/nest/sessions-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/nest/tariffs-module": ["libs/commercial/ocpi-service/v221/nest/tariffs-module/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/tariffs-module/specs": ["libs/commercial/ocpi-service/v221/nest/tariffs-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/nest/tokens-module": ["libs/commercial/ocpi-service/v221/nest/tokens-module/src/index.ts"], "@experience/commercial/ocpi-service/v221/nest/tokens-module/specs": ["libs/commercial/ocpi-service/v221/nest/tokens-module/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/prisma/client": ["libs/commercial/ocpi-service/v221/prisma/client/src/index.ts"], "@experience/commercial/ocpi-service/v221/prisma/client/specs": ["libs/commercial/ocpi-service/v221/prisma/client/src/index.spec.ts"], "@experience/commercial/ocpi-service/v221/prisma/schema": ["libs/commercial/ocpi-service/v221/prisma/schema/src/index.ts"], "@experience/commercial/ocpi-service/v221/shared": ["libs/commercial/ocpi-service/v221/shared/src/index.ts"], "@experience/commercial/ocpi-service/v221/shared/specs": ["libs/commercial/ocpi-service/v221/shared/src/index.spec.ts"], "@experience/commercial/site-admin/domain/auth": ["libs/commercial/site-admin/domain/auth/src/index.ts"], "@experience/commercial/site-admin/domain/rfid": ["libs/commercial/site-admin/domain/rfid/src/index.ts"], "@experience/commercial/site-admin/domain/support": ["libs/commercial/site-admin/domain/support/src/index.ts"], "@experience/commercial/site-admin/maizzle": ["libs/commercial/site-admin/maizzle/src/index.ts"], "@experience/commercial/site-admin/nest/admin-module": ["libs/commercial/site-admin/nest/admin-module/src/index.ts"], "@experience/commercial/site-admin/nest/admin-module/specs": ["libs/commercial/site-admin/nest/admin-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/advert-module": ["libs/commercial/site-admin/nest/advert-module/src/index.ts"], "@experience/commercial/site-admin/nest/billing-module": ["libs/commercial/site-admin/nest/billing-module/src/index.ts"], "@experience/commercial/site-admin/nest/billing-module/specs": ["libs/commercial/site-admin/nest/billing-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/charge-module": ["libs/commercial/site-admin/nest/charge-module/src/index.ts"], "@experience/commercial/site-admin/nest/domain-module": ["libs/commercial/site-admin/nest/domain-module/src/index.ts"], "@experience/commercial/site-admin/nest/domain-module/specs": ["libs/commercial/site-admin/nest/domain-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/driver-module": ["libs/commercial/site-admin/nest/driver-module/src/index.ts"], "@experience/commercial/site-admin/nest/driver-module/specs": ["libs/commercial/site-admin/nest/driver-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/expense-module": ["libs/commercial/site-admin/nest/expense-module/src/index.ts"], "@experience/commercial/site-admin/nest/insights-module": ["libs/commercial/site-admin/nest/insights-module/src/index.ts"], "@experience/commercial/site-admin/nest/insights-module/specs": ["libs/commercial/site-admin/nest/insights-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/pod-module": ["libs/commercial/site-admin/nest/pod-module/src/index.ts"], "@experience/commercial/site-admin/nest/pod-module/specs": ["libs/commercial/site-admin/nest/pod-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/rfid-module": ["libs/commercial/site-admin/nest/rfid-module/src/index.ts"], "@experience/commercial/site-admin/nest/rfid-module/specs": ["libs/commercial/site-admin/nest/rfid-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/site-module": ["libs/commercial/site-admin/nest/site-module/src/index.ts"], "@experience/commercial/site-admin/nest/site-module/specs": ["libs/commercial/site-admin/nest/site-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/support-module": ["libs/commercial/site-admin/nest/support-module/src/index.ts"], "@experience/commercial/site-admin/nest/support-module/specs": ["libs/commercial/site-admin/nest/support-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/tariff-module": ["libs/commercial/site-admin/nest/tariff-module/src/index.ts"], "@experience/commercial/site-admin/nest/tariff-module/specs": ["libs/commercial/site-admin/nest/tariff-module/src/index.spec.ts"], "@experience/commercial/site-admin/nest/user-module": ["libs/commercial/site-admin/nest/user-module/src/index.ts"], "@experience/commercial/site-admin/nest/user-module/specs": ["libs/commercial/site-admin/nest/user-module/src/index.spec.ts"], "@experience/commercial/site-admin/next/admins-pages": ["libs/commercial/site-admin/next/admins-pages/src/index.ts"], "@experience/commercial/site-admin/next/advert-pages": ["libs/commercial/site-admin/next/advert-pages/src/index.ts"], "@experience/commercial/site-admin/next/auth-pages": ["libs/commercial/site-admin/next/auth-pages/src/index.ts"], "@experience/commercial/site-admin/next/billing-pages": ["libs/commercial/site-admin/next/billing-pages/src/index.ts"], "@experience/commercial/site-admin/next/driver-pages": ["libs/commercial/site-admin/next/driver-pages/src/index.ts"], "@experience/commercial/site-admin/next/expense-pages": ["libs/commercial/site-admin/next/expense-pages/src/index.ts"], "@experience/commercial/site-admin/next/group-pages": ["libs/commercial/site-admin/next/group-pages/src/index.ts"], "@experience/commercial/site-admin/next/help-pages": ["libs/commercial/site-admin/next/help-pages/src/index.ts"], "@experience/commercial/site-admin/next/home-page": ["libs/commercial/site-admin/next/home-page/src/index.ts"], "@experience/commercial/site-admin/next/insights-pages": ["libs/commercial/site-admin/next/insights-pages/src/index.ts"], "@experience/commercial/site-admin/next/pod-pages": ["libs/commercial/site-admin/next/pod-pages/src/index.ts"], "@experience/commercial/site-admin/next/profile-pages": ["libs/commercial/site-admin/next/profile-pages/src/index.ts"], "@experience/commercial/site-admin/next/rfid-pages": ["libs/commercial/site-admin/next/rfid-pages/src/index.ts"], "@experience/commercial/site-admin/next/shared": ["libs/commercial/site-admin/next/shared/src/index.ts"], "@experience/commercial/site-admin/next/site-pages": ["libs/commercial/site-admin/next/site-pages/src/index.ts"], "@experience/commercial/site-admin/next/statement-pages": ["libs/commercial/site-admin/next/statement-pages/src/index.ts"], "@experience/commercial/site-admin/next/support-pages": ["libs/commercial/site-admin/next/support-pages/src/index.ts"], "@experience/commercial/site-admin/next/tariff-pages": ["libs/commercial/site-admin/next/tariff-pages/src/index.ts"], "@experience/commercial/site-admin/next/terms-and-conditions-pages": ["libs/commercial/site-admin/next/terms-and-conditions-pages/src/index.ts"], "@experience/commercial/site-admin/prisma/rfid/client": ["libs/commercial/site-admin/prisma/rfid/client/src/index.ts"], "@experience/commercial/site-admin/prisma/rfid/schema": ["libs/commercial/site-admin/prisma/rfid/schema/src/index.ts"], "@experience/commercial/site-admin/typescript/domain-model": ["libs/commercial/site-admin/typescript/domain-model/src/index.ts"], "@experience/commercial/site-admin/typescript/domain-model-validation": ["libs/commercial/site-admin/typescript/domain-model-validation/src/index.ts"], "@experience/commercial/statement-service/axios": ["libs/commercial/statement-service/axios/src/index.ts"], "@experience/commercial/statement-service/nest/groups-module": ["libs/commercial/statement-service/nest/groups-module/src/index.ts"], "@experience/commercial/statement-service/nest/groups-module/specs": ["libs/commercial/statement-service/nest/groups-module/src/index.spec.ts"], "@experience/commercial/statement-service/nest/invoice-module": ["libs/commercial/statement-service/nest/invoice-module/src/index.ts"], "@experience/commercial/statement-service/nest/invoice-module/specs": ["libs/commercial/statement-service/nest/invoice-module/src/index.spec.ts"], "@experience/commercial/statement-service/nest/queue-module": ["libs/commercial/statement-service/nest/queue-module/src/index.ts"], "@experience/commercial/statement-service/nest/shared": ["libs/commercial/statement-service/nest/shared/src/index.ts"], "@experience/commercial/statement-service/nest/sites-module": ["libs/commercial/statement-service/nest/sites-module/src/index.ts"], "@experience/commercial/statement-service/nest/sites-module/specs": ["libs/commercial/statement-service/nest/sites-module/src/index.spec.ts"], "@experience/commercial/statement-service/nest/statement-module": ["libs/commercial/statement-service/nest/statement-module/src/index.ts"], "@experience/commercial/statement-service/nest/statement-module/specs": ["libs/commercial/statement-service/nest/statement-module/src/index.spec.ts"], "@experience/commercial/statement-service/nest/stats-module": ["libs/commercial/statement-service/nest/stats-module/src/index.ts"], "@experience/commercial/statement-service/nest/stats-module/specs": ["libs/commercial/statement-service/nest/stats-module/src/index.spec.ts"], "@experience/commercial/statement-service/nest/stripe-module": ["libs/commercial/statement-service/nest/stripe-module/src/index.ts"], "@experience/commercial/statement-service/nest/stripe-module/specs": ["libs/commercial/statement-service/nest/stripe-module/src/index.spec.ts"], "@experience/commercial/statement-service/nest/webhook-module": ["libs/commercial/statement-service/nest/webhook-module/src/index.ts"], "@experience/commercial/statement-service/nest/webhook-module/specs": ["libs/commercial/statement-service/nest/webhook-module/src/index.spec.ts"], "@experience/commercial/statement-service/nest/work-item-module": ["libs/commercial/statement-service/nest/work-item-module/src/index.ts"], "@experience/commercial/statement-service/nest/work-item-module/specs": ["libs/commercial/statement-service/nest/work-item-module/src/index.spec.ts"], "@experience/commercial/statement-service/next/actions": ["libs/commercial/statement-service/next/actions/src/index.ts"], "@experience/commercial/statement-service/next/chargers-pages": ["libs/commercial/statement-service/next/chargers-pages/src/index.ts"], "@experience/commercial/statement-service/next/group-pages": ["libs/commercial/statement-service/next/group-pages/src/index.ts"], "@experience/commercial/statement-service/next/home-pages": ["libs/commercial/statement-service/next/home-pages/src/index.ts"], "@experience/commercial/statement-service/next/invoice-pages": ["libs/commercial/statement-service/next/invoice-pages/src/index.ts"], "@experience/commercial/statement-service/next/shared": ["libs/commercial/statement-service/next/shared/src/index.ts"], "@experience/commercial/statement-service/next/statements-pages": ["libs/commercial/statement-service/next/statements-pages/src/index.ts"], "@experience/commercial/statement-service/next/worklist-pages": ["libs/commercial/statement-service/next/worklist-pages/src/index.ts"], "@experience/commercial/statement-service/prisma/statements/client": ["libs/commercial/statement-service/prisma/statements/client/src/index.ts"], "@experience/commercial/statement-service/prisma/statements/schema": ["libs/commercial/statement-service/prisma/statements/schema/src/index.ts"], "@experience/commercial/statement-service/shared": ["libs/commercial/statement-service/shared/src/index.ts"], "@experience/data-platform/api/docs": ["libs/data-platform/api/docs/src/index.ts"], "@experience/driver-account-api/api-client": ["libs/mobile/driver-account/api/axios/src/index.ts"], "@experience/driver-account-api/msw-client": ["libs/mobile/driver-account/api/msw/src/handlers.ts"], "@experience/installer/api/axios": ["libs/mobile/installer/api/axios/src/index.ts"], "@experience/installer/api/dart": ["libs/mobile/installer/api/dart/src/index.ts"], "@experience/installer/api/msw": ["libs/mobile/installer/api/msw/src/handlers.ts"], "@experience/installer/api/nestjs": ["libs/mobile/installer/api/nestjs/src/index.ts"], "@experience/installer/types": ["libs/mobile/installer/types/src/index.ts"], "@experience/mobile/api/axios": ["libs/mobile/api/axios/src/index.ts"], "@experience/mobile/api/dart": ["libs/mobile/api/dart/src/index.ts"], "@experience/mobile/api/e2e": ["apps/mobile/api/src/index.ts"], "@experience/mobile/api/nestjs": ["libs/mobile/api/nestjs/src/index.ts"], "@experience/mobile/clean-architecture": ["libs/mobile/clean-architecture/src/index.ts"], "@experience/mobile/driver-account/database": ["libs/mobile/driver-account/database/src/index.ts"], "@experience/mobile/driver-account/domain/auth": ["libs/mobile/driver-account/domain/auth/src/index.ts"], "@experience/mobile/driver-account/domain/user": ["libs/mobile/driver-account/domain/user/src/index.ts"], "@experience/mobile/driver-account/maizzle": ["libs/mobile/driver-account/maizzle/src/index.ts"], "@experience/mobile/driver-account/typescript/domain-model-validation": ["libs/mobile/driver-account/typescript/domain-model-validation/src/index.ts"], "@experience/mobile/events": ["libs/mobile/events/src/index.ts"], "@experience/mobile/identity/next/pages": ["libs/mobile/identity/next/pages/src/index.ts"], "@experience/mobile/nest/api3-token": ["libs/mobile/nest/api3-token/src/index.ts"], "@experience/mobile/nest/app-upgrade-module": ["libs/mobile/nest/app-upgrade-module/src/index.ts"], "@experience/mobile/nest/app-upgrade-module/specs": ["libs/mobile/nest/app-upgrade-module/src/index.spec.ts"], "@experience/mobile/nest/auth": ["libs/mobile/nest/auth/src/index.ts"], "@experience/mobile/nest/auth-service": ["libs/mobile/nest/auth-service/src/index.ts"], "@experience/mobile/nest/auth-service/specs": ["libs/mobile/nest/auth-service/src/index.spec.ts"], "@experience/mobile/nest/auth/specs": ["libs/mobile/nest/auth/src/index.spec.ts"], "@experience/mobile/nest/authorisation": ["libs/mobile/nest/authorisation/src/index.ts"], "@experience/mobile/nest/device-config": ["libs/mobile/nest/device-config/src/index.ts"], "@experience/mobile/nest/exception": ["libs/mobile/nest/exception/src/index.ts"], "@experience/mobile/nest/revoke-token": ["libs/mobile/nest/revoke-token/src/index.ts"], "@experience/mobile/nest/rewards-activity-log": ["libs/mobile/nest/rewards-activity-log/src/index.ts"], "@experience/mobile/nest/sqs-event-router": ["libs/mobile/nest/sqs-event-router/src/index.ts"], "@experience/mobile/nest/swagger": ["libs/mobile/nest/swagger/src/index.ts"], "@experience/mobile/nest/typeorm-transactions": ["libs/mobile/nest/typeorm-transactions/src/index.ts"], "@experience/mobile/payments-api/axios": ["libs/mobile/payments-api/axios/src/index.ts"], "@experience/mobile/payments-api/msw": ["libs/mobile/payments-api/msw/src/handlers.ts"], "@experience/mobile/rewards-api/axios": ["libs/mobile/rewards-api/axios/src/index.ts"], "@experience/mobile/rewards-api/msw": ["libs/mobile/rewards-api/msw/src/handlers.ts"], "@experience/mobile/slick/client": ["libs/mobile/slick/client/src/index.ts"], "@experience/mobile/subscriptions-api/axios": ["libs/mobile/subscriptions-api/axios/src/index.ts"], "@experience/mobile/subscriptions-api/msw": ["libs/mobile/subscriptions-api/msw/src/handlers.ts"], "@experience/mobile/test/mocking": ["libs/mobile/test/mocking/src/index.ts"], "@experience/shared/axios/acquired-api-client": ["libs/shared/axios/acquired-api-client/src/index.ts"], "@experience/shared/axios/acquired-api-client-msw": ["libs/shared/axios/acquired-api-client-msw/src/handlers.ts"], "@experience/shared/axios/assets-api-client": ["libs/shared/axios/assets-api-client/src/index.ts"], "@experience/shared/axios/assets-api-client-msw": ["libs/shared/axios/assets-api-client-msw/src/handlers.ts"], "@experience/shared/axios/assets-api-client/fixtures": ["libs/shared/axios/assets-api-client/__fixtures__/index.ts"], "@experience/shared/axios/assets-configuration-api-client": ["libs/shared/axios/assets-configuration-api-client/src/index.ts"], "@experience/shared/axios/assets-configuration-api-client-msw": ["libs/shared/axios/assets-configuration-api-client-msw/src/handlers.ts"], "@experience/shared/axios/assets-configuration-api-client-msw-deprecated": ["libs/shared/axios/assets-configuration-api-client-msw-deprecated/src/handlers.ts"], "@experience/shared/axios/assets-configuration-api-client/fixtures": ["libs/shared/axios/assets-configuration-api-client/__fixtures__/index.ts"], "@experience/shared/axios/assets-provisioning-api-client": ["libs/shared/axios/assets-provisioning-api-client/src/index.ts"], "@experience/shared/axios/assets-provisioning-api-client-msw": ["libs/shared/axios/assets-provisioning-api-client-msw/src/handlers.ts"], "@experience/shared/axios/aws/signature-v4-interceptor": ["libs/shared/axios/aws/signature-v4-interceptor/src/index.ts"], "@experience/shared/axios/axle-api-client": ["libs/shared/axios/axle-api-client/src/index.ts"], "@experience/shared/axios/axle-api-client-msw": ["libs/shared/axios/axle-api-client-msw/src/handlers.ts"], "@experience/shared/axios/competitions-service-client": ["libs/shared/axios/competitions-service-client/src/index.ts"], "@experience/shared/axios/competitions-service-client-msw": ["libs/shared/axios/competitions-service-client-msw/src/handlers.ts"], "@experience/shared/axios/connectivity-commands-api-client": ["libs/shared/axios/connectivity-commands-api-client/src/index.ts"], "@experience/shared/axios/connectivity-commands-api-client-msw": ["libs/shared/axios/connectivity-commands-api-client-msw/src/handlers.ts"], "@experience/shared/axios/connectivity-commands-api-client/fixtures": ["libs/shared/axios/connectivity-commands-api-client/__fixtures__/index.ts"], "@experience/shared/axios/connectivity-service-client": ["libs/shared/axios/connectivity-service-client/src/index.ts"], "@experience/shared/axios/connectivity-service-client-msw": ["libs/shared/axios/connectivity-service-client-msw/src/handlers.ts"], "@experience/shared/axios/connectivity-service-client/fixtures": ["libs/shared/axios/connectivity-service-client/__fixtures__/index.ts"], "@experience/shared/axios/connectivity-service-client/stubs": ["libs/shared/axios/connectivity-service-client/__stubs__/index.ts"], "@experience/shared/axios/currency-converter-client": ["libs/shared/axios/currency-converter-client/src/index.ts"], "@experience/shared/axios/currency-converter-client-msw": ["libs/shared/axios/currency-converter-client-msw/src/handlers.ts"], "@experience/shared/axios/data-platform-api-client": ["libs/shared/axios/data-platform-api-client/src/index.ts"], "@experience/shared/axios/data-platform-api-client-msw": ["libs/shared/axios/data-platform-api-client-msw/src/handlers.ts"], "@experience/shared/axios/data-platform-api-client/fixtures": ["libs/shared/axios/data-platform-api-client/__fixtures__/index.ts"], "@experience/shared/axios/data-platform-api-client/stubs": ["libs/shared/axios/data-platform-api-client/__stubs__/index.ts"], "@experience/shared/axios/diagnostics-service-client": ["libs/shared/axios/diagnostics-service-client/src/index.ts"], "@experience/shared/axios/diagnostics-service-client-msw": ["libs/shared/axios/diagnostics-service-client-msw/src/handlers.ts"], "@experience/shared/axios/diagnostics-service-client/fixtures": ["libs/shared/axios/diagnostics-service-client/__fixtures__/index.ts"], "@experience/shared/axios/diagnostics-service-client/stubs": ["libs/shared/axios/diagnostics-service-client/__stubs__/index.ts"], "@experience/shared/axios/enode-api-client": ["libs/shared/axios/enode-api-client/src/index.ts"], "@experience/shared/axios/enode-api-client-msw": ["libs/shared/axios/enode-api-client-msw/src/handlers.ts"], "@experience/shared/axios/firmware-upgrade-client": ["libs/shared/axios/firmware-upgrade-client/src/index.ts"], "@experience/shared/axios/firmware-upgrade-client-msw": ["libs/shared/axios/firmware-upgrade-client-msw/src/handlers.ts"], "@experience/shared/axios/firmware-upgrade-client/fixtures": ["libs/shared/axios/firmware-upgrade-client/__fixtures__/index.ts"], "@experience/shared/axios/firmware-upgrade-client/stubs": ["libs/shared/axios/firmware-upgrade-client/__stubs__/index.ts"], "@experience/shared/axios/internal-site-admin-api-client": ["libs/shared/axios/internal-site-admin-api-client/src/index.ts"], "@experience/shared/axios/internal-site-admin-api-client-msw": ["libs/shared/axios/internal-site-admin-api-client-msw/src/handlers.ts"], "@experience/shared/axios/smart-charging-service-client": ["libs/shared/axios/smart-charging-service-client/src/index.ts"], "@experience/shared/axios/smart-charging-service-client-msw": ["libs/shared/axios/smart-charging-service-client-msw/src/handlers.ts"], "@experience/shared/axios/tariffs-api-client": ["libs/shared/axios/tariffs-api-client/src/index.ts"], "@experience/shared/axios/tariffs-api-client-msw": ["libs/shared/axios/tariffs-api-client-msw/src/handlers.ts"], "@experience/shared/axios/vehicle-api-client": ["libs/shared/axios/vehicle-api-client/src/index.ts"], "@experience/shared/axios/vehicle-api-client-msw": ["libs/shared/axios/vehicle-api-client-msw/src/handlers.ts"], "@experience/shared/cypress": ["libs/shared/cypress/src/index.ts"], "@experience/shared/firebase/admin": ["libs/shared/firebase/admin/src/index.ts"], "@experience/shared/i18n": ["libs/shared/i18n/src/index.ts"], "@experience/shared/mdx/utils": ["libs/shared/mdx/utils/src/index.ts"], "@experience/shared/nest/acquired": ["libs/shared/nest/acquired/src/index.ts"], "@experience/shared/nest/aws/cloudwatch-module": ["libs/shared/nest/aws/cloudwatch-module/src/index.ts"], "@experience/shared/nest/aws/eventbridge-module": ["libs/shared/nest/aws/eventbridge-module/src/index.ts"], "@experience/shared/nest/aws/s3-module": ["libs/shared/nest/aws/s3-module/src/index.ts"], "@experience/shared/nest/aws/ses-module": ["libs/shared/nest/aws/ses-module/src/index.ts"], "@experience/shared/nest/aws/sns-module": ["libs/shared/nest/aws/sns-module/src/index.ts"], "@experience/shared/nest/aws/sqs-module": ["libs/shared/nest/aws/sqs-module/src/index.ts"], "@experience/shared/nest/dev-utils": ["libs/shared/nest/dev-utils/src/index.ts"], "@experience/shared/nest/remote-config": ["libs/shared/nest/remote-config/src/index.ts"], "@experience/shared/nest/remote-config/specs": ["libs/shared/nest/remote-config/src/index.spec.ts"], "@experience/shared/nest/stripe": ["libs/shared/nest/stripe/src/index.ts"], "@experience/shared/nest/utils": ["libs/shared/nest/utils/src/index.ts"], "@experience/shared/nest/version-module": ["libs/shared/nest/version-module/src/index.ts"], "@experience/shared/next/components": ["libs/shared/next/components/src/index.ts"], "@experience/shared/next/firebase": ["libs/shared/next/firebase/src/index.ts"], "@experience/shared/next/hooks": ["libs/shared/next/hooks/src/index.ts"], "@experience/shared/next/logger": ["libs/shared/next/logger/src/index.ts"], "@experience/shared/next/utils": ["libs/shared/next/utils/src/index.ts"], "@experience/shared/react/application-shells/sidebar-layout": ["libs/shared/react/application-shells/sidebar-layout/src/index.ts"], "@experience/shared/react/design-system": ["libs/shared/react/design-system/src/index.ts"], "@experience/shared/react/design-system-icons": ["libs/shared/react/design-system/src/lib/icons/index.ts"], "@experience/shared/react/error-pages": ["libs/shared/react/error-pages/src/index.ts"], "@experience/shared/react/form": ["libs/shared/react/form/src/index.ts"], "@experience/shared/react/google-maps": ["libs/shared/react/google-maps/src/index.ts"], "@experience/shared/react/headings": ["libs/shared/react/headings/src/index.ts"], "@experience/shared/react/hooks": ["libs/shared/react/hooks/src/index.ts"], "@experience/shared/react/layouts": ["libs/shared/react/layouts/src/index.ts"], "@experience/shared/react/modals": ["libs/shared/react/modals/src/index.ts"], "@experience/shared/react/server-hooks": ["libs/shared/react/server-hooks/src/index.ts"], "@experience/shared/salesforce/client": ["libs/shared/salesforce/client/src/index.ts"], "@experience/shared/salesforce/types": ["libs/shared/salesforce/types/src/index.ts"], "@experience/shared/sequelize/auth-service": ["libs/shared/sequelize/auth-service/src/index.ts"], "@experience/shared/sequelize/podadmin": ["libs/shared/sequelize/podadmin/src/index.ts"], "@experience/shared/swr/utils": ["libs/shared/swr/utils/src/index.ts"], "@experience/shared/typescript/csv-utils": ["libs/shared/typescript/csv-utils/src/index.ts"], "@experience/shared/typescript/feature-flag-service": ["libs/shared/typescript/feature-flag-service/src/index.ts"], "@experience/shared/typescript/oidc-utils": ["libs/shared/typescript/oidc-utils/src/index.ts"], "@experience/shared/typescript/oidc-utils/specs": ["libs/shared/typescript/oidc-utils/src/index.spec.ts"], "@experience/shared/typescript/test-utils": ["libs/shared/typescript/test-utils/src/index.ts"], "@experience/shared/typescript/utils": ["libs/shared/typescript/utils/src/index.ts"], "@experience/shared/typescript/validation": ["libs/shared/typescript/validation/src/index.ts"], "@experience/support/cypress": ["libs/support/cypress/src/index.ts"], "@experience/support/support-tool/next": ["libs/support/support-tool/next/src/index.ts"], "@experience/support/support-tool/shared": ["libs/support/support-tool/shared/src/index.ts"], "@experience/support/support-tool/shared/specs": ["libs/support/support-tool/shared/src/index.spec.ts"], "connectivity-commands-api-client-msw": ["libs/shared/axios/connectivity-commands-api-client-msw/src/handlers.ts"]}, "resolveJsonModule": true, "rootDir": ".", "skipDefaultLibCheck": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "strictPropertyInitialization": false, "target": "es2015"}, "exclude": ["node_modules", "tmp"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}