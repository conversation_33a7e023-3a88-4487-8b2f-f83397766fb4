import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Remove from delegated control', () => {
  beforeEach(() => {
    process.env.SUPPORT_TOOL_API_URL = 'http://localhost:7102';
  });

  it('should remove from delegated control', async () => {
    const ppid = 'PSL-12345';

    await appRequestHandler(
      `http://localhost:7102/chargers/${ppid}/delegated-control`,
      {
        headers: { 'content-type': 'application/json' },
        method: 'DELETE',
      }
    );

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:7102/chargers/${ppid}/delegated-control`,
      {
        headers: { 'content-type': 'application/json' },
        method: 'DELETE',
      }
    );
  });
});
