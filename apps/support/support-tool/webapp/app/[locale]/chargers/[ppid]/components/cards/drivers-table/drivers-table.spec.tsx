import 'whatwg-fetch';
import { fireEvent, screen, within } from '@testing-library/react';
import { mockCommercialAttributes } from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../../test-utils';
import DriversTable, { DriversTableProps } from './drivers-table';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const defaultProps: DriversTableProps = {
  drivers: mockCommercialAttributes.drivers,
};

describe('DriversTable', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithProviders(
      <DriversTable {...defaultProps} />
    );
    expect(baseElement).toBeTruthy();
  });

  it('should render a table caption', () => {
    renderWithProviders(<DriversTable {...defaultProps} />);
    expect(screen.getByText('Table of drivers')).toBeInTheDocument();
  });

  it('should have rows for each driver', () => {
    renderWithProviders(<DriversTable {...defaultProps} />);

    const rows = document.querySelectorAll('tbody tr');

    expect(rows.length).toBe(defaultProps.drivers.length);
    expect(screen.getByText(defaultProps.drivers[0].email)).toBeInTheDocument();
  });

  it('should not render a table if there are no drivers', () => {
    renderWithProviders(<DriversTable drivers={[]} />);

    const rows = document.querySelectorAll('tbody tr');

    expect(rows.length).toBe(0);
    expect(
      screen.getByText(
        'This group this charger belongs to has no drivers configured.'
      )
    ).toBeInTheDocument();
  });

  it('should open modal when expand icon is clicked', () => {
    renderWithProviders(<DriversTable {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Expand' }));

    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();
    expect(
      within(dialog).getByText(defaultProps.drivers[0].email)
    ).toBeInTheDocument();
  });
});
