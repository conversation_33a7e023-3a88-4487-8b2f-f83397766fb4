import { renderWithProviders } from '../../../../../test-utils';
import DiagnosticsLogsViewerPage from './page';

describe('View charger diagnostics logs viewer page', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithProviders(<DiagnosticsLogsViewerPage />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(<DiagnosticsLogsViewerPage />);
    expect(baseElement).toMatchSnapshot();
  });
});
