'use server';

import { SupportToolUpdateRevenueCostRequest } from '@experience/support/support-tool/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { getTranslations } from 'next-intl/server';
import { plainToInstance } from 'class-transformer';
import { revalidatePath } from 'next/cache';
import { validate } from 'class-validator';

export interface UpdateRevenueCostFormState {
  success: boolean | null;
  errors: {
    revenueCost: string;
    server: string;
  };
}

export const updateRevenueCost = async (
  formData: FormData
): Promise<UpdateRevenueCostFormState> => {
  const t = await getTranslations('Errors');
  const actionState: UpdateRevenueCostFormState = {
    success: null,
    errors: {
      revenueCost: '',
      server: '',
    },
  };

  const formFields = {
    revenueCost: formData.get('revenue-cost') as string,
    totalCost: formData.get('total-cost') as string,
  };
  const ppid = formData.get('ppid') as string;
  const chargeId = formData.get('charge-id') as string;

  const request: SupportToolUpdateRevenueCostRequest = plainToInstance(
    SupportToolUpdateRevenueCostRequest,
    formFields
  );
  const errors = await validate(request);

  if (errors.length > 0) {
    errors.forEach((error) => {
      const [context] = Object.values(error.contexts ?? {});

      actionState.errors.revenueCost = t(context.translationKey, {
        price: formFields.revenueCost,
      });
    });

    return actionState;
  }

  try {
    const response = await appRequestHandler(
      `${process.env.SUPPORT_TOOL_API_URL}/chargers/${ppid}/charges/${chargeId}/update-revenue-cost`,
      {
        body: JSON.stringify(request),
        method: 'POST',
        headers: { 'content-type': 'application/json' },
      },
      {
        400: async () => 'chargeAlreadyExpensedError',
        500: async () => 'commonInternalServerError',
      }
    );

    if (
      response === 'chargeAlreadyExpensedError' ||
      response === 'commonInternalServerError'
    ) {
      return {
        success: false,
        errors: {
          revenueCost: '',
          server: response,
        },
      };
    }
  } catch (error) {
    return {
      success: false,
      errors: {
        revenueCost: '',
        server: 'commonInternalServerError',
      },
    };
  }

  revalidatePath(`/chargers/${ppid}/recent-charges`);
  return { ...actionState, success: true };
};
