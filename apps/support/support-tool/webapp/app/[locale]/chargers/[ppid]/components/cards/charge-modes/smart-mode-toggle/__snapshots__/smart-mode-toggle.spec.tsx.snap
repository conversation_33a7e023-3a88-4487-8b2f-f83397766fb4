// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`SmartModeToggle should match snapshot 1`] = `
<body>
  <div>
    <div
      class="pb-2 break-words"
    >
      <div
        class="flex"
      >
        <div
          data-headlessui-state=""
        >
          <div
            class="mr-2"
          >
            <label
              class="text-lg font-normal block mb-1.5"
              for="toggle-smart-mode"
              id="toggle-smart-mode-label"
            >
              Smart mode
            </label>
          </div>
          <button
            aria-checked="true"
            aria-label="toggle-smart-mode"
            class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
            data-checked=""
            data-headlessui-state="checked"
            id="toggle-smart-mode"
            role="switch"
            tabindex="0"
            type="button"
          >
            <span
              aria-hidden="true"
              class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
            />
          </button>
          <span
            hidden=""
            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          />
        </div>
      </div>
    </div>
  </div>
</body>
`;
