import { appRequestHand<PERSON> } from '@experience/commercial/next/app-request-utils';
import { requestDiagnosticLogs } from './request-diagnostic-logs';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Request diagnostic logs action', () => {
  const prevEnv = process.env;

  beforeEach(() => {
    process.env.SUPPORT_TOOL_API_URL = 'http://localhost:7102';
  });

  afterEach(() => {
    process.env = prevEnv;
  });

  it('should request diagnostic logs', async () => {
    const ppid = 'PSL-12345';
    const startTime = '2021-01-01T01:30:30.123Z';

    await requestDiagnosticLogs(ppid, startTime);

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:7102/chargers/${ppid}/logs/diagnostic?from=2021-01-01T01:30:30.123Z&to=2021-01-02T01:30:30.123Z`,
      {
        headers: { 'content-type': 'application/json' },
        method: 'POST',
      }
    );
  });

  it('should request diagnostic logs by socket', async () => {
    const ppid = 'PSL-12345';
    const date = '2021-01-01';
    const socket = 'A';

    await requestDiagnosticLogs(ppid, date, socket);

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:7102/chargers/${ppid}/logs/diagnostic?from=2021-01-01T00:00:00.000Z&to=2021-01-02T00:00:00.000Z&socket=${socket}`,
      {
        headers: { 'content-type': 'application/json' },
        method: 'POST',
      }
    );
  });
});
