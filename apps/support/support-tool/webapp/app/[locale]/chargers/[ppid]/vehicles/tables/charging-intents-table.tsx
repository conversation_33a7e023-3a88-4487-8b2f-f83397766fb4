import { ExtendedVehicleLinkResponseDto } from '@experience/shared/axios/smart-charging-service-client';
import { Paragraph, Table } from '@experience/shared/react/design-system';
import {
  VerticalSpacer,
  VerticalSpacerSize,
} from '@experience/shared/react/layouts';
import {
  formatIntentKwhAsPercentage,
  isENodeConnected,
} from '../utils/vehicle-utils';
import { useTranslations } from 'next-intl';
import { v4 as uuid } from 'uuid';

export interface IntentsTableProps {
  vehicleLink?: ExtendedVehicleLinkResponseDto;
}

export const ChargingIntentsTable = ({ vehicleLink }: IntentsTableProps) => {
  const t = useTranslations('Chargers.Components.ChargingIntentsTable');
  const dayOfWeekTranslationMap: Record<string, string> = {
    MONDAY: t('shortFormDayOfWeek1'),
    TUESDAY: t('shortFormDayOfWeek2'),
    WEDNESDAY: t('shortFormDayOfWeek3'),
    THURSDAY: t('shortFormDayOfWeek4'),
    FRIDAY: t('shortFormDayOfWeek5'),
    SATURDAY: t('shortFormDayOfWeek6'),
    SUNDAY: t('shortFormDayOfWeek7'),
  };

  const { vehicle, intents } = vehicleLink ?? {};

  if (!intents) {
    return <Paragraph>{t('noChargingIntents')}</Paragraph>;
  }

  return (
    <>
      {isENodeConnected(vehicle) ? (
        <>
          <VerticalSpacer size={VerticalSpacerSize.Small} />,
          <Paragraph>{t('enodeConnectedWarning')} </Paragraph>
          <VerticalSpacer size={VerticalSpacerSize.Small} />,
        </>
      ) : null}
      <Table caption={t('caption')}>
        <Table.Header>
          <Table.Row>
            <Table.Heading />
            {intents.details.map((intent) => (
              <Table.Heading key={uuid()}>
                {dayOfWeekTranslationMap[intent.dayOfWeek]}
              </Table.Heading>
            ))}
          </Table.Row>
        </Table.Header>
        <Table.Body>
          <Table.Row>
            <Table.Data>{t('batteryToAdd')}</Table.Data>
            {intents.details.map((intent) => (
              <Table.Data key={uuid()}>
                {formatIntentKwhAsPercentage(intent, vehicle)}
              </Table.Data>
            ))}
          </Table.Row>
          <Table.Row>
            <Table.Data>{t('vehicleChargeBy')}</Table.Data>
            {intents.details.map((intent) => (
              <Table.Data key={uuid()}>{intent.chargeByTime}</Table.Data>
            ))}
          </Table.Row>
        </Table.Body>
      </Table>
    </>
  );
};
