import 'whatwg-fetch';
import * as MockDate from 'mockdate';
import { DelegatedControlChargingStationResponseDtoStatusEnum } from '@experience/shared/axios/smart-charging-service-client';
import {
  mockChargeSchedules,
  mockCharger,
  mockDelegatedControlStatus,
} from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../../test-utils';
import { screen } from '@testing-library/react';
import { setIn } from 'immutable';
import ChargeSchedules, { ChargeSchedulesProps } from './charge-schedules';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: (url: string) => mockRouterPush(url),
  }),
}));

dayjs.extend(utc);

const defaultProps: ChargeSchedulesProps = {
  chargeSchedules: mockChargeSchedules.data,
  delegatedControlStatus: setIn(
    mockDelegatedControlStatus,
    ['status'],
    DelegatedControlChargingStationResponseDtoStatusEnum.Inactive
  ),
  smartMode: false,
  summary: mockCharger.summary,
};

describe('ChargeSchedules', () => {
  beforeAll(() => {
    MockDate.set('2021-10-01T00:00:00Z');
  });

  afterAll(() => {
    MockDate.reset();
  });

  it('should render successfully', () => {
    const { baseElement } = renderWithProviders(
      <ChargeSchedules {...defaultProps} />
    );
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <ChargeSchedules {...defaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render a table caption', () => {
    renderWithProviders(<ChargeSchedules {...defaultProps} />);
    expect(
      screen.getByText('Table of charge schedules and modes')
    ).toBeInTheDocument();
  });

  it.each([[], undefined])(
    'should render a message when no charge schedules are present',
    async (chargeSchedules) => {
      renderWithProviders(
        <ChargeSchedules {...defaultProps} chargeSchedules={chargeSchedules} />
      );

      expect(
        screen.getByText(
          'This charger can be used at all times, there are no charge schedules.'
        )
      ).toBeInTheDocument();
    }
  );

  it.each([
    [
      'view schedule when there is an active delegated control',
      setIn(defaultProps, ['delegatedControlStatus'], {
        status: DelegatedControlChargingStationResponseDtoStatusEnum.Active,
      }),
      'View schedules',
    ],
    [
      'add schedule when there are no schedules',
      setIn(defaultProps, ['chargeSchedules'], []),
      'Add schedules',
    ],
    [
      'edit schedule when there is an existing schedule',
      defaultProps,
      'Edit schedules',
    ],
  ])('should render button to %s', (_, props, buttonText) => {
    renderWithProviders(<ChargeSchedules {...props} />);

    expect(screen.getByRole('link', { name: buttonText })).toBeInTheDocument();
  });

  it.each([
    [
      'delegated control',
      setIn(defaultProps, ['delegatedControlStatus'], {
        status: DelegatedControlChargingStationResponseDtoStatusEnum.Active,
      }),
      'This charger is currently under delegated control. The below schedules are indicative only and subject to change.',
      'This charger is currently in manual mode. The below schedules will not take effect.',
    ],
    [
      'manual mode',
      {
        chargeSchedules: mockChargeSchedules.data,
        delegatedControlStatus: {
          ...mockDelegatedControlStatus,
          status: DelegatedControlChargingStationResponseDtoStatusEnum.Inactive,
        },
        smartMode: false,
        summary: mockCharger.summary,
      },
      'This charger is currently in manual mode. The below schedules will not take effect.',
      'This charger is currently under delegated control. The below schedules are indicative only and subject to change.',
    ],
  ])(
    'should show a banner when the charger is in %s',
    (_, props, expectedBannerText, otherBannerText) => {
      renderWithProviders(<ChargeSchedules {...props} />, {});

      expect(screen.getByText(expectedBannerText)).toBeInTheDocument();
      expect(screen.queryByText(otherBannerText)).not.toBeInTheDocument();
    }
  );
});
