import 'whatwg-fetch';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { mockSummary } from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../test-utils';
import ViewPcbHistoryPage from './page';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

jest.mock('next/navigation', () => ({
  useRouter: () => jest.fn(),
  useSearchParams: () => ({ get: jest.fn() }),
}));

const defaultParams = {
  params: { ppid: 'PSL-12345' },
  searchParams: { socket: undefined },
};

describe('View pcb history page', () => {
  beforeEach(() => {
    mockRequestHandler
      .mockResolvedValueOnce(mockSummary)
      .mockResolvedValueOnce({
        data: [
          {
            id: '2a8eabaa-8a1f-4456-bb8d-1384456846a4',
            door: 'A',
            ppid: 'PSL-123456',
            serialNumber: '12345678',
            status: 'Success',
            errorCode: 'EAP0006',
            timestamp: '2021-07-01T12:00:00Z',
          },
          {
            id: '2a8eabaa-8a1f-4456-bb8d-1384456846a5',
            door: 'B',
            ppid: 'PSL-789000',
            serialNumber: '87654321',
            status: 'Success',
            errorCode: 'EAP0006',
            timestamp: '2021-07-01T12:00:00Z',
          },
        ],
      });
  });

  it('should render successfully', async () => {
    const page = await ViewPcbHistoryPage(defaultParams);
    expect(page).toBeDefined();

    const { baseElement } = renderWithProviders(page);

    expect(baseElement).toBeInTheDocument();
    expect(mockRequestHandler).toHaveBeenNthCalledWith(
      1,
      `${process.env.SUPPORT_TOOL_API_URL}/chargers/PSL-12345/summary`
    );
    expect(mockRequestHandler).toHaveBeenNthCalledWith(
      2,
      `${process.env.SUPPORT_TOOL_API_URL}/chargers/PSL-12345/pcbs/history`
    );
  });

  it('should match snapshot', async () => {
    const page = await ViewPcbHistoryPage(defaultParams);
    expect(page).toBeDefined();

    const { baseElement } = renderWithProviders(page);

    expect(baseElement).toMatchSnapshot();
  });
});
