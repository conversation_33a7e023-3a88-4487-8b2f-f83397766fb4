import 'whatwg-fetch';
import { mockSummary } from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../../test-utils';
import { screen } from '@testing-library/react';
import { setIn } from 'immutable';
import Tags, { TagsProps } from './tags';

const defaultProps: TagsProps = {
  summary: mockSummary,
};

describe('Tags', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithProviders(<Tags {...defaultProps} />);
    expect(baseElement).toBeTruthy();
  });

  it('should render a table caption', () => {
    renderWithProviders(<Tags {...defaultProps} />);
    expect(screen.getByText('Table of tags')).toBeInTheDocument();
  });

  it('should have rows for each tag', () => {
    renderWithProviders(<Tags {...defaultProps} />);

    const rows = document.querySelectorAll('tbody tr');

    expect(rows.length).toBe(defaultProps.summary.tags.length);
    expect(
      screen.getByText(defaultProps.summary.tags[0].key as string)
    ).toBeInTheDocument();
  });

  it('should not render a table if there are no tags', () => {
    renderWithProviders(<Tags summary={setIn(mockSummary, ['tags'], [])} />);

    const rows = document.querySelectorAll('tbody tr');

    expect(rows.length).toBe(0);
    expect(screen.getByText('This charger has no tags.')).toBeInTheDocument();
  });
});
