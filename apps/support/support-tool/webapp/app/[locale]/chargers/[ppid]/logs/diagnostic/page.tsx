import { ChargingStationSummary } from '@experience/shared/axios/assets-api-client';
import { Door } from '@experience/shared/axios/assets-configuration-api-client';
import { GetLogResponse } from '@experience/shared/axios/diagnostics-service-client';
import { Metadata } from 'next';
import {
  MetadataProps,
  generatePageMetadata,
} from '@experience/support/support-tool/next';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { mapSocketToEvseId } from '@experience/support/support-tool/shared';
import PageContent from './page-content';

export const generateMetadata = async (
  props: MetadataProps
): Promise<Metadata> => {
  const searchParams = await props.searchParams;
  const params = await props.params;

  return await generatePageMetadata({
    namespace: 'Chargers.DiagnosticsLogsViewerPage',
    ppid: params.ppid,
    socket: searchParams.socket,
    title: 'topPageTitle',
  });
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const ChargerLogsPage = async (props: {
  params: Promise<{ ppid: string }>;
  searchParams: Promise<Record<string, string | undefined>>;
}) => {
  const searchParams = await props.searchParams;
  const params = await props.params;
  const { socket = 'A' } = searchParams;
  const evseId = mapSocketToEvseId(socket as Door);

  const logs =
    (await appRequestHandler<GetLogResponse[]>(
      `${process.env.SUPPORT_TOOL_API_URL}/chargers/${params.ppid}/logs/diagnostic?socket=${socket}`
    )) ?? [];

  const summary = await appRequestHandler<ChargingStationSummary>(
    `${process.env.SUPPORT_TOOL_API_URL}/chargers/${params.ppid}/summary`
  );

  return (
    <PageContent
      evseId={evseId}
      logs={logs}
      socket={socket as Door}
      summary={summary}
    />
  );
};

export default ChargerLogsPage;
