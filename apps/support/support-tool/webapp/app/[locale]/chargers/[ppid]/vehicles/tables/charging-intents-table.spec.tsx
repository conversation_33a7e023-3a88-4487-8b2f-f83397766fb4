import 'whatwg-fetch';
import { ChargingIntentsTable } from './charging-intents-table';
import { mockVehicles } from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../test-utils';

describe('chargingIntentsTable', () => {
  it('should render correctly', () => {
    const { baseElement } = renderWithProviders(
      <ChargingIntentsTable vehicleLink={mockVehicles.data[0]} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot for an enode connected vehicle', () => {
    const enodeVehicle = {
      ...mockVehicles.data[0],
      vehicle: {
        ...mockVehicles.data[0].vehicle,
        enodeVehicleId: '12345',
        chargeState: {
          batteryCapacity: 78,
          batteryLevelPercent: 78,
          chargeLimitPercent: 78,
          chargeRate: 78,
          chargeTimeRemaining: 78,
          isCharging: true,
          isFullyCharged: false,
          isPluggedIn: true,
          lastUpdated: '2023-10-01T12:00:00Z',
        },
      },
    };

    const { baseElement } = renderWithProviders(
      <ChargingIntentsTable vehicleLink={enodeVehicle} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot for a non-enode connected vehicle', () => {
    const nonEnodeVehicle = {
      ...mockVehicles.data[0],
      vehicle: {
        ...mockVehicles.data[0].vehicle,
        enodeVehicleId: undefined,
        chargeState: {
          batteryCapacity: 78,
        },
      },
    };
    const { baseElement } = renderWithProviders(
      <ChargingIntentsTable vehicleLink={nonEnodeVehicle} />
    );
    expect(baseElement).toMatchSnapshot();
  });
});
