import { Card, Paragraph } from '@experience/shared/react/design-system';
import { ChargingStationSummary } from '@experience/shared/axios/assets-api-client';
import { KeyValueTable } from '@experience/shared/react/layouts';
import { useTranslations } from 'next-intl';

export interface TagsProps {
  summary: ChargingStationSummary;
}

export const Tags = ({ summary }: TagsProps) => {
  const t = useTranslations('Chargers.Components.Tags');

  return (
    <Card>
      <Card.Header>{t('header')}</Card.Header>
      {summary.tags.length > 0 ? (
        <KeyValueTable
          caption={t('tableCaption')}
          valueClassName={'text-left'}
          rows={summary.tags.map((value) => ({
            key: value.key ?? '-',
            value: value.value ?? '-',
          }))}
        />
      ) : (
        <Paragraph>{t('noTags')}</Paragraph>
      )}
    </Card>
  );
};

export default Tags;
