import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { retrieveChargeRewardData } from './retrieve-charge-reward-data';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('retrieve charge reward data action', () => {
  const prevEnv = process.env;

  beforeEach(() => {
    process.env.SUPPORT_TOOL_API_URL = 'http://localhost:7102';
  });

  afterEach(() => {
    process.env = prevEnv;
  });

  it('should retrieve charge reward data action', async () => {
    const ppid = 'PSL-12345';
    const chargeStartTime = '2024-01-01T23:00:00';
    const chargeEndTime = '2024-01-01T23:00:00';
    const kWhFromGrid = 30.5;
    const kWhFromGeneration = 10.5;

    await retrieveChargeRewardData(
      ppid,
      chargeStartTime,
      chargeEndTime,
      kWhFromGrid,
      kWhFromGeneration
    );

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:7102/chargers/${ppid}/charges/rewards?chargeStartTime=${chargeStartTime}&chargeEndTime=${chargeEndTime}&kWhFromGrid=${kWhFromGrid}&kWhFromGeneration=${kWhFromGeneration}`,
      {
        headers: { 'content-type': 'application/json' },
        method: 'GET',
      }
    );
  });
});
