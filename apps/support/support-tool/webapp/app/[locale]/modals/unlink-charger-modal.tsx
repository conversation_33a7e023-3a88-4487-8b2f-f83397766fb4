import { Modal, Paragraph } from '@experience/shared/react/design-system';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { sendUnlinkChargerRequest } from '../accounts/actions/send-unlink-charger-request';
import { useState } from 'react';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

export interface UnlinkChargerModalProps {
  authId: string;
  chargerPage?: boolean;
  open: boolean;
  ppid: string;
  setOpen: (value: boolean) => void;
}

export const UnlinkChargerModal = ({
  authId,
  chargerPage,
  open,
  ppid,
  setOpen,
}: UnlinkChargerModalProps) => {
  const t = useTranslations('Shared.UnlinkChargerModal');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleUnlinkCharger = async () => {
    setIsSubmitting(true);
    try {
      await sendUnlinkChargerRequest(authId, ppid);
      toast.success(t('successToast'));
    } catch (error) {
      toast.error(t('errorToast'));
    } finally {
      setOpen(false);
      setIsSubmitting(false);
    }
  };

  const confirmMessage = chargerPage
    ? t('chargerPageConfirmMessage')
    : t('confirmMessage');

  return (
    <Modal
      title={t('title')}
      open={open}
      setOpen={setOpen}
      content={
        <>
          <Paragraph>{confirmMessage}</Paragraph>
          <VerticalSpacer />
          <Paragraph>{t('confirmMessageParagraph')}</Paragraph>
        </>
      }
      isSubmitting={isSubmitting}
      confirmButtonText={t('confirmButtonText')}
      handleConfirm={() => handleUnlinkCharger()}
      cancelButtonText={t('cancelButtonText')}
      handleCancel={() => setOpen(false)}
    />
  );
};
