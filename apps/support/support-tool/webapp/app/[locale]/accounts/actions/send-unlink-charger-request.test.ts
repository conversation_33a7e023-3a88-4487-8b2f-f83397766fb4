import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { revalidatePath } from 'next/cache';
import { sendUnlinkChargerRequest } from './send-unlink-charger-request';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

jest.mock('next/cache');
const mockRevalidatePath = jest.mocked(revalidatePath);

describe('unlinkCharger', () => {
  beforeEach(() => {
    process.env.SUPPORT_TOOL_API_URL = 'http://localhost:7102';
  });

  it('should submit request to unlink charger', async () => {
    const userId = 'f11eb98f-1ea2-41cf-a146-363259087d7d';
    const ppid = 'PSL-12345';
    mockRequestHandler.mockResolvedValueOnce({ status: 204 });

    await sendUnlinkChargerRequest(userId, ppid);

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:7102/accounts/${userId}/chargers/${ppid}`,
      {
        headers: { 'content-type': 'application/json' },
        method: 'DELETE',
      }
    );

    expect(mockRevalidatePath).toHaveBeenCalledWith(
      `/accounts/${userId}/details`
    );
  });
});
