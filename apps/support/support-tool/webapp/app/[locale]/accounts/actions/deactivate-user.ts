'use server';

import { appRequestHand<PERSON> } from '@experience/commercial/next/app-request-utils';
import { setTimeout as delay } from 'timers/promises';
import { revalidatePath } from 'next/cache';

export const deactivateUser = async (
  authId: string,
  force = false
): Promise<void> => {
  const url = new URL(`${process.env.SUPPORT_TOOL_API_URL}/accounts/${authId}`);

  if (force) {
    url.searchParams.append('force', 'true');
  }

  await appRequestHandler(url.toString(), {
    headers: { 'content-type': 'application/json' },
    method: 'DELETE',
  });

  await delay(2000); // Allow sync from podadmin to GIP

  revalidatePath(`/accounts/${authId}/details`);
};
