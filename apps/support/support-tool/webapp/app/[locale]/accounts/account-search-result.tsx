import {
  Button,
  ButtonTypes,
  Card,
  Heading,
  Paragraph,
} from '@experience/shared/react/design-system';
import { SearchResult } from './actions/get-accounts';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import AvatarPlaceholder from '../components/avatar-placeholder/avatar-placeholder';

interface AccountSearchResultProps {
  account: SearchResult;
}

export const AccountSearchResult = ({ account }: AccountSearchResultProps) => {
  const router = useRouter();
  const t = useTranslations('Accounts.SearchPage');

  const { authId, email, firstName, lastName } = account;

  const fullName = `${firstName} ${lastName}`;

  const goToUserDetails = () => {
    router.push(`/accounts/${authId}/details`);
  };

  return (
    <Card className="mt-4">
      <div className="flex flex-row">
        <AvatarPlaceholder name={fullName} />

        <div className="flex-1 flex flex-col justify-center">
          <Heading.H3>{fullName}</Heading.H3>
          <Paragraph>{email}</Paragraph>
        </div>

        <Button
          className="self-center"
          buttonType={ButtonTypes.PRIMARY_OUTLINE}
          onClick={goToUserDetails}
        >
          {t('searchResultsLink')}
        </Button>
      </div>
    </Card>
  );
};
