import {
  SendUpdateEmailRequestModal,
  SendUpdateEmailRequestModalProps,
} from './send-update-email-request-modal';
import { Toaster } from 'react-hot-toast';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { renderWithProviders } from '../../../../test-utils';
import { sendUpdateEmailRequest } from '../../../actions/send-update-email-request';

jest.mock('../../../actions/send-update-email-request');
const mockSendUpdateEmailRequest = jest.mocked(sendUpdateEmailRequest);

const mockSetOpen = jest.fn();

const defaultProps: SendUpdateEmailRequestModalProps = {
  open: true,
  setOpen: mockSetOpen,
  authId: 'abc123',
  email: '<EMAIL>',
};

describe('SendUpdateEmailRequestModal', () => {
  beforeEach(() => {
    renderWithProviders(
      <>
        <Toaster />
        <SendUpdateEmailRequestModal {...defaultProps} />
      </>
    );
  });

  it('should be able to open the modal', () => {
    expect(screen.getByTestId('new-email-input')).toBeVisible();
    expect(screen.getByRole('button', { name: 'Cancel' })).toBeVisible();
    expect(screen.getByRole('button', { name: 'Save' })).toBeVisible();
  });

  it('should be able to close the modal with cancel', () => {
    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
  });

  it('should be able to the submit a send update email request', async () => {
    mockSendUpdateEmailRequest.mockResolvedValueOnce();

    fireEvent.input(screen.getByTestId('new-email-input'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Save' }));

    await waitFor(() => {
      expect(mockSendUpdateEmailRequest).toHaveBeenCalledWith('abc123', {
        newEmail: '<EMAIL>',
      });
      expect(
        screen.getByText('Update email address request sent')
      ).toBeInTheDocument();
      expect(mockSetOpen).toHaveBeenCalledWith(false);
    });
  });

  it('should handle a duplicate email error response from API', async () => {
    mockSendUpdateEmailRequest.mockResolvedValueOnce('DUPLICATE_EMAIL_ERROR');

    fireEvent.input(screen.getByTestId('new-email-input'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Save' }));

    await waitFor(() => {
      expect(
        screen.getByText('An account with this email address already exists')
      ).toBeInTheDocument();
      expect(mockSetOpen).toHaveBeenCalledWith(false);
    });
  });

  it('should handle an error response from API', async () => {
    mockSendUpdateEmailRequest.mockRejectedValueOnce({ status: 500 });

    fireEvent.input(screen.getByTestId('new-email-input'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Save' }));

    await waitFor(() => {
      expect(
        screen.getByText('Failed to send update email request')
      ).toBeInTheDocument();
      expect(mockSetOpen).toHaveBeenCalledWith(false);
    });
  });

  it('should not be able to save with empty new email field', () => {
    fireEvent.input(screen.getByTestId('new-email-input'), {
      target: { value: '' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Save' }));

    expect(mockSendUpdateEmailRequest).not.toHaveBeenCalled();
  });

  it('should not be able to save if there is no change', () => {
    fireEvent.input(screen.getByTestId('new-email-input'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Save' }));

    expect(mockSendUpdateEmailRequest).not.toHaveBeenCalled();
  });
});
