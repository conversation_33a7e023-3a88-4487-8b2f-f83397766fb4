import {
  AccountCharger,
  getAccountChargers,
} from '../../../actions/get-details';
import {
  ActionItem,
  Anchor,
  Spinner,
  Table,
  UnlinkWithChargerIcon,
} from '@experience/shared/react/design-system';
import { UnlinkChargerModal } from '../../../../modals/unlink-charger-modal';
import { useCallback, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

interface AccountChargersProps {
  authId: string;
}

export const AccountChargers = ({ authId }: AccountChargersProps) => {
  const t = useTranslations('Accounts.DetailsPage');

  const [loading, setLoading] = useState(true);
  const [ppid, setPpid] = useState('');
  const [chargers, setChargers] = useState<AccountCharger[]>([]);
  const [openUnlinkModal, setOpenUnlinkModal] = useState(false);

  const loadData = useCallback(async () => {
    setLoading(true);

    try {
      const chargers = await getAccountChargers(authId);
      setChargers(chargers);
    } finally {
      setLoading(false);
    }
  }, [authId]);

  const handleUnlinkCharger = async (ppid: string): Promise<void> => {
    setPpid(ppid);
    setOpenUnlinkModal(true);
  };

  useEffect(() => {
    loadData();
  }, [loadData]);

  return (
    <>
      {loading ? (
        <Spinner />
      ) : (
        <>
          <Table caption={t('chargersTableTitle')}>
            <Table.Header>
              <Table.Row>
                <Table.Heading>{t('chargersTablePpidHeading')}</Table.Heading>
                <Table.Heading>
                  {t('chargersTableActionsHeading')}
                </Table.Heading>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {chargers.map((charger) => (
                <Table.Row key={charger.ppid}>
                  <Table.Data>
                    <Anchor href={`/chargers/${charger.ppid}`}>
                      {charger.ppid}
                    </Anchor>
                  </Table.Data>
                  <Table.Data>
                    <ActionItem
                      text={t('unlinkChargerButton')}
                      aria-label={`${t('unlinkChargerButton')} ${charger.ppid}`}
                      id={`unlink-charger-${charger.ppid}`}
                      name={`unlink-charger-${charger.ppid}`}
                      onClick={() => handleUnlinkCharger(charger.ppid)}
                      icon={<UnlinkWithChargerIcon.LIGHT aria-hidden />}
                    />
                  </Table.Data>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
          <UnlinkChargerModal
            authId={authId}
            ppid={ppid}
            open={openUnlinkModal}
            setOpen={setOpenUnlinkModal}
          />
        </>
      )}
    </>
  );
};

export default AccountChargers;
