import { Spinner, Table } from '@experience/shared/react/design-system';
import { useCallback, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

interface AccountPaymentsProps {
  authId: string;
}

export const AccountPayments = ({ authId }: AccountPaymentsProps) => {
  const t = useTranslations('Accounts.DetailsPage');
  const [loading, setLoading] = useState(false);

  const loadData = useCallback(async () => {
    setLoading(true);

    try {
      // Do loading of payments
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return (
    <>
      {loading ? (
        <Spinner />
      ) : (
        <Table caption={t('accountPaymentsTableTitle', { authId })} />
      )}
    </>
  );
};

export default AccountPayments;
