import { Modal } from '@experience/shared/react/design-system';
import { resendEmailVerification } from '../../../actions/resend-email-verification';
import { useState } from 'react';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

export interface ResendEmailVerificationModalProps {
  authId: string;
  email: string;
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const ResendEmailVerificationModal = ({
  authId,
  email,
  open,
  setOpen,
}: ResendEmailVerificationModalProps) => {
  const t = useTranslations('Accounts.DetailsPage');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleResendEmailVerification = async () => {
    setIsSubmitting(true);

    try {
      await resendEmailVerification(authId);

      toast.success(t('resendVerificationEmailSuccessMessage'));
    } catch (error) {
      toast.error(t('resendVerificationEmailErrorMessage'));
    } finally {
      setOpen(false);

      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      title={t('resendVerificationEmailModalTitle')}
      open={open}
      setOpen={setOpen}
      content={
        <>
          <div className="grid grid-rows-1">
            <h4>A verification email will be sent to {email}</h4>
          </div>
        </>
      }
      isSubmitting={isSubmitting}
      confirmButtonText={t('resendVerificationEmailModalConfirmButton')}
      handleConfirm={() => handleResendEmailVerification()}
      cancelButtonText={t('cancelButton')}
      handleCancel={() => setOpen(false)}
    />
  );
};
