import { startOpenTelemetryWithXRayPropagator } from '@experience/shared/nest/utils';
startOpenTelemetryWithXRayPropagator('support-tool-api');
import { AppModule } from './app/app.module';
import { bootstrapCommandRunner } from '@experience/shared/nest/utils';
import axios from 'axios';

axios.defaults.headers.common['User-Agent'] = 'experience/support-tool-api';

void bootstrapCommandRunner(AppModule);
