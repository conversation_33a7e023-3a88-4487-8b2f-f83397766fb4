import { AMAZON_OIDC_DATA_HEADER } from '@experience/shared/typescript/oidc-utils';
import {
  BREAKER_SIZE_MIN_VALUE_ERROR,
  CT_CLAMP_THRESHOLD_MAX_VALUE_ERROR,
  CT_CLAMP_THRESHOLD_MIN_VALUE_ERROR,
  MAX_CURRENT_RATING_MIN_VALUE_ERROR,
  PEN_FAULT_MODE_VALUE_ERROR,
  POWER_BALANCING_MAX_VALUE_ERROR,
  POWER_BALANCING_MIN_VALUE_ERROR,
  SOLAR_EXPORT_MARGIN_MAX_VALUE_ERROR,
  SOLAR_EXPORT_MARGIN_MIN_VALUE_ERROR,
  SOLAR_MAX_GRID_IMPORT_VALUE_ERROR,
  SOLAR_MIN_GRID_IMPORT_VALUE_ERROR,
  SOLAR_START_HYSTERESIS_MIN_VALUE_ERROR,
  SOLAR_STOP_HYSTERESIS_MIN_VALUE_ERROR,
} from '@experience/support/support-tool/shared';
import { ChargerConfigController } from './charger-config.controller';
import { ChargerConfigService } from './charger-config.service';
import { INestApplication } from '@nestjs/common';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import { TEST_UPDATE_CONFIG_REQUEST } from '@experience/support/support-tool/shared/specs';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

jest.mock('./charger-config.service');

describe('ChargerConfigController', () => {
  let app: INestApplication;
  let controller: ChargerConfigController;
  let service: ChargerConfigService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerConfigController],
      providers: [ChargerConfigService],
    }).compile();

    controller = module.get<ChargerConfigController>(ChargerConfigController);
    service = module.get<ChargerConfigService>(ChargerConfigService);

    app = module.createNestApplication();
    await app.init();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should update by ppid', async () => {
    const mockSetConfiguration = jest
      .spyOn(service, 'setConfiguration')
      .mockResolvedValueOnce();

    await request(app.getHttpServer())
      .put(`/chargers/PSL-12345/configuration`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send(TEST_UPDATE_CONFIG_REQUEST)
      .expect(204);

    expect(mockSetConfiguration).toHaveBeenCalledWith(
      'PSL-12345',
      'A',
      TEST_UPDATE_CONFIG_REQUEST,
      TEST_OIDC_USER
    );
  });

  it('should update by ppid and socket', async () => {
    const mockSetConfiguration = jest
      .spyOn(service, 'setConfiguration')
      .mockResolvedValueOnce();

    await request(app.getHttpServer())
      .put(`/chargers/PSL-12345/configuration?socket=B`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send(TEST_UPDATE_CONFIG_REQUEST)
      .expect(204);

    expect(mockSetConfiguration).toHaveBeenCalledWith(
      'PSL-12345',
      'B',
      TEST_UPDATE_CONFIG_REQUEST,
      TEST_OIDC_USER
    );
  });

  it('should update individual charger configuration fields', async () => {
    const mockService = jest
      .spyOn(service, 'setConfiguration')
      .mockResolvedValueOnce();

    await request(app.getHttpServer())
      .put(`/chargers/PSL-12345/configuration`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send({ chargeCurrentLimit: '32' })
      .expect(204);

    expect(mockService).toHaveBeenCalledWith(
      'PSL-12345',
      'A',
      { chargeCurrentLimit: '32' },
      TEST_OIDC_USER
    );
  });

  it.each([
    [
      'minimum charge current limit',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        chargeCurrentLimit: '-1',
      },
      MAX_CURRENT_RATING_MIN_VALUE_ERROR,
    ],
    [
      'minimum power balancing clamp rating',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        powerBalancingCurrentLimit: '-1',
      },
      POWER_BALANCING_MIN_VALUE_ERROR,
    ],
    [
      'maximum power balancing clamp rating',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        powerBalancingCurrentLimit: '101',
      },
      POWER_BALANCING_MAX_VALUE_ERROR,
    ],
    [
      'minimum CT clamp fault threshold',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        ppDevClampFaultThreshold: '0',
      },
      CT_CLAMP_THRESHOLD_MIN_VALUE_ERROR,
    ],
    [
      'maximum CT clamp fault threshold',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        ppDevClampFaultThreshold: '6',
      },
      CT_CLAMP_THRESHOLD_MAX_VALUE_ERROR,
    ],
    [
      'minimum breaker size',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        rcdBreakerSize: '-1',
      },
      BREAKER_SIZE_MIN_VALUE_ERROR,
    ],
    [
      'minimum grid import',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        solarMaxGridImport: '-0.1',
      },
      SOLAR_MIN_GRID_IMPORT_VALUE_ERROR,
    ],
    [
      'maximum grid import',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        solarMaxGridImport: '6.1',
      },
      SOLAR_MAX_GRID_IMPORT_VALUE_ERROR,
    ],
    [
      'minimum solar start hysteresis',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        solarStartHysteresis: '-1',
      },
      SOLAR_START_HYSTERESIS_MIN_VALUE_ERROR,
    ],
    [
      'minimum solar stop hysteresis',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        solarStopHysteresis: '-1',
      },
      SOLAR_STOP_HYSTERESIS_MIN_VALUE_ERROR,
    ],
    [
      'minimum solar export margin',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        solarExportMargin: '-0.1',
      },
      SOLAR_EXPORT_MARGIN_MIN_VALUE_ERROR,
    ],
    [
      'maximum solar export margin',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        solarExportMargin: '6.1',
      },
      SOLAR_EXPORT_MARGIN_MAX_VALUE_ERROR,
    ],
    [
      'valid pen fault modes',
      {
        ...TEST_UPDATE_CONFIG_REQUEST,
        penFaultMode: 'On',
      },
      PEN_FAULT_MODE_VALUE_ERROR,
    ],
  ])(
    'should return a 400 bad request with the correct error message if the request fails validation due to %s',
    async (_, badRequest, error) => {
      await request(app.getHttpServer())
        .put(`/chargers/PSL-12345/configuration`)
        .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
        .send(badRequest)
        .expect(400)
        .expect({
          statusCode: 400,
          message: [error],
          error: 'Bad Request',
        });
    }
  );

  it('should return a 500 internal error if an error is thrown when updating the charger configuration', async () => {
    jest
      .spyOn(service, 'setConfiguration')
      .mockRejectedValueOnce(new Error('Test error'));

    await request(app.getHttpServer())
      .put(`/chargers/PSL-12345/configuration`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send(TEST_UPDATE_CONFIG_REQUEST)
      .expect(500)
      .expect({
        statusCode: 500,
        message: 'Internal server error',
      });
  });

  it('should refresh charger configuration', async () => {
    const mockRefreshConfiguration = jest
      .spyOn(service, 'refreshConfiguration')
      .mockResolvedValueOnce();

    await request(app.getHttpServer())
      .post(`/chargers/PSL-12345/configuration/refresh?socket=B`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(202);

    expect(mockRefreshConfiguration).toHaveBeenCalledWith(
      'PSL-12345',
      'B',
      TEST_OIDC_USER
    );
  });

  it('should return a 500 internal error if an error is thrown when refreshing the charger configuration', async () => {
    jest
      .spyOn(service, 'refreshConfiguration')
      .mockRejectedValueOnce(new Error('Test error'));

    await request(app.getHttpServer())
      .post(`/chargers/PSL-12345/configuration/refresh`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(500)
      .expect({
        statusCode: 500,
        message: 'Internal server error',
      });
  });
});
