import { Body, Controller, HttpCode, Param, Post } from '@nestjs/common';
import { ChargerCommandsService } from './charger-commands.service';
import {
  ResetChargerRequest,
  SetChargerAvailabilityRequest,
  UnlockConnectorRequest,
} from '@experience/support/support-tool/shared';
import { OidcUser } from '@experience/shared/nest/utils';

@Controller('chargers/:ppid/commands')
export class ChargerCommandsController {
  constructor(
    private readonly chargerCommandsService: ChargerCommandsService
  ) {}

  @Post('/reset')
  @HttpCode(200)
  async sendResetCommand(
    @Param('ppid') ppid: string,
    @Body() request: ResetChargerRequest,
    @OidcUser() user: OidcUser
  ): Promise<void> {
    return this.chargerCommandsService.sendResetCommand(ppid, request, user);
  }

  @Post('/availability')
  @HttpCode(200)
  async sendAvailabilityCommand(
    @Param('ppid') ppid: string,
    @Body() request: SetChargerAvailabilityRequest,
    @OidcUser() user: OidcUser
  ): Promise<void> {
    return this.chargerCommandsService.sendAvailabilityCommand(
      ppid,
      request,
      user
    );
  }

  @Post('/unlock-connector')
  @HttpCode(200)
  async sendUnlockConnectorCommand(
    @Param('ppid') ppid: string,
    @Body() request: UnlockConnectorRequest,
    @OidcUser() user: OidcUser
  ): Promise<void> {
    return this.chargerCommandsService.sendUnlockConnectorCommand(
      ppid,
      request,
      user
    );
  }
}
