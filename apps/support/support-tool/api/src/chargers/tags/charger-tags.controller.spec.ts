import {
  AMAZON_OIDC_DATA_HEADER,
  Oidc<PERSON><PERSON><PERSON>,
  getOidcUser,
} from '@experience/shared/typescript/oidc-utils';
import { APP_GUARD } from '@nestjs/core';
import { ChargerTagsController } from './charger-tags.controller';
import { ChargerTagsService } from './charger-tags.service';
import { INestApplication } from '@nestjs/common';
import { RolesGuard, useGlobalPipes } from '@experience/shared/nest/utils';
import {
  TAG_INVALID_KEY_ERROR_KEY,
  TAG_INVALID_VALUE_ERROR_KEY,
  TAG_VALUE_LOWERCASE_ERROR_KEY,
  TAG_VALUE_WHITESPACE_ERROR_KEY,
} from '@experience/support/support-tool/shared';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import {
  TEST_UPDATE_TAGS_REQUEST,
  mockSummary,
} from '@experience/support/support-tool/shared/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { setIn } from 'immutable';
import request from 'supertest';

jest.mock('./charger-tags.service');

const TEST_PPID = mockSummary.ppid;

jest.mock('@experience/shared/typescript/oidc-utils');
const mockGetOidcUser = jest.mocked(getOidcUser);

const TEST_OIDC_USER_WITH_ROLE = {
  ...TEST_OIDC_USER,
  roles: [OidcRoles.CHARGER_EDIT_OPERATOR],
};

describe('charger tag controller', () => {
  let app: INestApplication;
  let controller: ChargerTagsController;
  let service: ChargerTagsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerTagsController],
      providers: [
        { provide: APP_GUARD, useClass: RolesGuard },
        ChargerTagsService,
      ],
    }).compile();

    controller = module.get<ChargerTagsController>(ChargerTagsController);
    service = module.get<ChargerTagsService>(ChargerTagsService);

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  beforeEach(() => {
    mockGetOidcUser.mockReturnValue(TEST_OIDC_USER_WITH_ROLE);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should update charger tag', async () => {
    const mockUpdateChargerTag = jest
      .spyOn(service, 'updateChargerTags')
      .mockResolvedValueOnce();

    await request(app.getHttpServer())
      .put(`/chargers/${TEST_PPID}/tags`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send(TEST_UPDATE_TAGS_REQUEST)
      .expect(200);

    expect(mockUpdateChargerTag).toHaveBeenCalledWith(
      TEST_PPID,
      TEST_UPDATE_TAGS_REQUEST,
      TEST_OIDC_USER_WITH_ROLE
    );
  });

  it('should throw a 403 forbidden error if the user does not have the correct role', async () => {
    mockGetOidcUser.mockReturnValueOnce(TEST_OIDC_USER);

    await request(app.getHttpServer())
      .put(`/chargers/${TEST_PPID}/tags`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send(TEST_UPDATE_TAGS_REQUEST)
      .expect(403)
      .expect({
        error: 'Forbidden',
        message: 'Forbidden resource',
        statusCode: 403,
      });
  });

  it.each([
    ['has whitespace', 'test operator', TAG_VALUE_WHITESPACE_ERROR_KEY],
    ['is not lowercase', 'testOperator', TAG_VALUE_LOWERCASE_ERROR_KEY],
    [
      'is not a valid for the key',
      'test-operator',
      TAG_INVALID_VALUE_ERROR_KEY,
    ],
  ])(
    'should throw a validation error if the tag value %s',
    async (_, value, error) => {
      await request(app.getHttpServer())
        .put(`/chargers/${TEST_PPID}/tags`)
        .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
        .send(setIn(TEST_UPDATE_TAGS_REQUEST, ['tags', 0, 'value'], value))
        .expect(400)
        .expect({
          statusCode: 400,
          message: [`tags.0.${error}`],
          error: 'Bad Request',
        });
    }
  );

  it('should throw a validation error if the tag key is invalid', async () => {
    await request(app.getHttpServer())
      .put(`/chargers/${TEST_PPID}/tags`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send(setIn(TEST_UPDATE_TAGS_REQUEST, ['tags', 0, 'key'], 'test-key'))
      .expect(400)
      .expect({
        statusCode: 400,
        message: [
          `tags.0.${TAG_INVALID_KEY_ERROR_KEY}`,
          `tags.0.${TAG_INVALID_VALUE_ERROR_KEY}`,
        ],
        error: 'Bad Request',
      });
  });

  it('should return a 500 internal error if an error is thrown when updating the tag', async () => {
    jest
      .spyOn(service, 'updateChargerTags')
      .mockRejectedValueOnce(new Error('Oops'));

    await request(app.getHttpServer())
      .put(`/chargers/${TEST_PPID}/tags`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .send(TEST_UPDATE_TAGS_REQUEST)
      .expect(500)
      .expect({
        statusCode: 500,
        message: 'Internal server error',
      });
  });
});
