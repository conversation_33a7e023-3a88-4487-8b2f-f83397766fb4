import { AccountsModule } from '../accounts/accounts.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';
import {
  SubscriptionsApi,
  Configuration as SubscriptionsApiConfiguration,
} from '@experience/mobile/subscriptions-api/axios';
import { SubscriptionsController } from './subscriptions.controller';
import { SubscriptionsService } from './subscriptions.service';
import { RewardsActivityLogModule } from '@experience/mobile/nest/rewards-activity-log';

@Module({
  imports: [AccountsModule, ConfigModule, RewardsActivityLogModule],
  controllers: [SubscriptionsController],
  providers: [
    SubscriptionsService,
    {
      inject: [ConfigService],
      provide: SubscriptionsApi,
      useFactory: async (configService: ConfigService) =>
        new SubscriptionsApi(
          new SubscriptionsApiConfiguration({
            basePath: configService.get('SUBSCRIPTIONS_API_BASE_URL'),
          })
        ),
    },
  ],
})
export class SubscriptionsModule {}
