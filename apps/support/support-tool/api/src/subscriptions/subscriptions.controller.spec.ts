import 'whatwg-fetch';
import { AMAZON_OIDC_DATA_HEADER } from '@experience/shared/typescript/oidc-utils';
import { INestApplication } from '@nestjs/common';
import { PersistedSubscriptionDTO } from '@experience/mobile/subscriptions-api/axios';
import { SubscriptionsController } from './subscriptions.controller';
import { SubscriptionsService } from './subscriptions.service';
import {
  TEST_ACCOUNT,
  TEST_SUBSCRIPTION,
  mockSubscription,
  TEST_REWARDS_TRANSACTIONS,
} from '@experience/support/support-tool/shared/specs';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { convertAllDatesToISOString } from '@experience/shared/typescript/utils';
import request from 'supertest';

jest.mock('./subscriptions.service');

describe('subscriptions controller', () => {
  let app: INestApplication;
  let subscriptionsController: SubscriptionsController;
  let subscriptionsService: SubscriptionsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      controllers: [SubscriptionsController],
      providers: [SubscriptionsService],
    }).compile();

    app = module.createNestApplication();
    subscriptionsController = module.get(SubscriptionsController);
    subscriptionsService = module.get(SubscriptionsService);

    await app.init();
  });

  it('should be defined', () => {
    expect(subscriptionsController).toBeDefined();
    expect(subscriptionsService).toBeDefined();
  });

  it('should find subscriptions', async () => {
    const mockFindSubscriptionsByUserEmail = jest
      .spyOn(subscriptionsService, 'find')
      .mockResolvedValue([TEST_SUBSCRIPTION]);

    await request(app.getHttpServer())
      .get(`/subscriptions?query=${TEST_ACCOUNT.email}`)
      .expect(200)
      .expect([TEST_SUBSCRIPTION]);

    expect(mockFindSubscriptionsByUserEmail).toHaveBeenCalledWith(
      TEST_ACCOUNT.email
    );
  });

  it('should find a subscription by subscription id', async () => {
    const mockFindSubscriptionById = jest
      .spyOn(subscriptionsService, 'findById')
      .mockResolvedValue(mockSubscription);

    await request(app.getHttpServer())
      .get(`/subscriptions/${mockSubscription.id}`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect(
        convertAllDatesToISOString(mockSubscription) as PersistedSubscriptionDTO
      );

    expect(mockFindSubscriptionById).toHaveBeenCalledWith(
      mockSubscription.id,
      TEST_OIDC_USER
    );
  });

  it("should get a subscription's rewards wallet summary", async () => {
    const mockGetSubscriptionRewardsWalletSummary = jest
      .spyOn(subscriptionsService, 'getSubscriptionRewardsWalletSummary')
      .mockResolvedValue({
        milesAllowance: 5000,
        milesRemaining: 2500,
        milesRenewalDate: '2025-09-09T15:25:00.000Z',
      });

    await request(app.getHttpServer())
      .get(`/subscriptions/${mockSubscription.id}/rewards/wallet`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200, {
        wallet: {
          milesAllowance: 5000,
          milesRemaining: 2500,
          milesRenewalDate: '2025-09-09T15:25:00.000Z',
        },
      });

    expect(mockGetSubscriptionRewardsWalletSummary).toHaveBeenCalledTimes(1);
    expect(mockGetSubscriptionRewardsWalletSummary).toHaveBeenCalledWith(
      mockSubscription.id,
      TEST_OIDC_USER
    );
  });

  it("should get a subscription's rewards activity log", async () => {
    const mockGetSubscriptionRewardsActivityLog = jest
      .spyOn(subscriptionsService, 'getSubscriptionRewardsActivityLog')
      .mockResolvedValue(TEST_REWARDS_TRANSACTIONS);

    await request(app.getHttpServer())
      .get(`/subscriptions/${mockSubscription.id}/rewards/activity`)
      .query({
        count: 10,
        lastKey: 'd96476e7-d10a-43cc-9f34-4881489d90cf',
      })
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200, TEST_REWARDS_TRANSACTIONS);

    expect(mockGetSubscriptionRewardsActivityLog).toHaveBeenCalledTimes(1);
    expect(mockGetSubscriptionRewardsActivityLog).toHaveBeenCalledWith(
      mockSubscription.id,
      TEST_OIDC_USER,
      '10',
      'd96476e7-d10a-43cc-9f34-4881489d90cf'
    );
  });
});
