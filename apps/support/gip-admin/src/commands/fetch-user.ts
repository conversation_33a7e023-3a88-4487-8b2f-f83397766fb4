import { Command } from 'commander';
import { fetchUser, fetchUserDetails } from '../helpers/user-helpers';

export const addfetchUserCommand = (program: Command) => {
  const fetch = program.command('fetch');

  fetch
    .description('Fetch details for specified user')
    .argument('<userId>', 'Identifier for user')
    .action(async (userId: string) => {
      const uid = await fetchUser(userId);
      fetchUserDetails(uid);
    });
};
