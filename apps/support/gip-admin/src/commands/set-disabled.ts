import { Command } from 'commander';
import { fetchUser, fetchUserDetails } from '../helpers/user-helpers';
import { getAuth } from '@experience/shared/firebase/admin';

export const addSetDisabled = (program: Command) => {
  const setDisabled = program.command('setDisabled');

  setDisabled
    .description('Enable/Disable a specified user')
    .argument('<userId>', 'Identifier for user')
    .argument(
      '<boolean>',
      'Value indicating whether user account is disabled',
      (value) => value.toLowerCase() === 'true'
    )
    .action(async (userId: string, value: boolean) => {
      const uid = await fetchUser(userId);
      await getAuth().updateUser(uid, { disabled: value });
      fetchUserDetails(uid);
    });
};
