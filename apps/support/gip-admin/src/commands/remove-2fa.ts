import { Command } from 'commander';
import { Logger } from '@nestjs/common';
import { getAuth } from '@experience/shared/firebase/admin';

const logger = new Logger();

export const addRemove2fa = (program: Command) => {
  const fetch = program.command('remove-2fa');

  fetch
    .description('Remove 2FA from a given user')
    .argument('<userId>', 'Identifier for user')
    .action(async (userId: string) => {
      await getAuth().updateUser(userId, {
        multiFactor: {
          enrolledFactors: [],
        },
      });

      logger.log(`${userId} has had all 2FA removed`);
    });
};
