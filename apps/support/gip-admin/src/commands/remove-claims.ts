import { Command } from 'commander';
import { Logger } from '@nestjs/common';
import { fetchUser, fetchUserDetails } from '../helpers/user-helpers';
import { getAuth } from '@experience/shared/firebase/admin';

const logger = new Logger();

export const addRemoveClaims = (program: Command) => {
  const removeClaims = program.command('removeClaims');

  removeClaims
    .description('Remove all claims for a specified user')
    .argument('<userId>', 'Identifier for user')
    .action(async (userId: string) => {
      logger.log(`Will remove claims`);
      const uid = await fetchUser(userId);
      await getAuth().setCustomUserClaims(uid, null);
      logger.log(`Successfully removed claims`);
      fetchUserDetails(uid);
    });
};
