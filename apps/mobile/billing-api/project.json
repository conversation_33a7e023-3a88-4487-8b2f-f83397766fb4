{"name": "billing-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile/billing-api/src", "projectType": "application", "tags": ["mobile", "package"], "implicitDependencies": ["billing-api-maizzle"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/mobile/billing-api", "main": "apps/mobile/billing-api/src/main.ts", "tsConfig": "apps/mobile/billing-api/tsconfig.app.json", "assets": ["apps/mobile/billing-api/src/assets"], "webpackConfig": "apps/mobile/billing-api/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {"optimization": true, "extractLicenses": true, "inspect": false}, "swagger": {"main": "{projectRoot}/src/swagger/generate-swagger.ts"}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "billing-api:build"}, "configurations": {"development": {"buildTarget": "billing-api:build:development"}, "production": {"buildTarget": "billing-api:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mobile/billing-api/jest.config.ts"}}, "generate-swagger": {"executor": "@nx/js:node", "options": {"buildTarget": "{projectName}:build:swagger", "watch": false}}}}