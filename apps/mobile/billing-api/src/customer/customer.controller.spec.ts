import { CustomerController } from './customer.controller';
import { CustomerService } from './customer.service';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import {
  FailedToGetCustomerPaymentProcessorError,
  FailedToUpdateCustomerError,
  NoCustomerPaymentProcessorError,
} from './customer.errors';
import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { UpdateCustomerRequest } from '@experience/shared/nest/stripe';
import request from 'supertest';

describe('CustomerController', () => {
  const MOCK_AUTH_ID = '7141029a-d863-4e0d-9ae6-511063b24235';

  let app: INestApplication;
  let service: DeepMocked<CustomerService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [CustomerController],
      providers: [
        {
          provide: CustomerService,
          useValue: createMock<CustomerService>(),
        },
      ],
    }).compile();

    service = module.get(CustomerService);

    app = module.createNestApplication();

    await app.init();
  });

  afterEach(() => jest.resetAllMocks());

  describe('PUT /:authId', () => {
    const MOCK_UPDATE: UpdateCustomerRequest = {
      email: '<EMAIL>',
    };

    it('returns a 500 if a FailedToGetCustomerPaymentProcessorError is thrown', async () => {
      service.updateCustomer.mockRejectedValue(
        new FailedToGetCustomerPaymentProcessorError()
      );

      await request(app.getHttpServer())
        .patch(`/customer/${MOCK_AUTH_ID}`)
        .send(MOCK_UPDATE)
        .expect(500);

      expect(service.updateCustomer).toHaveBeenCalledTimes(1);
      expect(service.updateCustomer).toHaveBeenCalledWith(
        MOCK_AUTH_ID,
        MOCK_UPDATE
      );
    });

    it('returns a 500 if a FailedToUpdateCustomerError is thrown', async () => {
      service.updateCustomer.mockRejectedValue(
        new FailedToUpdateCustomerError()
      );

      await request(app.getHttpServer())
        .patch(`/customer/${MOCK_AUTH_ID}`)
        .send(MOCK_UPDATE)
        .expect(500);

      expect(service.updateCustomer).toHaveBeenCalledTimes(1);
      expect(service.updateCustomer).toHaveBeenCalledWith(
        MOCK_AUTH_ID,
        MOCK_UPDATE
      );
    });

    it('returns a 404 if a NoCustomerPaymentProcessorError is thrown', async () => {
      service.updateCustomer.mockRejectedValue(
        new NoCustomerPaymentProcessorError()
      );

      await request(app.getHttpServer())
        .patch(`/customer/${MOCK_AUTH_ID}`)
        .send(MOCK_UPDATE)
        .expect(404);

      expect(service.updateCustomer).toHaveBeenCalledTimes(1);
      expect(service.updateCustomer).toHaveBeenCalledWith(
        MOCK_AUTH_ID,
        MOCK_UPDATE
      );
    });

    it('returns a 200 if successfully updates customer', async () => {
      const MOCK_RESPONSE = {
        id: MOCK_AUTH_ID,
        paymentProcessorId: 'bbbfd3ad-89e6-4919-b427-aa0a5a21a57c',
        ...MOCK_UPDATE,
      };

      service.updateCustomer.mockResolvedValue(MOCK_RESPONSE);

      await request(app.getHttpServer())
        .patch(`/customer/${MOCK_AUTH_ID}`)
        .send(MOCK_UPDATE)
        .expect(200, MOCK_RESPONSE);

      expect(service.updateCustomer).toHaveBeenCalledTimes(1);
      expect(service.updateCustomer).toHaveBeenCalledWith(
        MOCK_AUTH_ID,
        MOCK_UPDATE
      );
    });
  });
});
