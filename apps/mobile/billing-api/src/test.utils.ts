import { BillingAccounts } from '@experience/shared/sequelize/podadmin';
import { jest } from '@jest/globals';
import Stripe from 'stripe';

export type MockRepository = Partial<
  Record<keyof typeof BillingAccounts, jest.Mock>
>;
export const createMockRepository = (): MockRepository => ({
  create: jest.fn(),
  update: jest.fn(),
  findOne: jest.fn(),
  count: jest.fn(),
});

// Create a mock PaymentIntent object
export const mockTopUpPaymentIntent: Stripe.PaymentIntent = {
  payment_method_configuration_details: null,
  id: 'pi_3MtwBwLkdIwHu7ix28a3tqPa',
  object: 'payment_intent',
  amount: 2000,
  amount_capturable: 0,
  amount_details: {
    tip: {},
  },
  amount_received: 0,
  application: null,
  application_fee_amount: null,
  automatic_payment_methods: {
    enabled: true,
  },
  canceled_at: null,
  cancellation_reason: null,
  capture_method: 'automatic',
  client_secret: 'pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_YrKJUKribcBjcG8HVhfZluoGH',
  confirmation_method: 'automatic',
  created: **********,
  currency: 'gbp',
  customer: 'cus_QOUbwhpAZFRPvb',
  description: 'payment went through',
  excluded_payment_method_types: null,
  last_payment_error: null,
  latest_charge: null,
  livemode: false,
  metadata: {
    origin: 'billing-api',
    product: 'PAYG Top Up',
  },
  next_action: null,
  on_behalf_of: null,
  payment_method: null,
  payment_method_options: {
    card: {
      installments: null,
      mandate_options: null,
      network: null,
      request_three_d_secure: 'automatic',
    },
    link: {
      persistent_token: null,
    },
  },
  payment_method_types: ['card', 'link'],
  processing: null,
  receipt_email: null,
  review: null,
  setup_future_usage: null,
  shipping: null,
  source: null,
  statement_descriptor: null,
  statement_descriptor_suffix: null,
  status: 'requires_payment_method',
  transfer_data: null,
  transfer_group: null,
};
export const mockGuestPaymentIntent: Stripe.PaymentIntent = {
  payment_method_configuration_details: null,
  id: 'pi_3MtwBwLkdIwHu7ix28a3tqPa',
  object: 'payment_intent',
  amount: 2000,
  amount_capturable: 0,
  amount_details: {
    tip: {},
  },
  amount_received: 0,
  application: null,
  application_fee_amount: null,
  automatic_payment_methods: {
    enabled: true,
  },
  canceled_at: null,
  cancellation_reason: null,
  capture_method: 'automatic',
  client_secret: 'pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_YrKJUKribcBjcG8HVhfZluoGH',
  confirmation_method: 'automatic',
  created: **********,
  currency: 'gbp',
  customer: null,
  description: 'payment went through',
  excluded_payment_method_types: null,
  last_payment_error: null,
  latest_charge: null,
  livemode: false,
  metadata: {
    origin: 'billing-api',
    product: 'Guest Charge',
  },
  next_action: null,
  on_behalf_of: null,
  payment_method: null,
  payment_method_options: {
    card: {
      installments: null,
      mandate_options: null,
      network: null,
      request_three_d_secure: 'automatic',
    },
    link: {
      persistent_token: null,
    },
  },
  payment_method_types: ['card', 'link'],
  processing: null,
  receipt_email: null,
  review: null,
  setup_future_usage: null,
  shipping: null,
  source: null,
  statement_descriptor: null,
  statement_descriptor_suffix: null,
  status: 'requires_payment_method',
  transfer_data: null,
  transfer_group: null,
};

// Create a mock event for payment_intent.succeeded
const mockEvent: Stripe.Event = {
  id: 'evt_1IEAaZG16qv9bVQKQ0Sk0qHg',
  object: 'event',
  api_version: '2020-08-27',
  created: 1617184800,
  data: {
    object: mockTopUpPaymentIntent,
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: 'req_123456789',
    idempotency_key: null,
  },
  type: 'payment_intent.succeeded',
};

export const mockFailedPaymentIntent: Stripe.PaymentIntent = {
  id: 'pi_3PbP1c4ndFfyXYyp1cuv27he',
  object: 'payment_intent',
  amount: 1000,
  amount_capturable: 0,
  amount_details: {
    tip: {},
  },
  amount_received: 0,
  application: null,
  application_fee_amount: null,
  automatic_payment_methods: {
    allow_redirects: 'always',
    enabled: true,
  },
  canceled_at: null,
  cancellation_reason: null,
  capture_method: 'automatic_async',
  client_secret: 'pi_3PbP1c4ndFfyXYyp1cuv27he_secret_BnDxxGd4qe0PG7bOhlOYhXFIy',
  confirmation_method: 'automatic',
  created: 1720712116,
  currency: 'gbp',
  customer: 'cus_QRWl0zVpVkkyAh',
  description: null,
  excluded_payment_method_types: null,
  last_payment_error: {
    charge: 'ch_3PbP1c4ndFfyXYyp1WeLQNBb',
    code: 'card_declined',
    decline_code: 'insufficient_funds',
    doc_url: 'https://stripe.com/docs/error-codes/card-declined',
    message: 'Your card has insufficient funds.',
    payment_method: {
      id: 'pm_1PbP2e4ndFfyXYypirJ3GIpc',
      object: 'payment_method',
      allow_redisplay: 'unspecified',
      billing_details: {
        address: {
          city: null,
          country: 'GB',
          line1: null,
          line2: null,
          postal_code: 'eh75ee',
          state: null,
        },
        email: null,
        name: null,
        phone: null,
        tax_id: null,
      },
      card: {
        brand: 'visa',
        checks: {
          address_line1_check: null,
          address_postal_code_check: 'pass',
          cvc_check: 'pass',
        },
        country: 'US',
        display_brand: 'visa',
        exp_month: 12,
        exp_year: 2034,
        fingerprint: '8wqajOFW7K7RbG8j',
        funding: 'credit',
        generated_from: null,
        last4: '9995',
        networks: {
          available: ['visa'],
          preferred: null,
        },
        three_d_secure_usage: {
          supported: true,
        },
        wallet: null,
        regulated_status: null,
      },
      created: 1720712180,
      customer: null,
      livemode: false,
      metadata: {},
      type: 'card',
    },
    type: 'card_error',
  },
  latest_charge: 'ch_3PbP1c4ndFfyXYyp1WeLQNBb',
  livemode: false,
  metadata: {
    origin: 'billing-api',
    product: 'PAYG Top Up',
  },
  next_action: null,
  on_behalf_of: null,
  payment_method: null,
  payment_method_configuration_details: {
    id: 'pmc_1KdbGO4ndFfyXYypFFprC0Wg',
    parent: null,
  },
  payment_method_options: {
    card: {
      installments: null,
      mandate_options: null,
      network: null,
      request_three_d_secure: 'automatic',
      setup_future_usage: 'off_session',
    },
  },
  payment_method_types: ['card'],
  processing: null,
  receipt_email: '<EMAIL>',
  review: null,
  setup_future_usage: null,
  shipping: null,
  source: null,
  statement_descriptor: null,
  statement_descriptor_suffix: null,
  status: 'requires_payment_method',
  transfer_data: null,
  transfer_group: null,
};

export const mockFailedEvent: Stripe.Event = {
  id: 'evt_3PbP1c4ndFfyXYyp1Kbh9799',
  object: 'event',
  api_version: '2024-06-20',
  created: 1720712185,
  data: {
    object: mockFailedPaymentIntent,
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: null,
    idempotency_key:
      'pi_3PbP1c4ndFfyXYyp1cuv27he-payatt_3PbP1c4ndFfyXYyp17ODO0Gb',
  },
  type: 'payment_intent.payment_failed',
};

export default mockEvent;
