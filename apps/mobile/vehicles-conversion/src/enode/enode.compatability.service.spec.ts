import { EnodeCompatabilityService } from './enode.compatability.service';

const mockGetHealthVehicleVendors = jest.fn();
const mockGetEnodeVehicleCapabilities = jest.fn();

const CSV_HEADERS =
  'brand,model,year,type,integration_status,activation_required,regions,reliability,capabilities,information,charge_state,location,start_stop_commands,smart_charging,scheduling,statistics';

jest.mock('./client/enode.health.client', () => ({
  getEnodeHealthClient: () => ({
    getHealthVehicleVendors: mockGetHealthVehicleVendors,
  }),
}));

jest.mock('./client/enode.capabilities.client', () => ({
  getEnodeVehicleCapabilities: () => mockGetEnodeVehicleCapabilities(),
}));

describe('EnodeCompatabilityService', () => {
  let service: EnodeCompatabilityService;

  beforeEach(() => {
    service = new EnodeCompatabilityService();
  });

  describe('retrieveCompatability', () => {
    it('should populate brands from API endpoint', async () => {
      mockGetHealthVehicleVendors.mockResolvedValueOnce({
        data: [
          {
            vendor: 'TESLA',
            displayName: 'Tesla',
            portalName: 'Tesla',
            status: 'READY',
            linkingStatus: 'READY',
          },
          {
            vendor: 'BMW',
            displayName: 'BMW',
            portalName: 'My BMW',
            status: 'READY',
            linkingStatus: 'READY',
          },
          {
            vendor: 'AUDI',
            displayName: 'Audi',
            portalName: 'myAudi',
            status: 'READY',
            linkingStatus: 'READY',
          },
        ],
      });

      mockGetEnodeVehicleCapabilities.mockResolvedValueOnce(
        [CSV_HEADERS].join('\n')
      );

      const results = await service.retrieveCompatability();

      expect(mockGetHealthVehicleVendors).toHaveBeenCalled();
      expect(mockGetEnodeVehicleCapabilities).toHaveBeenCalled();

      expect(results.brands).toEqual(['tesla', 'bmw', 'audi']);
    });

    it('should not populate brands and models from CSV endpoint', async () => {
      mockGetHealthVehicleVendors.mockResolvedValueOnce({
        data: [],
      });

      mockGetEnodeVehicleCapabilities.mockResolvedValueOnce(
        [
          CSV_HEADERS,
          '"Audi","A3","2015+","PHEV","Live","false","Europe,US","3","Full","true","true","true","true","true","true","true"',
          '"BMW","225e","2022+","PHEV","Live","false","Europe,US","3","Full","true","true","true","true","true","true","true"',
          '"Cadillac","Lyriq","2023+","BEV","Live","true","US","1","Full","true","true","true","true","true","true","true"',
        ].join('\n')
      );

      const results = await service.retrieveCompatability();

      expect(mockGetHealthVehicleVendors).toHaveBeenCalled();
      expect(mockGetEnodeVehicleCapabilities).toHaveBeenCalled();

      expect(results.brands).toEqual([]);
    });

    it('should include only brands from API', async () => {
      mockGetHealthVehicleVendors.mockResolvedValueOnce({
        data: [
          {
            vendor: 'TESLA',
            displayName: 'Tesla',
            portalName: 'Tesla',
            status: 'READY',
            linkingStatus: 'READY',
          },
          {
            vendor: 'BMW',
            displayName: 'BMW',
            portalName: 'My BMW',
            status: 'READY',
            linkingStatus: 'READY',
          },
        ],
      });

      mockGetEnodeVehicleCapabilities.mockResolvedValueOnce(
        [
          CSV_HEADERS,
          'Audi","A3","2015+","PHEV","Live","false","Europe,US","3","Full","true","true","true","true","true","true","true"',
        ].join('\n')
      );

      const results = await service.retrieveCompatability();

      expect(mockGetHealthVehicleVendors).toHaveBeenCalled();
      expect(mockGetEnodeVehicleCapabilities).toHaveBeenCalled();

      expect(results.brands).toEqual(['tesla', 'bmw']);
    });

    it.each([
      ['Citroën ', { from: 'Citroën', to: 'citroen' }],
      ['Cupra ', { from: 'cupra', to: 'cupra' }],
      ['Mercedes ', { from: 'Mercedes', to: 'mercedes-benz' }],
      ['MINI ', { from: 'MINI', to: 'mini' }],
      ['ŠKODA ', { from: 'ŠKODA', to: 'skoda' }],
    ])('should normalise Enode vehicle brand name - %s', async (_, data) => {
      mockGetHealthVehicleVendors.mockResolvedValueOnce({
        data: [
          {
            displayName: data.from,
          },
        ],
      });

      mockGetEnodeVehicleCapabilities.mockResolvedValueOnce(CSV_HEADERS);

      const results = await service.retrieveCompatability();

      expect(results.brands).toEqual([data.to]);
    });
  });
});
