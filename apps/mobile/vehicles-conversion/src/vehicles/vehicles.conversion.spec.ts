import { ConversionOutput } from './vehicle.conversion.types';
import { VehiclesConversionService } from './vehicles.conversion.service';
import bevVehicles from '../assets/vehicles_ev_database_sample.json';
import fs from 'fs';

jest.mock('fs');

const isUnique = (a: unknown[]): boolean => {
  const aSorted = a.sort();
  const bSorted = [...new Set(a)].sort();

  return aSorted.every((item, index) => item === bSorted[index]);
};

describe('VehiclesConversionService', () => {
  const ENODE_COMPATIBILITY = {
    brands: ['bmw', 'nissan'],
    models: {},
  };

  let service: VehiclesConversionService;

  beforeEach(() => {
    service = new VehiclesConversionService();
  });

  describe('convert', () => {
    it('returns unique brands', () => {
      const res = service.convert(bevVehicles.slice(0, 4), ENODE_COMPATIBILITY);

      expect(res).toHaveProperty('brands');
      expect(res.brands).toBeInstanceOf(Array);
      expect(res.brands).toMatchSnapshot();

      expect(isUnique(res.brands.map((b) => b.id))).toBeTruthy();
    });

    it('returns unique models', () => {
      const res = service.convert(bevVehicles.slice(0, 4), ENODE_COMPATIBILITY);

      expect(res).toHaveProperty('models');
      expect(res.models).toBeInstanceOf(Array);
      expect(res.models).toMatchSnapshot();

      expect(isUnique(res.models.map((m) => m.id))).toBeTruthy();
    });

    it('returns unique model variants', () => {
      const res = service.convert(bevVehicles.slice(0, 4), ENODE_COMPATIBILITY);

      expect(res).toHaveProperty('modelVariants');
      expect(res.modelVariants).toBeInstanceOf(Array);
      expect(res.modelVariants).toMatchSnapshot();

      expect(isUnique(res.modelVariants.map((v) => v.id))).toBeTruthy();
    });
  });

  describe('exportJson', () => {
    it('should export vehicles array to JSON file', () => {
      const vehicles: ConversionOutput = {
        brands: [
          {
            id: 'Tesla',
            name: 'Tesla',
            logoUrl:
              'https://cdn.pod-point.com/home-app/vehicle-icons/tesla.svg',
            enodeCompatible: true,
          },
        ],
        models: [
          {
            id: 'tesla-model-s',
            name: 'Model S',
            brandId: 'tesla',
          },
        ],
        modelVariants: [],
      };

      const fileName = 'test.json';

      service.exportJson(fileName, vehicles);

      expect(fs.writeFile).toHaveBeenCalledWith(
        `${__dirname}/../assets/${fileName}`,
        JSON.stringify(vehicles),
        expect.any(Function)
      );
    });
  });
});
