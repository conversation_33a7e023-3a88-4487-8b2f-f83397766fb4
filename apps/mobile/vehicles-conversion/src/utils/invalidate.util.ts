import {
  CloudFrontClient,
  CreateInvalidationCommand,
} from '@aws-sdk/client-cloudfront';

const cfClient = new CloudFrontClient({
  region: 'eu-west-1',
});

export const invalidateItems = (items: string[]) =>
  cfClient.send(
    new CreateInvalidationCommand({
      DistributionId: process.env.POD_POINT_CDN_DISTRIBUTION_ID,
      InvalidationBatch: {
        Paths: {
          Quantity: 1,
          Items: items,
        },
        CallerReference: new Date().getTime().toString(),
      },
    })
  );
