{"name": "vehicles-conversion", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile/vehicles-conversion/src", "projectType": "application", "tags": ["mobile"], "targets": {"convert": {"executor": "nx:run-commands", "options": {"command": "ts-node --project apps/mobile/vehicles-conversion/tsconfig.app.json --require tsconfig-paths/register apps/mobile/vehicles-conversion/src/convert.ts"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/mobile/vehicles-conversion"], "options": {"jestConfig": "apps/mobile/vehicles-conversion/jest.config.ts", "passWithNoTests": false}}}}