{"name": "rewards-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile/rewards-api/src", "projectType": "application", "tags": ["mobile", "package"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/mobile/rewards-api", "main": "apps/mobile/rewards-api/src/main.ts", "tsConfig": "apps/mobile/rewards-api/tsconfig.app.json", "webpackConfig": "apps/mobile/rewards-api/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {"optimization": true, "extractLicenses": true, "inspect": false}, "swagger": {"main": "{projectRoot}/src/swagger/generate-swagger.ts"}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "rewards-api:build"}, "configurations": {"development": {"buildTarget": "rewards-api:build:development"}, "production": {"buildTarget": "rewards-api:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mobile/rewards-api/jest.config.ts"}}, "generate-swagger": {"executor": "@nx/js:node", "options": {"buildTarget": "{projectName}:build:swagger", "watch": false}}}}