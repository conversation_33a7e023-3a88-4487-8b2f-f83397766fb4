export enum Currency {
  GBP = 'gbp',
  MILES = 'miles',
}

/**
 * Represents the status of a transaction.
 *  - `PENDING`: The transaction has been initiated but not yet completed.
 *  - `IN_TRANSIT`: The transaction is in progress.
 *  - `PAID`: The transaction has been completed.
 *  - `CANCELLED`: The transaction has been cancelled.
 *  - `FAILED`: The transaction has failed.
 */
export enum TransactionStatus {
  PENDING = 'pending',
  IN_TRANSIT = 'in_transit',
  PAID = 'paid',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

export enum AccountType {
  ALLOWANCE = 'allowance',
  REWARDS = 'rewards',
  BANK = 'bank',
  SYSTEM_MILES = 'system_miles',
}
