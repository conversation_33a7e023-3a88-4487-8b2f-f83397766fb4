import { Account } from './account';
import { AccountType, Currency } from './types';
import { Wallet } from './wallet';

export class AllowanceAccount extends Account {
  minBalance = 0;

  constructor(wallet: Wallet) {
    super(wallet, AccountType.ALLOWANCE);
  }

  transfer(
    target: Account,
    amount: number,
    currency: Currency,
    reference?: string
  ) {
    if (this.getBalance() - amount < this.minBalance) {
      super.transfer(target, this.getBalance(), this.currency, reference);
      // eslint-disable-next-line no-console
      console.warn(
        `Insufficient allowance to pay full amount. Have paid remaining allowance of ${this.getBalance()} ${
          this.currency
        }`
      );
      return;
    }
    super.transfer(target, amount, currency, reference);
  }
}
