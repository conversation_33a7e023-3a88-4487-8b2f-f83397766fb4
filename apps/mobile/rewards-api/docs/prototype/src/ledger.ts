import { Account } from './account';
import { Currency } from './types';
import { Transaction } from './transaction';

export class Ledger {
  transactions: Transaction[];

  constructor() {
    this.transactions = [];
  }

  record(
    source: Account,
    dest: Account,
    amount: number,
    currency: Currency,
    reference?: string
  ): void {
    const transaction = new Transaction(
      source,
      dest,
      amount,
      currency,
      reference
    );
    this.transactions.push(transaction);
    // eslint-disable-next-line no-console
    console.log({ transaction });
  }

  accountTransactions(account: Account): Transaction[] {
    return this.transactions.filter(
      (transaction) =>
        transaction.source.id === account.id ||
        transaction.dest.id === account.id
    );
  }

  print(): void {
    for (const t of this.transactions) {
      // eslint-disable-next-line no-console
      console.log({ transaction: t });
    }
  }
}
