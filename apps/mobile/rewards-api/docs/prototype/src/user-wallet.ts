import { AllowanceAccount } from './allowance-account';
import { BankAccount } from './bank-account';
import { Currency } from './types';
import { INITIAL_MILES_ALLOWANCE } from './constants';
import { RewardsAccount } from './rewards-account';
import { RewardsSystem } from './rewards-system';
import { Wallet } from './wallet';

export class UserWallet extends Wallet {
  allowanceAccount: AllowanceAccount;
  rewardsAccount: RewardsAccount;
  bankAccount: BankAccount;
  subscriptionId: string;

  constructor(
    userId: string,
    rewardsSystem: RewardsSystem,
    subscriptionId: string
  ) {
    super(userId, rewardsSystem);
    this.allowanceAccount = new AllowanceAccount(this);
    this.rewardsAccount = new RewardsAccount(this);
    this.bankAccount = new BankAccount(this);
    this.subscriptionId = subscriptionId;

    // set an initial allowance
    this.rewardsSystem.addMilesToAllowance(this, INITIAL_MILES_ALLOWANCE);
  }

  withdraw() {
    const rewardsBalance = this.rewardsAccount.getBalance();
    this.rewardsSystem.withdraw(this, rewardsBalance);
  }

  claimRewardMiles(amount: number, reference: string) {
    this.allowanceAccount.transfer(
      this.rewardsAccount,
      amount,
      Currency.MILES,
      reference
    );
  }

  getBalances() {
    return {
      allowance: this.allowanceAccount.getBalance(),
      rewards: this.rewardsAccount.getBalance(),
      bank: this.bankAccount.getBalance(),
    };
  }
}
