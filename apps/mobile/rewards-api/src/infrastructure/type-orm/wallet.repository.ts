import { BaseTypeOrmRepository } from '@experience/mobile/nest/typeorm-transactions';
import {
  <PERSON>L<PERSON>ger,
  CouldNotPersistEntityError,
  FatalRepositoryError,
  Transformer,
} from '@experience/mobile/clean-architecture';
import { ClsService } from 'nestjs-cls';
import { EntityManager } from 'typeorm';
import { RewardsWallet } from '@experience/mobile/driver-account/database';
import {
  UnpersistedWalletEntity,
  WalletEntity,
  WalletEntityType,
} from '../../domain/entities/wallet.entity';
import { WalletRepositoryInterface } from '../../domain/repositories/wallet.repository.interface';

export class TypeOrmWalletRepository
  extends BaseTypeOrmRepository
  implements WalletRepositoryInterface
{
  constructor(
    private readonly logger: CleanLogger,
    private readonly entityTransformer: Transformer<
      RewardsWallet,
      WalletEntity
    >,
    entityManager: EntityManager,
    cls: ClsService
  ) {
    super(entityManager, cls);
  }

  async getByType(type: WalletEntityType): Promise<WalletEntity[]> {
    return (
      await this.manager.find(RewardsWallet, { where: { type }, take: 100 })
    ).map(this.entityTransformer.transform);
  }

  async read(id: WalletEntity['id']): Promise<WalletEntity | null> {
    const maybeWallet = await this.manager.findOne(RewardsWallet, {
      where: { id },
    });

    if (maybeWallet) {
      return this.entityTransformer.transform(maybeWallet);
    }

    return null;
  }

  async create(entity: UnpersistedWalletEntity): Promise<WalletEntity> {
    try {
      const pendingCreationRewardWallet = this.manager.create(RewardsWallet, {
        type: entity.type,
        userId: entity.userId,
        subscriptionId: entity.subscriptionId,
      });

      const created = await this.manager.save(
        RewardsWallet,
        pendingCreationRewardWallet
      );

      return this.entityTransformer.transform(created);
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }

      this.logger.error(
        {
          error,
        },
        'Failed to create'
      );

      throw new CouldNotPersistEntityError(
        entity,
        'Failed to create due to ORM error'
      );
    }
  }

  async getByUserId(userId: string): Promise<WalletEntity | null> {
    try {
      const maybeWallet = await this.manager.findOneBy(RewardsWallet, {
        userId,
      });

      if (!maybeWallet) {
        return null;
      }

      return this.entityTransformer.transform(maybeWallet);
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }

      this.logger.error({ error }, 'failed to get wallet by user id');

      throw new FatalRepositoryError('Failed to read due to ORM error');
    }
  }

  async update(
    entity: WalletEntity,
    fields: Partial<UnpersistedWalletEntity>
  ): Promise<WalletEntity> {
    try {
      await this.manager.update(
        RewardsWallet,
        {
          id: entity.id,
        },
        fields
      );

      return new WalletEntity({
        ...entity,
        ...fields,
      });
    } catch (error) {
      this.logger.error({ entity, fields, error }, 'failed to update entity');

      throw new FatalRepositoryError(
        'Failed to update entity due to ORM error'
      );
    }
  }
}
