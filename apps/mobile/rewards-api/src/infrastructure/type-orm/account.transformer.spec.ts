import {
  AccountEntity,
  AccountEntityType,
} from '../../domain/entities/account.entity';
import { AccountTransformer } from './account.transformer';
import { InvalidEntityError } from '@experience/mobile/clean-architecture';
import { RewardsAccount } from '@experience/mobile/driver-account/database';
import { beforeEach, describe, expect, it } from '@jest/globals';
import { v4 } from 'uuid';

describe(AccountTransformer.name, () => {
  let transformer: AccountTransformer;

  beforeEach(() => {
    transformer = new AccountTransformer();
  });

  it('throws InvalidEntityError if type is incorrect', () => {
    expect(() =>
      transformer.transform({
        id: v4(),
        type: 'invalid',
        rewardWalletId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      } as RewardsAccount)
    ).toThrow(InvalidEntityError);
  });

  it('returns a AccountEntity instance', () => {
    const actual = transformer.transform({
      id: v4(),
      type: AccountEntityType.ALLOWANCE,
      rewardWalletId: v4(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    } as RewardsAccount);

    expect(actual).toBeInstanceOf(AccountEntity);
  });

  it('returns a AccountEntity with expected content', () => {
    const ormMockedEntity = {
      id: v4(),
      type: AccountEntityType.ALLOWANCE,
      rewardWalletId: v4(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    const actual = transformer.transform(ormMockedEntity as RewardsAccount);

    const expected = new AccountEntity({
      id: ormMockedEntity.id,
      type: ormMockedEntity.type,
      rewardWalletId: ormMockedEntity.rewardWalletId,
      createdAt: ormMockedEntity.createdAt,
      updatedAt: ormMockedEntity.updatedAt,
      deletedAt: ormMockedEntity.deletedAt,
    });

    expect(actual).toEqual(expected);
  });
});
