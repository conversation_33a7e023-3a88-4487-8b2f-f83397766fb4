import {
  AccountEntity,
  AccountEntityType,
  UnpersistedAccountEntity,
} from '../../domain/entities/account.entity';
import { AccountRepositoryInterface } from '../../domain/repositories/account.repository.interface';
import { BaseTypeOrmRepository } from '@experience/mobile/nest/typeorm-transactions';
import {
  CleanLogger,
  CouldNotFindEntityError,
  CouldNotPersistEntityError,
  FatalRepositoryError,
  Transformer,
} from '@experience/mobile/clean-architecture';
import { ClsService } from 'nestjs-cls';
import { EntityManager } from 'typeorm';
import { RewardsAccount } from '@experience/mobile/driver-account/database';

export class TypeOrmAccountRepository
  extends BaseTypeOrmRepository
  implements AccountRepositoryInterface
{
  constructor(
    private readonly logger: CleanLogger,
    private readonly entityTransformer: Transformer<
      RewardsAccount,
      AccountEntity
    >,
    entityManager: EntityManager,
    cls: ClsService
  ) {
    super(entityManager, cls);
  }

  async getAllowanceAccount(rewardWalletId: string): Promise<AccountEntity> {
    const account = await this.manager.findOne(RewardsAccount, {
      where: {
        type: AccountEntityType.ALLOWANCE,
        rewardWalletId,
      },
    });

    if (!account) {
      this.logger.error(
        { rewardWalletId },
        `${AccountEntityType.ALLOWANCE} account not found`
      );

      throw new CouldNotFindEntityError(
        { rewardWalletId },
        `${AccountEntityType.ALLOWANCE} account not found`
      );
    }

    return this.entityTransformer.transform(account);
  }

  async getRewardsAccount(rewardWalletId: string): Promise<AccountEntity> {
    const account = await this.manager.findOne(RewardsAccount, {
      where: {
        type: AccountEntityType.REWARDS,
        rewardWalletId,
      },
    });

    if (!account) {
      this.logger.error(
        { rewardWalletId },
        `${AccountEntityType.REWARDS} account not found`
      );

      throw new CouldNotFindEntityError(
        { rewardWalletId },
        `${AccountEntityType.REWARDS} account not found`
      );
    }

    return this.entityTransformer.transform(account);
  }

  async getSystemAccount(rewardWalletId: string): Promise<AccountEntity> {
    const account = await this.manager.findOne(RewardsAccount, {
      where: {
        type: AccountEntityType.SYSTEM_MILES,
        rewardWalletId,
      },
    });

    if (!account) {
      this.logger.error(
        { rewardWalletId },
        `${AccountEntityType.SYSTEM_MILES} account not found`
      );

      throw new CouldNotFindEntityError(
        { rewardWalletId },
        `${AccountEntityType.SYSTEM_MILES} account not found`
      );
    }

    return this.entityTransformer.transform(account);
  }

  async read(id: AccountEntity['id']): Promise<AccountEntity | null> {
    const maybeAccount = await this.manager.findOne(RewardsAccount, {
      where: { id },
    });

    if (maybeAccount) {
      return this.entityTransformer.transform(maybeAccount);
    }

    return null;
  }

  async create(entity: UnpersistedAccountEntity): Promise<AccountEntity> {
    try {
      const pendingCreationRewardAccount = this.manager.create(RewardsAccount, {
        type: entity.type,
        rewardWalletId: entity.rewardWalletId,
      });

      const created = await this.manager.save(
        RewardsAccount,
        pendingCreationRewardAccount
      );

      return this.entityTransformer.transform(created);
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }

      this.logger.error(
        {
          error,
        },
        'Failed to create'
      );

      throw new CouldNotPersistEntityError(
        entity,
        'Failed to create due to ORM error'
      );
    }
  }
}
