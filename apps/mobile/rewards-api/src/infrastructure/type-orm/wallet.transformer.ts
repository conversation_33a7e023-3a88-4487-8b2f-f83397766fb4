import {
  InvalidEntityError,
  Transformer,
} from '@experience/mobile/clean-architecture';
import { RewardsWallet } from '@experience/mobile/driver-account/database';
import {
  WalletEntity,
  WalletEntityType,
} from '../../domain/entities/wallet.entity';

export class WalletTransformer extends Transformer<
  RewardsWallet,
  WalletEntity
> {
  /**
   * @throws InvalidEntityError
   */
  transform(input: RewardsWallet): WalletEntity {
    function assertTypeIsValid(
      type: unknown
    ): asserts type is WalletEntityType {
      // eslint-disable-next-line unicorn/prefer-includes
      if (Object.values(WalletEntityType).some((val) => val === type)) {
        return;
      }

      throw new InvalidEntityError({ id: input.id, type }, 'type is invalid');
    }

    assertTypeIsValid(input.type);

    return new WalletEntity({
      id: input.id,
      type: input.type,
      userId: input.userId,
      subscriptionId: input.subscriptionId ?? null,
      createdAt: input.createdAt,
      updatedAt: input.updatedAt,
      deletedAt: input.deletedAt ?? null,
    });
  }
}
