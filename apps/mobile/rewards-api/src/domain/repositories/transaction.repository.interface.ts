import {
  CreateRepositoryInterface,
  ReadRepositoryInterface,
} from '@experience/mobile/clean-architecture';
import {
  KeyCountPaginationParams,
  KeyCountPaginationResult,
} from '@experience/shared/nest/utils';
import { TransactionEntity } from '../entities/transaction.entity';

export interface TransactionRepositoryInterface
  extends ReadRepositoryInterface<TransactionEntity>,
    CreateRepositoryInterface<TransactionEntity> {
  readByAccountId(accountId: string): Promise<TransactionEntity[]>;

  readByAccountIdPaginated(
    accountId: string,
    params: KeyCountPaginationParams
  ): Promise<KeyCountPaginationResult<TransactionEntity>>;

  readByIdempotencyKey(idempotencyKey: string): Promise<TransactionEntity[]>;

  sum(
    column: 'amount',
    by: { accountId: string; at?: Date }
  ): Promise<number | null>;
}
