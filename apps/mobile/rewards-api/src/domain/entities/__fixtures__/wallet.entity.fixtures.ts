import { WalletEntity, WalletEntityType } from '../wallet.entity';

export const MOCK_WALLET_ID = 'f99cfb05-65a2-4d26-a818-986ebc3e27fe';
export const MOCK_SUBSCRIPTION_ID = '2c31fe4f-2411-4175-926f-770d7c20a4d8';
export const MOCK_USER_ID = '1135b17a-9bb5-4d36-bbef-734a61413c39';
export const MOCK_BANK_ACCOUNT_ID = '505b26e1-1905-4971-81ea-e2fb89ec016b';

export const MOCK_SYSTEM_WALLET_ID = '4714bd0e-15cf-4712-b59b-d07aaafeef6b';

export const MOCK_WALLET_ENTITY: WalletEntity = new WalletEntity({
  createdAt: new Date('2024-05-01T15:34:00.000Z'),
  deletedAt: null,
  id: MOCK_WALLET_ID,
  subscriptionId: MOCK_SUBSCRIPTION_ID,
  type: WalletEntityType.POD_DRIVE,
  updatedAt: new Date('2024-05-01T15:34:00.000Z'),
  userId: MOCK_USER_ID,
});

export const MOCK_SYSTEM_WALLET_ENTITY: WalletEntity = new WalletEntity({
  createdAt: new Date('2024-05-01T15:34:00.000Z'),
  deletedAt: null,
  id: MOCK_SYSTEM_WALLET_ID,
  subscriptionId: '',
  type: WalletEntityType.SYSTEM,
  updatedAt: new Date('2024-05-01T15:34:00.000Z'),
  userId: '',
});
