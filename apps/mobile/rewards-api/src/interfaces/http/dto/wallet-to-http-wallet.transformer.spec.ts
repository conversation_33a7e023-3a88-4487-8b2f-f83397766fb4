import { PersistedWalletHttpDto } from './persisted-wallet.dto';
import {
  WalletEntity,
  WalletEntityType,
} from '../../../domain/entities/wallet.entity';
import { WalletToHttpWalletTransformer } from './wallet-to-http-wallet.transformer';
import { beforeEach, describe, expect, it } from '@jest/globals';
import { v4 } from 'uuid';

describe(WalletToHttpWalletTransformer.name, () => {
  let transformer: WalletToHttpWalletTransformer;

  beforeEach(() => {
    transformer = new WalletToHttpWalletTransformer();
  });

  it('returns an instance of PersistedWalletHttpDto', () => {
    const entity = new WalletEntity({
      id: v4(),
      type: WalletEntityType.POD_DRIVE,
      userId: v4(),
      subscriptionId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });

    expect(transformer.transform(entity)).toBeInstanceOf(
      PersistedWalletHttpDto
    );
  });

  it('returns correct shape', () => {
    const entity = new WalletEntity({
      id: v4(),
      type: WalletEntityType.POD_DRIVE,
      userId: v4(),
      subscriptionId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });

    expect(transformer.transform(entity)).toEqual({
      id: entity.id,
      type: entity.type,
      subscriptionId: entity.subscriptionId,
    });
  });
});
