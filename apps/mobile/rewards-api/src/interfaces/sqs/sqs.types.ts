// Derived from libs/data-platform/event-sourcing/chargecompleted/completedevent.asyncapi.yml
export interface ChargeCompletedEvent {
  charge: ChargeCompletedEventCharge;
  energy: ChargeCompletedEventEnergy;
  chargingStation: ChargeCompletedEventChargingStation;
  rewards: ChargeCompletedEventRewards | null;
  publishedAt: string;
}

export interface ChargeCompletedEventCharge {
  id: string;
  ref: string;
  endedAt: string | null;
  startedAt: string | null;
  updatedAt: string;
  energyTotal: number | null;
  pluggedInAt: string;
  unpluggedAt: string;
  gridEnergyTotal: number | null;
  generationEnergyTotal: number | null;
  chargeDurationTotal: number;
}

export interface ChargeCompletedEventEnergy {
  cost: number;
  costCurrency: string | null;
}

export interface ChargeCompletedEventChargingStation {
  id: string;
  doorId: string;
}

export interface ChargeCompletedEventRewards {
  eligibleEnergy: number;
  vehicleId: string | null;
}

export interface RewardPayoutRefundEvent {
  action: 'REWARD_PAYOUT_REFUND';
  data: RewardPayoutRefundData;
}

export interface RewardPayoutRefundData {
  userId: string;
  subscriptionId: string;
  rewardsTransactionId: string;
  walletId: string;
  idempotencyKey: string;
}

export interface RenewAllowanceEvent {
  type: 'renew-miles-allowance';
  userId: string;
  idempotencyKey: string;
  miles: number;
  renewalBehaviour: `SET` | 'ADD';
}
