import { BaseEventRoute } from '@experience/mobile/nest/sqs-event-router';
import { RenewAllowanceEvent } from './sqs.types';
import { EventService } from '../../application/services/event.service';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class RenewAllowanceRoute implements BaseEventRoute {
  private logger: Logger = new Logger(RenewAllowanceRoute.name);

  constructor(private readonly eventService: EventService) {}

  private isValid(
    decodedBody: ReturnType<JSON['parse']>
  ): decodedBody is RenewAllowanceEvent {
    return decodedBody['type'] === 'renew-miles-allowance';
  }

  async handleEvent(message: unknown): Promise<boolean> {
    if (!this.isValid(message)) {
      return false;
    }

    this.logger.log({ message }, 'processing renew allowance event');

    const { userId, idempotencyKey, miles, renewalBehaviour } = message;

    await this.eventService.processRenewAllowanceEvent(
      userId,
      idempotencyKey,
      miles,
      renewalBehaviour
    );

    return true;
  }
}
