import { AccountModule } from './account.module';
import { ClsService } from 'nestjs-cls';
import { DatabaseModule } from './database.module';
import { DependencyInjectionToken } from './constants';
import { ENTITY_MANAGER } from '@experience/mobile/driver-account/database';
import { EntityManager } from 'typeorm';
import { Logger, Module, forwardRef } from '@nestjs/common';
import { SubscriptionModule } from './subscription.module';
import { TypeOrmTransactionProvider } from '@experience/mobile/nest/typeorm-transactions';
import { TypeOrmWalletRepository } from '../infrastructure/type-orm/wallet.repository';
import { WalletController } from '../interfaces/http/wallet.controller';
import { WalletService } from '../application/services/wallet.service';
import { WalletTransformer } from '../infrastructure/type-orm/wallet.transformer';

@Module({
  imports: [
    DatabaseModule,
    forwardRef(() => AccountModule),
    SubscriptionModule,
  ],
  controllers: [WalletController],
  providers: [
    TypeOrmWalletRepository,
    TypeOrmTransactionProvider,
    WalletService,
    {
      provide: TypeOrmWalletRepository,
      inject: [ENTITY_MANAGER, ClsService],
      useFactory: (
        entityManger: EntityManager,
        cls: ClsService
      ): TypeOrmWalletRepository =>
        new TypeOrmWalletRepository(
          new Logger(TypeOrmWalletRepository.name),
          new WalletTransformer(),
          entityManger,
          cls
        ),
    },
    {
      provide: DependencyInjectionToken.WALLET_SERVICE_LOGGER,
      useFactory: () => new Logger(WalletService.name),
    },
    {
      provide: DependencyInjectionToken.WALLETS_REPOSITORY,
      useExisting: TypeOrmWalletRepository,
    },
    {
      provide: DependencyInjectionToken.TRANSACTION_PROVIDER,
      useExisting: TypeOrmTransactionProvider,
    },
  ],
  exports: [WalletService],
})
export class WalletModule {}
