process.env.DRIVER_AUTH_ISSUER_URL = 'http://localhost/';
process.env.DRIVER_AUTH_AUDIENCE = 'test_client';
process.env.DRIVER_AUTH_JWKS = `{
    "kty": "RSA",
    "e": "AQ<PERSON>",
    "kid": "",
    "n": "ql5F2L0HkrBGhVA1Zx_zAxfwzVAFmd2Jx2wDr7lf_SJ020-2H89klAMj2mdmZ_fZDmNlFzLTcaRu55aFaTuKQZS-PcowOXIwMwVi2OVVpWW8eBXXTk2S9zCJUou4WJ13IXdd7oSuQO3mVqJwlD1zsrgISFQcJdDQtyABEhFrN0B4cw_oGj7F__efqjrJLf_NkfwMYBpGZc6CKhE3BsQOGzwxLEook8GgPEO_RVoVmbHfsJE_7N19xVFzirecaY0JzAqyYgtYLLusayCfYql6ZNTYV2PcMbiRzZd6TObO_kl9Z_iAdvRc5ff5da_vSZSyFo0hGJVEXpUBn56MuTXhzw"
}`;
process.env.DRIVER_AUTH_PRIVATE_KEY = `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;
process.env.GIP_AUTH_ISSUER_URL = 'http://localhost:9090/';
process.env.GIP_AUDIENCE = 'demo-experience';
process.env.GIP_AUTH_JWKS = `{
  "kty": "RSA",
  "n": "6SabnHnp5e8ND2wM5P9k84yJXrzEz7YZntKpvVS5bPDg1-3w_r1Emh3JP2c7mTKNJOiS3BCye89LHcphZrIk-l-ZCKpE7UtLjB4aFZlnEXdGFY28q377Iy3IW6FtdMAIyom2RK7odVxr6lv2T0UKJdlit6Dqd-FJNG1996wRtBx6k24U0ODV4b0e4KA7ac0nYcZ2p4AKuPlkANbxtxfPXvbKW-Wk7MgDlB6RuEHx6TMW_hQxq2d1iipwgvNLAMsKa2ZWzBdr7hjmubuK3S20JUlFgT-P6_zkrdhA9q-_gdK5iSQWrZTtcqvTiHdE_WeBNg9M0C6moQ5DE-4EwsdYLQ",
  "e": "AQAB",
  "alg": "RS256"
}`;
process.env.GIP_AUTH_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;
process.env.API3_BASE_URL = 'http://localhost:6666';
process.env.FIREBASE_AUTH_EMULATOR_HOST = '127.0.0.1:9099';
process.env.FIREBASE_API_KEY = 'AIzaSyC1T56Z4wYWNos1rYtgEw24IumpQ-BlWW4';
process.env.FIREBASE_APP_ID = 'test';
process.env.FIREBASE_AUTH_DOMAIN = 'test';
process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';
process.env.FIREBASE_MESSAGING_SENDER_ID = '10000000000';
process.env.FIREBASE_PRIVATE_KEY =
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
process.env.FIREBASE_PROJECT_ID = 'demo-experience';
process.env.API3_CLIENT_ID = '1';
process.env.API3_CLIENT_SECRET = 'secret';
process.env.AUTH_SERVICE_USER_URL =
  'https://auth-staging.pod-point.com/api/v1/user';
process.env.DB_USERNAME = 'user';
process.env.DB_PASSWORD = 'password';
process.env.DB_DATABASE = 'podpoint';
process.env.DB_HOST = 'localhost';
process.env.DB_HOST_RO = 'localhost';
process.env.DB_PORT = '3308';
process.env.DB_LOGGING = 'false';
process.env.DRIVER_ACCOUNT_API_BASE_URL =
  'https://driver-account-api-staging.pod-point.com';
process.env.SMART_CHARGING_SERVICE_API_URL = 'http://localhost:5110';

process.env.AWS_ACCESS_KEY_ID = 'test';
process.env.AWS_SECRET_ACCESS_KEY = 'test';
process.env.AWS_SESSION_TOKEN = 'test';

process.env.AWS_REGION = 'eu-west-1';
process.env.AWS_ENDPOINT_URL = 'http://localhost:4566';
process.env.EXPENSES_REPORT_QUEUE_URL =
  'http://sqs.eu-west-1.localhost.localstack.cloud:4566/************/generate_reports';
process.env.FIRMWARE_UPGRADE_BASE_URL =
  'http://firmware-upgrade-service.example.com';
process.env.ASSETS_SERVICE_API_BASE_URL = 'http://assets-api.example.com';
process.env.ASSETS_CONFIGURATION_API_BASE_URL = 'http://localhost:5107';
process.env.BILLING_API_BASE_URL = 'http://localhost:5108';
process.env.VEHICLE_API_BASE_URL = 'http://localhost:5109';
process.env.SUBSCRIPTIONS_API_BASE_URL = 'http://localhost:5120';
process.env.REWARDS_API_BASE_URL = 'http://localhost:5115';
process.env.LOYALTY_CARD_SERVICE_API_BASE_URL = 'http://localhost:5111';
process.env.PAYMENTS_API_BASE_URL = 'http://localhost:5121';
process.env.ENODE_OAUTH_BASE_URL = 'http://localhost:5112';
process.env.ENODE_API_BASE_URL = 'http://localhost:8888';
process.env.ENODE_CLIENT_ID = 'enode-client-id';
process.env.ENODE_CLIENT_SECRET = 'enode-client-secret';
process.env.CUSTOM_API_TOKEN = 'dsfsdfsdfsd';
process.env.REMOTE_LOCK_UNSUPPORTED_CHARGERS = 'solo,solo3';
process.env.ENODE_VEHICLE_JSON_URL =
  'https://cdn.pod-point.com/home-app/vehicles.json';
process.env.APPUPGRADE_BASE_URL = 'http://localhost:7777';
process.env.TARIFFS_API_URL = 'http://localhost:5113';
process.env.AXLE_API_BASE_URL = 'http://localhost:5114';
process.env.AXLE_API_USERNAME = 'podpoint';
process.env.AXLE_API_PASSWORD = 'very secure password';
process.env.COHORTS_BASE_URL = 'https://cdn.pod-point.com/home-app/dev/cohorts';
process.env.OCPI_API_BASE_URL = 'http://localhost:6102';
process.env.SITE_ADMIN_API_BASE_URL = 'http://localhost:4102';
process.env.ACQUIRED_API_BASE_URL = 'http://localhost:8989/v1';
process.env.ACQUIRED_API_APP_ID = '1234';
process.env.ACQUIRED_API_APP_KEY = 'super-secure-i-love-security';
process.env.POD_APP_MAGIC_LINK_URL =
  'https://identity-dev.podenergy.com/pod/email-login';
process.env.COP_TEST_MODE = 'true';
