import {
  CarbonIntensityApi,
  ChargersApi,
  Configuration,
} from '@experience/shared/axios/data-platform-api-client';
import { ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { AuthenticationModule } from '../authentication/authentication.module';
import { CacheModule } from '@nestjs/cache-manager';
import { CarbonController } from './carbon.controller';
import { CarbonService } from './carbon.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [AuthenticationModule, CacheModule.register(), UsersModule],
  controllers: [CarbonController],
  providers: [
    ConfigService,
    CarbonService,
    {
      inject: [ConfigService],
      provide: CarbonIntensityApi,
      useFactory: async (configService: ConfigService) =>
        new CarbonIntensityApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: ChargersApi,
      useFactory: async (configService: ConfigService) =>
        new ChargersApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
  ],
})
export class CarbonModule {}
