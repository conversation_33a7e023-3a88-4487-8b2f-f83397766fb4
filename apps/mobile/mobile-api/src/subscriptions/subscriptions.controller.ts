import {
  ActionUpdateDTO,
  AffordabilityActionDTO,
  AffordabilityActionV2DTO,
  CreateSubscriptionDTO,
  FormDataDTO,
  SetupDirectDebitActionDTO,
  SubscriptionCheckAffordabilityActionDTO,
  SubscriptionCheckAffordabilityV2ActionDTO,
  SubscriptionConfirmChargerAddressAndMPANActionDTO,
  SubscriptionConfirmationOfPayeeDTO,
  SubscriptionConfirmationOfPayeeResponse,
  SubscriptionDTO,
  SubscriptionDirectDebitDTO,
  SubscriptionDocumentsDTO,
  SubscriptionInstallChargingStationActionDTO,
  SubscriptionListDTO,
  SubscriptionPayUpfrontFeeActionDTO,
  SubscriptionSetupDirectDebitActionDTO,
  SubscriptionSignDocumentsActionDTO,
  SubscriptionSignRewardsTOSActionDTO,
  SubscriptionSurveyActionDTO,
  UpdateConfirmChargerAddressAndMPANActionDTO,
  UpdateHomeSurveyActionDTO,
  UpdateLinkExistingChargerActionDTO,
  UpdateSignDocumentsActionDTO,
  UpdateSignRewardsTOSActionDTO,
} from './subscriptions.types';
import {
  ApiBadRequestException,
  ApiConflictException,
  ApiForbiddenException,
  ApiInternalServerException,
  ApiNotFoundException,
  ApiUnauthorizedException,
} from '@experience/mobile/nest/swagger';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { AuthGuardToken } from '../authentication/authguard.token';
import {
  Body,
  Controller,
  Get,
  Header,
  Param,
  Patch,
  Post,
  StreamableFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ITokenAuthUser,
  TokenAuthUser,
} from '@experience/mobile/nest/authorisation';
import { PersistedSubscriptionDTOActionsInner } from '@experience/mobile/subscriptions-api/axios';
import { SubscriptionsInterceptor } from './subscriptions.interceptor';
import { SubscriptionsService } from './subscriptions.service';
import { readFile } from 'fs/promises';
import path from 'path';

@ApiBearerAuth()
@ApiTags('Subscriptions')
@Controller('subscriptions')
@UseGuards(AuthGuardToken)
@UseInterceptors(SubscriptionsInterceptor)
export class SubscriptionsController {
  constructor(private readonly subscriptionsService: SubscriptionsService) {}

  @Get()
  @ApiOperation({
    description: 'Lists all the subscriptions associated with the user',
  })
  @ApiOkResponse({
    type: SubscriptionListDTO,
  })
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async getSubscriptions(
    @TokenAuthUser() user: ITokenAuthUser
  ): Promise<SubscriptionListDTO> {
    const data = await this.subscriptionsService.getSubscriptions(user.uuid);

    return { data };
  }

  @Get(':subscriptionId/documents')
  @ApiOperation({
    description: 'Lists all documents associated with a subscription',
  })
  @ApiOkResponse({
    type: SubscriptionDocumentsDTO,
    description: 'Retrieved subscription documents',
  })
  @ApiParam({
    name: 'subscriptionId',
    description: 'The ID of the subscription',
    format: 'uuid',
  })
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async getSubscriptionDocuments(
    @TokenAuthUser() user: ITokenAuthUser,
    @Param('subscriptionId') subscriptionId: string
  ): Promise<SubscriptionDocumentsDTO> {
    return this.subscriptionsService.getSubscriptionDocuments(
      user.uuid,
      subscriptionId
    );
  }

  @Get(':subscriptionId/documents/:documentId')
  @Header('Content-Type', 'application/pdf')
  @ApiOperation({
    summary: 'Get subscription document',
    description: 'Downloads the PDF file of a given document',
  })
  @ApiNotFoundException('Returned when subscription or document does not exist')
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    type: String,
    format: 'uuid',
  })
  @ApiParam({
    description: 'The ID of the document',
    name: 'documentId',
    type: String,
  })
  async getSubscriptionDocument(
    @TokenAuthUser() user: ITokenAuthUser,
    @Param('subscriptionId') subscriptionId: string,
    @Param('documentId') documentId: string
  ): Promise<StreamableFile> {
    const stream = await this.subscriptionsService.getSubscriptionDocument(
      user.uuid,
      subscriptionId,
      documentId
    );

    return new StreamableFile(stream);
  }

  @Get(':subscriptionId/direct-debit')
  @ApiOperation({
    description:
      'Retrieves direct debit details associated with a subscription',
  })
  @ApiOkResponse({
    type: SubscriptionDirectDebitDTO,
    description: 'Retrieved subscription direct debit details',
  })
  @ApiNotFoundException('Returned when direct debit details are unavailable')
  @ApiParam({
    name: 'subscriptionId',
    description: 'The ID of the subscription',
    format: 'uuid',
  })
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async getSubscriptionDirectDebit(
    @TokenAuthUser() user: ITokenAuthUser,
    @Param('subscriptionId') subscriptionId: string
  ): Promise<SubscriptionDirectDebitDTO> {
    return this.subscriptionsService.getSubscriptionDirectDebitDetails(
      user.uuid,
      subscriptionId
    );
  }

  @Patch(':subscriptionId/actions/:actionId')
  @ApiOperation({
    description: 'Updates a given action of a subscription',
  })
  @ApiExtraModels(
    AffordabilityActionDTO,
    AffordabilityActionV2DTO,
    SetupDirectDebitActionDTO,
    UpdateHomeSurveyActionDTO,
    SubscriptionCheckAffordabilityActionDTO,
    SubscriptionCheckAffordabilityV2ActionDTO,
    SubscriptionSetupDirectDebitActionDTO,
    SubscriptionConfirmChargerAddressAndMPANActionDTO,
    SubscriptionSurveyActionDTO,
    UpdateSignDocumentsActionDTO,
    UpdateSignRewardsTOSActionDTO,
    UpdateLinkExistingChargerActionDTO,
    UpdateConfirmChargerAddressAndMPANActionDTO
  )
  @ApiBody({
    schema: {
      oneOf: [
        { $ref: getSchemaPath(AffordabilityActionDTO) },
        { $ref: getSchemaPath(AffordabilityActionV2DTO) },
        { $ref: getSchemaPath(SetupDirectDebitActionDTO) },
        { $ref: getSchemaPath(UpdateHomeSurveyActionDTO) },
        { $ref: getSchemaPath(UpdateSignDocumentsActionDTO) },
        { $ref: getSchemaPath(UpdateConfirmChargerAddressAndMPANActionDTO) },
        { $ref: getSchemaPath(UpdateSignRewardsTOSActionDTO) },
        { $ref: getSchemaPath(UpdateLinkExistingChargerActionDTO) },
      ],
    },
  })
  @ApiOkResponse({
    schema: {
      oneOf: [
        { $ref: getSchemaPath(SubscriptionCheckAffordabilityActionDTO) },
        { $ref: getSchemaPath(SubscriptionCheckAffordabilityV2ActionDTO) },
        { $ref: getSchemaPath(SubscriptionSetupDirectDebitActionDTO) },
        { $ref: getSchemaPath(SubscriptionSurveyActionDTO) },
        { $ref: getSchemaPath(SubscriptionSignDocumentsActionDTO) },
        {
          $ref: getSchemaPath(
            SubscriptionConfirmChargerAddressAndMPANActionDTO
          ),
        },
        { $ref: getSchemaPath(SubscriptionSignRewardsTOSActionDTO) },
      ],
    },
  })
  @ApiNotFoundException(
    'Returned if the user does own a subscription with the given ID'
  )
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async updateSubscriptionAction(
    @Param('subscriptionId') subscriptionId: string,
    @Param('actionId') actionId: string,
    @Body() payload: ActionUpdateDTO,
    @TokenAuthUser() user: ITokenAuthUser
  ) {
    return this.subscriptionsService.updateSubscriptionAction(
      user.uuid,
      subscriptionId,
      actionId,
      payload
    );
  }

  @Post('actions/SETUP_DIRECT_DEBIT_V1/confirmation-of-payee')
  @ApiOperation({
    description: 'Performs a confirmation of payee check',
  })
  @ApiCreatedResponse({
    type: SubscriptionConfirmationOfPayeeResponse,
  })
  @ApiUnauthorizedException()
  @ApiBadRequestException()
  @ApiInternalServerException()
  async doConfirmationOfPayeeCheck(
    @TokenAuthUser() user: ITokenAuthUser,
    @Body() body: SubscriptionConfirmationOfPayeeDTO
  ): Promise<SubscriptionConfirmationOfPayeeResponse> {
    return this.subscriptionsService.doConfirmationOfPayeeCheck(
      user.uuid,
      body
    );
  }

  @Get(':id')
  @ApiOperation({
    description: 'Get a subscription by id',
  })
  @ApiOkResponse({
    type: SubscriptionDTO,
  })
  @ApiBadRequestException('Subscription ID is not a UUID')
  @ApiNotFoundException('Subscription not found')
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async getSubscriptionById(
    @TokenAuthUser() user: ITokenAuthUser,
    @Param('id') id: string
  ): Promise<SubscriptionDTO> {
    return this.subscriptionsService.getSubscriptionById(user.uuid, id);
  }

  @Get(':subscriptionId/actions/:actionId')
  @ApiOperation({
    description: 'Get a subscription action by id',
  })
  @ApiExtraModels(
    AffordabilityActionDTO,
    AffordabilityActionV2DTO,
    SetupDirectDebitActionDTO,
    SubscriptionSurveyActionDTO,
    SubscriptionInstallChargingStationActionDTO,
    SubscriptionCheckAffordabilityActionDTO,
    SubscriptionCheckAffordabilityV2ActionDTO,
    SubscriptionSetupDirectDebitActionDTO,
    SubscriptionSignDocumentsActionDTO,
    SubscriptionPayUpfrontFeeActionDTO
  )
  @ApiOkResponse({
    schema: {
      oneOf: [
        { $ref: getSchemaPath(SubscriptionSurveyActionDTO) },
        { $ref: getSchemaPath(SubscriptionInstallChargingStationActionDTO) },
        { $ref: getSchemaPath(SubscriptionCheckAffordabilityActionDTO) },
        { $ref: getSchemaPath(SubscriptionCheckAffordabilityV2ActionDTO) },
        { $ref: getSchemaPath(SubscriptionSetupDirectDebitActionDTO) },
        { $ref: getSchemaPath(SubscriptionSignDocumentsActionDTO) },
        { $ref: getSchemaPath(SubscriptionPayUpfrontFeeActionDTO) },
      ],
    },
  })
  @ApiNotFoundException('Subscription action not found')
  @ApiUnauthorizedException()
  async getSubscriptionActionById(
    @TokenAuthUser() user: ITokenAuthUser,
    @Param('subscriptionId') subscriptionId: string,
    @Param('actionId') actionId: string
  ): Promise<PersistedSubscriptionDTOActionsInner> {
    return await this.subscriptionsService.getSubscriptionActionById(
      user.uuid,
      subscriptionId,
      actionId
    );
  }

  @ApiOperation({
    description: 'Get the form data for check affordability action',
  })
  @ApiOkResponse({
    type: FormDataDTO,
    description: 'Returns the form data for check affordability action',
  })
  @ApiUnauthorizedException()
  @Get('actions/CHECK_AFFORDABILITY_V1/form-data')
  async getCheckAffordabilityFormData(): Promise<FormDataDTO> {
    const json = await readFile(
      path.join(
        './assets/mobile-api/json-schema-forms/affordability-form.json'
      ),
      'utf8'
    );

    return JSON.parse(json);
  }

  @ApiOperation({
    description: 'Get the form data for setup direct debit action',
  })
  @ApiOkResponse({
    type: FormDataDTO,
    description: 'Returns the form data for setup direct debit action',
  })
  @ApiUnauthorizedException()
  @Get('actions/SETUP_DIRECT_DEBIT_V1/form-data')
  async getSetupDirectDebitFormData(): Promise<FormDataDTO> {
    const json = await readFile(
      path.resolve(
        './assets/mobile-api/json-schema-forms/direct-debit-form-fixed.json'
      ),
      'utf8'
    );

    return JSON.parse(json);
  }

  @Post()
  @ApiOperation({
    description: 'Allows a self-serviceable subscription to be created',
  })
  @ApiCreatedResponse()
  @ApiBadRequestException()
  @ApiUnauthorizedException()
  @ApiForbiddenException(
    'Returned if user is not linked to the charger they are trying to create a subscription for'
  )
  @ApiConflictException(
    'Returned if an active subscription already exists for the given PPID'
  )
  @ApiInternalServerException()
  async createSubscription(
    @TokenAuthUser() user: ITokenAuthUser,
    @Body() body: CreateSubscriptionDTO
  ) {
    return this.subscriptionsService.createSubscription(user.uuid, body);
  }
}
