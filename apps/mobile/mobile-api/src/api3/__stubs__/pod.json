{"pods": {"id": 13718, "name": "<PERSON><PERSON>-<PERSON>", "ppid": "PG-95721", "payg": true, "home": false, "public": false, "evZone": false, "location": {"lat": 51.511378, "lng": -2.57713}, "address_id": 8146, "description": "This is an open charge unit installed at the Airbus - Car Park 39 (Barnwell) site at Filton Pegasus House, Aerospace Avenue, Bristol", "commissioned_at": "2019-05-10T09:00:00+00:00", "created_at": "2019-06-19T15:13:06+00:00", "last_contact_at": "2023-09-20T08:19:09+00:00", "contactless_enabled": false, "unit_id": 54391, "timezone": "UTC", "model": {"id": 102, "name": "S7-UP-6mA-2", "vendor": "Pod Point", "supports_payg": true, "supports_ocpp": false, "supports_contactless": false, "image_url": "https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg"}, "price": {"id": 404, "name": "Airbus PAYG Tariff", "currency": "GBP", "cost": [{"start": 0, "price": 20, "description": "£0.20 per kWh", "type": "energy", "begin_time": "00:00:00", "begin_day": 0, "end_time": "00:00:00", "end_day": 0}]}, "charges": [], "advert": null, "unit_connectors": [{"status": {"id": 1, "name": "Available", "key_name": "available", "label": "Available"}, "connector": {"id": 103, "door": "A", "door_id": 1, "power": 7, "current": 32, "voltage": 230, "charge_method": "Single Phase AC", "has_cable": false, "socket": {"type": "IEC 62196-2 Type 2", "description": "Type 2 socket", "ocpp_name": "sType2", "ocpp_code": 3}}}], "range": {"id": 5, "name": "soloArch2"}}}