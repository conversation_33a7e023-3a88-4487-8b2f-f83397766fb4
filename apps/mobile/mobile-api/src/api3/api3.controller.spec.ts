import { Api3<PERSON>ontroller } from './api3.controller';
import { Api3Service } from './api3.service';
import { AuthGuardToken } from '../authentication/authguard.token';
import { AuthenticationModule } from '../authentication/authentication.module';
import { AuthorisationModule } from '@experience/mobile/nest/authorisation';
import { BadRequestException, INestApplication } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import { FlagsService } from '@experience/shared/nest/remote-config';
import { LocalesBadRequestError } from '../locales/locales.exception';
import { TEST_FIRMWARE_STATUS_TYPES } from '@experience/shared/axios/firmware-upgrade-client/fixtures';
import { TariffRequest, UserResponse } from './api3.types';
import { Test, TestingModule } from '@nestjs/testing';
import { UserNotFoundException } from '../users/users.exception';
import { api3Providers } from './api3.providers';
import { createMock } from '@golevelup/ts-jest';
import {
  defaultMockAuthenticationOptions,
  mockAuthenticatedUser,
  mockAuthenticatedUserWithMFA,
} from '../test.utils';
import locales from './__stubs__/locales.json';
import request from 'supertest';
import users from './__stubs__/users.json';

jest.mock('./api3.service');
describe('Api3Controller', () => {
  let app: INestApplication;
  let module: TestingModule;
  let service: Api3Service;
  let token: string;
  const usersResponse = Object.assign(new UserResponse(), users);
  const tariffRequest: TariffRequest = {
    user_id: 10,
    energy_supplier_id: null,
    tiers: [{ rate: 1.1 }],
  };

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        AuthorisationModule,
        AuthenticationModule,
        ConfigModule,
        CacheModule.register(),
      ],
      controllers: [Api3Controller],
      providers: [
        {
          provide: FlagsService,
          useValue: createMock<FlagsService>(),
        },
        Api3Service,
        AuthGuardToken,
        ...api3Providers,
      ],
    }).compile();

    service = module.get<Api3Service>(Api3Service);

    app = module.createNestApplication();
    await app.init();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await app.close();
  });
  describe('api3 auth endpoint', () => {
    it('should return user info', async () => {
      token = mockAuthenticatedUser();
      const mockRestrictions = jest
        .spyOn(service, 'getUser')
        .mockResolvedValueOnce(usersResponse);

      const response = await request(app.getHttpServer())
        .get(`/api3/v5/auth`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockRestrictions).toHaveBeenCalledTimes(1);
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(users);
    });

    it('should return an unauthorised error', async () => {
      const response = await request(app.getHttpServer()).get(`/api3/v5/auth`);

      expect(response.status).toEqual(401);
    });

    it('should return a bad request error if received via the service', async () => {
      token = mockAuthenticatedUser();
      jest
        .spyOn(service, 'getUser')
        .mockRejectedValue(new BadRequestException());

      const response = await request(app.getHttpServer())
        .get(`/api3/v5/auth`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(400);
    });

    it('should return a not found error if received via the service', async () => {
      mockAuthenticatedUser();
      jest
        .spyOn(service, 'getUser')
        .mockRejectedValue(new UserNotFoundException());

      const response = await request(app.getHttpServer())
        .get(`/api3/v5/auth`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(500);
    });
  });

  describe('get current firmware', () => {
    it('For current firmware version request should return a 200 if it is successful', async () => {
      jest
        .spyOn(service, 'getCurrentFirmware')
        .mockResolvedValue(TEST_FIRMWARE_STATUS_TYPES);

      const response = await request(app.getHttpServer()).get(
        `/api3/v5/units/PSL1234/firmware`
      );

      expect(response.status).toEqual(200);
      expect(response.body).toEqual({ data: TEST_FIRMWARE_STATUS_TYPES });
    });
  });

  describe('store tariff info', () => {
    it('should store tariff info', async () => {
      token = mockAuthenticatedUser();
      const mockStoreTariff = jest
        .spyOn(service, 'storeTariff')
        .mockResolvedValueOnce({ id: '110' });

      const response = await request(app.getHttpServer())
        .post(`/api3/v5/tariffs`)
        .set('Authorization', 'Bearer ' + token)
        .send(tariffRequest);

      expect(mockStoreTariff).toHaveBeenCalledTimes(1);
      expect(mockStoreTariff).toHaveBeenCalledWith(
        defaultMockAuthenticationOptions.id,
        tariffRequest
      );
      expect(response.status).toEqual(201);
      expect(response.body).toEqual({ id: '110' });
    });

    it('should return an unauthorised error for tariffs endpoint', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api3/v5/tariffs`)
        .send(tariffRequest);

      expect(response.status).toEqual(401);
    });
  });

  describe('locales', () => {
    it('should get locales', async () => {
      const mockGetLocales = jest
        .spyOn(service, 'getLocales')
        .mockResolvedValueOnce(locales);

      const response = await request(app.getHttpServer()).get(
        `/api3/v5/locales`
      );

      expect(mockGetLocales).toHaveBeenCalledTimes(1);
      expect(mockGetLocales).toHaveBeenCalledWith('en');
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(locales);
    });

    it('should get locales when accept language is set to es', async () => {
      const mockGetLocales = jest
        .spyOn(service, 'getLocales')
        .mockResolvedValueOnce(locales);

      const response = await request(app.getHttpServer())
        .get(`/api3/v5/locales`)
        .set('Accept-Language', 'es');

      expect(mockGetLocales).toHaveBeenCalledTimes(1);
      expect(mockGetLocales).toHaveBeenCalledWith('es');
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(locales);
    });

    it('should throw a bad request when it cannot read the locales', async () => {
      jest
        .spyOn(service, 'getLocales')
        .mockRejectedValue(new LocalesBadRequestError());

      const response = await request(app.getHttpServer())
        .get(`/api3/v5/locales`)
        .set('Accept-Language', 'es');

      expect(response.status).toEqual(400);
      expect(response.body).toEqual({
        error: 'LOCALES_BAD_REQUEST',
        message: 'There was a problem with returning locales',
        statusCode: 400,
      });
    });
  });

  describe('claim charge request', () => {
    it('should proxy a claim charge request', async () => {
      token = mockAuthenticatedUserWithMFA();

      const CLAIM_CHARGE_RESPONSE = {
        id: 1,
      };

      const mockClaimCharge = jest
        .spyOn(service, 'claimCharge')
        .mockResolvedValue(CLAIM_CHARGE_RESPONSE);

      const response = await request(app.getHttpServer())
        .post('/api3/v5/charges')
        .set('Authorization', `Bearer ${token}`)
        .send({
          user: 1,
          pod: 2,
          door: 3,
          claimed_by: 4,
        });

      expect(mockClaimCharge).toHaveBeenCalledTimes(1);
      expect(mockClaimCharge).toHaveBeenCalledWith(
        {
          uuid: defaultMockAuthenticationOptions.authId,
          email: defaultMockAuthenticationOptions.email,
          id: defaultMockAuthenticationOptions.id,
        },
        {
          user: 1,
          pod: 2,
          door: 3,
          claimed_by: 4,
        }
      );

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(CLAIM_CHARGE_RESPONSE);
    });
  });

  describe('topup', () => {
    it('should proxy a top up account request', async () => {
      token = mockAuthenticatedUserWithMFA();

      const TOP_UP_RESPONSE = {};

      const mockTopUpAccount = jest
        .spyOn(service, 'topUpAccount')
        .mockResolvedValue(TOP_UP_RESPONSE);

      const response = await request(app.getHttpServer())
        .post('/api3/v5/users/1/account/topup')
        .set('Authorization', `Bearer ${token}`)
        .send({
          id: 1,
          currency: 'GBP',
          amount: 500,
          card_id: 'abc_123',
          token: '123_abc',
        });

      expect(mockTopUpAccount).toHaveBeenCalledTimes(1);
      expect(mockTopUpAccount).toHaveBeenCalledWith(
        {
          uuid: defaultMockAuthenticationOptions.authId,
          email: defaultMockAuthenticationOptions.email,
          id: defaultMockAuthenticationOptions.id,
        },
        '1',
        {
          id: 1,
          currency: 'GBP',
          amount: 500,
          card_id: 'abc_123',
          token: '123_abc',
        }
      );

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(TOP_UP_RESPONSE);
    });
  });
});
