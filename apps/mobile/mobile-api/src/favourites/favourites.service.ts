import { Inject, Injectable, Logger } from '@nestjs/common';
import { Favourites } from '@experience/shared/sequelize/podadmin';
import {
  FavouriteNotAddedError,
  FavouriteNotRemovedError,
  FavouritesNotFoundError,
} from './favourites.errors';
import {
  MapLocation,
  OcpiLocation,
  OcpiResponse,
} from '../map/location/location.types';
import { ConfigService } from '@nestjs/config';
import { v5 as uuid } from 'uuid';
import { convertSnakeCaseToSentenceCase } from '@experience/shared/typescript/utils';
import { UsersService } from '../users/users.service';

@Injectable()
export class FavouritesService {
  private readonly logger = new Logger(FavouritesService.name);

  constructor(
    @Inject('FAVOURITES_REPOSITORY')
    private readonly favouritesRepository: typeof Favourites,
    private readonly configService: ConfigService,
    private readonly usersService: UsersService
  ) {}

  async getFavourites(userUid: string): Promise<MapLocation[]> {
    this.logger.log({ userUid }, 'getting favourites for user');
    const user = await this.usersService.getPodAdminIdUserByAuthId(userUid);

    const podAddressIds = await this.favouritesRepository
      .findAll({
        where: { userId: user.id },
      })
      .then((entities) => entities.map((entity) => entity.podAddressId));

    if (podAddressIds.length === 0) {
      return [];
    }

    // https://datatracker.ietf.org/doc/html/rfc4122#section-4.3
    const namespace = '1628c735-51d5-48e4-8ca0-f4ad1e2c179a';
    // We map the pod address IDs to UUIDs in the OCPI namespace to get the location IDs.
    // We are following the same approach as the one used in the OCPI location mapper.
    const locationIds = podAddressIds.map((id) =>
      uuid(`location-${id}`, namespace)
    );

    const ocpiLocations = await Promise.all(
      locationIds.map(async (id) => this.getOcpiLocationById(id))
    ).then((locations) => locations.filter(Boolean));

    if (ocpiLocations.length === 0) {
      this.logger.error({ podAddressIds }, 'no pod addresses found for user');
      throw new FavouritesNotFoundError();
    }

    return ocpiLocations.map((ocpiLocation) => this.mapLocation(ocpiLocation));
  }

  async addToFavourites(locationId: string, userUid: string): Promise<void> {
    this.logger.log({ locationId, userUid }, 'adding to favourites');
    const user = await this.usersService.getPodAdminIdUserByAuthId(userUid);
    const podAddressId = await this.getPodAddressIdFromLocationId(locationId);

    if (!podAddressId) {
      throw new FavouriteNotAddedError();
    }

    await this.favouritesRepository
      .create({
        userId: user.id,
        podAddressId,
      })
      .catch((error) => {
        this.logger.error(
          { locationId, userId: user.id, error },
          'failed to add to favourites'
        );
        throw new FavouriteNotAddedError();
      });
  }

  async removeFromFavourites(
    locationId: string,
    userUid: string
  ): Promise<void> {
    this.logger.log({ locationId, userUid }, 'removing from favourites');
    const user = await this.usersService.getPodAdminIdUserByAuthId(userUid);

    const podAddressId = await this.getPodAddressIdFromLocationId(locationId);

    if (!podAddressId) {
      throw new FavouriteNotRemovedError();
    }

    await this.favouritesRepository
      .destroy({
        where: {
          userId: user.id,
          podAddressId,
        },
      })
      .then((count) => {
        if (count === 0) {
          this.logger.error(
            { locationId, userId: user.id },
            'no favourites found to remove'
          );
          throw new FavouritesNotFoundError();
        }
      });
  }

  private async getPodAddressIdFromLocationId(
    locationId: string
  ): Promise<number | null> {
    return fetch(
      `${this.configService.get<string>('OCPI_API_BASE_URL')}/ocpi/cpo/2.2.1/locations/${locationId}/podAdminSiteId`,
      {
        method: 'GET',
        headers: {
          Authorization: `Token ${this.configService.get<string>(
            'OCPI_API_TOKEN'
          )}`,
          'Content-Type': 'application/json',
        },
      }
    )
      .then((res) => {
        if (!res.ok) {
          this.logger.error({ locationId }, 'failed to fetch pod address ID');
          return null;
        }
        return res
          .json()
          .then((response: OcpiResponse<number>) => response.data);
      })
      .catch((error) => {
        this.logger.error(
          { locationId, error },
          'failed to fetch pod address ID'
        );
        return null;
      });
  }

  private async getOcpiLocationById(locationId: string): Promise<OcpiLocation> {
    return fetch(
      `${this.configService.get<string>('OCPI_API_BASE_URL')}/ocpi/cpo/2.2.1/locations/${locationId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Token ${this.configService.get<string>(
            'OCPI_API_TOKEN'
          )}`,
          'Content-Type': 'application/json',
        },
      }
    )
      .then((res) => {
        if (!res.ok) {
          this.logger.error({ locationId }, 'failed to fetch location');
          return null;
        }
        return res
          .json()
          .then((response: OcpiResponse<OcpiLocation>) => response.data);
      })
      .catch((error) => {
        this.logger.error({ locationId, error }, 'failed to fetch location');
        return null;
      });
  }

  private mapLocation(ocpiLocation: OcpiLocation): MapLocation {
    return {
      address: `${ocpiLocation.address}, ${ocpiLocation.city}, ${ocpiLocation.postal_code}`,
      id: ocpiLocation.id,
      coordinates: {
        lat: parseFloat(ocpiLocation.coordinates.latitude),
        long: parseFloat(ocpiLocation.coordinates.longitude),
      },
      fullyOccupied: ocpiLocation.evses.every(
        (evse) => evse.status === 'CHARGING'
      ),
      name: ocpiLocation.name,
      notices: ocpiLocation.directions?.map(({ text }) => text) ?? [],
      type: convertSnakeCaseToSentenceCase(ocpiLocation.facilities?.[0] ?? ''),
    };
  }
}
