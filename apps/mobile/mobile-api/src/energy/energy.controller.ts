import {
  ApiBearerAuth,
  ApiOkResponse,
  Api<PERSON>peration,
  ApiTags,
} from '@nestjs/swagger';
import {
  ApiInternalServerException,
  ApiUnauthorizedException,
} from '@experience/mobile/nest/swagger';
import { AuthGuardToken } from '../authentication/authguard.token';
import { AxiosHttpExceptionFilter } from '@experience/mobile/nest/exception';
import { Controller, Get, UseFilters, UseGuards } from '@nestjs/common';
import { EnergyService } from './energy.service';
import { SupplierDtoImpl } from './energy.types';

@ApiBearerAuth()
@ApiTags('Energy')
@Controller('energy')
@UseFilters(AxiosHttpExceptionFilter)
export class EnergyController {
  constructor(private readonly energyService: EnergyService) {}

  @Get('suppliers')
  @UseGuards(AuthGuardToken)
  @ApiOperation({
    summary: 'get a list of suppliers',
  })
  @ApiOkResponse({
    description: 'Returns a list of energy suppliers',
    type: [SupplierDtoImpl],
  })
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async getSuppliers(): Promise<SupplierDtoImpl[]> {
    return this.energyService.getSuppliers();
  }
}
