import 'whatwg-fetch';
import { CACHE_MANAGER, Cache, CacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import { UserManagementApi } from '@experience/shared/axios/enode-api-client';
import { createMock } from '@golevelup/ts-jest';
import { provideEnodeApi } from './enode-provider.factory';
import axios, { InternalAxiosRequestConfig } from 'axios';

describe('provideEnodeFactory()', () => {
  it('returns the given Enode API as a Nest.js provider', async () => {
    const result = provideEnodeApi(UserManagementApi);

    expect(result).toStrictEqual({
      provide: UserManagementApi,
      inject: [CACHE_MANAGER, ConfigService],
      useFactory: expect.any(Function),
    });

    const module = await Test.createTestingModule({
      imports: [CacheModule.register(), ConfigModule],
      providers: [result],
    }).compile();

    expect(module.get(UserManagementApi)).toBeInstanceOf(UserManagementApi);
  });

  it('uses the interceptor to refresh the access token', async () => {
    const module = await Test.createTestingModule({
      imports: [CacheModule.register(), ConfigModule],
      providers: [provideEnodeApi(UserManagementApi)],
    }).compile();

    const axiosPostMock = jest.spyOn(axios, 'post').mockResolvedValue({
      data: {
        access_token: 'token',
      },
    });

    const formDataSpy = jest.spyOn(FormData.prototype, 'append');

    const interceptor =
      module.get(UserManagementApi)['axios'].interceptors.request[
        'handlers'
      ][0]['fulfilled'];

    const config = await interceptor(createMock<InternalAxiosRequestConfig>());

    expect(formDataSpy).toHaveBeenCalledTimes(1);
    expect(formDataSpy).toHaveBeenCalledWith(
      'grant_type',
      'client_credentials'
    );

    expect(axiosPostMock).toHaveBeenCalledTimes(1);
    expect(axiosPostMock).toHaveBeenCalledWith(
      'https://oauth.sandbox.enode.io/oauth2/token',
      expect.any(FormData),
      {
        auth: {
          username: 'enode-client-id',
          password: 'enode-client-secret',
        },
      }
    );

    expect(config.headers['Authorization']).toEqual('Bearer token');
  });

  it('does not refresh the access token if cached', async () => {
    const module = await Test.createTestingModule({
      imports: [CacheModule.register(), ConfigModule],
      providers: [provideEnodeApi(UserManagementApi)],
    }).compile();

    const axiosPostMock = jest.spyOn(axios, 'post').mockResolvedValue({
      data: {
        access_token: 'token',
      },
    });

    const cacheManager = module.get<Cache>(CACHE_MANAGER);

    const interceptor =
      module.get(UserManagementApi)['axios'].interceptors.request[
        'handlers'
      ][0]['fulfilled'];

    expect(await cacheManager.get('enode_access_token')).toBeUndefined();

    const before = await interceptor(createMock<InternalAxiosRequestConfig>());

    expect(await cacheManager.get('enode_access_token')).toEqual('token');

    const after = await interceptor(createMock<InternalAxiosRequestConfig>());

    expect(await cacheManager.get('enode_access_token')).toEqual('token');

    expect(axiosPostMock).toHaveBeenCalledTimes(1);
    expect(before.headers['Authorization']).toEqual('Bearer token');
    expect(after.headers['Authorization']).toEqual('Bearer token');
  });
});
