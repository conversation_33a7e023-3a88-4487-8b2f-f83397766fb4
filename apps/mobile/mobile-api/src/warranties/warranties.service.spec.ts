import { AxiosResponse } from 'axios';
import {
  MOCK_SUBSCRIPTION,
  MOCK_SUBSCRIPTION_SA,
} from '../subscriptions/__fixtures__/subscriptions.interface';
import { SubscriptionNotFoundError } from '../subscriptions/subscriptions.errors';
import { SubscriptionsApi } from '@experience/mobile/subscriptions-api/axios';
import { Test } from '@nestjs/testing';
import { UnsupportedPlanTypeError } from './warranties.error';
import { WarrantiesService } from './warranties.service';
import { WarrantyProductCode } from './warranties.types';

describe('WarrantiesService', () => {
  let service: WarrantiesService;
  let subscriptionsApi: SubscriptionsApi;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        WarrantiesService,
        {
          provide: SubscriptionsApi,
          useValue: new SubscriptionsApi(),
        },
      ],
    }).compile();

    module.useLogger(false);

    service = module.get(WarrantiesService);
    subscriptionsApi = module.get(SubscriptionsApi);
  });

  afterEach(() => jest.resetAllMocks());

  describe(WarrantiesService.prototype.getWarranty, () => {
    it('throws SubscriptionNotFoundError if subscription not found', async () => {
      const getSubscriptions = jest
        .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
        .mockResolvedValue({
          data: {
            subscriptions: [],
          },
        } as AxiosResponse);

      const ppid = 'PSL-12345';

      await expect(service.getWarranty(ppid)).rejects.toThrow(
        SubscriptionNotFoundError
      );

      expect(getSubscriptions).toHaveBeenCalledTimes(1);
      expect(getSubscriptions).toHaveBeenCalledWith(undefined, ppid);
    });

    it('throws UnsupportedPlanTypeError if subscription is not PodDrive', async () => {
      const subscription = {
        ...MOCK_SUBSCRIPTION,
        plan: {
          ...MOCK_SUBSCRIPTION.plan,
          type: 'PodFly',
        },
      };

      const getSubscriptions = jest
        .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
        .mockResolvedValue({
          data: {
            subscriptions: [subscription],
          },
        } as AxiosResponse);

      const ppid = 'PSL-12345';

      await expect(service.getWarranty(ppid)).rejects.toThrow(
        UnsupportedPlanTypeError
      );

      expect(getSubscriptions).toHaveBeenCalledTimes(1);
      expect(getSubscriptions).toHaveBeenCalledWith(undefined, ppid);
    });

    it.each([MOCK_SUBSCRIPTION, MOCK_SUBSCRIPTION_SA])(
      'retrieves warranty details from subscription for type $plan.type',
      async (subscription) => {
        const getSubscriptions = jest
          .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
          .mockResolvedValue({
            data: {
              subscriptions: [subscription],
            },
          } as AxiosResponse);

        const ppid = 'PSL-12345';

        expect(await service.getWarranty(ppid)).toEqual({
          startDate: '2025-02-01',
          endDate: '2026-08-01',
          slaHours: 48,
          productCode: WarrantyProductCode.LIFETIME_48_HOURS,
        });

        expect(getSubscriptions).toHaveBeenCalledTimes(1);
        expect(getSubscriptions).toHaveBeenCalledWith(undefined, ppid);
      }
    );
  });
});
