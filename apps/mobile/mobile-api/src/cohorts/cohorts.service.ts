import { Cache } from '@nestjs/cache-manager';
import { Cohort, CohortCache, CohortData } from './cohorts.types';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class CohortsService {
  private static readonly COHORT_CACHE_TTL = 3_600_000;
  private readonly logger = new Logger(CohortsService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly cacheManager: Cache
  ) {}

  private async getCohort(cohort: Cohort): Promise<CohortCache> {
    const cacheKey = `${cohort}_COHORTS`;

    const cohortCache = await this.cacheManager.get<CohortCache | undefined>(
      cacheKey
    );

    const chargers = cohortCache?.chargers ?? new Set();
    const users = cohortCache?.users ?? new Set();

    this.logger.log(
      { cohort, chargers: chargers.size, users: users.size, cacheKey },
      'got enabled chargers and users from cache'
    );

    const cohortsBaseUrl =
      this.configService.getOrThrow<string>('COHORTS_BASE_URL');

    if (!cohortCache) {
      this.logger.log(
        { cohort, cohortsBaseUrl },
        'no matching cohort in cache, getting from cdn'
      );

      const { data } = await axios.get<CohortData[]>(
        `${cohortsBaseUrl}/${cohort.replaceAll('_', '-').toLowerCase()}.json`
      );

      for (const cohort of data) {
        for (const charger of cohort.chargers ?? []) {
          chargers.add(charger);
        }

        for (const user of cohort.users ?? []) {
          users.add(user);
        }
      }

      this.logger.log(
        { cohort, chargers: chargers.size, users: users.size },
        'got chargers and users from cdn'
      );

      this.cacheManager.set(
        cacheKey,
        { chargers, users },
        CohortsService.COHORT_CACHE_TTL
      );

      this.logger.log(
        { cohort, chargers: chargers.size, users: users.size, cacheKey },
        'set chargers and users in cache'
      );
    }

    return { chargers, users };
  }

  async isUserInCohort(authId: string, cohort: Cohort): Promise<boolean> {
    try {
      const { users } = await this.getCohort(cohort);

      return users.has(authId);
    } catch (error) {
      this.logger.error(
        { authId, cohort, error },
        'failed to check if user is in cohort'
      );

      return false;
    }
  }

  async isChargerInCohort(ppid: string, cohort: Cohort): Promise<boolean> {
    try {
      const { chargers } = await this.getCohort(cohort);

      return chargers.has(ppid);
    } catch (error) {
      this.logger.error(
        { ppid, cohort, error },
        'failed to check if charger is in cohort'
      );

      return false;
    }
  }
}
