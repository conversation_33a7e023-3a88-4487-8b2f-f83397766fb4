import { ApiProperty, refs } from '@nestjs/swagger';
import {
  ChargeDetailDto,
  ConnectedChargeState,
  ConnectedChargeStatePowerDeliveryStateEnum,
  ConnectedStatefulVehicleDto,
  CreateVehicleLinkRequestDto,
  CreateVehicleRequestDto,
  DelegatedControlChargingStationResponseDto,
  ExtendedVehicleLinkResponseDto,
  GenericChargeState,
  GenericStatefulVehicleDto,
  InterventionDto,
  SetDelegatedControlIntentsResponseDto,
  UpdateVehicleLinkRequestDto,
  VehicleChargeInfoDto,
  VehicleChargeInfoDtoCannotMeetTargetReasonEnum,
  VehicleChargeInfoDtoChargingStation,
  VehicleChargeInfoDtoTariffTypeEnum,
  VehicleInformation,
  VehicleIntentEntryDto,
  VehicleIntentEntryDtoDayOfWeekEnum,
  VehicleIntentsRequestDto,
  VehicleIntentsResponseDto,
  VehicleLinkResponseDto,
} from '@experience/shared/axios/smart-charging-service-client';
import {
  DelegatedControlStatus,
  DelegatedControlStatusEnum,
} from '../chargers/chargers.types';

export class ChargersAndVehicles {
  ppid: string;
  vehicles?: ExtendedVehicleLinksResponseDtoImpl[];
}

class ConnectedChargeStateImpl implements ConnectedChargeState {
  @ApiProperty({
    description: 'Battery capacity',
    type: Number,
    example: 78,
  })
  batteryCapacity: number;

  @ApiProperty({
    description: 'Battery level percent',
    type: Number,
    example: 78,
    nullable: true,
  })
  batteryLevelPercent: number;

  @ApiProperty({
    description: 'Charge limit percent',
    type: Number,
    example: 78,
    nullable: true,
  })
  chargeLimitPercent: number;

  @ApiProperty({
    description: 'Charge rate',
    type: Number,
    example: 78,
    nullable: true,
  })
  chargeRate: number;

  @ApiProperty({
    description: 'Charge time remaining',
    type: Number,
    example: 78,
    nullable: true,
  })
  chargeTimeRemaining: number;

  @ApiProperty({
    description: 'Is charging?',
    type: Boolean,
    example: true,
    nullable: true,
  })
  isCharging: boolean;

  @ApiProperty({
    description: 'Is fully charged?',
    type: Boolean,
    example: true,
    nullable: true,
  })
  isFullyCharged: boolean;

  @ApiProperty({
    description: 'Is plugged in?',
    type: Boolean,
    example: true,
    nullable: true,
  })
  isPluggedIn: boolean;

  @ApiProperty({
    description: 'Last updated',
    type: String,
    example: '2021-08-12T09:00:00Z',
    nullable: true,
  })
  lastUpdated: string;

  @ApiProperty({
    description: 'Max current',
    type: Number,
    example: 32,
    nullable: true,
  })
  maxCurrent: number;

  @ApiProperty({
    enum: ConnectedChargeStatePowerDeliveryStateEnum,
    nullable: true,
  })
  powerDeliveryState: ConnectedChargeStatePowerDeliveryStateEnum;

  @ApiProperty({
    description: 'Range',
    type: Number,
    example: 300,
    nullable: true,
  })
  range: number;
}

class VehicleIntentEntryDtoImpl implements VehicleIntentEntryDto {
  @ApiProperty({
    description: 'Charge by time',
    type: String,
    example: '07:00:00',
  })
  chargeByTime: string;

  @ApiProperty({
    description: 'Charge KWh',
    type: Number,
    example: 28,
  })
  chargeKWh: number;

  @ApiProperty({
    enum: VehicleIntentEntryDtoDayOfWeekEnum,
  })
  dayOfWeek: VehicleIntentEntryDtoDayOfWeekEnum;
}

export class VehicleIntentsRequestDtoImpl implements VehicleIntentsRequestDto {
  @ApiProperty({
    description: 'Charging intents',
    type: VehicleIntentEntryDtoImpl,
    isArray: true,
  })
  intentDetails: VehicleIntentEntryDtoImpl[];
}

class GenericChargeStateImpl implements GenericChargeState {
  @ApiProperty({
    description: 'Battery capacity',
    type: Number,
    example: 78,
  })
  batteryCapacity: number;
}

class VehicleInformationImpl implements VehicleInformation {
  @ApiProperty({
    description: 'Vehicle brand',
    type: String,
    example: 'Polestar',
    required: false,
    nullable: true,
  })
  brand?: string | null;

  @ApiProperty({
    description: 'Vehicle model',
    type: String,
    example: '2',
    required: false,
    nullable: true,
  })
  model?: string | null;

  @ApiProperty({
    description: 'Vehicle model variant',
    type: String,
    example: 'Long range',
    required: false,
    nullable: true,
  })
  modelVariant?: string | null;

  @ApiProperty({
    description: 'Vehicle registration plate',
    type: String,
    example: 'ABC123',
    required: false,
    nullable: true,
  })
  vehicleRegistrationPlate?: string | null;

  @ApiProperty({
    description: 'Vehicle display name',
    type: String,
    example: 'My car',
    required: false,
    nullable: true,
  })
  displayName?: string | null;

  @ApiProperty({
    description: 'The ID of the vehicle in the EV Database',
    type: String,
    required: false,
  })
  evDatabaseId?: string;
}

class VehicleRequestDtoImpl implements CreateVehicleRequestDto {
  @ApiProperty({
    description: 'Vehicle data',
    type: VehicleInformationImpl,
    required: false,
  })
  vehicleInformation?: VehicleInformationImpl;

  @ApiProperty({
    description: 'Vehicle charge state data',
    type: GenericChargeStateImpl,
    required: false,
  })
  chargeState: GenericChargeStateImpl;

  @ApiProperty({
    description: 'Enode user id',
    type: String,
    example: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    required: false,
  })
  enodeUserId?: string;

  @ApiProperty({
    description: 'Enode vehicle id',
    type: String,
    example: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    required: false,
  })
  enodeVehicleId?: string;
}

export class InterventionDtoImpl implements InterventionDto {
  @ApiProperty({
    description: 'The endpoint to extract all interventions',
    type: String,
  })
  all: string;

  @ApiProperty({
    description: 'The individual interventions for charge state',
    type: [String],
  })
  chargeState: string[];

  @ApiProperty({
    description: 'The individual interventions for vehicle information',
    type: [String],
  })
  information: string[];
}

export class ConnectedStatefulVehicleDtoImpl
  implements ConnectedStatefulVehicleDto
{
  @ApiProperty({
    description: 'Vehicle Id',
    type: String,
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6"',
  })
  id: string;

  @ApiProperty({
    description: 'Last seen date',
    type: String,
    example: '2021-08-12T09:00:00Z',
    required: false,
    nullable: true,
  })
  lastSeen?: string;

  @ApiProperty({
    description: 'Enode user id',
    type: String,
    example: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    required: false,
    nullable: true,
  })
  enodeUserId?: string;

  @ApiProperty({
    description: 'Enode vehicle id',
    type: String,
    example: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
    required: false,
    nullable: true,
  })
  enodeVehicleId?: string;

  @ApiProperty({
    description: 'Vehicle data',
    type: VehicleInformationImpl,
  })
  vehicleInformation: VehicleInformationImpl;

  @ApiProperty({
    description: 'Vehicle charge state data',
    type: ConnectedChargeStateImpl,
  })
  chargeState: ConnectedChargeStateImpl;

  @ApiProperty({
    description: 'Vehicle interventions',
    type: InterventionDtoImpl,
  })
  interventions: InterventionDtoImpl;
}

class VehicleIntentsResponseDtoImpl implements VehicleIntentsResponseDto {
  @ApiProperty({
    description: 'Id',
    type: String,
    example: 'string',
  })
  id: string;

  @ApiProperty({
    description: 'Vehicle intent details',
    type: VehicleIntentEntryDtoImpl,
    isArray: true,
  })
  details: VehicleIntentEntryDtoImpl[];

  @ApiProperty({
    description: 'The maximum price per kWh',
    type: Number,
    example: 0.25,
    nullable: true,
  })
  maxPrice: number | null;
}

export class GenericStatefulVehicleDtoImpl
  implements GenericStatefulVehicleDto
{
  @ApiProperty({
    description: 'Id',
    type: String,
    example: 'string',
  })
  id: string;

  @ApiProperty({
    description: 'Vehicle data',
    type: VehicleInformationImpl,
  })
  vehicleInformation: VehicleInformationImpl;

  @ApiProperty({
    description: 'Vehicle charge state data',
    type: GenericChargeStateImpl,
  })
  chargeState: GenericChargeStateImpl;

  @ApiProperty({
    description: "The user's Enode ID",
    nullable: true,
  })
  enodeUserId?: string | null;

  @ApiProperty({
    description: "The vehicles's Enode ID",
    nullable: true,
  })
  enodeVehicleId?: string | null;
}

export class VehicleLinkResponseDtoImpl implements VehicleLinkResponseDto {
  @ApiProperty({
    description: 'Id',
    type: String,
    example: 'string',
  })
  id: string;

  @ApiProperty({
    description: 'Is this the primary charger for this vehicle',
    type: Boolean,
    example: true,
  })
  isPrimary: boolean;

  @ApiProperty({
    description: 'Is this vehicle plugged into the charger',
    type: Boolean,
    example: true,
  })
  isPluggedInToThisCharger: boolean;

  @ApiProperty({
    description: 'Vehicle data',
    anyOf: refs(ConnectedStatefulVehicleDtoImpl, GenericStatefulVehicleDtoImpl),
  })
  vehicle: ConnectedStatefulVehicleDtoImpl | GenericStatefulVehicleDtoImpl;

  @ApiProperty({
    description: 'Intent data',
    type: VehicleIntentsResponseDtoImpl,
  })
  intents: VehicleIntentsResponseDtoImpl;
}

class VehicleChargeInfoDtoImpl implements ChargeDetailDto {
  @ApiProperty({
    nullable: true,
    type: Number,
  })
  expectedChargeByTargetPercent: number | null;

  @ApiProperty({
    nullable: true,
    type: Number,
  })
  expectedChargeByTarget_kWh: number | null;

  @ApiProperty({
    nullable: true,
    type: String,
  })
  fullChargeByTime: string | null;
}

class CurrentIntentDtoChargingStationImpl
  implements VehicleChargeInfoDtoChargingStation
{
  @ApiProperty({
    nullable: true,
    type: String,
  })
  ppid?: string;
}

class CurrentIntentDtoImpl implements VehicleChargeInfoDto {
  @ApiProperty({
    description: 'The tariff type for the vehicle charge',
    enum: VehicleChargeInfoDtoTariffTypeEnum,
  })
  tariffType: VehicleChargeInfoDtoTariffTypeEnum;

  @ApiProperty({
    description: 'Whether or not the intent needs peak to meet the target',
    type: Boolean,
  })
  needsPeakToMeetTarget: boolean;

  @ApiProperty({
    description: 'The time by which the intent should be ready',
    type: String,
  })
  readyByTime: string;

  @ApiProperty({
    description: 'Whether or not the intent can meet the target',
    type: Boolean,
    nullable: true,
  })
  canMeetTarget: boolean;

  @ApiProperty({
    description: 'The charge details',
    type: VehicleChargeInfoDtoImpl,
  })
  chargeDetail: VehicleChargeInfoDtoImpl;

  @ApiProperty({
    description: 'The charging station',
    type: CurrentIntentDtoChargingStationImpl,
  })
  chargingStation: CurrentIntentDtoChargingStationImpl;

  @ApiProperty({
    description: 'The reason why the target can not be met',
    enum: VehicleChargeInfoDtoCannotMeetTargetReasonEnum,
    nullable: true,
  })
  cannotMeetTargetReason: VehicleChargeInfoDtoCannotMeetTargetReasonEnum | null;
}

export class ExtendedVehicleLinksResponseDtoImpl
  extends VehicleLinkResponseDtoImpl
  implements ExtendedVehicleLinkResponseDto
{
  @ApiProperty({
    description: 'The current intent for the vehicle',
    type: CurrentIntentDtoImpl,
    nullable: true,
  })
  currentIntent: CurrentIntentDtoImpl;
}

export class VehicleLinkRequestDtoImpl implements CreateVehicleLinkRequestDto {
  @ApiProperty({
    description: 'Is this the primary charger for this vehicle',
    type: Boolean,
    example: true,
    required: false,
  })
  isPrimary?: boolean;

  @ApiProperty({
    description: 'Vehicle data',
    type: VehicleRequestDtoImpl,
  })
  vehicle: VehicleRequestDtoImpl;

  @ApiProperty({
    description: 'Charging intents',
    type: VehicleIntentEntryDtoImpl,
    isArray: true,
  })
  intents: VehicleIntentEntryDtoImpl[];
}

export class UpdateVehicleLinkRequestDtoImpl
  implements UpdateVehicleLinkRequestDto
{
  @ApiProperty({
    description: 'Is this the primary charger for this vehicle',
    type: Boolean,
    example: true,
    required: false,
  })
  isPrimary?: boolean;

  @ApiProperty({
    description: 'Vehicle data',
    type: VehicleRequestDtoImpl,
    required: false,
  })
  vehicle?: VehicleRequestDtoImpl;
}

export class VehicleIntentEntry implements VehicleIntentEntryDto {
  @ApiProperty({ example: '07:00:00' })
  chargeByTime: string;

  @ApiProperty({ example: 28 })
  chargeKWh: number;

  @ApiProperty({
    enum: VehicleIntentEntryDtoDayOfWeekEnum,
  })
  dayOfWeek: VehicleIntentEntryDtoDayOfWeekEnum;
}

export class SetVehicleIntent implements SetDelegatedControlIntentsResponseDto {
  @ApiProperty({ example: '2024-07-31T12:34:56.789Z' })
  createdAt: string;

  @ApiProperty({ example: '2024-07-31T12:34:56.789Z' })
  updatedAt: string;

  @ApiProperty({ example: 'e36468da-ffb7-47ef-ab94-62239384b78c' })
  delegatedControlChargingStationVehicleId: string;

  @ApiProperty({ example: '7d06b432-6de3-425a-ae42-02f81bf5370f' })
  id: string;

  @ApiProperty({
    type: [VehicleIntentEntry],
  })
  intentDetails: VehicleIntentEntry[];

  @ApiProperty({
    description: 'The maximum price per kWh',
    type: Number,
    example: 0.25,
    nullable: true,
  })
  maxPrice: number;
}

export class VehicleIntentEntryRequest {
  @ApiProperty({
    type: [VehicleIntentEntry],
  })
  intentDetails: VehicleIntentEntry[];
}

export class DelegatedControlChargingStationResponseDtoImpl
  implements Omit<DelegatedControlChargingStationResponseDto, 'status'>
{
  @ApiProperty({
    description: 'Id',
    type: String,
    example: 'string',
  })
  id: string;

  @ApiProperty({
    description: 'Pod Point id',
    type: String,
    example: 'string',
  })
  ppid: string;

  @ApiProperty({
    enum: DelegatedControlStatusEnum,
    required: false,
  })
  status?: DelegatedControlStatus;

  @ApiProperty({
    description: 'Created at',
    type: String,
    example: '2021-01-01T00:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Vehicle links',
    type: VehicleLinkRequestDtoImpl,
    isArray: true,
    required: false,
  })
  vehicleLinks?: VehicleLinkResponseDtoImpl[];
}

export interface Brand {
  id: string;
  name: string;
  logoUrl: string;

  enodeCompatible: boolean;
  enodeVendor?: string;
}

export interface Model {
  id: string;
  name: string;

  brandId: string;
}

export interface ModelVariant {
  id: string;
  modelId: string;
  enodeCompatible: boolean;

  name?: string | null;
  batteryCapacityUseable?: number;
  batteryCapacityFull?: number;
  drivetrainType?: string;
  rangeReal?: number;
  fromDate?: string | null;
  toDate?: string | null;
}

export interface VehicleData {
  brands: Brand[];
  models: Model[];
  modelVariants: ModelVariant[];
}

export class SmartChargingPreferencesDTO {
  @ApiProperty({
    description: 'The maximum price of energy willing to be paid for',
    nullable: true,
    required: false,
  })
  maxPrice?: number | null;
}
