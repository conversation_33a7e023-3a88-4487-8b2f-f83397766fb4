import { TEST_EXPENSES_REQUEST } from './__fixtures__/expenses.interface';
import { TEST_EXPENSES_RESPONSE } from '@experience/shared/axios/data-platform-api-client/fixtures';
import axios from 'axios';

export const describeExpensesModule = (
  baseUrl: string,
  token: () => string
) => {
  describe('expenses module', () => {
    const organisationId = 1234;
    describe('expenses endpoint', () => {
      it('should return an unauthorised error when charges endpoint is called', async () => {
        await expect(
          axios.post(`${baseUrl}/expenses/groups/${organisationId}`)
        ).rejects.toThrow('Request failed with status code 401');
      });

      it('should return a successful submission of charges', async () => {
        const response = await axios.post(
          `${baseUrl}/expenses/groups/${organisationId}`,
          TEST_EXPENSES_REQUEST,
          { headers: { Authorization: `Bearer ${token()}` } }
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual(TEST_EXPENSES_RESPONSE);
      });
    });
  });
};
