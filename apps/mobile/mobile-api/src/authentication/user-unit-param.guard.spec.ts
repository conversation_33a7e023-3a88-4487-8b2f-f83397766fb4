import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { ExecutionContext } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { UserUnitParamGuard } from './user-unit-param.guard';
import { UsersService } from '../users/users.service';

const getMockExecutionContext = (uuid: string, ppid: string) =>
  createMock<ExecutionContext>({
    switchToHttp: () => ({
      getRequest: () => ({
        params: {
          ppid,
        },
        user: {
          uuid,
        },
      }),
    }),
  });

describe('UserUnitParamGuard', () => {
  let guard: UserUnitParamGuard;
  let usersService: DeepMocked<UsersService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserUnitParamGuard,
        {
          provide: UsersService,
          useValue: createMock<UsersService>(),
        },
      ],
    }).compile();

    guard = module.get(UserUnitParamGuard);
    usersService = module.get(UsersService);
  });

  afterEach(() => jest.resetAllMocks());

  describe('canActivate()', () => {
    it('returns true if the user owns the given charger', async () => {
      usersService.doesUserOwnCharger.mockResolvedValue(true);

      const res = await guard.canActivate(
        getMockExecutionContext(
          '6179cd2d-8c63-482b-8fb0-4fa3fa051ae8',
          'PSL-123456'
        )
      );

      expect(res).toBeTruthy();

      expect(usersService.doesUserOwnCharger).toHaveBeenCalledTimes(1);
      expect(usersService.doesUserOwnCharger).toHaveBeenCalledWith(
        '6179cd2d-8c63-482b-8fb0-4fa3fa051ae8',
        'PSL-123456'
      );
    });

    it('returns false if the user does not own the given charger', async () => {
      usersService.doesUserOwnCharger.mockResolvedValue(false);

      const res = await guard.canActivate(
        getMockExecutionContext(
          '6179cd2d-8c63-482b-8fb0-4fa3fa051ae8',
          'PSL-000000'
        )
      );

      expect(res).toBeFalsy();

      expect(usersService.doesUserOwnCharger).toHaveBeenCalledTimes(1);
      expect(usersService.doesUserOwnCharger).toHaveBeenCalledWith(
        '6179cd2d-8c63-482b-8fb0-4fa3fa051ae8',
        'PSL-000000'
      );
    });

    it('returns false if an error occurs', async () => {
      usersService.doesUserOwnCharger.mockImplementation(() => {
        throw new Error();
      });

      const res = await guard.canActivate(
        getMockExecutionContext(
          '6179cd2d-8c63-482b-8fb0-4fa3fa051ae8',
          'PSL-123456'
        )
      );

      expect(res).toBeFalsy();

      expect(usersService.doesUserOwnCharger).toHaveBeenCalledTimes(1);
      expect(usersService.doesUserOwnCharger).toHaveBeenCalledWith(
        '6179cd2d-8c63-482b-8fb0-4fa3fa051ae8',
        'PSL-123456'
      );
    });
  });
});
