import { AuthGuardToken } from './authguard.token';
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';

@Injectable()
export class AuthGuardTokenWithoutEmailVerification implements CanActivate {
  private logger = new Logger('HTTP');

  constructor(private readonly authGuardToken: AuthGuardToken) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    await this.authGuardToken.validateToken(context);

    return true;
  }
}
