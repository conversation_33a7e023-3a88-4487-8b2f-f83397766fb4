import {
  VehicleInterventionDtoDomainEnum,
  VehicleInterventionResolutionDtoAgentEnum,
} from '@experience/shared/axios/vehicle-api-client';
import { VehicleInterventionDtoImpl } from '../vehicles.types';

export const TEST_GET_INTERVENTION: VehicleInterventionDtoImpl = {
  id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  vendor: 'string',
  vendorType: 'string',
  brand: 'string',
  introducedAt: '2024-11-14T14:02:06.317Z',
  domain: VehicleInterventionDtoDomainEnum.Account,
  resolution: {
    title: 'Replace battery',
    description: 'Replace the battery with a new one',
    access: 'Physical',
    agent: VehicleInterventionResolutionDtoAgentEnum.User,
  },
};
