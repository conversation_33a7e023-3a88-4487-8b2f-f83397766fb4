import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AuthenticationModule } from '../../authentication/authentication.module';
import { LocationController } from './location.controller';
import { LocationService } from './location.service';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import request from 'supertest';
import { TEST_MAP_LOCATION } from './__fixtures__/map-location';
import { TEST_MAP_LOCATION_DETAILS } from './__fixtures__/map-location-details';
import { TEST_CHARGER_DETAILS } from './__fixtures__/charger-details';
import {
  MapLocationError,
  OcpiLocationByIdFetchException,
  OcpiNearbyLocationsFetchException,
} from './location.exception';

jest.mock('./location.service');

describe('LocationController', () => {
  let app: INestApplication;
  let module: TestingModule;
  let service: LocationService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CacheModule.register(), ConfigModule, AuthenticationModule],
      controllers: [LocationController],
      providers: [LocationService],
    }).compile();

    service = module.get<LocationService>(LocationService);

    app = module.createNestApplication();
    await app.init();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return a list of nearby locations', async () => {
    const mockFindNearbyLocations = jest
      .spyOn(service, 'findNearbyLocations')
      .mockResolvedValue([TEST_MAP_LOCATION]);

    const response = await request(app.getHttpServer())
      .get('/map/locations/nearby')
      .query({ latitude: 51.5074, longitude: -0.1278, distance: 10000 });

    expect(response.status).toBe(200);
    expect(response.body).toEqual([TEST_MAP_LOCATION]);
    expect(mockFindNearbyLocations).toHaveBeenCalledWith(
      51.5074,
      -0.1278,
      10000
    );
  });

  it.each([null, undefined, NaN])(
    'should return 400 for invalid latitude query param - %s',
    async (query) => {
      const response = await request(app.getHttpServer())
        .get('/map/locations/nearby')
        .query({ latitude: query, longitude: -0.1278, distance: 10000 });

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        error: 'Bad Request',
        message: 'Validation failed (numeric string is expected)',
        statusCode: 400,
      });
    }
  );

  it('should return 500 if there is an error when fetching nearby locations', async () => {
    jest
      .spyOn(service, 'findNearbyLocations')
      .mockRejectedValue(new OcpiNearbyLocationsFetchException());

    await request(app.getHttpServer())
      .get('/map/locations/nearby')
      .query({ latitude: 51.5074, longitude: -0.1278, distance: 10000 })
      .expect(500)
      .expect({
        statusCode: 500,
        message: 'Failed to fetch nearby locations from OCPI',
        error: MapLocationError.OCPI_NEARBY_LOCATIONS_FETCH_ERROR,
      });
  });

  it.each([null, undefined, NaN])(
    'should return 400 for invalid longitude query param - %s',
    async (query) => {
      const response = await request(app.getHttpServer())
        .get('/map/locations/nearby')
        .query({ latitude: 51.5074, longitude: query, distance: 10000 });

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        error: 'Bad Request',
        message: 'Validation failed (numeric string is expected)',
        statusCode: 400,
      });
    }
  );

  it.each([null, undefined, NaN])(
    'should return 400 for invalid distance query param - %s',
    async (query) => {
      const response = await request(app.getHttpServer())
        .get('/map/locations/nearby')
        .query({ latitude: 51.5074, longitude: -0.1278, distance: query });

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        error: 'Bad Request',
        message: 'Validation failed (numeric string is expected)',
        statusCode: 400,
      });
    }
  );

  it('should get location details by ID', async () => {
    const mockGetLocationDetailsById = jest
      .spyOn(service, 'getLocationDetailsById')
      .mockResolvedValue(TEST_MAP_LOCATION_DETAILS);

    const response = await request(app.getHttpServer()).get(
      `/map/locations/${TEST_MAP_LOCATION.id}`
    );

    expect(mockGetLocationDetailsById).toHaveBeenCalledWith(
      TEST_MAP_LOCATION.id
    );
    expect(response.status).toBe(200);
    expect(response.body).toEqual(TEST_MAP_LOCATION_DETAILS);
  });

  it('should return 500 if there is an error when fetching location details by ID', async () => {
    jest
      .spyOn(service, 'getLocationDetailsById')
      .mockRejectedValue(new OcpiLocationByIdFetchException());

    await request(app.getHttpServer())
      .get(`/map/locations/${TEST_MAP_LOCATION.id}`)
      .expect(500)
      .expect({
        statusCode: 500,
        message: 'Failed to fetch location by ID from OCPI',
        error: MapLocationError.OCPI_LOCATION_BY_ID_FETCH_ERROR,
      });
  });

  it.each([null, undefined])(
    'should return 400 for invalid locationId param when getting location details by ID - %s',
    async (id) => {
      const response = await request(app.getHttpServer()).get(
        `/map/locations/${id}`
      );

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        error: 'Bad Request',
        message: 'Validation failed (uuid is expected)',
        statusCode: 400,
      });
    }
  );

  it('should get charger details by location ID and PPID', async () => {
    const mockGetChargerDetailsByLocationIdAndPpid = jest
      .spyOn(service, 'getChargerDetailsByLocationIdAndPpid')
      .mockResolvedValue(TEST_CHARGER_DETAILS);

    const response = await request(app.getHttpServer()).get(
      `/map/locations/${TEST_MAP_LOCATION.id}/chargers/${TEST_CHARGER_DETAILS.ppid}`
    );

    expect(response.status).toBe(200);
    expect(response.body).toEqual(TEST_CHARGER_DETAILS);
    expect(mockGetChargerDetailsByLocationIdAndPpid).toHaveBeenCalledWith(
      TEST_MAP_LOCATION.id,
      TEST_CHARGER_DETAILS.ppid
    );
  });

  it('should return 500 if there is an error when fetching charger details by location ID and PPID', async () => {
    jest
      .spyOn(service, 'getChargerDetailsByLocationIdAndPpid')
      .mockRejectedValue(new OcpiLocationByIdFetchException());

    await request(app.getHttpServer())
      .get(
        `/map/locations/${TEST_MAP_LOCATION.id}/chargers/${TEST_CHARGER_DETAILS.ppid}`
      )
      .expect(500)
      .expect({
        statusCode: 500,
        message: 'Failed to fetch location by ID from OCPI',
        error: MapLocationError.OCPI_LOCATION_BY_ID_FETCH_ERROR,
      });
  });

  it.each([null, undefined, 'invalid-string'])(
    'should return 400 for invalid locationId param when getting charger details by location ID and PPID - %s',
    async (id) => {
      const response = await request(app.getHttpServer()).get(
        `/map/locations/${id}/chargers/${TEST_CHARGER_DETAILS.ppid}`
      );

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        error: 'Bad Request',
        message: 'Validation failed (uuid is expected)',
        statusCode: 400,
      });
    }
  );
});
