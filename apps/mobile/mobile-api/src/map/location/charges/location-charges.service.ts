import { Inject, Injectable, Logger } from '@nestjs/common';
import { UsersService } from '../../../users/users.service';
import { PodLocations, PodUnits } from '@experience/shared/sequelize/podadmin';
import { Api3TokenService } from '@experience/mobile/nest/api3-token';
import { ITokenAuthUser } from '@experience/mobile/nest/authorisation';
import axios from 'axios';
import { Api3ConfirmChargeRequest } from './location-charges.types';
import { ConfigService } from '@nestjs/config';
import { ChargerNotFoundError } from '@experience/mobile/nest/exception';
import {
  OutOfOrderSocketError,
  SocketNotFoundError,
} from './location-charges.errors';
import { LocationService } from '../location.service';

@Injectable()
export class ChargesService {
  private logger: Logger = new Logger(ChargesService.name);
  constructor(
    private readonly api3TokenService: Api3TokenService,
    private readonly configService: ConfigService,
    private readonly locationService: LocationService,
    @Inject('POD_UNITS_REPOSITORY')
    private readonly podUnitsRepository: typeof PodUnits,
    private readonly usersService: UsersService
  ) {}

  async confirmCharge(
    locationId: string,
    ppid: string,
    socketId: string,
    user: ITokenAuthUser
  ) {
    this.logger.log(
      { locationId, ppid, socketId, userId: user.id },
      'confirming charge'
    );
    await this.checkSocketStatus(locationId, ppid, socketId);

    const podAdminUserId = await this.usersService
      .getPodAdminIdUserByAuthId(user.uuid)
      .then((user) => user.id);

    const podLocationId = await this.podUnitsRepository
      .findOne({
        where: { ppid },
        include: [{ model: PodLocations, as: 'podLocation' }],
      })
      .then((unit) => {
        if (!unit || !unit.podLocation) {
          this.logger.error({ ppid }, 'Charger not found');
          throw new ChargerNotFoundError();
        }
        return unit.podLocation.id;
      });

    const request: Api3ConfirmChargeRequest = {
      user: podAdminUserId,
      pod: podLocationId,
      door: parseInt(socketId),
    };

    const token = await this.api3TokenService.refreshApi3Token(user.id);

    const res = await axios.post(
      `${this.configService.get('API3_BASE_URL')}/v5/charges`,
      request,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return res.data;
  }

  private async checkSocketStatus(
    locationId: string,
    ppid: string,
    socketId: string
  ): Promise<void> {
    const locationDetails =
      await this.locationService.getChargerDetailsByLocationIdAndPpid(
        locationId,
        ppid
      );

    const socket = locationDetails.sockets.find(
      (socket) => socket.id === socketId
    );

    if (!socket) {
      this.logger.error(
        { locationId, ppid, socketId },
        'Socket not found on charger'
      );
      throw new SocketNotFoundError();
    }

    if (['OUTOFORDER'].includes(socket.status)) {
      throw new OutOfOrderSocketError();
    }
  }
}
