import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import dayjs from 'dayjs';
import {
  ChargerDetails,
  OcpiLocation,
  OcpiResponse,
  MapLocation,
  MapLocationDetails,
  OcpiEvse,
  Charger,
  Socket,
  Tariff,
  TariffSchedule,
} from './location.types';
import { convertSnakeCaseToSentenceCase } from '@experience/shared/typescript/utils';
import {
  Charger as CommercialAttributes,
  ChargerApi as SmsChargerApi,
  Tariff as SmsTariff,
  TariffSchedule as SmsTariffSchedule,
} from '@experience/shared/axios/internal-site-admin-api-client';
import {
  getChargerStatus,
  tariffOverlapsStartOfWeek,
  tariffOverlapsStartOfDay,
} from './utils/utils';
import {
  OcpiLocationByIdFetchException,
  OcpiNearbyLocationsFetchException,
} from './location.exception';

@Injectable()
export class LocationService {
  private readonly logger = new Logger(LocationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly smsChargerApi: SmsChargerApi
  ) {}

  async findNearbyLocations(
    lat: number,
    long: number,
    distance: number
  ): Promise<MapLocation[]> {
    this.logger.log({ lat, long, distance }, 'finding nearby locations');

    return this.getNearbyOcpiLocations(lat, long, distance).then((locations) =>
      locations.map((location) => this.mapLocation(location))
    );
  }

  async getLocationDetailsById(
    locationId: string
  ): Promise<MapLocationDetails> {
    this.logger.log({ locationId }, 'getting location details');

    return this.getOcpiLocationById(locationId).then((ocpiLocation) =>
      this.mapLocationDetails(ocpiLocation)
    );
  }

  async getChargerDetailsByLocationIdAndPpid(
    locationId: string,
    ppid: string
  ): Promise<ChargerDetails> {
    this.logger.log({ locationId, ppid }, 'getting charger details');
    const [ocpiLocation, commercialAttributes] = await Promise.all([
      this.getOcpiLocationById(locationId),
      this.smsChargerApi
        .chargerControllerFindByIdentifier(
          ppid,
          'admins,billing,domains,drivers,group,name,ppid,rfidCards,settings,site,tariff'
        )
        .catch((error) => {
          this.logger.error(
            { error, ppid },
            'error fetching commercial attributes'
          );
          return undefined;
        })
        .then((response) =>
          response ? (response.data as CommercialAttributes) : undefined
        ),
    ]);
    const charger = this.mapChargers(ocpiLocation.evses).find(
      (charger) => charger.ppid === ppid
    );
    return this.mapChargerDetails(charger, commercialAttributes);
  }

  private async getNearbyOcpiLocations(
    lat: number,
    long: number,
    distance: number
  ): Promise<OcpiLocation[]> {
    return fetch(
      `${this.configService.get<string>('OCPI_API_BASE_URL')}/ocpi/cpo/2.2.1/locations/nearby?latitude=${lat}&longitude=${long}&distance=${distance}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Token ${this.configService.get<string>(
            'OCPI_API_TOKEN'
          )}`,
          'Content-Type': 'application/json',
        },
      }
    )
      .then((res) => {
        if (!res.ok) {
          this.logger.error(
            { status: res.status, statusText: res.statusText },
            'error fetching nearby locations'
          );
          throw new OcpiNearbyLocationsFetchException();
        }
        return res.json();
      })
      .catch((error) => {
        this.logger.error({ error }, 'error fetching nearby locations');
        throw new OcpiNearbyLocationsFetchException();
      })
      .then((response: OcpiResponse<OcpiLocation[]>) => response.data);
  }

  private async getOcpiLocationById(locationId: string): Promise<OcpiLocation> {
    return fetch(
      `${this.configService.get<string>('OCPI_API_BASE_URL')}/ocpi/cpo/2.2.1/locations/${locationId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Token ${this.configService.get<string>(
            'OCPI_API_TOKEN'
          )}`,
          'Content-Type': 'application/json',
        },
      }
    )
      .catch((error) => {
        this.logger.error(
          { error, locationId },
          'error fetching location by id'
        );
        throw new OcpiLocationByIdFetchException();
      })
      .then((res) => {
        if (!res.ok) {
          this.logger.error(
            { status: res.status, statusText: res.statusText, locationId },
            'error fetching location by id'
          );
          throw new OcpiLocationByIdFetchException();
        }
        return res.json();
      })
      .then((response: OcpiResponse<OcpiLocation>) => response.data);
  }

  private mapLocation(ocpiLocation: OcpiLocation): MapLocation {
    return {
      address: `${ocpiLocation.address}, ${ocpiLocation.city}, ${ocpiLocation.postal_code}`,
      id: ocpiLocation.id,
      coordinates: {
        lat: parseFloat(ocpiLocation.coordinates.latitude),
        long: parseFloat(ocpiLocation.coordinates.longitude),
      },
      fullyOccupied: ocpiLocation.evses.every(
        (evse) => evse.status === 'CHARGING'
      ),
      name: ocpiLocation.name,
      notices: ocpiLocation.directions?.map(({ text }) => text) ?? [],
      type: convertSnakeCaseToSentenceCase(ocpiLocation.facilities?.[0] ?? ''),
    };
  }

  private mapLocationDetails(ocpiLocation: OcpiLocation): MapLocationDetails {
    return {
      ...this.mapLocation(ocpiLocation),
      chargers: this.mapChargers(ocpiLocation.evses),
    };
  }

  private mapChargers(evses: OcpiEvse[]): Charger[] {
    return evses.reduce((result, evse) => {
      if (
        evse.physical_reference &&
        !result.find((item) => item.name === evse.physical_reference)
      ) {
        const matchingEvses = evses.filter(
          (e) => e.physical_reference === evse.physical_reference
        );

        let nextSocketId = 1;

        result.push({
          name: evse.physical_reference,
          ppid: this.mapEvseUuidToPpid(evse.evse_id),
          sockets: matchingEvses
            .map((e) => {
              const sockets = this.mapSockets(e, nextSocketId);
              nextSocketId += sockets.length;
              return sockets;
            })
            .flat(),
          status: getChargerStatus(matchingEvses),
        });
      }
      return result;
    }, [] as Charger[]);
  }

  private async mapChargerDetails(
    charger: Charger,
    commercialAttributes?: CommercialAttributes
  ): Promise<ChargerDetails> {
    if (!commercialAttributes) {
      // If no data from SMS - map the alphabetical characters for the socket name manually
      return {
        ...charger,
        sockets: charger.sockets.map((socket, index) => ({
          ...socket,
          name: `${String.fromCharCode(index + 65)}`,
        })),
      };
    }

    const {
      tariff: smsTariff,
      site: { pods },
    } = commercialAttributes;

    const smsCharger = pods.find((p) => p.ppid === charger.ppid);

    const activeTariff = smsTariff?.schedule
      ? this.getActiveTariff(smsTariff)
      : undefined;

    return {
      ...charger,
      sockets: charger.sockets.map((socket, index) => ({
        ...socket,
        name: smsCharger.sockets[index].door,
      })),
      tariff: smsTariff?.schedule ? this.mapTariff(smsTariff) : undefined,
      activeTariff,
    };
  }

  private mapSockets(evse: OcpiEvse, nextSocketId = 1): Socket[] {
    return evse.connectors.map((connector, index) => ({
      maxChargeSpeed: connector.max_electric_power ?? 0,
      id: `${nextSocketId + index}`,
      status: evse.status,
    }));
  }

  private mapEvseUuidToPpid(evseUid: string): string {
    const isValid = this.validateEvseUid(evseUid);

    if (!isValid) {
      return evseUid;
    }

    const afterStar = evseUid.split('*').pop();
    const [ppid] = afterStar.split('E');

    const prefix = ppid.match(/^([A-Za-z]+)(\d.*)$/)?.[1];
    return prefix ? ppid.replace(prefix, `${prefix}-`) : ppid;
  }

  private validateEvseUid(evseUid: string): boolean {
    const evseUidPattern = /^[A-Z]{2}\*[A-Z]{3}\*[A-Z]\*[A-Za-z0-9]+$/;
    return evseUidPattern.test(evseUid);
  }

  private mapTariff(smsTariff: SmsTariff): Tariff {
    const tariff = {
      id: `${smsTariff.id}`,
      name: smsTariff.name,
      currency: smsTariff.currency,
    };

    if (!smsTariff.schedule) {
      return tariff;
    }

    return {
      ...tariff,
      schedule: {
        drivers: smsTariff.schedule.drivers?.map((schedule) =>
          this.mapTariffSchedule(schedule)
        ),
        members: smsTariff.schedule.members?.map((schedule) =>
          this.mapTariffSchedule(schedule)
        ),
        public: smsTariff.schedule.public?.map((schedule) =>
          this.mapTariffSchedule(schedule)
        ),
      },
    };
  }

  private mapTariffSchedule(schedule: SmsTariffSchedule): TariffSchedule {
    return {
      id: `${schedule.id}`,
      bands: schedule.bands.map((band) => ({
        id: band.id,
        after: band.after,
        cost: band.cost,
        prettyPrint: band.prettyPrint,
      })),
      endDay: schedule.endDay,
      endTime: schedule.endTime,
      pricingModel: schedule.pricingModel,
      startDay: schedule.startDay,
      startTime: schedule.startTime,
      tariffTier: schedule.tariffTier,
    };
  }

  private getActiveTariff(
    smsTariff: SmsTariff
  ): { type: string; price: number; prettyPrint: string } | undefined {
    const now = dayjs();
    const currentDay = now.day(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const currentTime = now.format('HH:mm');

    const activeSchedule = smsTariff.schedule.public?.find((schedule) => {
      const isWithinDayRange = tariffOverlapsStartOfWeek(
        currentDay,
        schedule.startDay,
        schedule.endDay
      );
      const isWithinTimeRange = tariffOverlapsStartOfDay(
        currentTime,
        schedule.startTime,
        schedule.endTime
      );

      return isWithinDayRange && isWithinTimeRange;
    });

    if (activeSchedule && activeSchedule.bands.length > 0) {
      const [currentBand] = activeSchedule.bands;
      const currentPrice = currentBand.cost;

      return {
        type: 'per kWh',
        price: currentPrice,
        prettyPrint: `${currentBand.prettyPrint} per kWh`,
      };
    }

    return undefined;
  }
}
