import { Test, TestingModule } from '@nestjs/testing';
import { LocationService } from './location.service';
import { ConfigModule } from '@nestjs/config';
import { TEST_OCPI_LOCATION } from './__fixtures__/ocpi-location';
import { TEST_MAP_LOCATION } from './__fixtures__/map-location';
import { TEST_MAP_LOCATION_DETAILS } from './__fixtures__/map-location-details';
import { setIn, Map } from 'immutable';
import { TEST_OCPI_EVSE } from './__fixtures__/ocpi-evse';
import { TEST_SOCKET } from './__fixtures__/socket';
import { ChargerApi as SmsChargerApi } from '@experience/shared/axios/internal-site-admin-api-client';
import { TEST_OCPI_CONNECTOR } from './__fixtures__/ocpi-connector';
import { TEST_CHARGER } from './__fixtures__/charger';
import { TEST_COMMERCIAL_ATTRIBUTES } from './__fixtures__/commercial-attributes';
import { AxiosResponse } from 'axios';
import { TEST_CHARGER_DETAILS } from './__fixtures__/charger-details';
import {
  OcpiLocationByIdFetchException,
  OcpiNearbyLocationsFetchException,
} from './location.exception';

describe('LocationService', () => {
  let service: LocationService;
  let smsChargerApi: SmsChargerApi;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule],
      providers: [
        LocationService,
        {
          provide: SmsChargerApi,
          useValue: new SmsChargerApi(),
        },
      ],
    }).compile();

    service = module.get<LocationService>(LocationService);
    smsChargerApi = module.get<SmsChargerApi>(SmsChargerApi);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(smsChargerApi).toBeDefined();
  });

  it('should return a list of nearby locations', async () => {
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: async () => ({
        status_code: 200,
        data: [TEST_OCPI_LOCATION],
        timestamp: new Date().toISOString(),
      }),
    } as Response);

    const locations = await service.findNearbyLocations(
      51.5074,
      -0.1278,
      10000
    );

    expect(mockFetch).toHaveBeenCalledWith(
      'http://localhost:6102/ocpi/cpo/2.2.1/locations/nearby?latitude=51.5074&longitude=-0.1278&distance=10000',
      {
        headers: {
          Authorization: 'Token ocpi-token',
          'Content-Type': 'application/json',
        },
        method: 'GET',
      }
    );
    expect(locations).toEqual([TEST_MAP_LOCATION]);
  });

  it('should throw an error if fetching nearby locations fails', async () => {
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
    } as Response);

    await expect(
      service.findNearbyLocations(51.5074, -0.1278, 10000)
    ).rejects.toThrow(new OcpiNearbyLocationsFetchException());
  });

  it('should throw an error if there is a network error when fetching nearby locations', async () => {
    jest.spyOn(global, 'fetch').mockRejectedValue(new Error('Network Error'));

    await expect(
      service.findNearbyLocations(51.5074, -0.1278, 10000)
    ).rejects.toThrow(new OcpiNearbyLocationsFetchException());
  });

  it.each([
    ['GB*POD*E*PG454321E1', 'PG-454321', '1'],
    ['GB*POD*E*PG4543216E1', 'PG-4543216', '1'],
    ['GB*POD*E*PP454321E1', 'PP-454321', '1'],
    ['GB*POD*E*PSL454321E1', 'PSL-454321', '1'],
    ['GB*POD*E*veefil454321', 'veefil-454321', '1'],
    ['GB*POD*E*UNKNOWNE1', 'UNKNOWN', '1'],
    ['INVALID_EVSE_ID', 'INVALID_EVSE_ID', '1'],
  ])(
    'should return location details and map evse id to ppid and socket id - %s - %s - %s',
    async (evseId, ppid, socketId) => {
      const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: async () => ({
          status_code: 200,
          data: setIn(TEST_OCPI_LOCATION, ['evses', 0, 'evse_id'], evseId),
          timestamp: new Date().toISOString(),
        }),
      } as Response);

      const location = await service.getLocationDetailsById(
        '1715433a-104d-56e0-8e54-6a390aba2046'
      );

      expect(location).toEqual(
        Map(TEST_MAP_LOCATION_DETAILS)
          .setIn(['chargers', 0, 'ppid'], ppid)
          .setIn(['chargers', 0, 'sockets', 0, 'id'], socketId)
          .toJS()
      );
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:6102/ocpi/cpo/2.2.1/locations/1715433a-104d-56e0-8e54-6a390aba2046',
        {
          headers: {
            Authorization: 'Token ocpi-token',
            'Content-Type': 'application/json',
          },
          method: 'GET',
        }
      );
    }
  );

  it.each([
    [
      'multiple evses',
      [
        TEST_OCPI_EVSE,
        Map(TEST_OCPI_EVSE)
          .setIn(['evse_id'], 'GB*POD*E*PG454321E2')
          .setIn(['status'], 'CHARGING')
          .setIn(['connectors', 0, 'max_electric_power'], 7000)
          .toJS(),
      ],
      setIn(
        TEST_MAP_LOCATION_DETAILS,
        ['chargers', 0, 'sockets'],
        [
          TEST_SOCKET,
          Map(TEST_SOCKET)
            .setIn(['id'], '2')
            .setIn(['status'], 'CHARGING')
            .setIn(['maxChargeSpeed'], 7000)
            .toJS(),
        ]
      ),
    ],
    [
      'multiple connectors',
      [
        Map(TEST_OCPI_EVSE)
          .setIn(
            ['connectors'],
            [
              TEST_OCPI_CONNECTOR,
              setIn(TEST_OCPI_CONNECTOR, ['max_electric_power'], 7000),
            ]
          )
          .toJS(),
      ],
      setIn(
        TEST_MAP_LOCATION_DETAILS,
        ['chargers', 0, 'sockets'],
        [
          TEST_SOCKET,
          Map(TEST_SOCKET)
            .setIn(['id'], '2')
            .setIn(['maxChargeSpeed'], 7000)
            .toJS(),
        ]
      ),
    ],
    [
      'multiple evses with multiple connectors',
      [
        Map(TEST_OCPI_EVSE)
          .setIn(
            ['connectors'],
            [
              TEST_OCPI_CONNECTOR,
              setIn(TEST_OCPI_CONNECTOR, ['max_electric_power'], 7000),
            ]
          )
          .toJS(),
        setIn(TEST_OCPI_EVSE, ['evse_id'], 'GB*POD*E*PG454321E2'),
      ],
      setIn(
        TEST_MAP_LOCATION_DETAILS,
        ['chargers'],
        [
          setIn(
            TEST_CHARGER,
            ['sockets'],
            [
              TEST_SOCKET,
              Map(TEST_SOCKET)
                .setIn(['id'], '2')
                .setIn(['maxChargeSpeed'], 7000)
                .toJS(),
              setIn(TEST_SOCKET, ['id'], '3'),
            ]
          ),
        ]
      ),
    ],
  ])(
    'should map OCPI evses and connectors to a charger with sockets when getting location details by ID - %s',
    async (_, evses, result) => {
      const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: async () => ({
          status_code: 200,
          data: setIn(TEST_OCPI_LOCATION, ['evses'], evses),
          timestamp: new Date().toISOString(),
        }),
      } as Response);

      const location = await service.getLocationDetailsById(
        '1715433a-104d-56e0-8e54-6a390aba2046'
      );

      expect(location).toEqual(result);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:6102/ocpi/cpo/2.2.1/locations/1715433a-104d-56e0-8e54-6a390aba2046',
        {
          headers: {
            Authorization: 'Token ocpi-token',
            'Content-Type': 'application/json',
          },
          method: 'GET',
        }
      );
    }
  );

  it('should throw an error if fetching location by ID fails', async () => {
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: false,
      status: 404,
      statusText: 'Not Found',
    } as Response);

    await expect(
      service.getLocationDetailsById('1715433a-104d-56e0-8e54-6a390aba2046')
    ).rejects.toThrow(new OcpiLocationByIdFetchException());
  });

  it('should throw an error if there is a network error when fetching location by ID', async () => {
    jest.spyOn(global, 'fetch').mockRejectedValue(new Error('Network Error'));

    await expect(
      service.getLocationDetailsById('1715433a-104d-56e0-8e54-6a390aba2046')
    ).rejects.toThrow(new OcpiLocationByIdFetchException());
  });

  it('should return charger details by location ID and ppid', async () => {
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: async () => ({
        status_code: 200,
        data: TEST_OCPI_LOCATION,
        timestamp: new Date().toISOString(),
      }),
    } as Response);
    const mockChargerApi = jest
      .spyOn(smsChargerApi, 'chargerControllerFindByIdentifier')
      .mockResolvedValueOnce({
        data: TEST_COMMERCIAL_ATTRIBUTES,
        status: 200,
      } as AxiosResponse);

    const chargerDetails = await service.getChargerDetailsByLocationIdAndPpid(
      TEST_OCPI_LOCATION.id,
      TEST_COMMERCIAL_ATTRIBUTES.ppid
    );

    expect(chargerDetails).toEqual(TEST_CHARGER_DETAILS);
    expect(mockFetch).toHaveBeenCalledWith(
      'http://localhost:6102/ocpi/cpo/2.2.1/locations/1715433a-104d-56e0-8e54-6a390aba2046',
      {
        headers: {
          Authorization: 'Token ocpi-token',
          'Content-Type': 'application/json',
        },
        method: 'GET',
      }
    );
    expect(mockChargerApi).toHaveBeenCalledWith(
      'PG-80368',
      'admins,billing,domains,drivers,group,name,ppid,rfidCards,settings,site,tariff'
    );
  });

  it('should return charger details by location ID and ppid even if there is an error connecting to SMS', async () => {
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: async () => ({
        status_code: 200,
        data: TEST_OCPI_LOCATION,
        timestamp: new Date().toISOString(),
      }),
    } as Response);
    jest
      .spyOn(smsChargerApi, 'chargerControllerFindByIdentifier')
      .mockRejectedValue({
        status: 500,
      } as AxiosResponse);

    const chargerDetails = await service.getChargerDetailsByLocationIdAndPpid(
      TEST_OCPI_LOCATION.id,
      TEST_COMMERCIAL_ATTRIBUTES.ppid
    );

    expect(chargerDetails).toEqual(
      setIn(
        setIn(TEST_CHARGER_DETAILS, ['tariff'], undefined),
        ['activeTariff'],
        undefined
      )
    );
  });

  it('should return charger details by location ID and ppid even if there is no schedule', async () => {
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: async () => ({
        status_code: 200,
        data: TEST_OCPI_LOCATION,
        timestamp: new Date().toISOString(),
      }),
    } as Response);
    jest
      .spyOn(smsChargerApi, 'chargerControllerFindByIdentifier')
      .mockResolvedValueOnce({
        data: setIn(
          TEST_COMMERCIAL_ATTRIBUTES,
          ['tariff', 'schedule'],
          undefined
        ),
        status: 200,
      } as AxiosResponse);

    const chargerDetails = await service.getChargerDetailsByLocationIdAndPpid(
      TEST_OCPI_LOCATION.id,
      TEST_COMMERCIAL_ATTRIBUTES.ppid
    );

    expect(chargerDetails).toEqual(
      setIn(
        setIn(TEST_CHARGER_DETAILS, ['tariff'], undefined),
        ['activeTariff'],
        undefined
      )
    );
  });

  it('should throw an error if fetching location by ID fails when getting charger details by location ID and ppid', async () => {
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: false,
      status: 404,
      statusText: 'Not Found',
    } as Response);

    await expect(
      service.getChargerDetailsByLocationIdAndPpid(
        TEST_OCPI_LOCATION.id,
        TEST_COMMERCIAL_ATTRIBUTES.ppid
      )
    ).rejects.toThrow(new OcpiLocationByIdFetchException());
  });

  it('should throw an error if there is a network error when fetching location by ID when getting charger details by location ID and ppid', async () => {
    jest.spyOn(global, 'fetch').mockRejectedValue(new Error('Network Error'));

    await expect(
      service.getChargerDetailsByLocationIdAndPpid(
        TEST_OCPI_LOCATION.id,
        TEST_COMMERCIAL_ATTRIBUTES.ppid
      )
    ).rejects.toThrow(new OcpiLocationByIdFetchException());
  });
});
