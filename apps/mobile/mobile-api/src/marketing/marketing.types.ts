import { ApiProperty } from '@nestjs/swagger';

export enum MarketingOpportunity {
  REWARDS = 'REWARDS',
  TARIFF = 'TARIFF',
  MIGRATE = 'MIGRATE',
}

export interface MarketingOpportunities {
  opportunities: MarketingOpportunity[];
}

export class MarketingOpportunitiesDTO implements MarketingOpportunities {
  @ApiProperty({
    description:
      'A list of all the marketing opportunities a charger is eligible for',
    enum: MarketingOpportunity,
    isArray: true,
  })
  opportunities: MarketingOpportunity[];
}
