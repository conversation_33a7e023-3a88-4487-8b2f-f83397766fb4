import axios from 'axios';

export const describeMarketingModule = (
  baseUrl: string,
  token: () => string
) => {
  describe('marketing module', () => {
    describe('marketing controller', () => {
      it('gets the marketing opportunities for a given charger', async () => {
        const res = await axios.get(
          `${baseUrl}/marketing/opportunities/PSL-123456`,
          { headers: { Authorization: `Bearer ${token()}` } }
        );

        expect(res.status).toEqual(200);
        expect(res.data).toMatchSnapshot();
      });

      it('gets the marketing opportunities for a given charger when cached', async () => {
        await axios.get(`${baseUrl}/marketing/opportunities/PSL-123456`, {
          headers: { Authorization: `Bearer ${token()}` },
        });

        const res = await axios.get(
          `${baseUrl}/marketing/opportunities/PSL-123456`,
          { headers: { Authorization: `Bearer ${token()}` } }
        );

        expect(res.status).toEqual(200);
        expect(res.data).toMatchSnapshot();
      });
    });
  });
};
