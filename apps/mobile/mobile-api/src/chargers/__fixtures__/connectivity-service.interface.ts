import {
  DataStatusResponse,
  TypesConnector,
  TypesEvse,
} from '@experience/shared/axios/connectivity-service-client';
import {
  TEST_CHARGER_PPID,
  TEST_CHARGER_PPID_ARCH5,
  TEST_CHARGER_SERIAL_NUMBER,
  TEST_CHARGER_SERIAL_NUMBER_ARCH5,
} from './chargers.interface';

export const TEST_CONNECTIVITY_SERVICE_EVSE_ARCH2: TypesEvse = {
  id: 1,
  connectivityState: {
    protocol: 'POW',
    connectivityStatus: 'ONLINE',
    signalStrength: -44,
    lastMessageAt: '2023-08-21T15:05:47Z',
    connectionStartedAt: '2023-08-21T14:16:47.622Z',
    connectionQuality: 5,
  },
  connectors: [
    {
      id: 1,
      door: 'A',
      chargingState: 'CHARGING',
    },
  ],
  serialNumber: TEST_CHARGER_SERIAL_NUMBER,
  architecture: '2.1',
  macAddress: '00-B0-D0-63-C2-26',
};

export const TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3: TypesEvse = {
  id: 1,
  connectivityState: {
    protocol: 'POW',
    connectivityStatus: 'ONLINE',
    signalStrength: -44,
    lastMessageAt: '2023-08-21T15:05:47Z',
    connectionStartedAt: '2023-08-21T14:16:47.622Z',
    connectionQuality: 5,
  },
  connectors: [
    {
      id: 1,
      door: 'A',
      chargingState: 'CHARGING',
    },
  ],
  serialNumber: TEST_CHARGER_SERIAL_NUMBER,
  architecture: '3.0',
  macAddress: '00-B0-D0-63-C2-26',
};

export const TEST_CONNECTIVITY_SERVICE_EVSE_ARCH5: TypesEvse = {
  id: 1,
  connectivityState: {
    protocol: 'OCPP1.6',
    connectivityStatus: 'ONLINE',
    connectionQuality: 5,
    connectionStartedAt: '2023-08-21T14:16:47.622Z',
    lastSeenAt: '2023-08-21T15:05:47Z',
  },
  connectors: [
    {
      id: 1,
      door: 'A',
      chargingState: 'CHARGING',
    },
  ],
  serialNumber: TEST_CHARGER_SERIAL_NUMBER_ARCH5,
  architecture: '5.0',
  macAddress: '00-B0-D0-63-C2-26',
};

export const TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH2: DataStatusResponse =
  {
    ppid: TEST_CHARGER_PPID,
    connectedComponents: ['evses'],
    evses: [TEST_CONNECTIVITY_SERVICE_EVSE_ARCH2],
  };

export const TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3: DataStatusResponse =
  {
    ppid: TEST_CHARGER_PPID,
    connectedComponents: ['evses'],
    evses: [TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3],
  };

export const TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH5: DataStatusResponse =
  {
    ppid: TEST_CHARGER_PPID_ARCH5,
    connectedComponents: ['evses'],
    evses: [TEST_CONNECTIVITY_SERVICE_EVSE_ARCH5],
  };

export const addEvsesConnectorToConnectivityStatus = (
  connectivityStatus: DataStatusResponse,
  connector: TypesConnector
) => {
  connectivityStatus.evses[0].connectors = [connector];

  return connectivityStatus;
};
