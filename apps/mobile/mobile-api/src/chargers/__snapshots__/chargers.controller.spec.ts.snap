// Jest <PERSON>napshot v1, https://jestjs.io/docs/snapshot-testing

exports[`ChargersController deleteTariff should delete the tariff for an authenticated user 1`] = `{}`;

exports[`ChargersController getChargerTariffs should get the tariff for an authenticated user on a charger they own 1`] = `
{
  "data": [
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2024-11-28",
      "id": "aef6f37a-0903-40ba-88d3-619b6fbc062b",
      "maxChargePrice": 0.2,
      "ppid": "PSL-1234567",
      "supplierId": "84848e36-1f52-43c1-9a6c-7354e43f0250",
      "tariffInfo": [
        {
          "days": [
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "01:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
  ],
  "metadata": {
    "criteria": {
      "ppid": "PSL-1234567",
    },
  },
}
`;

exports[`ChargersController getChargers returns a user's chargers 1`] = `
[
  {
    "delegatedControl": {
      "status": "ACTIVE",
    },
    "linkedAt": "2024-01-01T00:00:00.000Z",
    "modelInfo": {
      "architecture": "5.0",
      "colour": "black",
      "ledColourSet": "uk",
      "style": "solo3",
    },
    "ppid": "PSL-1234567",
    "subscription": {
      "status": "AVAILABLE",
    },
    "timezone": "Etc/UTC",
    "unitId": 1,
  },
  {
    "delegatedControl": {
      "status": "ACTIVE",
    },
    "linkedAt": "2024-01-01T01:00:00.000Z",
    "modelInfo": {
      "architecture": "5.0",
      "colour": "black",
      "ledColourSet": "uk",
      "style": "solo3",
    },
    "ppid": "PSL-7654321",
    "subscription": {
      "status": "AVAILABLE",
    },
    "timezone": "Etc/UTC",
    "unitId": 1,
  },
]
`;

exports[`ChargersController setChargerTariffs should set the tariff for an authenticated user on a charger they own 1`] = `
{
  "cheapestUnitPrice": 0.2,
  "effectiveFrom": "2024-11-28",
  "id": "a9698b5c-4c03-4487-9d99-c794be362db9",
  "ppid": "PSL-1234567",
  "supplierId": "84848e36-1f52-43c1-9a6c-7354e43f0250",
  "tariffInfo": [
    {
      "days": [
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "01:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
  ],
  "timezone": "Europe/London",
}
`;
