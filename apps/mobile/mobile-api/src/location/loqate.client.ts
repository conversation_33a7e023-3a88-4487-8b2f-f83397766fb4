import { AxiosInstance } from 'axios';
import { ILoqateReqOptions } from './loqate.types';

export class LoqateClient {
  constructor(private key: string, private client: AxiosInstance) {}

  public send(endPoint: string, options: ILoqateReqOptions) {
    options.params.Key = this.key;

    // do not use IP address for biasing
    options.params.IsMiddleware = 'true';

    // restrict search to UK, Ireland, Spain, France and Norway
    options.params.Countries = ['GB', 'IE', 'ES', 'FR', 'NO'].join();

    return this.client.get(`${endPoint}`, options);
  }
}
