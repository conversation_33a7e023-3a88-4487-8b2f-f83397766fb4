import { ConfigService } from '@nestjs/config';
import { HttpStatus, NotImplementedException } from '@nestjs/common';
import { HttpStatusCode } from 'axios';
import {
  ILoqateReqOptions,
  LOQATE_FIND_URL,
  LOQATE_RETRIEVE_URL,
} from '../loqate.types';

export const TEST_LANGUAGE_OVERRIDE_SEARCH = '?text=blah&language=fr';
export const TEST_LANGUAGE_OVERRIDE_SEARCH_OBJECT = {
  text: 'blah',
  language: 'fr',
};
export const TEST_LANGUAGE_ACCEPT_LANG_SEARCH = `?text=blah`;
export const TEST_LANGUAGE_ACCEPT_LANG_SEARCH_OBJECT = {
  text: 'blah',
  language: 'es',
};
export const TEST_LANGUAGE_OVERRIDE_SEARCH_LOQATE_OBJECT = {
  Text: 'blah',
  Language: 'fr',
};
export const TEST_LANGUAGE_ACCEPT_LANG_SEARCH_LOQATE_OBJECT = {
  Text: 'blah',
  Language: 'es',
};

export const TEST_VALID_TEXT_SEARCH = '?text=EC1Y%208QE&limit=2';
export const TEST_VALID_TEXT_SEARCH_OBJECT = { text: 'EC1Y 8QE', limit: '2' };
export const TEST_VALID_TEXT_SEARCH_REQUEST = { text: 'EC1Y 8QE', limit: 2 };
export const TEST_VALID_TEXT_SEARCH_LOQATE_OBJECT = {
  Text: 'EC1Y 8QE',
  Limit: 2,
  Container: undefined,
  Language: 'en',
};
export const TEST_VALID_TEXT_SEARCH_LOQATE_RESPONSE = [
  {
    Id: 'GB|RM|A|8182563|ENG',
    Type: 'Address',
    Text: 'Pod Point Ltd Discovery House 28-42 Banner Street London EC1Y 8QE',
    Highlight: '',
    Description: '',
  },
  {
    Id: 'GB|RM|A|8182561|ENG',
    Type: 'Address',
    Text: 'R I B A Enterprise Discovery House 28-42 Banner Street London EC1Y 8QE',
    Highlight: '',
    Description: '',
  },
];

export const TEST_INVALID_CONTAINER_SEARCH =
  '?text=EC1Y%208QE&containerId=GB%7CRM%7CENG%7C8QE-EC1&limit=2';
export const TEST_INVALID_CONTAINER_SEARCH_OBJECT = {
  text: 'EC1Y 8QE',
  containerId: 'GB|RM|ENG|8QE-EC1',
  limit: '2',
};
export const TEST_INVALID_CONTAINER_SEARCH_REQUEST = {
  text: 'EC1Y 8QE',
  containerId: 'GB|RM|ENG|8QE-EC1',
  limit: 2,
};
export const TEST_INVALID_CONTAINER_SEARCH_LOQATE_OBJECT = {
  Text: 'EC1Y 8QE',
  Container: 'GB|RM|ENG|8QE-EC1',
  Language: 'en',
  Limit: 2,
};
// Loqate seems to return [] rather than an error when invalid data is passed
export const TEST_INVALID_CONTAINER_SEARCH_LOQATE_RESPONSE = [];

export const TEST_VALID_ADDRESS_ID = 'GB|RM|A|8182563';
export const TEST_INVALID_ADDRESS_ID = 'GB|RM|ENG';

export const TEST_VALID_ADDRESS_ID_LOQATE_REPSONSE = {
  Id: 'gb-rm|rjSgi4YBX8V_g8REPjmo',
  Description: ' - 26 Addresses',
  Company: 'Pod Point Ltd',
};
export const TEST_VALID_ADDRESS_ID_REPSONSE = [
  {
    Id: 'gb-rm|rjSgi4YBX8V_g8REPjmo',
    Description: ' - 26 Addresses',
    Company: 'Pod Point Ltd',
  },
];
export const TEST_INVALID_ADDRESS_ID_LOQATE_REPSONSE = {
  Error: '1001',
  Description: 'Id Invalid',
  Cause: 'The Id parameter supplied was invalid.',
};
export const TEST_INVALID_ADDRESS_ID_REPSONSE = {
  statusCode: HttpStatusCode.BadRequest,
  message: '1001 - Id Invalid',
  error: 'The Id parameter supplied was invalid.',
};

export class MockLoqateClient {
  constructor(private config: ConfigService) {}

  public send(endPoint: string, options: ILoqateReqOptions) {
    switch (endPoint) {
      case LOQATE_RETRIEVE_URL:
        switch (options.params.Id) {
          case TEST_INVALID_ADDRESS_ID:
            return {
              data: { Items: [TEST_INVALID_ADDRESS_ID_LOQATE_REPSONSE] },
              status: HttpStatusCode.Ok,
              statusText: HttpStatus.OK,
              headers: {},
              config: {},
              request: {},
            };
            break;
          case TEST_VALID_ADDRESS_ID:
            return {
              data: { Items: [TEST_VALID_ADDRESS_ID_LOQATE_REPSONSE] },
              status: HttpStatusCode.Ok,
              statusText: HttpStatus.OK,
            };
            break;

          default:
            throw new NotImplementedException(options.params.Id);
        }
        break;

      case LOQATE_FIND_URL:
        if (
          options.params.Text == TEST_VALID_TEXT_SEARCH_LOQATE_OBJECT.Text &&
          options.params.Container == null &&
          options.params.Limit == TEST_VALID_TEXT_SEARCH_LOQATE_OBJECT.Limit
        ) {
          return {
            data: {
              Items: TEST_VALID_TEXT_SEARCH_LOQATE_RESPONSE,
            },
            status: HttpStatusCode.Ok,
            statusText: HttpStatus.OK,
          };
        } else if (
          options.params.Text ==
            TEST_INVALID_CONTAINER_SEARCH_LOQATE_OBJECT.Text &&
          options.params.Container ==
            TEST_INVALID_CONTAINER_SEARCH_LOQATE_OBJECT.Container &&
          options.params.Limit ==
            TEST_INVALID_CONTAINER_SEARCH_LOQATE_OBJECT.Limit
        ) {
          return {
            data: {
              Items: TEST_INVALID_CONTAINER_SEARCH_LOQATE_RESPONSE,
            },
            status: HttpStatusCode.Ok,
            statusText: HttpStatus.OK,
          };
        } else if (
          [
            TEST_LANGUAGE_OVERRIDE_SEARCH_LOQATE_OBJECT.Text,
            TEST_LANGUAGE_ACCEPT_LANG_SEARCH_LOQATE_OBJECT.Text,
          ].includes(options.params.Text) &&
          [
            TEST_LANGUAGE_OVERRIDE_SEARCH_LOQATE_OBJECT.Language,
            TEST_LANGUAGE_ACCEPT_LANG_SEARCH_LOQATE_OBJECT.Language,
          ].includes(options.params.Language)
        ) {
          return {
            data: {
              Items: TEST_VALID_TEXT_SEARCH_LOQATE_RESPONSE,
            },
            status: HttpStatusCode.Ok,
            statusText: HttpStatus.OK,
          };
        } else {
          throw new NotImplementedException(options.params);
        }

      default:
        throw new NotImplementedException(endPoint);
    }
  }
}
