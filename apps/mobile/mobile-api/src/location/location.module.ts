import { AuthenticationModule } from '../authentication/authentication.module';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LOQATE_BASE_URL } from './loqate.types';
import { LocationController } from './location.controller';
import { LocationService } from './location.service';
import { LoqateClient } from './loqate.client';
import { Module } from '@nestjs/common';
import { UsersModule } from '../users/users.module';
import axios from 'axios';

@Module({
  imports: [
    AuthenticationModule,
    CacheModule.register(),
    ConfigModule,
    UsersModule,
  ],
  controllers: [LocationController],
  providers: [
    LocationService,
    {
      inject: [ConfigService],
      provide: LoqateClient,
      useFactory: async (config: ConfigService) =>
        new LoqateClient(
          config.get<string>('LOQATE_API_KEY'),
          axios.create({ baseURL: LOQATE_BASE_URL, timeout: 5000 })
        ),
    },
  ],
})
export class LoqateModule {}
