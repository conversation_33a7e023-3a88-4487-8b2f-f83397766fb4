import {
  ApiBadRequestException,
  ApiInternalServerException,
  ApiUnauthorizedException,
} from '@experience/mobile/nest/swagger';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuardToken } from '../authentication/authguard.token';
import {
  ChargeStatsResponse,
  ChargesDtoParam,
  ChargesResponse,
  ChargesStatsDtoParam,
} from './charges.types';
import { ChargesService } from './charges.service';
import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  DriverStatsResponse,
  DriversChargesResponse,
} from '@experience/shared/axios/data-platform-api-client';
import {
  ITokenAuthUser,
  TokenAuthUser,
} from '@experience/mobile/nest/authorisation';

@ApiBearerAuth()
@ApiTags('Charges')
@Controller('charges')
export class ChargesController {
  constructor(private readonly chargesService: ChargesService) {}

  @UseGuards(AuthGuardToken)
  @Get()
  @ApiOperation({
    summary: 'retrieve charger sessions',
    description: 'For given dates it should return charger sessions.',
  })
  @ApiOkResponse({
    description: 'OK response',
    type: ChargesResponse,
  })
  @ApiBadRequestException('Returns bad request when from or to are not dates.')
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async getCharges(
    @Query() query: ChargesDtoParam,
    @TokenAuthUser() user: ITokenAuthUser
  ): Promise<DriversChargesResponse> {
    return this.chargesService.getCharges(query, user);
  }

  @UseGuards(AuthGuardToken)
  @Get('stats')
  @ApiOperation({
    summary: 'retrieve charger stats',
    description: 'For given dates and inteval it should return charger stats.',
  })
  @ApiOkResponse({
    description: 'OK response',
    type: ChargeStatsResponse,
  })
  @ApiBadRequestException('Returns bad request when from or to are not dates.')
  @ApiUnauthorizedException()
  @ApiInternalServerException()
  async getChargesStats(
    @Query() query: ChargesStatsDtoParam,
    @TokenAuthUser() user: ITokenAuthUser
  ): Promise<DriverStatsResponse> {
    return this.chargesService.getChargesStats(query, user);
  }
}
