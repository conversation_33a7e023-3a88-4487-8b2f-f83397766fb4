import {
  type ActionCodeInfo,
  type AuthError,
  checkActionCode,
} from 'firebase/auth';
import { getAuth } from '../../../src/firebase/firebase';
import { getHeaders } from '../../../src/utils';
import { logger } from '@experience/shared/next/logger';

export const DELETE = async (request: Request) => {
  const firebaseOptions = {
    apiKey: process.env.FIREBASE_API_KEY,
    appId: process.env.FIREBASE_APP_ID,
    authDomain: process.env.FIREBASE_AUTH_DOMAIN,
    messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
    projectId: process.env.FIREBASE_PROJECT_ID,
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  };

  let updateValues: ActionCodeInfo;

  const { oobCode, phoneNumber, countryCode } = await request.json();

  try {
    logger.info({ request }, 'checking action code');

    const auth = await getAuth(firebaseOptions);

    updateValues = await checkActionCode(auth, oobCode);
  } catch (error) {
    const authError = error as AuthError;

    logger.error({ request, error }, 'failed to check action code');

    return new Response('Action code error', {
      status: 400,
      statusText: authError.code,
    });
  }

  const {
    data: { email },
  } = updateValues;

  try {
    logger.info(
      { request },
      'sending factor removal request to driver account api'
    );

    return await fetch(
      `${process.env.DRIVER_ACCOUNT_API_BASE_URL}/v1/auth/factor`,
      {
        method: 'DELETE',
        body: JSON.stringify({ email, phoneNumber, countryCode }),
        cache: 'no-store',
        headers: getHeaders(request),
      }
    );
  } catch (error) {
    logger.error({ request, error }, 'failed to remove factor from email');

    return new Response('Error removing factor from email', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  }
};
