networks:
  pod-point:
    name: pod-point
services:
  driver-account-webapp:
    build:
      context: ../../..
      dockerfile: apps/mobile/driver-account-webapp/Dockerfile
    container_name: driver-account-webapp
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
    env_file: .env.local
    networks:
      - pod-point
    platform: linux/amd64
    ports:
      - '5105:5105'
