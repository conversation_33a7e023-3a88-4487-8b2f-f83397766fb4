import { DeepMocked, createMock } from '@golevelup/ts-jest';
import {
  FailedToGetNotificationTokensError,
  FailedToSaveNotificationTokenError,
} from './notifications.errors';
import { MockRepository, createMockRepository } from '../test.utils';
import { NotificationsService } from './notifications.service';
import { PROFILE_FCM_TOKENS_REPOSITORY } from '@experience/mobile/driver-account/database';
import { ProfileFcmToken } from '@experience/mobile/driver-account/database';
import { ProfileUser } from '@experience/mobile/driver-account/database';
import { TOKEN_VALIDITY_IN_DAYS } from './notifications.const';
import { Test } from '@nestjs/testing';
import { UserProfileService } from '../user-profile/user-profile.service';
import { plainToInstance } from 'class-transformer';

describe('NotificationsService', () => {
  const USER_PROFILE = {
    id: 1,
    uuid: '94ef5390-892b-452a-9055-46ad14fbc322',
  };

  const TOKEN = {
    id: 1,
    user: USER_PROFILE,
    fcmToken: 'd7g1h2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l',
  };

  let service: NotificationsService;
  let mockFcmTokensRepository: MockRepository<ProfileFcmToken>;
  let mockUserProfileService: DeepMocked<UserProfileService>;

  beforeEach(async () => {
    jest.clearAllMocks();
    const module = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: PROFILE_FCM_TOKENS_REPOSITORY,
          useValue: createMockRepository(),
        },
        {
          provide: UserProfileService,
          useValue: createMock<UserProfileService>(),
        },
      ],
    }).compile();

    service = module.get(NotificationsService);
    mockFcmTokensRepository = module.get<MockRepository<ProfileFcmToken>>(
      PROFILE_FCM_TOKENS_REPOSITORY
    );
    mockUserProfileService = module.get(UserProfileService);
  });

  afterEach(() => jest.resetAllMocks());

  describe('getTokensForUser()', () => {
    const assertGetTokens = async (
      id: string,
      fcmToken: string,
      createdAt: string,
      updatedAt: string | null
    ) => {
      const userProfile = plainToInstance(ProfileUser, { id: 1 });

      mockUserProfileService.getUserProfile.mockResolvedValue(userProfile);

      mockFcmTokensRepository.findBy.mockImplementation(async () => [
        {
          id,
          user: userProfile,
          fcmToken,
          createdAt,
          updatedAt,
        },
      ]);

      const res = await service.getTokensForUser(id);

      expect(res).toMatchSnapshot();

      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledTimes(1);
      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledWith(id);

      expect(mockFcmTokensRepository.findBy).toHaveBeenCalledTimes(1);
      expect(mockFcmTokensRepository.findBy).toHaveBeenCalledWith({
        user: userProfile,
      });
    };
    it('returns the tokens saved against a given user', async () => {
      await assertGetTokens(
        '7d022987-388f-4439-b7d3-b5126ec8633b',
        'd7g1h2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l',
        '2024-10-24T13:53:00.000Z',
        null
      );
    });

    it('returns the tokens saved against a given user with updatedAt timestamp when they are refreshed', async () => {
      await assertGetTokens(
        '7d022987-388f-4439-b7d3-b5126ec8633b',
        'd7g1h2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l',
        '2024-10-24T13:53:00.000Z',
        '2024-10-30T13:53:00.000Z'
      );
    });

    it('throws a FailedToGetNotificationTokensError if unable to get the tokens', async () => {
      const userProfile = plainToInstance(ProfileUser, { id: 1 });

      mockUserProfileService.getUserProfile.mockResolvedValue(userProfile);
      mockFcmTokensRepository.findBy.mockImplementation(() => {
        throw new Error();
      });

      await expect(
        service.getTokensForUser('94ef5390-892b-452a-9055-46ad14fbc322')
      ).rejects.toThrow(FailedToGetNotificationTokensError);
    });
  });

  describe('deleteToken()', () => {
    it('deletes a token for a given user', async () => {
      mockFcmTokensRepository.setGetOne(jest.fn().mockResolvedValueOnce(TOKEN));

      await service.deleteToken(USER_PROFILE.uuid, TOKEN.fcmToken);

      expect(mockFcmTokensRepository.delete).toHaveBeenCalledTimes(1);
      expect(mockFcmTokensRepository.delete).toHaveBeenCalledWith(TOKEN.id);
    });
  });

  describe('saveToken()', () => {
    it('creates and saves a record for the given token against the user', async () => {
      const userProfile = plainToInstance(ProfileUser, USER_PROFILE);

      mockUserProfileService.getUserProfile.mockResolvedValue(userProfile);
      mockFcmTokensRepository.create.mockReturnValue(new ProfileFcmToken());

      await service.saveToken(USER_PROFILE.uuid, {
        token: TOKEN.fcmToken,
        timestamp: new Date('2024-10-24T13:53:00.000Z'),
      });

      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledTimes(1);
      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledWith(
        USER_PROFILE.uuid
      );

      expect(mockFcmTokensRepository.create).toHaveBeenCalledTimes(1);
      expect(mockFcmTokensRepository.create).toHaveBeenCalledWith({
        user: userProfile,
        createdAt: new Date('2024-10-24T13:53:00.000Z'),
        updatedAt: new Date('2024-10-24T13:53:00.000Z'),
        fcmToken: TOKEN.fcmToken,
      });
    });

    it('updates a record for the given token against the user', async () => {
      const userProfile = plainToInstance(ProfileUser, USER_PROFILE);
      const fcmToken = new ProfileFcmToken();
      fcmToken.fcmToken = TOKEN.fcmToken;

      mockUserProfileService.getUserProfile.mockResolvedValue(userProfile);
      mockFcmTokensRepository.findOneBy.mockImplementationOnce(() =>
        Promise.resolve(fcmToken)
      );

      await service.saveToken(USER_PROFILE.uuid, {
        token: TOKEN.fcmToken,
        timestamp: new Date('2024-10-24T13:53:00.000Z'),
      });

      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledTimes(1);
      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledWith(
        USER_PROFILE.uuid
      );

      expect(mockFcmTokensRepository.findOneBy).toHaveBeenCalledTimes(1);
      expect(mockFcmTokensRepository.findOneBy).toHaveBeenCalledWith({
        fcmToken: TOKEN.fcmToken,
      });
      expect(mockFcmTokensRepository.create).toHaveBeenCalledTimes(0);
      expect(mockFcmTokensRepository.save).toHaveBeenCalledWith(fcmToken);
    });

    it('throws a FailedToSaveNotificationTokenError if unable to save the token', async () => {
      const userProfile = plainToInstance(ProfileUser, USER_PROFILE);

      mockUserProfileService.getUserProfile.mockResolvedValue(userProfile);
      mockFcmTokensRepository.create.mockReturnValue(new ProfileFcmToken());
      mockFcmTokensRepository.save.mockImplementation(() => {
        throw new Error();
      });

      await expect(
        service.saveToken(USER_PROFILE.uuid, {
          token: TOKEN.fcmToken,
          timestamp: new Date('2024-10-24T13:53:00.000Z'),
        })
      ).rejects.toThrow(FailedToSaveNotificationTokenError);

      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledTimes(1);
      expect(mockUserProfileService.getUserProfile).toHaveBeenCalledWith(
        USER_PROFILE.uuid
      );

      expect(mockFcmTokensRepository.create).toHaveBeenCalledTimes(1);
      expect(mockFcmTokensRepository.create).toHaveBeenCalledWith({
        user: userProfile,
        createdAt: new Date('2024-10-24T13:53:00.000Z'),
        fcmToken: TOKEN.fcmToken,
        updatedAt: new Date('2024-10-24T13:53:00.000Z'),
      });
    });
  });

  describe('removeStaleTokens()', () => {
    it('should remove stale tokens', async () => {
      const updatedAt = new Date();
      updatedAt.setDate(updatedAt.getDate() - (TOKEN_VALIDITY_IN_DAYS + 1));

      mockFcmTokensRepository.findBy.mockImplementation(() => [
        { ...TOKEN, updatedAt },
      ]);

      await service.removeStaleTokens();

      expect(mockFcmTokensRepository.delete).toHaveBeenCalledTimes(1);
      expect(mockFcmTokensRepository.delete).toHaveBeenCalledWith([TOKEN.id]);
    });
  });
});
