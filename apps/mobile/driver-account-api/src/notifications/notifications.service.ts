import {
  FailedToGetNotificationTokensError,
  FailedToSaveNotificationTokenError,
} from './notifications.errors';
import { FcmTokenPayload } from './notifications.types';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { LessThan, Repository } from 'typeorm';
import { PROFILE_FCM_TOKENS_REPOSITORY } from '@experience/mobile/driver-account/database';
import { ProfileFcmToken } from '@experience/mobile/driver-account/database';
import { TOKEN_VALIDITY_IN_DAYS } from './notifications.const';
import { UserProfileService } from '../user-profile/user-profile.service';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    @Inject(PROFILE_FCM_TOKENS_REPOSITORY)
    private readonly fcmTokensRepository: Repository<ProfileFcmToken>,
    private readonly userProfileService: UserProfileService
  ) {}

  async getTokensForUser(uuid: string): Promise<FcmTokenPayload[]> {
    this.logger.log(
      { uuid },
      'attempting to get notifications tokens for user...'
    );

    try {
      const userProfile = await this.userProfileService.getUserProfile(uuid);

      this.logger.log(
        { uuid, userProfileId: userProfile.id },
        'got user profile'
      );

      const tokens = await this.fcmTokensRepository.findBy({
        user: userProfile,
      });

      this.logger.log(
        { uuid, tokensLength: tokens.length },
        'got tokens for user'
      );

      return tokens.map((entity) => ({
        token: entity.fcmToken,
        timestamp: entity.updatedAt ?? entity.createdAt,
      }));
    } catch (error) {
      this.logger.error(
        { uuid, error },
        'failed to get notification tokens for user'
      );

      throw new FailedToGetNotificationTokensError();
    }
  }

  async saveToken(
    uuid: string,
    { token, timestamp }: FcmTokenPayload
  ): Promise<void> {
    this.logger.log({ uuid }, 'attempting to save notification token');

    try {
      const userProfile = await this.userProfileService.getUserProfile(uuid);

      this.logger.log(
        { uuid, userProfileId: userProfile.id },
        'got user profile'
      );

      let tokenEntity = await this.fcmTokensRepository.findOneBy({
        fcmToken: token,
      });

      if (tokenEntity) {
        tokenEntity.updatedAt = timestamp;
      } else {
        tokenEntity = this.fcmTokensRepository.create({
          user: userProfile,
          createdAt: timestamp,
          fcmToken: token,
          updatedAt: timestamp,
        });
      }

      await this.fcmTokensRepository.save(tokenEntity);

      this.logger.log(
        { uuid },
        'successfully saved notification token for user'
      );
    } catch (error) {
      this.logger.error({ uuid, error }, 'failed to save token');

      throw new FailedToSaveNotificationTokenError();
    }
  }

  async deleteToken(userId: string, token: string) {
    this.logger.log(
      { userId, token },
      'attempting to delete notification token'
    );

    try {
      const dbToken = await this.fcmTokensRepository
        .createQueryBuilder('t')
        .innerJoinAndSelect('users', 'u', 't.user = u.id')
        .where('t.fcmToken = :fcmToken', { fcmToken: token })
        .andWhere('u.uuid = :uuid', { uuid: userId })
        .getOne();

      if (!dbToken) {
        this.logger.log({ userId, token }, 'no token found');

        return;
      }

      await this.fcmTokensRepository.delete(dbToken.id);

      this.logger.log({ userId, token }, 'deleted notification token');
    } catch (error) {
      this.logger.error(
        { userId, token, error },
        'error while attempting to delete token'
      );

      throw error;
    }
  }

  async removeStaleTokens() {
    this.logger.log('attempting to remove stale tokens');

    const validFrom = new Date();

    validFrom.setDate(validFrom.getDate() - TOKEN_VALIDITY_IN_DAYS);

    this.logger.log({ validFrom }, 'removing stale tokens older than');

    const results = await this.fcmTokensRepository.findBy({
      updatedAt: LessThan(validFrom),
    });

    const tokensToDelete = results.map((result) => result.id);

    if (tokensToDelete.length > 0) {
      await this.fcmTokensRepository.delete(tokensToDelete);
    }

    this.logger.log({ tokensToDelete, validFrom }, 'deleted tokens');
  }
}
