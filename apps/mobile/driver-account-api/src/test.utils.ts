import { Repository } from 'typeorm';
import { jest } from '@jest/globals';

export type MockRepository<T> = Partial<
  Record<keyof Repository<T>, jest.Mock>
> & {
  /* eslint-disable @typescript-eslint/no-explicit-any */
  setGetOne: (fn: any) => void;
  setGetMany: (fn: any) => void;
  setWhere: (fn: any) => void;
  setLeftJoinAndMapMany: (fn: any) => void;
  setLeftJoinAndMapOne: (fn: any) => void;
  setTake: (fn: any) => void;
  /* eslint-enable */
};

export const createMockRepository = <T>(): MockRepository<T> => {
  let getOne = jest.fn().mockReturnThis();
  let getMany = jest.fn().mockReturnThis();
  let where = jest.fn().mockReturnThis();
  const andWhere = jest.fn().mockReturnThis();
  const innerJoinAndSelect = jest.fn().mockReturnThis();
  let leftJoinAndMapMany = jest.fn().mockReturnThis();
  let leftJoinAndMapOne = jest.fn().mockReturnThis();
  let take = jest.fn().mockReturnThis();

  return {
    create: jest.fn(),
    findOneBy: jest.fn(),
    findBy: jest.fn(),
    find: jest.fn(),
    delete: jest.fn(),
    merge: jest
      .fn()
      .mockImplementation((a: object, b: object) => ({ ...a, ...b })),
    remove: jest.fn(),
    save: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      where,
      andWhere,
      orderBy: jest.fn().mockReturnThis(),
      getOne,
      getMany,
      innerJoinAndSelect,
      leftJoinAndMapMany,
      leftJoinAndMapOne,
      take,
    })),
    /* eslint-disable @typescript-eslint/no-explicit-any */
    setGetOne: (fn: any) => (getOne = fn),
    setGetMany: (fn: any) => (getMany = fn),
    setWhere: (fn: any) => (where = fn),
    setTake: (fn: any) => (take = fn),
    setLeftJoinAndMapMany: (fn: any) => (leftJoinAndMapMany = fn),
    setLeftJoinAndMapOne: (fn: any) => (leftJoinAndMapOne = fn),
    /* eslint-enable */
  };
};
