import * as crypto from 'crypto';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OAuthApplication } from '@experience/mobile/driver-account/database';
import { OAuthClientService } from './oauth.client.service';
import { OAuthCustomClaims } from './types/oauth-custom-claims';
import { getAuth } from '@experience/shared/firebase/admin';

export type AuthorizationCodeFlowParams = {
  clientId: string;
  redirectUri: string;
  responseType: string;
  codeChallengeMethod: string;
  codeChallenge: string;
  scope?: string;
  state?: string;
};

type KeysEnum<T> = { [P in keyof Required<T>]: true | false };
const PARAMS_KEY_REQUIRED: KeysEnum<AuthorizationCodeFlowParams> = {
  clientId: true,
  redirectUri: true,
  codeChallenge: true,
  codeChallengeMethod: true,
  responseType: true,
  scope: false,
  state: false,
};

@Injectable()
export class OAuthAuthorizeService {
  private readonly logger = new Logger(OAuthAuthorizeService.name);

  private readonly firebaseAuth = getAuth();

  private readonly identityBaseUrl: string;

  constructor(
    private readonly oAuthClientService: OAuthClientService,
    private readonly configService: ConfigService
  ) {
    this.identityBaseUrl = this.configService.getOrThrow(
      'IDENTITY_BASE_URL_CLASSIC'
    );
    if (!this.identityBaseUrl.startsWith('http')) {
      this.identityBaseUrl = `https://${this.identityBaseUrl}`;
    }
  }

  async authorize(params: AuthorizationCodeFlowParams): Promise<string> {
    // implements /authorize
    // see: https://www.rfc-editor.org/rfc/rfc6749#section-3.1

    const validated = await this.validateAuthorizeCodeParams(params);
    if (validated.valid === false) {
      return validated.url;
    }

    // forward the state (query strings) to the redirect uri
    // note that PKCE allows the exposure of `code_challenge` to the client
    //  because it has been hashed, and the client cannot reverse it.
    //  see: https://datatracker.ietf.org/doc/html/rfc7636#section-7.2
    const uri = new URL('/oauth/login', this.identityBaseUrl);
    uri.searchParams.set('client_id', params.clientId);
    uri.searchParams.set('redirect_uri', params.redirectUri);
    uri.searchParams.set('response_type', params.responseType);
    uri.searchParams.set('code_challenge_method', params.codeChallengeMethod);
    uri.searchParams.set('code_challenge', params.codeChallenge);
    if (validated.scope) {
      uri.searchParams.set('scope', validated.scope);
    }
    if (params.state) {
      uri.searchParams.set('state', params.state);
    }
    return uri.toString();
  }

  async consent(
    params: AuthorizationCodeFlowParams & {
      userId: string;
    }
  ): Promise<string> {
    // implements the exchange of user consent (login and agreement to
    //  third-party access) for an authorization code.
    // i.e. [/authorize] -> [/login] -> [/consent] -> [/token]

    const validated = await this.validateAuthorizeCodeParams(params);
    if (validated.valid === false) {
      return validated.url;
    }

    // validation of the token requires knowing the original redirect uri
    //  so encode it into the token as a nonce claim.
    // see: https://www.rfc-editor.org/rfc/rfc6749#section-4.1.3
    const nonce = this.generateNonce({
      plainTextPrefix: params.codeChallenge,
      hashedSuffix: params.redirectUri,
      secretSalt: validated.client.secretSalt,
    });

    const claims: OAuthCustomClaims = {
      scope: validated.scope,
      client_id: params.clientId,
      'https://pod-point.com/oauth/third_party': true,
      'https://pod-point.com/oauth/nonce': nonce,
    };
    let code: string;
    try {
      const token = await this.firebaseAuth.createCustomToken(
        params.userId,
        claims
      );
      // firebase custom tokens are just jwt's that can be exchanged using the
      // firebase restful api.
      // we want to enforce additional checks, so symmetrically encrypt the
      // token so that the recipient cannot just by-pass our endpoint.
      const iv = crypto.randomBytes(16);
      const key = crypto
        .createHash('sha256')
        .update(validated.client.secretSalt)
        .digest();
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      code = `${iv.toString('base64url')}.`;
      code += cipher.update(token, 'utf8', 'base64url');
      code += cipher.final('base64url');
    } catch (error) {
      this.logger.error({ params, error }, 'Failed to create custom token');
      throw new BadRequestException('invalid_request');
    }

    const uri = new URL(params.redirectUri);
    uri.searchParams.set('code', code);
    if (params.state) {
      uri.searchParams.set('state', params.state);
    }
    return uri.toString();
  }

  public generateNonce(params: {
    plainTextPrefix: string;
    hashedSuffix: string;
    secretSalt: string;
  }): string {
    // rfc7636 allows session information inside the code, so long as it is
    // opaque to the client.
    // code_challenge is already hashed, however we also need to match the
    // redirect_uri to prevent replay attacks.
    const hmac = crypto.createHmac('sha256', params.secretSalt);
    hmac.update(params.hashedSuffix);
    return `${params.plainTextPrefix}.${hmac.digest('hex')}`;
  }

  private async validateAuthorizeCodeParams(
    params: AuthorizationCodeFlowParams
  ): Promise<
    | {
        valid: false;
        url: string;
      }
    | {
        valid: true;
        client: OAuthApplication;
        scope: string | null;
      }
  > {
    // the oauth authorization flow results in two types of errors.
    // where the client id or redirect uri is invalid, we cannot redirect,
    // because we cannot trust that the location we are redirecting to is safe.
    // in all other cases we redirect using the oauth error codes to allow the
    // client to handle the error.
    // see: https://www.rfc-editor.org/rfc/rfc6749#section-4.1.2.1

    // validate the client id
    const client = await this.oAuthClientService.getClient(params);
    if (!client) {
      // https://www.rfc-editor.org/rfc/rfc6749#section-5.2
      this.logger.error({ params }, 'Client not found');
      throw new BadRequestException('invalid_client');
    }

    // validate that the redirect uri is correctly formed and allowed
    try {
      new URL(params.redirectUri);
    } catch (error) {
      this.logger.error({ params, error }, 'Invalid redirect URI');
      throw new BadRequestException('invalid_request');
    }
    if (
      typeof params.redirectUri !== 'string' ||
      !client.redirectUris.includes(params.redirectUri)
    ) {
      this.logger.error({ params }, 'Redirect URI not allowed');
      throw new BadRequestException('invalid_request');
    }
    const uri = new URL(params.redirectUri);

    // validate params exist and are strings (everything else is conditional)
    // note that additional keys are allowed by the spec; we ignore them.
    for (const [key, required] of Object.entries(PARAMS_KEY_REQUIRED)) {
      const value = params[key];
      if (!required && value === undefined) {
        continue;
      }
      if (typeof value !== 'string') {
        this.logger.error(
          { params, key, required, value },
          'Invalid request (expected string)'
        );
        uri.searchParams.set('error', 'invalid_request');
        return { valid: false, url: uri.toString() };
      }
    }

    // client _must_ be using the code grant type
    if (
      client.grantType !== 'authorization_code' ||
      params.responseType.toLowerCase() !== 'code'
    ) {
      uri.searchParams.set('error', 'unauthorized_client');
      return { valid: false, url: uri.toString() };
    }

    // PKCE using sha 256 extensions is required for code flow
    if (
      params.codeChallengeMethod.toUpperCase() !== 'S256' ||
      !params.codeChallenge
    ) {
      uri.searchParams.set('error', 'unsupported_response_type');
      return { valid: false, url: uri.toString() };
    }

    // do not allow state to be unbounded or ambiguously defined
    if (
      params.state &&
      params.state.length > 255 &&
      params.state.trim().length > 0
    ) {
      uri.searchParams.set('error', 'invalid_request');
      return { valid: false, url: uri.toString() };
    }

    // the requested scopes are mostly just a guideline, we are allowed to
    // ignore any scopes that the client is not allowed to access.
    // see: https://www.rfc-editor.org/rfc/rfc6749#section-3.3
    const scope = (params.scope || '')
      .split(' ')
      .filter((s) => client.allowedScopes.map((a) => a.name).includes(s))
      .join(' ');

    return {
      valid: true,
      client,
      scope: scope.length > 0 ? scope : null,
    };
  }
}
