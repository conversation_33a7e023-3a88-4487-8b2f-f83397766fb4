import { OAUTH_APPLICATION_REPOSITORY } from '@experience/mobile/driver-account/database';
import { OAuthApplication } from '@experience/mobile/driver-account/database';
import { OAuthClientService } from './oauth.client.service';
import { Repository } from 'typeorm';
import { Test } from '@nestjs/testing';

describe(OAuthClientService.name, () => {
  let service: OAuthClientService;
  let oAuthApplicationRepository: jest.Mocked<Repository<OAuthApplication>>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        OAuthClientService,
        {
          provide: OAUTH_APPLICATION_REPOSITORY,
          useValue: {
            findOne: jest.fn(),
          } as jest.Mocked<Partial<Repository<OAuthApplication>>>,
        },
      ],
    }).compile();

    service = module.get<OAuthClientService>(OAuthClientService);
    oAuthApplicationRepository = module.get(OAUTH_APPLICATION_REPOSITORY);
  });

  it('gets a client by its client id', async () => {
    const client: Omit<OAuthApplication, 'id'> = {
      allowedScopes: [{ id: 1, name: 'read:example' }],
      grantType: 'authorization_code',
      imageUrl: 'https://example.com/image.png',
      websiteUrl: 'https://example.com',
      maximumSessionLengthSeconds: 3_600,
      name: 'Example',
      redirectUris: ['https://example.com/redirect'],
      secretSalt: 'salty',
    };
    oAuthApplicationRepository.findOne.mockImplementation(async (q) => ({
      id: (q.where as unknown as OAuthApplication).id,
      ...client,
    }));

    const response = await service.getClient({
      clientId: 'Ej5FZ-ibEtOkVkJmFBdAAA',
    });
    expect(response).toEqual({
      ...client,
      id: '123e4567-e89b-12d3-a456-************',
      clientId: 'Ej5FZ-ibEtOkVkJmFBdAAA',
    });
    expect(oAuthApplicationRepository.findOne).toHaveBeenCalledTimes(1);
  });

  it.each([
    null, // invalid type
    '123e4567-e89b-12d3-a456-************', // direct uuid access
    'not-a-uuid', // unconvertible
    'Ej5FZ-ibEtOkVkJmFBdAAA==', // padding
    'Ej5FZ+ibEtOkVkJmFBdAAA', // base64 (not url) encoding
    'Ej5FZ-ibEtOkVkJmFBdAAAab', // out of range
    'Ej5FZ-ibEtOkVkJmFBdAAAlong', // too long,
    'Ej5FZ-ibEtOkVkJmFBdAA', // too short
  ])('returns null if the client id (%p) is invalid', async (clientId) => {
    const client: Omit<OAuthApplication, 'id'> = {
      allowedScopes: [{ id: 1, name: 'read:example' }],
      grantType: 'authorization_code',
      imageUrl: 'https://example.com/image.png',
      websiteUrl: 'https://example.com',
      maximumSessionLengthSeconds: 3_600,
      name: 'Example',
      redirectUris: ['https://example.com/redirect'],
      secretSalt: 'salty',
    };
    oAuthApplicationRepository.findOne.mockImplementation(async (q) => ({
      id: (q.where as unknown as OAuthApplication).id,
      ...client,
    }));

    const response = await service.getClient({ clientId });
    expect(response).toBeNull();
  });

  it('returns null if the client is not found', async () => {
    oAuthApplicationRepository.findOne.mockReturnValue(null);

    const response = await service.getClient({
      clientId: 'Ej5FZ-ibEtOkVkJmFBdAAA',
    });
    expect(response).toBeNull();
  });
});
