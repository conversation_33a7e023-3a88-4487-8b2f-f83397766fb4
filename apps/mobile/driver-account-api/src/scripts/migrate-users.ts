import {
  AuthServiceSequelizeModule,
  Users as AuthServiceUsers,
  RoleUser,
} from '@experience/shared/sequelize/auth-service';
import { INestApplication, Logger, Module } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Op } from 'sequelize';
import { UserImportRecord } from 'firebase-admin/auth';
import { getAuth } from '@experience/shared/firebase/admin';
import { splitArrayIntoChunks } from '@experience/shared/typescript/utils';

@Module({
  imports: [AuthServiceSequelizeModule],
  providers: [
    {
      provide: 'AUTH_SERVICE_USERS_REPOSITORY',
      useValue: AuthServiceUsers,
    },
  ],
})
class AppModule {}

const logger = new Logger();

const getAuthServiceUsers = async (
  app: INestApplication,
  queryParam: string
): Promise<AuthServiceUsers[]> => {
  const token = 'AUTH_SERVICE_USERS_REPOSITORY';
  const authServiceUsersRepository = app.get<typeof AuthServiceUsers>(token);
  return await authServiceUsersRepository.findAll({
    where: { email: { [Op.like]: queryParam } },
    include: [
      {
        model: RoleUser,
        as: 'roleUsers',
        where: { role_id: null },
        required: false,
      },
    ],
  });
};

const buildUserImportRecords = (
  authServiceUsers: AuthServiceUsers[],
  emailVerified: boolean
): UserImportRecord[] =>
  authServiceUsers.map((authServiceUser) =>
    buildUserImportRecord(authServiceUser, emailVerified)
  );

const buildUserImportRecord = (
  authServiceUser: AuthServiceUsers,
  emailVerified: boolean
): UserImportRecord => ({
  email: authServiceUser.email.trim(),
  emailVerified,
  passwordHash: authServiceUser.password
    ? Buffer.from(authServiceUser.password)
    : undefined,
  uid: authServiceUser.uuid,
  customClaims: { id: authServiceUser.id },
  disabled: !!authServiceUser.deletedAt,
});

const importFirebaseUsers = async (
  authServiceUsers: AuthServiceUsers[],
  emailVerified: boolean
): Promise<void> => {
  const userImportRecords = buildUserImportRecords(
    authServiceUsers,
    emailVerified
  );
  const emailsWithErrors = [];
  for (const chunk of splitArrayIntoChunks(userImportRecords, 1000)) {
    logger.log(`importing ${chunk.length} users into firebase`);
    await getAuth()
      .importUsers(chunk, {
        hash: {
          algorithm: 'BCRYPT',
          rounds: 10,
        },
      })
      .then((result) => {
        logger.log(result);
        result.errors.forEach((e) => {
          const importRecord = chunk[e.index];
          logger.log(importRecord);
          emailsWithErrors.push(importRecord.email);
        });
      });
  }

  logger.log(
    `From ${
      userImportRecords.length
    } accounts, the emails that failed to be migrated: ${emailsWithErrors.join(
      ','
    )}`
  );
};

const deleteFirebaseUsers = async (uuids: string[]): Promise<void> => {
  for (const chunk of splitArrayIntoChunks(uuids, 1000)) {
    logger.log(`deleting ${chunk.length} users from firebase`);
    await getAuth()
      .deleteUsers(chunk)
      .then((result) => logger.log(result));
  }
};

const deleteAllFirebaseUsers = async (pageToken?: string): Promise<void> => {
  const listUsersResult = await getAuth().listUsers(1000, pageToken);
  const uuids = listUsersResult.users.map((userRecord) => userRecord.uid);
  await deleteFirebaseUsers(uuids);
  if (listUsersResult.pageToken) {
    await deleteAllFirebaseUsers(listUsersResult.pageToken);
  }
};
const fetchUser = async (userUid: string) => {
  const user = await getAuth().getUser(userUid);
  logger.log(`Successfully fetched user data: ${JSON.stringify(user)}`);
};

const fetchUsers = async (userUids: { uid: string; email: string }[]) => {
  try {
    const getUsersResult = await getAuth().getUsers(userUids);
    if (getUsersResult.users.length < userUids.length) {
      logger.log(
        `Successfully fetched user data: ${getUsersResult.users.length} out of ${userUids.length}`
      );
    }
    return getUsersResult;
  } catch (error) {
    logger.log('Error fetching user data:', error);
  }
};

const migrateUsers = async (queryParam: string, emailVerified = true) => {
  logger.log(`Will run the migration with ${queryParam} and ${emailVerified}`);
  const app = await NestFactory.create(AppModule);
  const authServiceUsers = await getAuthServiceUsers(app, queryParam);
  await importFirebaseUsers(authServiceUsers, emailVerified);
  await app.close();
};

const findMissingUsers = async (queryParam: string) => {
  logger.log(
    `Will run the script for finding missing emails for emails that start with ${queryParam}}`
  );
  const app = await NestFactory.create(AppModule);
  const authServiceUsers = await getAuthServiceUsers(app, queryParam);

  const filter = authServiceUsers.map((authServiceUser) => ({
    uid: authServiceUser.uuid,
    email: authServiceUser.email,
  }));

  for (const chunk of splitArrayIntoChunks(filter, 100)) {
    const result = await fetchUsers(chunk);
    if (result.notFound.length > 0) {
      result.notFound.forEach((userIdentifier) => {
        logger.log(`Missing emails: ${JSON.stringify(userIdentifier)}`);
      });
    }
  }
};

if (process.argv.includes('--delete')) {
  deleteAllFirebaseUsers().catch((error) => logger.error(error));
}
//local test util to retrieve full user state from GIP
else if (process.argv.includes('--fetch')) {
  if (process.argv.length === 3) {
    logger.error(
      'Missing id e.g nx run driver-account-api:migrate-users --fetch e7116ca5-5f6b-44a2-b7e9-************'
    );
    process.exit(1);
  }
  if (process.argv[3].includes('--includes=')) {
    // eslint-disable-next-line prefer-destructuring
    const queryParam = process.argv[3].split('=')[1];
    findMissingUsers(queryParam).catch((error) => logger.error(error));
  } else {
    (async () => {
      await fetchUser(process.argv[3]);
    })();
  }
} else {
  if (process.argv.length < 3 || !process.argv[2].includes('--includes=')) {
    logger.error(
      'Missing id e.g nx run driver-account-api:migrate-users --includes=a%'
    );
    process.exit(1);
  }
  // eslint-disable-next-line prefer-destructuring
  const queryParam = process.argv[2].split('=')[1];
  const emailVerified = !(
    process.argv[3] && process.argv[3] === '--do-not-verify'
  );
  migrateUsers(queryParam, emailVerified).catch((error) => logger.error(error));
}
