import {
  AuthServiceSequelizeModule,
  Users as AuthServiceUsers,
} from '@experience/shared/sequelize/auth-service';
import {
  BillingAccounts,
  PodadminSequelizeModule,
  Users,
} from '@experience/shared/sequelize/podadmin';
import { CustomerApi } from '@experience/billing-api/api-client';
import { INestApplication } from '@nestjs/common';
import { MigrateService } from './migrate.service';
import { MigrateUserService } from './migrate.user.service';
import { OXRCurrencyConverter } from '@experience/shared/axios/currency-converter-client';
import { PodAdminMigrateUserService } from './pod-admin.migrate.user.service';
import { QueueConsumerService } from '@experience/shared/nest/aws/sqs-module';
import { Test, TestingModule } from '@nestjs/testing';
import { UserEventBody } from './types';
import { createMock } from '@golevelup/ts-jest';
import { jest } from '@jest/globals';

export type MockRepository = Partial<
  Record<keyof typeof AuthServiceUsers, jest.Mock>
>;
export const createMockRepository = (): MockRepository => ({
  findOne: jest.fn(),
  findAll: jest.fn(),
  update: jest.fn(),
});

const rawMessageFixture = {
  MessageId: '8cdbc6b4-eafe-4797-baa6-2b99500e503a',
  ReceiptHandle: 'AQEBhQ/7lw==',
  MD5OfBody: 'b9dfe4b2d92848e6b4a0d80990788cd7',
  Body: `{
      "Type" : "Notification",
      "MessageId" : "8c3aab26-1435-51b5-9859-f644954aa6e0",
      "TopicArn" : "arn:aws:sns:eu-west-1:XXXXXXXXX:accounts-service-user-events-staging",
      "Subject" : "users.updated",
      "Message" : "{\\"action\\":\\"updated\\",\\"data\\":{\\"uuid\\":\\"789deca6-402f-481c-b2f9-43eb454b925f\\",\\"first_name\\":\\"murray5\\",\\"last_name\\":\\"King\\",\\"email\\":\\"<EMAIL>\\",\\"locale\\":\\"ie\\",\\"roles\\":[],\\"preferences\\":[{\\"id\\":12775,\\"user_id\\":7059718,\\"key\\":\\"unitOfDistance\\",\\"value\\":\\"km\\",\\"created_at\\":\\"2023-08-02T09:48:01.000000Z\\",\\"updated_at\\":\\"2023-08-02T09:48:01.000000Z\\"}],\\"email_verified_at\\":null,\\"created_at\\":\\"2023-08-02T09:48:01.000000Z\\",\\"updated_at\\":\\"2023-08-25T10:15:27.000000Z\\",\\"deleted_at\\":null},\\"socket\\":null}",
      "Timestamp" : "2023-08-25T10:15:27.804Z"
    }`,
};

describe('MigrateService', () => {
  let migrateService: MigrateService;
  let migrateUserService: MigrateUserService;
  let queueConsumerService: QueueConsumerService;
  let app: INestApplication;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AuthServiceSequelizeModule, PodadminSequelizeModule],
      providers: [
        MigrateService,
        PodAdminMigrateUserService,
        {
          provide: QueueConsumerService,
          useValue: { start: jest.fn(), setMessageHandler: jest.fn() },
        },
        {
          provide: 'AUTH_SERVICE_USERS_REPOSITORY',
          useValue: createMock<typeof AuthServiceUsers>(),
        },
        {
          provide: 'USERS_REPOSITORY',
          useValue: createMock<typeof Users>(),
        },
        {
          provide: MigrateUserService,
          useValue: {
            migrateUser: jest.fn(),
            fetchUserFromAuth: jest.fn(),
            updateBillingAccountIfNeeded: jest.fn(),
            disableUserInFirebase: jest.fn(),
          },
        },
        {
          provide: 'BILLING_ACCOUNTS_REPOSITORY',
          useValue: createMock<typeof BillingAccounts>(),
        },
        {
          provide: 'AUTHORISERS_REPOSITORY',
          useValue: createMock<typeof BillingAccounts>(),
        },
        {
          provide: OXRCurrencyConverter,
          useValue: createMock<OXRCurrencyConverter>(),
        },
        {
          provide: CustomerApi,
          useValue: new CustomerApi(),
        },
      ],
    }).compile();

    app = module.createNestApplication();
    //for app bootstrap test;
    await app.init();

    migrateService = module.get<MigrateService>(MigrateService);
    migrateUserService = module.get(MigrateUserService);
    queueConsumerService = module.get(QueueConsumerService);
    jest.spyOn(migrateUserService, 'migrateUser').mockResolvedValue();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(migrateService).toBeDefined();
  });

  it('should extract user uuid, query auth service, and migrate user to gip', async () => {
    const userEventBody: UserEventBody = {
      action: 'created',
      data: {
        id: 1,
        uuid: 'aUuid-XXXX',
        email: '<EMAIL>',
        first_name: 'firstName',
        last_name: 'lastName',
        locale: 'en',
        preferences: [],
      },
    };
    const mockCreateMessage = {
      Body: JSON.stringify({ Message: JSON.stringify(userEventBody) }),
    };
    jest.spyOn(migrateUserService, 'fetchUserFromAuth').mockResolvedValue({
      email: '<EMAIL>',
      emailVerified: false,
      passwordHash: Buffer.from('aPasswordHash'),
      uid: 'aUuid-XXXX',
      customClaims: {
        id: 1,
      },
    });

    await migrateService.handleMessage(mockCreateMessage);

    expect(migrateUserService.migrateUser).toHaveBeenCalledWith({
      email: '<EMAIL>',
      emailVerified: false,
      passwordHash: Buffer.from('aPasswordHash'),
      uid: 'aUuid-XXXX',
      customClaims: {
        id: 1,
      },
    });
  });

  it('should extract user uuid, query auth service, and update user to gip', async () => {
    const userEventBody: UserEventBody = {
      action: 'updated',
      data: {
        id: 1,
        uuid: 'aUuid-XXXX',
        email: '<EMAIL>',
        first_name: 'firstName',
        last_name: 'lastName',
        locale: 'en',
        preferences: [],
      },
    };
    const mockCreateMessage = {
      Body: JSON.stringify({ Message: JSON.stringify(userEventBody) }),
    };
    jest.spyOn(migrateUserService, 'fetchUserFromAuth').mockResolvedValue({
      email: '<EMAIL>',
      emailVerified: true,
      passwordHash: Buffer.from('aPasswordHash'),
      uid: 'aUuid-XXXX',
      customClaims: {
        id: 1,
      },
    });
    jest
      .spyOn(migrateUserService, 'updateBillingAccountIfNeeded')
      .mockResolvedValue();

    await migrateService.handleMessage(mockCreateMessage);

    expect(migrateUserService.migrateUser).toHaveBeenCalledWith({
      email: '<EMAIL>',
      emailVerified: true,
      passwordHash: Buffer.from('aPasswordHash'),
      uid: 'aUuid-XXXX',
      customClaims: {
        id: 1,
      },
    });
  });

  it('should throw exception up if update user fails so message is never acked', async () => {
    const userEventBody: UserEventBody = {
      action: 'updated',
      data: {
        id: 1,
        uuid: 'aUuid-XXXX',
        email: '<EMAIL>',
        first_name: 'firstName',
        last_name: 'lastName',
        locale: 'en',
        preferences: [],
      },
    };
    const mockCreateMessage = {
      Body: JSON.stringify({ Message: JSON.stringify(userEventBody) }),
    };

    jest.spyOn(migrateUserService, 'migrateUser').mockImplementation(() => {
      throw new Error('testMessage');
    });

    await expect(
      migrateService.handleMessage(mockCreateMessage)
    ).rejects.toThrow('Error in message processing Error: testMessage');
  });

  it('should extract user uuid, query auth service, and migrate user to gip when there is no password', async () => {
    const userEventBody: UserEventBody = {
      action: 'created',
      data: {
        id: 1,
        uuid: 'aUuid-XXXX',
        email: '<EMAIL>',
        first_name: 'firstName',
        last_name: 'lastName',
        locale: 'en',
        preferences: [],
      },
    };
    const mockCreateMessage = {
      Body: JSON.stringify({ Message: JSON.stringify(userEventBody) }),
    };
    jest.spyOn(migrateUserService, 'fetchUserFromAuth').mockResolvedValue({
      email: '<EMAIL>',
      emailVerified: false,
      passwordHash: undefined,
      uid: 'aUuid-XXXX',
      customClaims: {
        id: 1,
      },
    });

    await migrateService.handleMessage(mockCreateMessage);

    expect(migrateUserService.migrateUser).toHaveBeenCalledWith({
      email: '<EMAIL>',
      emailVerified: false,
      passwordHash: undefined,
      uid: 'aUuid-XXXX',
      customClaims: {
        id: 1,
      },
    });
  });

  it('should extract user uuid, and use to disable user in gip', async () => {
    const userEventBody: UserEventBody = {
      action: 'deleted',
      data: {
        id: 1,
        uuid: 'aUuid-XXXX',
        email: '<EMAIL>',
        first_name: 'firstName',
        last_name: 'lastName',
        locale: 'en',
        preferences: [],
      },
    };
    const mockCreateMessage = {
      Body: JSON.stringify({ Message: JSON.stringify(userEventBody) }),
    };
    jest.spyOn(migrateUserService, 'disableUserInFirebase').mockResolvedValue();
    await migrateService.handleMessage(mockCreateMessage);
    expect(migrateUserService.disableUserInFirebase).toHaveBeenCalledWith(
      'aUuid-XXXX'
    );
  });

  it('should throw exception up if disable user fails so message is never acked', async () => {
    const userEventBody: UserEventBody = {
      action: 'deleted',
      data: {
        id: 1,
        uuid: 'aUuid-XXXX',
        email: '<EMAIL>',
        first_name: 'firstName',
        last_name: 'lastName',
        locale: 'en',
        preferences: [],
      },
    };
    const mockCreateMessage = {
      Body: JSON.stringify({ Message: JSON.stringify(userEventBody) }),
    };

    jest
      .spyOn(migrateUserService, 'disableUserInFirebase')
      .mockImplementation(() => {
        throw new Error('testMessage');
      });

    await expect(
      migrateService.handleMessage(mockCreateMessage)
    ).rejects.toThrow('Error in message processing Error: testMessage');
  });

  it('should throw exception for any error so message is never acked', async () => {
    const userEventBody: UserEventBody = {
      action: 'created',
      data: {
        id: 1,
        uuid: 'aUuid-XXXX',
        email: '<EMAIL>',
        first_name: 'firstName',
        last_name: 'lastName',
        locale: 'en',
        preferences: [],
      },
    };
    const mockCreateMessage = {
      Body: JSON.stringify({ Message: JSON.stringify(userEventBody) }),
    };
    jest.spyOn(migrateUserService, 'migrateUser').mockImplementation(() => {
      throw new Error('testMessage');
    });
    await expect(
      migrateService.handleMessage(mockCreateMessage)
    ).rejects.toThrow('Error in message processing Error: testMessage');
  });

  it('should extract body from message', () => {
    const result: UserEventBody =
      migrateService.extractMessageBody(rawMessageFixture);

    expect(result.action).toEqual('updated');
    expect(result.data.uuid).toEqual('789deca6-402f-481c-b2f9-43eb454b925f');
  });

  it('should start listening to queue on bootstrap', () => {
    expect(queueConsumerService.setMessageHandler).toHaveBeenCalledTimes(1);
    expect(queueConsumerService.start).toHaveBeenCalledTimes(1);
  });
});
