ENVIRONMENT=local
APPLICATION_VERSION=local
SENTRY_RELEASE=local

USER_QUEUE_NAME=migrate-users-events
USER_QUEUE_DISABLED=true
SQS_REGION=eu-west-1
USER_QUEUE_URL=https://localhost:4566/************/migrate-users-events
AWS_ENDPOINT_URL=http://127.0.0.1:4566
AWS_ACCESS_KEY_ID=ACCESSKEY1234
AWS_SECRET_ACCESS_KEY=SomeRandomStringThatMeansNothing
AWS_REGION=eu-west-1
AWS_PROFILE=localstack

AUTH_DB_HOST=localhost
AUTH_DB_PORT=3308
AUTH_DB_USERNAME=homestead
AUTH_DB_PASSWORD=secret
AUTH_DB_DATABASE=auth

DB_USERNAME=user
DB_PASSWORD=password
DB_DATABASE=podpoint
DB_HOST=localhost
DB_HOST_RO=localhost
DB_PORT=3307

FIREBASE_CLIENT_EMAIL=
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
IDENTITY_BASE_URL_CLASSIC=localhost:5105
IDENTITY_BASE_URL_POD_ENERGY=localhost:5105
API3_BASE_URL=
API3_CLIENT_ID=
API3_CLIENT_SECRET=
DRIVER_AUTH_PRIVATE_KEY=
AUTH_SERVICE_USER_URL=

DRIVER_ACCOUNT_DB_CONFIG='{"migrate":{"port":5432,"host":"localhost","database":"driver_account","username":"postgres","password":"password"},"read":{"port":5432,"host":"localhost","database":"driver_account","username":"postgres","password":"password"}, "write":{"port":5432,"host":"localhost","database":"driver_account","username":"postgres","password":"password"}}'

OXR_APP_ID=
SEGMENT_WRITE_KEY=
USER_EVENTS_QUEUE_URL=https://localhost:4566/************/driver-account-api-user-profile-events
BILLING_API_BASE_URL=

DATA_PLATFORM_API_BASE_URL=
SMART_CHARGING_SERVICE_API_BASE_URL=
SUBSCRIPTIONS_API_BASE_URL=localhost:5120
