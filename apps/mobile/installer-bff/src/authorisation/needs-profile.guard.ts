import { AccountService } from '../account/account.service';
import {
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { CompanyType, InstallerType } from '../account/account.types';

@Injectable()
export class NeedsProfileGuard implements CanActivate {
  private logger = new Logger(NeedsProfileGuard.name);
  constructor(@Inject(AccountService) private accountService: AccountService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const payload = context.switchToHttp().getRequest();

    let userProfile = await this.accountService.getProfileOrNull({
      authId: payload.user.user_id,
    });

    if (!userProfile && payload.user.email.includes('@pod-point.com')) {
      try {
        const [firstName, lastName] = payload.user.email
          .split('@')[0]
          .split('.');
        const podPointUser = {
          email: payload.user.email,
          authId: payload.user.user_id,
          firstName,
          lastName: lastName || '(Pod Point)',
          phoneNumber: '+44 ************',
          companyName: 'Pod Point Ltd',
          companyNumber: '********',
          marketingConsent: true,
          companyType: CompanyType.COMPANY,
          installerType: InstallerType.POD_POINT,
        };

        await this.accountService.createProfile(podPointUser);
        userProfile = await this.accountService.getProfileOrNull({
          authId: payload.user.user_id,
        });
      } catch (error) {
        this.logger.error({
          msg: `Failed to create a profile for email ${payload.user.email}`,
          error,
        });
        throw error;
      }
    }

    if (!userProfile) {
      throw new UnauthorizedException({
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
        cause: 'Incomplete user profile',
      });
    }
    payload.user.profile = { ...userProfile, authId: payload.user.user_id };
    return true;
  }
}
