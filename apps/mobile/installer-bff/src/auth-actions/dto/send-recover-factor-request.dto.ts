import { ApiProperty } from '@nestjs/swagger';
import {
  COMMON_CONTINUE_URL_ERROR,
  COMMON_EMAIL_MAX_LENGTH,
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/mobile/driver-account/typescript/domain-model-validation';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsUrl,
  MaxLength,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class SendRecoverFactorRequest {
  @ApiProperty({
    description: 'Email',
    type: String,
  })
  @Transform(({ value }) => value?.trim())
  @IsEmail({}, { message: COMMON_INVALID_EMAIL_ERROR })
  @MaxLength(COMMON_EMAIL_MAX_LENGTH, {
    message: COMMON_EMAIL_MAX_LENGTH_ERROR,
  })
  @IsNotEmpty({ message: COMMON_REQUIRED_ERROR })
  email: string;
  @ApiProperty({
    description: 'Redirect URL after factor recovery',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsUrl({}, { message: COMMON_CONTINUE_URL_ERROR })
  recover_factor_continue_url?: string;
}
