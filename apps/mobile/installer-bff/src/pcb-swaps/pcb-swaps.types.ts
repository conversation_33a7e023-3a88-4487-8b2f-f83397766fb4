import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { PcbSwapDoor, PcbSwapSuccessStatus } from './pcb-swaps.dtos';

export class PcbSwapRequest {
  @ApiProperty({
    description: 'The serial number of the new PCB that is being swapped in.',
    example: '0123456789',
  })
  @IsString()
  @IsNotEmpty()
  pcbSerialNumber: string;

  @ApiProperty({
    description:
      'The PSL Number of the unit that the new PCB is being swapped into.',
    example: 'PSL-123456',
  })
  @IsString()
  @IsNotEmpty()
  pslNumber: string;

  @ApiProperty({
    description: 'For twin units; the door this PCB swap is applicable to',
    enum: PcbSwapDoor,
    required: false,
    nullable: false,
  })
  door?: PcbSwapDoor;
}

export class PcbSwapSuccessfulResponse {
  @ApiProperty({
    description: 'Status of the successful PCB swap.',
    enum: PcbSwapSuccessStatus,
  })
  status: PcbSwapSuccessStatus;

  @ApiProperty({
    description: 'Summary of the successful PCB swap.',
    example: 'The PCB has been successfully swapped.',
  })
  message: 'The PCB has been successfully swapped.';
}

export class PcbSwapAttachDetachVirtualRequest extends PcbSwapRequest {}

export class PcbSwapAttachVirtualResponse {
  @ApiProperty({
    description: 'Summary of the successful attaching of a virtual PCB.',
    example: 'The virtual PCB has been successfully attached.',
  })
  message: 'The virtual PCB has been successfully attached.';
}

export class PcbSwapDetachVirtualResponse {
  @ApiProperty({
    description: 'Summary of the successful detaching of a virtual PCB.',
    example: 'The virtual PCB has been successfully detached.',
  })
  message: 'The virtual PCB has been successfully detached.';
}
