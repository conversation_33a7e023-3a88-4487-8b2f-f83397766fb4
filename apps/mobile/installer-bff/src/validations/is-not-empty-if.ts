import {
  ValidationArguments,
  ValidationOptions,
  registerDecorator,
} from 'class-validator';

/**
 * If the condition returns true, then the value must be a value and not null or undefined
 */
export const IsNotEmptyIf =
  (
    checkIfRequired: (object: unknown) => boolean,
    validationOptions?: ValidationOptions
  ): PropertyDecorator =>
  (object: object, propertyName: string): void => {
    registerDecorator({
      name: 'isNotEmptyIf',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate: (value: unknown, args: ValidationArguments) => {
          if (checkIfRequired(args.object)) {
            return value !== null && value !== undefined;
          }
          return true;
        },
      },
    });
  };
