import {
  AnonymisedAppointment,
  anonymiseAppointment,
} from '@experience/installer/types';
import {
  AppointmentApiException,
  AppointmentNotFoundException,
} from './appointment.errors';
import { FlagsService } from '@experience/shared/nest/remote-config';
import { Injectable, Logger } from '@nestjs/common';
import {
  SalesforceClient,
  isActiveSalesforceAppointment,
  isExistingSalesforceAppointment,
} from '@experience/shared/salesforce/client';

@Injectable()
export class AppointmentService {
  private readonly logger = new Logger(AppointmentService.name);

  constructor(
    private readonly salesforceClient: SalesforceClient,
    private readonly flagsService: FlagsService
  ) {}

  async getAppointmentByReference(
    appointmentReference: string
  ): Promise<AnonymisedAppointment> {
    return await this.getAppointmentByReferenceFromSalesforce(
      appointmentReference
    );
  }

  private async getAppointmentByReferenceFromSalesforce(
    appointmentReference: string
  ) {
    try {
      const { status, data: appointment } =
        await this.salesforceClient.getAppointmentByReference(
          appointmentReference
        );

      if (status === 200 && isExistingSalesforceAppointment(appointment)) {
        const useCaseFallback = this.getUseCaseForWorkType(
          appointment.workType
        );
        const useCase = appointment.useCase ?? useCaseFallback;

        if (
          (await this.isFilterOutInactiveAppointmentsEnabled()) &&
          !isActiveSalesforceAppointment(appointment) &&
          useCase === 'domestic'
        ) {
          this.logger.log({
            message: 'Inactive Salesforce appointment found',
            appointmentReference,
          });
          throw new AppointmentNotFoundException(appointmentReference);
        }

        return anonymiseAppointment({
          reference: appointment.reference,
          address: appointment.address,
        });
      }

      if (status === 204) {
        throw new AppointmentNotFoundException(appointmentReference);
      }

      throw new AppointmentApiException(
        `Unexpected response: ${JSON.stringify({ status, data: appointment })}`
      );
    } catch (error) {
      if (error instanceof AppointmentNotFoundException) {
        this.logger.error({
          message: 'Appointment was not found',
          appointmentReference,
          error,
        });
        throw error;
      }

      if (error instanceof AppointmentApiException) {
        this.logger.error({
          message: 'Appointment Api error',
          appointmentReference,
          error,
        });
        throw error;
      }

      const errorData = error.response?.data;
      if (errorData) {
        const errorMessage = errorData.message || 'Unknown error occurred';

        this.logger.error({
          message: 'Error occurred when calling Appointment Api',
          error,
          errorData,
        });

        throw new AppointmentApiException(errorMessage, errorData.errorCode);
      }

      this.logger.error({
        message: 'Error occurred when calling Appointment Api',
        error,
      });
      throw new AppointmentApiException('Unknown error occurred');
    }
  }

  private async isFilterOutInactiveAppointmentsEnabled(): Promise<boolean> {
    return await this.flagsService.doCheckForRemoteFlag(
      'ff_filter_out_inactive_appointments_enabled',
      'true'
    );
  }

  private getUseCaseForWorkType(workType: string): string {
    const domesticWorkTypes = ['Domestic Install', 'Domestic Installation'];

    const commercialWorkTypes = [
      'Install (Commercial)',
      'External Install (Commercial)',
      'In-house Install (Commercial)',
    ];

    if (domesticWorkTypes.includes(workType)) {
      return 'domestic';
    }

    if (commercialWorkTypes.includes(workType)) {
      return 'commercial';
    }

    return workType;
  }
}
