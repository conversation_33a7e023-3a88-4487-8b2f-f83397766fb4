import { AuthUser } from '../../decorators/jwt-auth-user.decorator';
import { CreateUserProfilePayload, InstallerType } from '../account.types';

export const TEST_AUTH_USER: AuthUser = {
  user_id: '1234',
  email: '<EMAIL>',
  email_verified: true,
  firebase: { sign_in_provider: 'password' },
};

export const TEST_ACCOUNT_PROFILE_PUT_REQUEST_PATH = '/account/profile';
export const TEST_ACCOUNT_PROFILE_DELETE_REQUEST_PATH = '/account/profile';

export const TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_POD_POINT_EMPLOYEE: CreateUserProfilePayload =
  {
    firstName: 'Joe',
    lastName: 'Spark',
    phoneNumber: '+44 ************',
    companyType: 'company',
    installerType: InstallerType.POD_POINT,
    marketingConsent: true,
  };

export const TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_SOLE_TRADER: CreateUserProfilePayload =
  {
    firstName: 'Joe',
    lastName: 'Spark',
    phoneNumber: '+44 ************',
    companyType: 'sole_trader',
    marketingConsent: true,
  };

export const TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_COMPANY: CreateUserProfilePayload =
  {
    firstName: 'Joe',
    lastName: 'Spark',
    phoneNumber: '+44 ************',
    companyType: 'company',
    companyName: 'Electrical Services Ltd.',
    companyNumber: 'SC327000',
    marketingConsent: true,
  };
