import {
  ApiBadRequestException,
  ApiUnauthorizedException,
} from '@experience/mobile/nest/swagger';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { AuthUserProfile } from '../account/account.types';
import { AuthorisationGuard } from '../authorisation/authorisation.guard';
import {
  Body,
  Controller,
  Logger,
  Param,
  Post,
  UseGuards,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import { FlagsService } from '@experience/shared/nest/remote-config';
import { InstallSession, UpsertInstallResponse } from './installs.types';
import { InstallerProfile } from '../decorators/installer-profile.decorator';
import { InstallsApi } from '@experience/installer/api/axios';
import { InstallsInterceptor } from './installs.interceptor';
import { InstallsService } from './installs.service';
import { NeedsProfileGuard } from '../authorisation/needs-profile.guard';
import { forwardClientError } from '../utils/forward-client-error';

@ApiTags('Installs')
@Controller({
  path: 'installs',
  version: '1',
})
@UseInterceptors(InstallsInterceptor)
@UseGuards(AuthorisationGuard, NeedsProfileGuard)
export class InstallsController {
  private readonly logger = new Logger(InstallsController.name);

  constructor(
    private installsService: InstallsService,
    private installsApi: InstallsApi,
    private flagsService: FlagsService
  ) {}

  @Post(':guid')
  @ApiParam({
    name: 'guid',
    description:
      'The unique guid that is created on the client and used to reference the installation',
  })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'create or update installation data',
    description: 'Create or update installation data',
  })
  @ApiCreatedResponse({
    description: 'The install session was successfully created/updated.',
  })
  @ApiOkResponse({
    type: UpsertInstallResponse,
  })
  @ApiBadRequestException()
  @ApiUnauthorizedException()
  async updateInstall(
    @InstallerProfile() installerProfile: AuthUserProfile,
    @Body(new ValidationPipe({ transform: true }))
    payload: InstallSession,
    @Param('guid') guid: string
  ): Promise<UpsertInstallResponse> {
    try {
      this.logger.log(`Creating/updating install through installer API`, {
        guid,
        payload,
        authId: installerProfile.authId,
      });

      const shouldSendEmail = await this.flagsService.doCheckForRemoteFlag(
        'ff_send_install_complete_email',
        'true'
      );

      const shouldPublishInstallCompletedEvent =
        await this.flagsService.doCheckForRemoteFlag(
          'ff_publish_installation_completed_event',
          'true'
        );

      const appointment = payload.appointment
        ? await this.installsService.getAppointmentByReference(
            payload.appointment.reference
          )
        : undefined;

      const session = {
        ...payload,
        authId: installerProfile.authId,
        appointment,
      };

      const response = await this.installsApi.installsControllerUpdateInstall(
        guid,
        shouldSendEmail,
        shouldPublishInstallCompletedEvent,
        session
      );

      this.logger.log(`Created/updated install through installer API`, {
        guid,
        payload,
        authId: installerProfile.authId,
        response,
      });

      return response.data;
    } catch (error) {
      forwardClientError({
        error,
        logger: this.logger,
        logData: { guid, payload, installerProfile },
      });
    }
  }
}
