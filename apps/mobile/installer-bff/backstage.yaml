apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: installer-bff
  title: BFF for the Installer App
  description: BFF for the installer app
  annotations:
    github.com/project-slug: Pod-Point/experience
    backstage.io/techdocs-ref: dir:.
  tags:
    - nestjs
spec:
  type: service
  owner: group:tfm-experience-mobile
  lifecycle: production
  providesApis:
    - installer-bff
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: installer-bff
  description: |
    BFF for the Installer App
spec:
  type: openapi
  owner: group:tfm-experience-mobile
  lifecycle: production
  definition:
    $text: ../../../libs/mobile/installer/bff/contract/openapi3.yaml
