import { ActionCodeInfo, AuthError, checkActionCode } from 'firebase/auth';
import { getAuth } from '../../../src/firebase/firebase';
import { logger } from '@experience/shared/next/logger';

export const POST = async (request: Request) => {
  const firebaseOptions = {
    apiKey: process.env.FIREBASE_API_KEY,
    appId: process.env.FIREBASE_APP_ID,
    authDomain: process.env.FIREBASE_AUTH_DOMAIN,
    messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
    projectId: process.env.FIREBASE_PROJECT_ID,
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  };

  let updateValues: ActionCodeInfo;

  try {
    logger.info({ request }, 'checking action code');

    const auth = await getAuth(firebaseOptions);
    const { oobCode } = await request.json();
    updateValues = await checkActionCode(auth, oobCode);
  } catch (error) {
    const authError = error as AuthError;

    logger.error({ request, error }, 'failed to check action code');

    return new Response('Action code error', {
      status: 400,
      statusText: authError.code,
    });
  }

  const {
    data: { email, previousEmail },
  } = updateValues;

  try {
    logger.info({ request }, 'sending change email request to installer api');

    return await fetch(
      `${process.env.INSTALLER_ACCOUNT_API_BASE_URL}/v1/account/email/update`,
      {
        method: 'PUT',
        body: JSON.stringify({ newEmail: email, oldEmail: previousEmail }),
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
          accept: 'application/json',
          'Accept-Language': request.headers.get('Accept-Language') || 'en',
        },
      }
    );
  } catch (error) {
    logger.error({ request, error }, 'failed to update email');

    return new Response('Error updating email', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  }
};
