{"extends": "../../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["jest", "node", "segment-analytics"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../../apps/mobile/installer-account-webapp/.next/types/**/*.ts", "../../../dist/apps/mobile/installer-account-webapp/.next/types/**/*.ts", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}