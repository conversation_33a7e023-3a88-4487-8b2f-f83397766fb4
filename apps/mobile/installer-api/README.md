# Installer API

## Getting started

### Build the application

Use the command

```bash
npx nx build installer-api
```

### Run the application (locally)

Pre-requisites:

1. Follow steps from [README.md](../../../README.md) to set up the project.
1. Copy `.env.local.sample` to `.env.local`.
1. Fill in `FIREBASE_CLIENT_EMAIL` and `FIREBASE_PRIVATE_KEY` in `.env.local` - ask the Assets Team for values.

To run, use the command

```bash
npx nx run installer-api:serve:dependencies
```

followed by

```bash
npx nx serve installer-api
```

You should now be able to access the API at: [Installer API](http://localhost:5103/) and the OpenAPI specification at: [Installer API OpenAPI docs](http://localhost:5103/api)

### Running tests

To run unit tests, use:

```bash
npx nx test installer-api
```

And for e2e tests (e.g. [installs.module.spec.ts](./src/installs/installs.module.spec.ts)), run:

```bash
npx nx run installer-api-e2e:e2e
```

### Generate Openapi file

Use the command

```bash
npx nx serve installer-api
```

Then check the openapi docs: http://localhost:5103/api and make sure that your modifications are visible there (if not you need to use appropriate decorators in your code)

Then go to: http://localhost:5103/api-yaml

This will download the new openapi file so copy its content and replace the contents of [libs/mobile/installer/api/contract/openapi3.yaml](../../../libs/mobile/installer/api/contract/openapi3.yaml).

### Auto generate client sources

Run the following commands keep in mind that it might generate files with different indentation, so you might have a lot of files being updated but the auto linting on commit will fix it.

```bash
npx nx run installer-api-dart:generate-sources:docker --skip-nx-cache
```

```bash
npx nx run installer-api-axios:generate-sources:docker --skip-nx-cache
```

```bash
npx nx run installer-api-nestjs:generate-sources:docker --skip-nx-cache
```

### Generate a new Database Migration

Run the following command to create a new migration file:

```bash
npx nx migration:create installer-api -- name-of-migration
```

### Run code formatter

Run the following command to format the code:

```bash
npx nx format:write
```

### Access to the application in staging

The installer-api is currently only accessible in staging:

- [staging](http://installer-api.destination.cluster.com:5103)
  - [staging docs](http://installer-api.destination.cluster.com:5103/api)
  - Staging requires you to be on the VPN.

### Creating a user / bearer token

In order to access the API for testing you'll need to create a bearer token. The below steps are for doing this in dev (although can be similarly followed in staging):

N.B. for dev, some steps will require being on the VPN.

1. Follow steps from the [gip-admin tool README](../../../apps/support/gip-admin/README.md) to set up the CLI for interacting with Firebase.
1. Run `npx nx run gip-admin:gip-admin create <userEmail>` to create a user in Firebase.
1. Run `npx nx run gip-admin:gip-admin setVerifyEmail <userId> true` to set their email address as verified, using the `uid` from the previous step.
1. Manually create a user in the dev `installer.users` table, with the following details (other fields can be left as defaults):
   1. `auth_id` - the `userId` from the previous steps
   1. `email` - the `userEmail` from the previous steps
   1. `marketing_consent` - false
   1. `first_name` and `last_name` - the user's name
   1. `phone_number` - 1234567890
   1. `company_type` - company
   1. `company_name` - Pod Point Ltd
   1. `company_number` - 06851754
   1. `installer_type` - POD_POINT
1. Visit https://installer-identity-dev.pod-point.com/en/request-reset and enter your email.
1. Click the link to reset your password.
1. Run `read -s INSTALLER_PASSWORD` and when prompted enter the password to set it as an env var without it appearing in your history.
1. Obtain an API key from https://console.firebase.google.com/project/installer-app-dev-44059/settings/general/web
1. Run `read -s INSTALLER_API_KEY` and when prompted enter API key to set it as an env var without it appearing in your history.
1. Run the following to obtain an access token, filling in the email from previous steps:
   ```bash
   curl -X POST "https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyPassword?key=$INSTALLER_API_KEY" \
   -H "Content-Type: application/json" \
   -d "{
       \"email\": \"<email>\",
       \"password\": \"$INSTALLER_PASSWORD\",
       \"returnSecureToken\": true
     }"
   ```
1. The response will contain an `idToken` which is the bearer token to use when calling the API. For example, this can be used for the `Authorize` button on [Installer API OpenAPI docs (dev)](https://installer-api-dev.pod-point.com/api).

Alternatively Postman or similar can be used to replace the `read` and `curl` commands above.

---

<img src="https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg" alt="Pod Point logo" style="float: right;" />

Driving shouldn’t cost the earth 🌍

Made with ❤️&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
