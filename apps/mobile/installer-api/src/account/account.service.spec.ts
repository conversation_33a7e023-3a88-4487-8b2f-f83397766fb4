import { BadRequestException, Logger, NotFoundException } from '@nestjs/common';
import {
  EXAMPLE_INSTALLER_PROFILE,
  TEST_ACCOUNT_PROFILE_POST_REQUEST_PAYLOAD_POD_POINT_EMPLOYEE,
  TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_COMPANY,
  TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_SOLE_TRADER,
  TEST_AUTH_USER,
} from './__fixtures__/account.interface';
import { IncompleteProfilePayloadException } from './account.exception';
import { Test } from '@nestjs/testing';
import { USER_REPOSITORY, USER_REPOSITORY_READ } from '../database/constants';
import { User } from '../database/entities/user.entity';
import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { AccountService } from './account.service';
import {
  CompanyType,
  CreateUserProfilePayload,
  InstallerType,
} from './account.types';
import { ConfigModule } from '@nestjs/config';
import { EntityManager } from 'typeorm';
import {
  EventBridgeModule,
  EventBridgeService,
} from '@experience/shared/nest/aws/eventbridge-module';
import { MockRepository, createMockRepository } from '../test.utils';
import { RevertEmailService } from '@experience/mobile/nest/auth';
import { createMock } from '@golevelup/ts-jest';
import { jest } from '@jest/globals';
import { mockApp, mockAuth } from '@experience/mobile/test/mocking';
import { mockDeep } from 'jest-mock-extended';

jest.mock('@experience/shared/nest/aws/eventbridge-module');

jest.mock('@experience/shared/firebase/admin', () => ({
  getAuth: () => mockAuth,
  getApp: () => mockApp,
  updateProjectConfig: jest.fn(),
}));

describe('AccountService', () => {
  let service: AccountService;
  let userRepository: MockRepository<User>;
  let userReadOnlyRepository: MockRepository<User>;
  let revertEmailService: RevertEmailService;
  let eventBridgeService: EventBridgeService;

  const logger = mockDeep<Logger>();

  const mockEntityManager = createMock<EntityManager>();
  const aUser: User = {
    id: 1,
    firstName: 'P',
    lastName: 'T',
    authId: '1234',
    marketingConsent: true,
    companyName: '',
    companyType: '',
    phoneNumber: '1234',
    companyNumber: '',
    createdAt: new Date().toString(),
    updatedAt: new Date().toString(),
    email: '<EMAIL>',
    deletedAt: null,
    installerType: 'POD_POINT',
  };

  beforeEach(() => jest.clearAllMocks());

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [
        I18nModule.forRoot({
          fallbackLanguage: 'en',
          loaderOptions: {
            path: './assets/installer-api/i18n/',
            watch: true,
          },
          resolvers: [
            { use: QueryResolver, options: ['lang'] },
            AcceptLanguageResolver,
          ],
        }),
        ConfigModule,
        EventBridgeModule,
      ],
      providers: [
        AccountService,
        {
          provide: RevertEmailService,
          useValue: createMock<RevertEmailService>(),
        },
        {
          provide: USER_REPOSITORY,
          useValue: createMockRepository<User>(),
        },
        {
          provide: USER_REPOSITORY_READ,
          useValue: createMockRepository<User>(),
        },
        EventBridgeService,
      ],
    }).compile();

    module.useLogger(logger);

    service = module.get<AccountService>(AccountService);
    userRepository = module.get<MockRepository<User>>(USER_REPOSITORY);
    userReadOnlyRepository =
      module.get<MockRepository<User>>(USER_REPOSITORY_READ);
    revertEmailService = module.get<RevertEmailService>(RevertEmailService);
    eventBridgeService = module.get<EventBridgeService>(EventBridgeService);
  });

  describe('getProfile', () => {
    describe('when profile with ID exists', () => {
      it('should return the profile object', async () => {
        const expectedUser: Partial<User> = { email: '<EMAIL>' };
        userReadOnlyRepository.findOneBy.mockReturnValue(expectedUser);
        expect(await service.getProfile({ authId: 'aUserIdExists' })).toEqual({
          ...expectedUser,
        });
      });
    });
    describe('otherwise', () => {
      it('should throw the "NotFoundException"', async () => {
        userReadOnlyRepository.findOneBy.mockReturnValue(undefined);
        await expect(
          service.getProfile({ authId: 'anUnknownUser' })
        ).rejects.toThrow(NotFoundException);
      });
    });
  });

  describe('createProfile for Pod Point Employee', () => {
    it('sets the installerType to POD_POINT if not supplied', async () => {
      const createSpy = jest.spyOn(userRepository, 'create');

      await service.createProfile({
        ...TEST_ACCOUNT_PROFILE_POST_REQUEST_PAYLOAD_POD_POINT_EMPLOYEE,
        authId: TEST_AUTH_USER.user_id,
      });

      expect(createSpy.mock.calls[0][0]).toHaveProperty(
        'installerType',
        'POD_POINT'
      );
    });

    it.each([InstallerType.PARTNER, InstallerType.THIRD_PARTY])(
      'does not allow the installerType to be set to %s',
      async (type) => {
        await expect(
          service.createProfile({
            ...TEST_ACCOUNT_PROFILE_POST_REQUEST_PAYLOAD_POD_POINT_EMPLOYEE,
            installerType: type,
            authId: TEST_AUTH_USER.user_id,
          })
        ).rejects.toThrow(BadRequestException);
      }
    );
  });

  describe.each([
    [
      CompanyType.SOLE_TRADER,
      TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_SOLE_TRADER,
    ],
    [CompanyType.COMPANY, TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_COMPANY],
  ])('createProfile for %s', (name, payload) => {
    it('should not throw if successful', async () => {
      expect(
        async () =>
          await service.createProfile({
            ...payload,
            authId: TEST_AUTH_USER.user_id,
          })
      ).not.toThrow(IncompleteProfilePayloadException);
    });

    it('should emit a UserCreatedEvent if successful', async () => {
      await service.createProfile({
        ...payload,
        authId: TEST_AUTH_USER.user_id,
      });

      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'installer-api',
          detailType: 'User.Created',
          detail: expect.objectContaining({
            specversion: '1.0',
            subject: expect.stringContaining('User.Created.Installer'),
            type: 'User.Created',
            source: '/experience/mobile/installer-api',
            data: {
              user: {
                id: payload.authId,
                email: payload.email,
                firstName: payload.firstName,
                lastName: payload.lastName,
                locale: 'en',
                consent: {
                  marketing: {
                    isConsentGiven: payload.marketingConsent,
                    type: null,
                    copy: null,
                    origin: null,
                  },
                },
                preferences: {
                  unitOfDistance: 'mi',
                },
              },
            },
          }),
        })
      );
    });

    it('should log an error if the UserCreatedEvent could not be sent', async () => {
      const error = new Error('Something went wrong');

      jest
        .spyOn(eventBridgeService, 'putEvent')
        .mockImplementationOnce(async () => {
          throw error;
        });

      await service.createProfile({
        ...payload,
        authId: TEST_AUTH_USER.user_id,
      });

      expect(logger.error).toHaveBeenCalledWith(
        { error, authId: TEST_AUTH_USER.user_id },
        'failed to submit user created event',
        AccountService.name
      );
    });

    it('should throw an incomplete payload exception if the payload is null', async () => {
      await expect(
        service.createProfile({
          authId: TEST_AUTH_USER.user_id,
        } as CreateUserProfilePayload)
      ).rejects.toThrow(IncompleteProfilePayloadException);
    });

    it('sets the installerType to THIRD_PARTY if not supplied', async () => {
      const createSpy = jest.spyOn(userRepository, 'create');

      await service.createProfile({
        ...payload,
        authId: TEST_AUTH_USER.user_id,
      });

      expect(createSpy.mock.calls[0][0]).toHaveProperty(
        'installerType',
        'THIRD_PARTY'
      );
    });

    it('does not let the installerType to be set to PARTNER if the user is a sole trader', async () => {
      await expect(
        service.createProfile({
          ...payload,
          installerType: InstallerType.PARTNER,
          companyType: 'sole_trader',
          authId: TEST_AUTH_USER.user_id,
        })
      ).rejects.toThrow(BadRequestException);
    });

    it.each([
      ['firstName', { firstName: null }],
      ['lastName', { lastName: null }],
      ['phoneNumber', { phoneNumber: null }],
      ['companyType', { companyType: null }],
      ['marketingConsent', { marketingConsent: null }],
    ])(
      'should throw an incomplete payload exception if the %s field is undefined',
      async (name, payloadOverride) => {
        const invalidPayload = {
          ...payload,
          ...payloadOverride,
        };
        await expect(
          service.createProfile({
            ...invalidPayload,
            authId: TEST_AUTH_USER.user_id,
          })
        ).rejects.toThrow(IncompleteProfilePayloadException);
      }
    );
  });

  describe('searchProfile', () => {
    describe('when email Param is passed', () => {
      const testSearchByFilter = async (params, email = '<EMAIL>') => {
        const firstName = 'M';
        const lastName = 'K';
        const phoneNumber = '12345';
        const expectedUser: Partial<User> = {
          email,
          firstName,
          lastName,
          phoneNumber,
        };
        const getManyMock = jest.fn().mockReturnValue([expectedUser]);
        userReadOnlyRepository.setGetMany(getManyMock);
        expect(await service.getByFilter(params)).toEqual([
          {
            companyName: undefined,
            companyNumber: undefined,
            companyType: undefined,
            email,
            firstName,
            lastName,
            phoneNumber,
          },
        ]);
      };
      it('should return a list of profile objects when email is passed', async () => {
        const email = '<EMAIL>';
        await testSearchByFilter({ email });
      });

      it('should return a list of profile objects when emailLike is passed', async () => {
        const email = '<EMAIL>';
        await testSearchByFilter({ emailLike: email });
      });

      it('should return an empty list of profile objects when no param is passed', async () => {
        const getManyMock = jest.fn().mockReturnValue([]);
        userReadOnlyRepository.setGetMany(getManyMock);
        expect(await service.getByFilter({})).toEqual([]);
      });
    });
  });

  describe('updateProfile', () => {
    it('should throw NotFoundException if profile does not exist', async () => {
      userReadOnlyRepository.findOneBy.mockReturnValue(undefined);
      await expect(
        service.updateProfile({
          authId: TEST_AUTH_USER.user_id,
          ...TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_SOLE_TRADER,
        })
      ).rejects.toThrow(NotFoundException);
    });

    it.each([InstallerType.PARTNER, InstallerType.THIRD_PARTY])(
      'does not allow the installerType to be set to %s for a Pod Point email',
      async (type) => {
        userReadOnlyRepository.findOneBy.mockReturnValue({
          email: '<EMAIL>',
        });

        await expect(
          service.updateProfile({
            ...TEST_ACCOUNT_PROFILE_POST_REQUEST_PAYLOAD_POD_POINT_EMPLOYEE,
            installerType: type,
            authId: TEST_AUTH_USER.user_id,
          })
        ).rejects.toThrow(BadRequestException);
      }
    );

    it('throws a bad request if a Pod Point employee tries to update their profile', async () => {
      userReadOnlyRepository.findOneBy.mockReturnValue({
        email: '<EMAIL>',
        installerType: 'POD_POINT',
      });

      await expect(
        service.updateProfile({
          authId: TEST_AUTH_USER.user_id,
          installerType: InstallerType.POD_POINT,
          ...TEST_ACCOUNT_PROFILE_POST_REQUEST_PAYLOAD_POD_POINT_EMPLOYEE,
        })
      ).rejects.toThrow(BadRequestException);
    });

    it('should successfully update an existing profile in the repository', async () => {
      const expectedUser: Partial<User> = { email: '<EMAIL>' };
      userReadOnlyRepository.findOneBy.mockReturnValue(expectedUser);

      await service.updateProfile({
        authId: TEST_AUTH_USER.user_id,
        ...TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_SOLE_TRADER,
      });

      expect(userRepository.merge).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should clear company name and company number when updating to a sole trader', async () => {
      const expectedUser: Partial<User> = {
        email: '<EMAIL>',
        companyType: CompanyType.COMPANY,
        companyName: 'Acme Ltd',
        companyNumber: '12345',
      };
      userReadOnlyRepository.findOneBy.mockReturnValue(expectedUser);

      await service.updateProfile({
        authId: TEST_AUTH_USER.user_id,
        ...TEST_ACCOUNT_PROFILE_PUT_REQUEST_PAYLOAD_SOLE_TRADER,
      });

      expect(userRepository.merge).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          companyName: null,
          companyNumber: null,
        })
      );
    });

    it('should raise a UserUpdatedEvent if successful', async () => {
      userReadOnlyRepository.findOneBy.mockReturnValue(
        EXAMPLE_INSTALLER_PROFILE
      );

      await service.updateProfile({
        authId: 'aUserIdExists',
        firstName: 'New',
        lastName: EXAMPLE_INSTALLER_PROFILE.lastName,
        phoneNumber: EXAMPLE_INSTALLER_PROFILE.phoneNumber,
        companyType: EXAMPLE_INSTALLER_PROFILE.companyType,
        marketingConsent: EXAMPLE_INSTALLER_PROFILE.marketingConsent,
      });

      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'installer-api',
          detailType: 'User.Updated',
          detail: expect.objectContaining({
            data: {
              user: {
                id: EXAMPLE_INSTALLER_PROFILE.authId,
                firstName: 'New',
                lastName: EXAMPLE_INSTALLER_PROFILE.lastName,
                email: EXAMPLE_INSTALLER_PROFILE.email,
                locale: 'en',
              },
            },
          }),
        })
      );
    });

    it('should log an error if UserUpdatedEvent could not be raised', async () => {
      const error = new Error('Something went wrong');

      jest
        .spyOn(eventBridgeService, 'putEvent')
        .mockImplementationOnce(async () => {
          throw error;
        });

      userReadOnlyRepository.findOneBy.mockReturnValue(
        EXAMPLE_INSTALLER_PROFILE
      );

      await service.updateProfile({
        authId: 'aUserIdExists',
        firstName: 'New',
        lastName: EXAMPLE_INSTALLER_PROFILE.lastName,
        phoneNumber: EXAMPLE_INSTALLER_PROFILE.phoneNumber,
        companyType: EXAMPLE_INSTALLER_PROFILE.companyType,
        marketingConsent: EXAMPLE_INSTALLER_PROFILE.marketingConsent,
      });

      expect(logger.error).toHaveBeenCalledWith(
        { error, authId: EXAMPLE_INSTALLER_PROFILE.authId },
        'failed to submit user updated event',
        AccountService.name
      );
    });
  });

  describe('deleteProfile', () => {
    it('should throw NotFoundException if profile does not exist', async () => {
      userReadOnlyRepository.findOneBy.mockReturnValue(undefined);
      await expect(
        service.deleteProfile({ authId: 'anUnknownUser' })
      ).rejects.toThrow(NotFoundException);
    });

    it('should successfully remove an existing profile from the repository', async () => {
      const expectedUser: Partial<User> = { email: '<EMAIL>' };
      jest
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .spyOn(service as any, 'getEntityManager')
        .mockReturnValue(mockEntityManager);
      jest.spyOn(mockEntityManager, 'transaction').mockResolvedValue(aUser);

      userReadOnlyRepository.findOneBy.mockReturnValue(expectedUser);

      const user = await service.deleteProfile({ authId: 'aUserIdExists' });

      expect(user).toEqual(aUser);
    });

    it('should raise a UserDeletedEvent if successful', async () => {
      const expectedUser: Partial<User> = { email: '<EMAIL>' };

      userReadOnlyRepository.findOneBy.mockReturnValue(expectedUser);

      const user = await service.deleteProfile({ authId: 'aUserIdExists' });

      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'installer-api',
          detailType: 'User.Deleted',
          detail: expect.objectContaining({
            specversion: '1.0',
            subject: expect.stringContaining('User.Deleted.Installer'),
            type: 'User.Deleted',
            source: '/experience/mobile/installer-api',
            data: {
              user: {
                id: user.authId,
                email: user.email,
              },
            },
          }),
        })
      );
    });

    it('should log an error if UserDeletedEvent could not be raised', async () => {
      const expectedUser: Partial<User> = { email: '<EMAIL>' };

      const error = new Error('Something went wrong');

      jest
        .spyOn(eventBridgeService, 'putEvent')
        .mockImplementationOnce(async () => {
          throw error;
        });

      userReadOnlyRepository.findOneBy.mockReturnValue(expectedUser);

      await service.deleteProfile({ authId: 'aUserIdExists' });

      expect(logger.error).toHaveBeenCalledWith(
        { error, authId: TEST_AUTH_USER.user_id },
        'failed to submit user deleted event',
        AccountService.name
      );
    });
  });

  describe('updateEmail', () => {
    beforeEach(() => {
      userReadOnlyRepository.findOneBy.mockReturnValue(Promise.resolve(aUser));
    });

    it("gets the user's profile based on the given email address", async () => {
      await service.updateEmail(
        {
          oldEmail: '<EMAIL>',
          newEmail: '<EMAIL>',
        },
        'en'
      );

      expect(userReadOnlyRepository.findOneBy).toHaveBeenCalledTimes(1);
      expect(userReadOnlyRepository.findOneBy).toHaveBeenCalledWith({
        email: '<EMAIL>',
      });
    });

    it('throws a NotFoundException if no user exists with the given email address', async () => {
      userReadOnlyRepository.findOneBy.mockReturnValue(Promise.resolve(null));

      await expect(
        service.updateEmail(
          {
            oldEmail: '<EMAIL>',
            newEmail: '<EMAIL>',
          },
          'en'
        )
      ).rejects.toThrow(NotFoundException);
    });

    it('sends the email if updating the user was successful', async () => {
      const revertEmailMock = jest
        .spyOn(revertEmailService, 'sendRevertEmail')
        .mockReturnValue(Promise.resolve());

      jest
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .spyOn(service as any, 'updateUserInFirebaseAndDatabase')
        .mockReturnValue(Promise.resolve());

      await service.updateEmail(
        {
          oldEmail: '<EMAIL>',
          newEmail: '<EMAIL>',
        },
        'fr'
      );

      expect(revertEmailMock).toHaveBeenCalledTimes(1);
      expect(revertEmailMock).toHaveBeenCalledWith(
        expect.objectContaining({
          emailAddress: '<EMAIL>',
        }),
        'fr'
      );
    });
  });
});
