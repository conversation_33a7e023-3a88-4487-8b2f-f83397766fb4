import { ApiProperty } from '@nestjs/swagger';
import { IsEmptyIf } from '../validations/is-empty-if';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { IsNotEmptyIf } from '../validations/is-not-empty-if';
import { Transform } from 'class-transformer';

export enum CompanyType {
  COMPANY = 'company',
  SOLE_TRADER = 'sole_trader',
}

export enum InstallerType {
  THIRD_PARTY = 'THIRD_PARTY',
  PARTNER = 'PARTNER',
  POD_POINT = 'POD_POINT',
}

export class UpdateUserProfilePayload {
  @ApiProperty({
    description: 'First name',
    type: String,
    example: 'John',
  })
  @Transform(({ value }) => value?.trim())
  @IsNotEmpty({ message: 'The firstName field is required' })
  firstName: string;

  @ApiProperty({
    description: 'Last name',
    type: String,
    example: '<PERSON>',
  })
  @Transform(({ value }) => value?.trim())
  @IsNotEmpty({ message: 'The lastName field is required' })
  lastName: string;

  @ApiProperty({
    description: 'Phone number',
    type: String,
    example: '01234567890',
  })
  @Transform(({ value }) => value?.trim())
  @IsNotEmpty({ message: 'The phoneNumber field is required' })
  phoneNumber: string;

  @ApiProperty({
    description:
      "The type of profile that's being created. Validation depends on this value",
    type: String,
    example: 'company',
    enum: CompanyType,
  })
  @Transform(({ value }) => value?.trim())
  @IsNotEmpty({ message: 'The companyType field is required' })
  @IsEnum(CompanyType)
  companyType: string;

  @ApiProperty({
    description:
      'Company name must be included for companyType of company, but not for sole_trader',
    type: String,
    example: 'My Company Ltd',
    required: false,
  })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  @IsNotEmptyIf(
    (o: CreateUserProfilePayload) => o.companyType == CompanyType.COMPANY,
    {
      message: 'The companyName field is required if companyType is company',
    }
  )
  @IsEmptyIf(
    (o: CreateUserProfilePayload) => o.companyType == CompanyType.SOLE_TRADER,
    {
      message:
        'The companyName field should not be included if companyType is sole_trader',
    }
  )
  companyName?: string;

  @ApiProperty({
    description:
      'Company number must be included for companyType of company, but not for sole_trader',
    type: String,
    example: '06851754',
    required: false,
  })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  @IsNotEmptyIf(
    (o: CreateUserProfilePayload) => o.companyType == CompanyType.COMPANY,
    {
      message: 'The companyNumber field is required if companyType is company',
    }
  )
  @IsEmptyIf(
    (o: CreateUserProfilePayload) => o.companyType == CompanyType.SOLE_TRADER,
    {
      message:
        'The companyNumber field should not be included if companyType is sole_trader',
    }
  )
  companyNumber?: string;

  @ApiProperty({
    description: 'Allowing marketing consent',
    type: Boolean,
    example: true,
  })
  @IsNotEmpty({ message: 'The marketingConsent field is required' })
  marketingConsent: boolean;

  @ApiProperty({
    description: 'The type of installer',
    type: InstallerType,
    enum: InstallerType,
    enumName: 'InstallerType',
    required: false,
  })
  @IsOptional()
  @IsEnum(InstallerType)
  installerType?: InstallerType;
}

export class CreateUserProfilePayload extends UpdateUserProfilePayload {
  @ApiProperty({
    description: 'Email',
    type: String,
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'The email field is required' })
  email: string;

  @ApiProperty({
    description: 'Google Identity Platform user id',
    type: String,
    example: 'f41e4a67-3aea-43bd-a8dc-12fdeb9524af',
  })
  @IsNotEmpty({ message: 'The authId field is required' })
  authId: string;
}

export class UserProfile {
  @ApiProperty({
    description: 'Auth Id',
    type: String,
    example: 'f41e4a67-3aea-43bd-a8dc-12fdeb9524af',
  })
  authId: string;
  @ApiProperty({
    description: 'Company',
    type: String,
    example: 'ABC LTD',
    required: false,
  })
  companyName: string;
  @ApiProperty({
    description: 'Company number',
    type: String,
    example: 'SC535049',
    required: false,
  })
  companyNumber: string;
  @ApiProperty({
    description: 'Company',
    type: String,
    example: 'sole_trader',
    enum: CompanyType,
  })
  companyType: string;
  @ApiProperty({
    description: 'Email',
    type: String,
    example: '<EMAIL>',
  })
  email: string;
  @ApiProperty({
    description: 'First name',
    type: String,
    example: 'John',
  })
  firstName: string;
  @ApiProperty({
    description: 'Last name',
    type: String,
    example: 'Smith',
  })
  lastName: string;
  @ApiProperty({
    description: 'Marketing consent',
    type: Boolean,
    example: true,
  })
  marketingConsent: boolean;
  @ApiProperty({
    description: 'Phone number',
    type: String,
    example: '01234567890',
  })
  phoneNumber: string;
  @ApiProperty({
    description: 'The type of installer',
    type: InstallerType,
    enum: InstallerType,
    enumName: 'InstallerType',
    required: false,
  })
  installerType: string;
}
