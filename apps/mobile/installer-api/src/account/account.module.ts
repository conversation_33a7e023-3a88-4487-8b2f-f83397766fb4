import { ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { AccountController } from './account.controller';
import { AccountService } from './account.service';
import { AuthModule } from '@experience/mobile/nest/auth';
import { DatabaseModule } from '../database/database.module';
import { EventBridgeModule } from '@experience/shared/nest/aws/eventbridge-module';

@Module({
  imports: [DatabaseModule, AuthModule, EventBridgeModule],
  controllers: [AccountController],
  providers: [ConfigService, AccountService],
  exports: [AccountService],
})
export class AccountModule {}
