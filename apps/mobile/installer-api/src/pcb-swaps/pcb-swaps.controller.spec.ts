import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { INestApplication } from '@nestjs/common';
import { PcbSwapDoesNotExistError, PcbSwapError } from './pcb-swaps.error';
import { PcbSwapStatus } from './pcb-swaps.types';
import { PcbSwapsController } from './pcb-swaps.controller';
import { PcbSwapsService } from './pcb-swaps.service';
import { Test } from '@nestjs/testing';
import request from 'supertest';

describe('PcbSwapsController', () => {
  let app: INestApplication;
  let pcbSwapsService: DeepMocked<PcbSwapsService>;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      controllers: [PcbSwapsController],
      providers: [
        {
          provide: PcbSwapsService,
          useValue: createMock<PcbSwapsService>(),
        },
      ],
    }).compile();

    app = module.createNestApplication();

    await app.init();

    pcbSwapsService = module.get(PcbSwapsService);
  });

  afterEach(() => jest.resetAllMocks());
  afterAll(async () => app.close());

  describe('getAllPcbSwaps()', () => {
    it('should return a 500 if something goes wrong', async () => {
      pcbSwapsService.getAllPcbSwaps.mockImplementation(() => {
        throw new PcbSwapError();
      });

      await request(app.getHttpServer()).get('/pcb-swaps').expect(500);
    });

    it('should return a 200 with the PCB swaps, passing along all filters', async () => {
      pcbSwapsService.getAllPcbSwaps.mockResolvedValue([
        {
          ppid: 'PSL-620893',
          serialNumber: '**********',
          status: PcbSwapStatus.SUCCESS,
          changedAt: '2024-09-24T16:48:00Z',
          emailedAt: null,
        },
        {
          ppid: 'PSL-123456',
          serialNumber: '1234567890',
          status: PcbSwapStatus.PARTIAL_SUCCESS,
          changedAt: '2024-09-24T17:38:00Z',
          emailedAt: null,
        },
      ]);

      await request(app.getHttpServer())
        .get('/pcb-swaps')
        .query({
          emailedAt: 'null',
          changedAtBefore: '2024-09-25T00:00:00Z',
        })
        .expect(200, [
          {
            ppid: 'PSL-620893',
            serialNumber: '**********',
            status: PcbSwapStatus.SUCCESS,
            changedAt: '2024-09-24T16:48:00Z',
            emailedAt: null,
          },
          {
            ppid: 'PSL-123456',
            serialNumber: '1234567890',
            status: PcbSwapStatus.PARTIAL_SUCCESS,
            changedAt: '2024-09-24T17:38:00Z',
            emailedAt: null,
          },
        ]);

      expect(pcbSwapsService.getAllPcbSwaps).toHaveBeenCalledTimes(1);
      expect(pcbSwapsService.getAllPcbSwaps).toHaveBeenCalledWith({
        emailedAt: 'null',
        changedAtBefore: '2024-09-25T00:00:00Z',
      });
    });
  });

  describe('registerPcbSwap()', () => {
    it('should return a 500 if something goes wrong', async () => {
      pcbSwapsService.registerPcbSwap.mockImplementation(() => {
        throw new PcbSwapError();
      });

      await request(app.getHttpServer())
        .post('/pcb-swaps')
        .send({
          ppid: 'PSL-620893',
          serialNumber: '**********',
        })
        .expect(500);
    });

    it('should return a 201 if the PCB swap is registered', async () => {
      pcbSwapsService.registerPcbSwap.mockResolvedValue({
        ppid: 'PSL-620893',
        serialNumber: '**********',
        status: PcbSwapStatus.SUCCESS,
        changedAt: '2024-09-24T16:48:00Z',
        emailedAt: null,
      });

      await request(app.getHttpServer())
        .post('/pcb-swaps')
        .send({
          ppid: 'PSL-620893',
          serialNumber: '**********',
        })
        .expect(201);
    });
  });

  describe('updatePcbSwap()', () => {
    it('should return a 500 if something goes wrong', async () => {
      pcbSwapsService.updatePcbSwap.mockImplementation(() => {
        throw new PcbSwapError();
      });

      await request(app.getHttpServer())
        .patch('/pcb-swaps/PSL-620893/**********')
        .send({
          emailedAt: '2024-09-24T16:15:00Z',
        })
        .expect(500);
    });

    it('should return a 404 if the requested PCB swap does not exist', async () => {
      pcbSwapsService.updatePcbSwap.mockImplementation(() => {
        throw new PcbSwapDoesNotExistError();
      });

      await request(app.getHttpServer())
        .patch('/pcb-swaps/PSL-000000/12313123')
        .send({
          emailedAt: '2024-09-24T16:15:00Z',
        })
        .expect(404);
    });

    it('should return a 200 if the PCB swap is updated', async () => {
      pcbSwapsService.updatePcbSwap.mockResolvedValue({
        ppid: 'PSL-620893',
        serialNumber: '**********',
        status: PcbSwapStatus.SUCCESS,
        changedAt: '2024-09-24T16:48:00Z',
        emailedAt: '2024-09-24T16:15:00Z',
      });

      await request(app.getHttpServer())
        .patch('/pcb-swaps/PSL-620893/**********')
        .send({
          emailedAt: '2024-09-24T16:15:00Z',
        })
        .expect(200);
    });
  });
});
