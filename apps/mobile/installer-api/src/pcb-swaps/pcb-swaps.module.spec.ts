import { PcbSwapStatus } from './pcb-swaps.types';
import axios from 'axios';

const isValidIsoTimestamp = (str: string): boolean =>
  new Date(str).toISOString() === str;

export const describePcbSwapsModule = (baseUrl: string) => {
  describe('PcbSwapsModule', () => {
    describe('GET /pcb-swaps', () => {
      it('returns a 200 with an array of all PCB swaps if no filters are provided', async () => {
        const res = await axios.get(`${baseUrl}/pcb-swaps`);

        expect(res.status).toEqual(200);
        expect(res.data).toMatchSnapshot();
      });

      it('returns a 200 with an array of all PCB swaps which do not have a emailedAt field if ?emailedAt=null is provided', async () => {
        const res = await axios.get(`${baseUrl}/pcb-swaps?emailedAt=null`);

        expect(res.status).toEqual(200);
        expect(res.data).toMatchSnapshot();
      });

      it('returns a 200 with an array of all PCB swaps changed before a given date if ?changedAtBefore is provided', async () => {
        const res = await axios.get(
          `${baseUrl}/pcb-swaps?changedAtBefore=2024-09-24T00:00:00.000Z`
        );

        expect(res.status).toEqual(200);
        expect(res.data).toMatchSnapshot();
      });

      it('returns a 200 with an array of all PCB swaps which do not have an emailedAt field and are changed before a given date if both filters are provided', async () => {
        const res = await axios.get(
          `${baseUrl}/pcb-swaps?emailedAt=null&changedAtBefore=2024-09-24T00:00:00.000Z`
        );

        expect(res.status).toEqual(200);
        expect(res.data).toMatchSnapshot();
      });
    });

    describe('POST /pcb-swaps', () => {
      it('returns a 400 if the payload is missing a PPID', async () => {
        await expect(
          axios.post(`${baseUrl}/pcb-swaps`, {
            serialNumber: '2422110742',
            status: PcbSwapStatus.SUCCESS,
          })
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('returns a 400 if the payload is missing a code serial number', async () => {
        await expect(
          axios.post(`${baseUrl}/pcb-swaps`, {
            ppid: 'PSL-620893',
            status: PcbSwapStatus.SUCCESS,
          })
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('returns a 400 if the payload is missing a code status', async () => {
        await expect(
          axios.post(`${baseUrl}/pcb-swaps`, {
            ppid: 'PSL-620893',
            serialNumber: '2422110742',
          })
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('returns a 400 if the payload is an invalid status', async () => {
        await expect(
          axios.post(`${baseUrl}/pcb-swaps`, {
            ppid: 'PSL-620893',
            serialNumber: '2422110742',
            status: 'broken',
          })
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('returns a 201 if the pcb swap is successfully recorded', async () => {
        const res = await axios.post(`${baseUrl}/pcb-swaps`, {
          ppid: 'PSL-620893',
          serialNumber: '2422110742',
          status: PcbSwapStatus.SUCCESS,
        });

        expect(res.status).toEqual(201);
        expect(res.data).toEqual({
          ppid: 'PSL-620893',
          serialNumber: '2422110742',
          status: PcbSwapStatus.SUCCESS,
          changedAt: expect.any(String),
          emailedAt: null,
        });

        expect(isValidIsoTimestamp(res.data.changedAt)).toBeTruthy();
      });
    });

    describe('PATCH /pcb-swaps/:ppid/:serialNumber', () => {
      it('returns a 400 if the payload is missing a emailedAt field', async () => {
        await expect(
          axios.patch(`${baseUrl}/pcb-swaps/PSL-620893/2422110742`, {})
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('returns a 404 if the given PPID does not exist', async () => {
        await expect(
          axios.patch(`${baseUrl}/pcb-swaps/PSL-000000/2422110742`, {
            emailedAt: '2024-09-24T16:15:00Z',
          })
        ).rejects.toThrow('Request failed with status code 404');
      });

      it('returns a 404 if the given serial number does not exist for the PPID', async () => {
        await expect(
          axios.patch(`${baseUrl}/pcb-swaps/PSL-620893/1234567890`, {
            emailedAt: '2024-09-24T16:15:00Z',
          })
        ).rejects.toThrow('Request failed with status code 404');
      });

      it('returns a 200 if the PCB swap is successfully updated', async () => {
        const res = await axios.patch(
          `${baseUrl}/pcb-swaps/PSL-620893/2422110742`,
          {
            emailedAt: '2024-09-24T16:15:00Z',
          }
        );

        expect(res.status).toEqual(200);
        expect(res.data).toMatchSnapshot();
      });
    });
  });
};
