import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPcbSwapsTable1727096588386 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS installer.pcb_swaps(
         id SERIAL PRIMARY KEY,
         ppid character varying NOT NULL,
         serial_number character varying NOT NULL,
         status character varying NOT NULL,
         changed_at TIMESTAMP NOT NULL DEFAULT now(),
         emailed_at timestamp
        )
      `);

    await queryRunner.query(`
      GRANT SELECT, INSERT, UPDATE on installer.pcb_swaps TO installer_api;
    `);

    await queryRunner.query(`
      GRANT USAGE, SELECT ON SEQUENCE installer.pcb_swaps_id_seq TO installer_api;
    `);

    await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS
          installer_pcb_swaps_ppid_emailed_at
        ON installer.pcb_swaps (ppid, emailed_at)
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('installer.pcb_swaps');
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE ON installer.pcb_swaps FROM installer_api;
      `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS installer.installer_pcb_swaps_ppid_emailed_at
    `);
  }
}
