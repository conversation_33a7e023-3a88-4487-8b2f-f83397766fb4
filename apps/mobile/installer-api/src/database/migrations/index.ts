import { AddInstallerType1718273101370 } from './1718273101370-add-installer-type';
import { AddInstallsAppointment1743425210319 } from './1743425210319-add-installs-appointment';
import { AddInstallsSocket1720005770392 } from './1720005770392-add-installs-socket';
import { AddPcbSwapsTable1727096588386 } from './1727096588386-add-pcb-swaps-table';
import { AddUserIdIndex1715328716312 } from './1715328716312-add-user-id-index';
import { ConvertTimestampToTimeZone1715335128371 } from './1715335128371-convert-timestamp-to-time-zone';
import { CreateInstallerSchema1687251006079 } from './1687251006079-create-installer-schema';
import { CreateServiceUser1688389396619 } from './1688389396619-create-service-user';
import { DeleteCascadeInstalls1691753365802 } from './1691753365802-delete-cascade-installs';
import { GrafanaRoUser1727963201179 } from './1727963201179-grafana-ro-user';
import { GrafanaUserAddUsage1728039953443 } from './1728039953443-grafana-user-add-usage.ts';
import { GrantDelete1691595617865 } from './1691595617865-grant-delete';
import { GrantSequencesPermissions1688553974349 } from './1688553974349-grant-sequences-permissions';
import { InstallImagesTable1688995140087 } from './1688995140087-create-install-images-table';
import { InstallerTable1688643810529 } from './1688643810529-installer-table';
import { InstallerTableJsonb1689248221207 } from './1689248221207-installer-table-jsonb';
import { InstallerTableSeqPermission1689255994158 } from './1689255994158-installer-table-grant';
import { InstallsConvertVarcharToUuid1715334177662 } from './1715334177662-installs-convert-varchar-to-uuid';
import { InstallsIndexPslNumber1715350186464 } from './1715350186464-installs-index-psl-number';
import { MigrationsTableConstraint1689249118228 } from './1689249118228-migrations-table-constraint';
import { RemoveIsPodPointStaff1719222259832 } from './1719222259832-remove-is-pod-point-staff';
import { UpdateInstallerType1718616756853 } from './1718616756853-update-installer-type';
import { UpdateUser1706186484331 } from './1706186484331-update-user';
import { UserTable1688395492489 } from './1688395492489-users-table';
import { CreateInstallerApiLoggingCommsUser1757934520384 } from './1757934520384-create-installer-api-logging-comms-user';
import { RecreateInstallerApiLoggingCommsUser1758101652568 } from './1758101652568-recreate-installer-api-logging-comms-user';
import { AddIndexesToUsersTable1758125001885 } from './1758125001885-add-indexes-to-users-table';
import { AddSocketIndexToInstallsTable1758192209252 } from './1758192209252-add-socket-index-to-installs-table';
import { RevertAdditionalIndexesOnUsersTable1758279912998 } from './1758279912998-revert-additional-indexes-on-users-table';
import { RevertSocketIndexOnInstallsTable1758279922128 } from './1758279922128-revert-socket-index-on-installs-table';
import { CreateInstallationsMaterialisedView1758546385611 } from './1758546385611-create-installations-materialised-view';
import { GrantPermissionsToGrafanaUserOnInstallsMaterializedView1758707305129 } from './1758707305129-grant-permissions-to-grafana-user-on-installs-materialized-view';
import { CreateInstallersMaterialisedView1758709092082 } from './1758709092082-create-installers-materialised-view';
import { GrantPermissionsToGrafanaUserOnInstallersMaterializedView1758727941932 } from './1758727941932-grant-permissions-to-grafana-user-on-installers-materialized-view';

export const migrations = [
  CreateInstallerSchema1687251006079,
  CreateServiceUser1688389396619,
  UserTable1688395492489,
  InstallImagesTable1688995140087,
  GrantSequencesPermissions1688553974349,
  InstallerTable1688643810529,
  InstallerTableJsonb1689248221207,
  MigrationsTableConstraint1689249118228,
  InstallerTableSeqPermission1689255994158,
  GrantDelete1691595617865,
  DeleteCascadeInstalls1691753365802,
  UpdateUser1706186484331,
  AddUserIdIndex1715328716312,
  InstallsConvertVarcharToUuid1715334177662,
  ConvertTimestampToTimeZone1715335128371,
  InstallsIndexPslNumber1715350186464,
  AddInstallerType1718273101370,
  UpdateInstallerType1718616756853,
  RemoveIsPodPointStaff1719222259832,
  AddInstallsSocket1720005770392,
  AddPcbSwapsTable1727096588386,
  GrafanaRoUser1727963201179,
  GrafanaUserAddUsage1728039953443,
  AddInstallsAppointment1743425210319,
  CreateInstallerApiLoggingCommsUser1757934520384,
  RecreateInstallerApiLoggingCommsUser1758101652568,
  AddIndexesToUsersTable1758125001885,
  AddSocketIndexToInstallsTable1758192209252,
  RevertAdditionalIndexesOnUsersTable1758279912998,
  RevertSocketIndexOnInstallsTable1758279922128,
  CreateInstallationsMaterialisedView1758546385611,
  GrantPermissionsToGrafanaUserOnInstallsMaterializedView1758707305129,
  CreateInstallersMaterialisedView1758709092082,
  GrantPermissionsToGrafanaUserOnInstallersMaterializedView1758727941932,
];
