import { validate } from './env.validate';
describe('DataSource can only form if mandatory process variables available', () => {
  let env: Record<string, string | number>;
  beforeEach(() => {
    env = {
      DB_HOST: 'localhost',
      DB_PORT: '5431',
      DB_USERNAME: 'postgres',
      DB_PASSWORD: 'password',
      DB_DATABASE: 'installer',
      PGSSLMODE: 'require',
      FIREBASE_CLIENT_EMAIL: '<EMAIL>',
      FIREBASE_PROJECT_ID: 'pod-point-install',
      FIREBASE_PRIVATE_KEY: 'private_key',
      INSTALLATION_COMPLETED_EVENTS_TOPIC_ARN: 'queue',
    };
  });
  it('Config validator successful if env variables set', () => {
    const config = () => validate(env);
    expect(config).not.toThrow();
  });
  it('Config validator throws error if DB_PORT env not set', () => {
    env.DB_PORT = '';
    const configEmpty = () => validate(env);
    expect(configEmpty).toThrow(/Missing required env vars : DB_PORT/);
    delete env.DB_PORT;
    const configMissing = () => validate(env);
    expect(configMissing).toThrow(/Missing required env vars : DB_PORT/);
  });
  test('Config validator throws error if DB_HOST env not set', () => {
    env.DB_HOST = '';
    const config = () => validate(env);
    expect(config).toThrow(/Missing required env vars : DB_HOST/);
  });
  it('Config validator throws error if DB_USER env not set', () => {
    env.DB_USERNAME = '';
    const config = () => validate(env);
    expect(config).toThrow(/Missing required env vars : DB_USERNAME/);
  });
  it('Config validator throws error if DB_PASSWORD env not set', () => {
    env.DB_PASSWORD = '';
    const config = () => validate(env);
    expect(config).toThrow(/Missing required env vars : DB_PASSWORD/);
  });
  it('Config validator throws error if DB_DATABASE env not set', () => {
    env.DB_DATABASE = '';
    const config = () => validate(env);
    expect(config).toThrow(/Missing required env vars : DB_DATABASE/);
  });
  it('Config validator throws error if INSTALLATION_COMPLETED_EVENTS_TOPIC_ARN env not set', () => {
    env.INSTALLATION_COMPLETED_EVENTS_TOPIC_ARN = '';
    const config = () => validate(env);
    expect(config).toThrow(
      /Missing required env vars : INSTALLATION_COMPLETED_EVENTS_TOPIC_ARN/
    );
  });
  it('Config validator will not throw error if additional properties set', () => {
    env.ADDITION_PROPERTY = '';
    const config = () => validate(env);
    expect(config).not.toThrow();
  });
});
