import { BankAccountService } from '../bank-account/bank-account.service';
import { ConfigService } from '@nestjs/config';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { EventsService } from './events.service';
import { Logger } from '@nestjs/common';
import { MOCK_BANK_ACCOUNT_RESPONSE_DTO } from '../bank-account/__fixtures__/bank-account.interface';
import { MOCK_REWARD_PAYOUT_EVENT } from './__fixtures__/events.interface';
import { MOCK_TRANSACTION_DTO } from '../transactions/__fixtures__/transactions.interface';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import { Test } from '@nestjs/testing';
import {
  TransactionStatus,
  TransactionType,
} from '../transactions/transactions.types';
import { TransactionsService } from '../transactions/transactions.service';
import { jest } from '@jest/globals';
import { mockDeep } from 'jest-mock-extended';
import { v4 } from 'uuid';

describe('EventsService', () => {
  let service: EventsService;
  let mockConfigService: DeepMocked<ConfigService>;
  let mockBankAccountService: DeepMocked<BankAccountService>;
  let mockTransactionsService: DeepMocked<TransactionsService>;
  let mockSqsClientService: DeepMocked<SqsClientService>;

  const logger = mockDeep<Logger>();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [],
      providers: [
        EventsService,
        {
          provide: ConfigService,
          useValue: createMock<ConfigService>(),
        },
        {
          provide: BankAccountService,
          useValue: createMock<BankAccountService>(),
        },
        {
          provide: TransactionsService,
          useValue: createMock<TransactionsService>(),
        },
        {
          provide: SqsClientService,
          useValue: createMock<SqsClientService>(),
        },
      ],
    }).compile();

    module.useLogger(logger);

    mockConfigService = module.get(ConfigService);
    mockBankAccountService = module.get(BankAccountService);
    mockTransactionsService = module.get(TransactionsService);
    mockSqsClientService = module.get(SqsClientService);

    service = module.get<EventsService>(EventsService);
  });

  afterEach(() => jest.resetAllMocks());

  it('should be defined', () => {
    expect(mockConfigService).toBeDefined();
    expect(mockBankAccountService).toBeDefined();
    expect(mockTransactionsService).toBeDefined();
    expect(mockSqsClientService).toBeDefined();
    expect(service).toBeDefined();
  });

  describe(EventsService.prototype.onRewardsPayout, () => {
    it('processes rewards payouts', async () => {
      mockBankAccountService.getBankAccountById.mockResolvedValue(
        MOCK_BANK_ACCOUNT_RESPONSE_DTO
      );
      mockTransactionsService.createTransaction.mockResolvedValue(
        MOCK_TRANSACTION_DTO
      );

      await service.onRewardsPayout(MOCK_REWARD_PAYOUT_EVENT);

      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledTimes(
        1
      );
      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledWith(
        MOCK_REWARD_PAYOUT_EVENT.data.bankAccountId
      );

      expect(mockTransactionsService.createTransaction).toHaveBeenCalledTimes(
        1
      );
      expect(mockTransactionsService.createTransaction).toHaveBeenCalledWith(
        {
          type: TransactionType.REWARDS,
          currency: MOCK_REWARD_PAYOUT_EVENT.data.currency,
          amount: MOCK_REWARD_PAYOUT_EVENT.data.amount,
          bankAccountId: MOCK_REWARD_PAYOUT_EVENT.data.bankAccountId,
          metadata: {
            walletId: MOCK_REWARD_PAYOUT_EVENT.data.walletId,
            rewardsTransactionId:
              MOCK_REWARD_PAYOUT_EVENT.data.rewardsTransactionId,
            subscriptionId: MOCK_REWARD_PAYOUT_EVENT.data.subscriptionId,
          },
        },
        MOCK_REWARD_PAYOUT_EVENT.data.idempotencyKey
      );
    });

    it('throws an error if the bank account does not belong to the given user', async () => {
      mockBankAccountService.getBankAccountById.mockResolvedValue({
        ...MOCK_BANK_ACCOUNT_RESPONSE_DTO,
        userId: 'random-user-not-the-same',
      });

      await expect(
        service.onRewardsPayout(MOCK_REWARD_PAYOUT_EVENT)
      ).rejects.toThrow(Error);

      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledTimes(
        1
      );
      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledWith(
        MOCK_REWARD_PAYOUT_EVENT.data.bankAccountId
      );

      expect(mockTransactionsService.createTransaction).not.toHaveBeenCalled();
    });
  });

  describe(EventsService.prototype.onStatusUpdate, () => {
    it('processed successful status update', async () => {
      const acquiredTransactionId = v4();
      const status = TransactionStatus.SUCCESS;

      mockTransactionsService.updateTransactionStatusByAcquiredId.mockResolvedValueOnce(
        MOCK_TRANSACTION_DTO
      );
      mockBankAccountService.getBankAccountById.mockResolvedValueOnce(
        MOCK_BANK_ACCOUNT_RESPONSE_DTO
      );

      await service.onStatusUpdate({
        acquiredTransactionId,
        status,
      });

      expect(
        mockTransactionsService.updateTransactionStatusByAcquiredId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockTransactionsService.updateTransactionStatusByAcquiredId
      ).toHaveBeenCalledWith(acquiredTransactionId, status);

      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledTimes(
        1
      );
      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledWith(
        MOCK_TRANSACTION_DTO.bankAccountId
      );

      expect(mockSqsClientService.sendMessage).not.toHaveBeenCalled();
    });

    it('processes declined status update', async () => {
      const acquiredTransactionId = v4();
      const status = TransactionStatus.DECLINED;
      const queueUrl = 'queueUrl';

      mockConfigService.getOrThrow.mockReturnValueOnce(queueUrl);
      mockTransactionsService.updateTransactionStatusByAcquiredId.mockResolvedValueOnce(
        { ...MOCK_TRANSACTION_DTO, status }
      );
      mockBankAccountService.getBankAccountById.mockResolvedValueOnce(
        MOCK_BANK_ACCOUNT_RESPONSE_DTO
      );

      await service.onStatusUpdate({
        acquiredTransactionId,
        status,
      });

      expect(
        mockTransactionsService.updateTransactionStatusByAcquiredId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockTransactionsService.updateTransactionStatusByAcquiredId
      ).toHaveBeenCalledWith(acquiredTransactionId, status);

      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledTimes(
        1
      );
      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledWith(
        MOCK_TRANSACTION_DTO.bankAccountId
      );

      expect(mockSqsClientService.sendMessage).toHaveBeenCalledTimes(1);
      expect(mockSqsClientService.sendMessage).toHaveBeenCalledWith(queueUrl, {
        messageBody: JSON.stringify({
          action: 'REWARD_PAYOUT_REFUND',
          data: {
            userId: MOCK_BANK_ACCOUNT_RESPONSE_DTO.userId,
            subscriptionId: MOCK_TRANSACTION_DTO.metadata.subscriptionId,
            rewardsTransactionId:
              MOCK_TRANSACTION_DTO.metadata.rewardsTransactionId,
            walletId: MOCK_TRANSACTION_DTO.metadata.walletId,
            idempotencyKey: MOCK_TRANSACTION_DTO.id,
          },
        }),
      });
    });

    it('processes error status update', async () => {
      const acquiredTransactionId = v4();
      const status = TransactionStatus.ERROR;
      const queueUrl = 'queueUrl';

      mockConfigService.getOrThrow.mockReturnValueOnce(queueUrl);
      mockTransactionsService.updateTransactionStatusByAcquiredId.mockResolvedValueOnce(
        { ...MOCK_TRANSACTION_DTO, status }
      );
      mockBankAccountService.getBankAccountById.mockResolvedValueOnce(
        MOCK_BANK_ACCOUNT_RESPONSE_DTO
      );

      await service.onStatusUpdate({
        acquiredTransactionId,
        status,
      });

      expect(
        mockTransactionsService.updateTransactionStatusByAcquiredId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockTransactionsService.updateTransactionStatusByAcquiredId
      ).toHaveBeenCalledWith(acquiredTransactionId, status);

      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledTimes(
        1
      );
      expect(mockBankAccountService.getBankAccountById).toHaveBeenCalledWith(
        MOCK_TRANSACTION_DTO.bankAccountId
      );

      expect(mockSqsClientService.sendMessage).toHaveBeenCalledTimes(1);
      expect(mockSqsClientService.sendMessage).toHaveBeenCalledWith(queueUrl, {
        messageBody: JSON.stringify({
          action: 'REWARD_PAYOUT_REFUND',
          data: {
            userId: MOCK_BANK_ACCOUNT_RESPONSE_DTO.userId,
            subscriptionId: MOCK_TRANSACTION_DTO.metadata.subscriptionId,
            rewardsTransactionId:
              MOCK_TRANSACTION_DTO.metadata.rewardsTransactionId,
            walletId: MOCK_TRANSACTION_DTO.metadata.walletId,
            idempotencyKey: MOCK_TRANSACTION_DTO.id,
          },
        }),
      });
    });

    it('throws an error if transaction status in invalid', async () => {
      await expect(
        service.onStatusUpdate({
          acquiredTransactionId: v4(),
          status: 'INVALID',
        })
      ).rejects.toThrow(Error);
    });

    it('rethrows exceptions', async () => {
      const error = new Error('boom');

      mockTransactionsService.updateTransactionStatusByAcquiredId.mockRejectedValueOnce(
        error
      );

      await expect(
        service.onStatusUpdate({
          acquiredTransactionId: v4(),
          status: 'SUCCESS',
        })
      ).rejects.toThrow(error);
    });
  });
});
