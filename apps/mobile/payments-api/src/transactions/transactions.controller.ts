import {
  ApiAcceptedResponse,
  ApiHeader,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import {
  ApiBadRequestException,
  ApiInternalServerException,
  ApiNotFoundException,
} from '@experience/mobile/nest/swagger';
import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  Param,
  Post,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import {
  CreateRewardsTransaction,
  TransactionDTO,
  TransactionsQueryParametersDTO,
} from './transactions.types';
import { MultiDTOValidationPipe } from '@experience/shared/nest/utils';
import { TransactionsInterceptor } from './transactions.interceptor';
import { TransactionsService } from './transactions.service';

@ApiTags('Transactions')
@Controller('transactions')
@UseInterceptors(TransactionsInterceptor)
export class TransactionsController {
  constructor(private readonly transactionService: TransactionsService) {}

  @Post()
  @HttpCode(202)
  @ApiOperation({
    summary: 'Pay a given user',
    description: 'For a given user, pay them',
  })
  @ApiHeader({
    name: 'x-idempotency-key',
    description:
      'The idempotency key to use with this operation. One will be generated if not provided',
    required: false,
  })
  @ApiAcceptedResponse({
    description: 'The transaction creation has been accepted',
    type: TransactionDTO,
  })
  @ApiBadRequestException('Invalid request')
  @ApiNotFoundException('No bank account for the provided ID was found')
  @ApiInternalServerException()
  async createTransaction(
    @Body(new MultiDTOValidationPipe([CreateRewardsTransaction]))
    payload: CreateRewardsTransaction,
    @Headers('x-idempotency-key') idempotencyKey?: string
  ): Promise<TransactionDTO> {
    return this.transactionService.createTransaction(payload, idempotencyKey);
  }

  @Get()
  @ApiOperation({
    summary: 'Search for a transaction',
    description: 'Search for a transaction',
  })
  @ApiOkResponse({
    description: 'An array of matching transactions',
    type: [TransactionDTO],
  })
  @ApiInternalServerException()
  async searchTransactions(
    @Query() parameters: TransactionsQueryParametersDTO
  ): Promise<TransactionDTO[]> {
    return this.transactionService.searchTransactions(parameters);
  }

  @Get('/:transactionId')
  @ApiOperation({
    summary: 'Retrieve transaction by id',
    description: 'Retrieve transaction by id',
  })
  @ApiOkResponse({
    description: 'Returned on successful retrieval of a transaction',
    type: TransactionDTO,
  })
  @ApiNotFoundResponse({
    description: 'Returned when transaction not found',
  })
  @ApiInternalServerException()
  async getTransactionsById(
    @Param('transactionId') transactionId: string
  ): Promise<TransactionDTO> {
    return await this.transactionService.getTransactionById(transactionId);
  }
}
