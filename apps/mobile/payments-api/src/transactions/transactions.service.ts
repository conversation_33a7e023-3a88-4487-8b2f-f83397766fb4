import { AcquiredPayoutService } from '@experience/shared/nest/acquired';
import {
  CreateRewardsTransaction,
  TransactionCurrency,
  TransactionDTO,
  TransactionTotal,
  TransactionType,
  TransactionsQueryParametersDTO,
} from './transactions.types';
import {
  FailedToCreateTransactionError,
  FailedToGetTransactionTotalsError,
  FailedToUpdateTransactionError,
  InvalidSearchParametersError,
  InvalidTransactionMetadataError,
  InvalidTransactionStatusChangeError,
  NoBankAccountFoundError,
  TransactionDoesNotExistError,
} from './transactions.errors';
import { Inject, Logger } from '@nestjs/common';
import { JsonContains, Repository } from 'typeorm';
import {
  PAYMENTS_BANK_ACCOUNTS_REPOSITORY,
  PAYMENTS_TRANSACTIONS_REPOSITORY,
  PaymentsBankAccounts,
  PaymentsTransactions,
} from '@experience/mobile/driver-account/database';
import { REWARDS_REFERENCE } from './transactions.constants';
import { TransactionStatus } from './transactions.types';
import { v4 } from 'uuid';
import z from 'zod';

const RewardsTransactionMetadata = z
  .object({
    type: z.literal(TransactionType.REWARDS),
    walletId: z.uuid(),
    subscriptionId: z.uuid(),
    rewardsTransactionId: z.uuid(),
  })
  .loose();

export class TransactionsService {
  private readonly logger = new Logger(TransactionsService.name);

  constructor(
    @Inject(PAYMENTS_TRANSACTIONS_REPOSITORY)
    private readonly transactionRepository: Repository<PaymentsTransactions>,
    @Inject(PAYMENTS_BANK_ACCOUNTS_REPOSITORY)
    private readonly bankAccountsRepository: Repository<PaymentsBankAccounts>,
    private readonly acquiredPayoutService: AcquiredPayoutService
  ) {}

  async getTransactionById(id: string): Promise<TransactionDTO> {
    this.logger.log({ id }, 'retrieving transaction by id');

    try {
      const transaction = await this.transactionRepository.findOne({
        where: {
          id,
        },
        relations: ['bankAccount'],
      });

      if (!transaction) {
        throw new TransactionDoesNotExistError();
      }

      return this.mapTransactionEntityToDTO(transaction);
    } catch (error) {
      this.logger.error({ id, error }, 'unable to retrieve transaction by id');

      throw error;
    }
  }

  async searchTransactions(
    parameters: TransactionsQueryParametersDTO
  ): Promise<TransactionDTO[]> {
    this.logger.log({ parameters }, 'searching transactions');

    if (
      !parameters.bankAccountId &&
      !parameters.userId &&
      !parameters.walletId &&
      !parameters.rewardsTransactionId
    ) {
      this.logger.error({ parameters }, 'invalid search parameters');

      throw new InvalidSearchParametersError();
    }

    let metadata = null;

    if (parameters.walletId || parameters.rewardsTransactionId) {
      metadata = {
        walletId: parameters.walletId,
        rewardsTransactionId: parameters.rewardsTransactionId,
      };
    }

    try {
      const transactions = await this.transactionRepository.find({
        relations: ['bankAccount'],
        withDeleted: true,
        where: {
          bankAccount: {
            id: parameters.bankAccountId,
            userId: parameters.userId,
          },
          metadata: metadata != null ? JsonContains(metadata) : undefined,
        },
      });

      return transactions.map((transaction) =>
        this.mapTransactionEntityToDTO(transaction)
      );
    } catch (error) {
      this.logger.error(
        { parameters, error },
        'failed to search bank accounts'
      );

      throw error;
    }
  }

  private isValidMetadata(
    metadata: unknown
  ): metadata is z.infer<typeof RewardsTransactionMetadata> {
    return RewardsTransactionMetadata.safeParse(metadata).success;
  }

  private mapTransactionEntityToDTO(
    transaction: PaymentsTransactions
  ): TransactionDTO {
    if (!this.isValidMetadata(transaction.metadata)) {
      throw new InvalidTransactionMetadataError();
    }

    const { type, ...metadata } = transaction.metadata;

    return {
      id: transaction.id,
      bankAccountId: transaction.bankAccount.id,
      amount: transaction.amount,
      currency: transaction.currency as TransactionCurrency,
      status: transaction.status as TransactionStatus,
      createdAt: transaction.createdAt.toISOString(),
      type,
      metadata,
    };
  }

  async createTransaction(
    payload: CreateRewardsTransaction,
    idempotencyKey: string = v4()
  ): Promise<TransactionDTO> {
    this.logger.log(
      {
        idempotencyKey,
        payload,
      },
      'attempting to create transaction'
    );

    const idempotentTransaction = await this.transactionRepository.findOne({
      where: { idempotencyKey },
      relations: ['bankAccount'],
    });

    if (idempotentTransaction) {
      this.logger.log(
        {
          idempotencyKey,
          payload,
          idempotentTransaction: idempotentTransaction.id,
        },
        'transaction with matching idempotency key found. returning rather than re-creating'
      );

      return this.mapTransactionEntityToDTO(idempotentTransaction);
    }

    const bankAccount = await this.bankAccountsRepository.findOneBy({
      id: payload.bankAccountId,
    });

    if (!bankAccount) {
      this.logger.error(
        { bankAccountId: payload.bankAccountId },
        'no bank account found with given ID'
      );

      throw new NoBankAccountFoundError();
    }

    try {
      const acquiredTransaction = await this.acquiredPayoutService.createPayout(
        {
          orderId: idempotencyKey,
          amount: payload.amount,
          currency: payload.currency,
          payeeId: bankAccount.acquiredPayeeId,
          reference: REWARDS_REFERENCE,
        }
      );

      this.logger.log(
        {
          acquiredTransactionId: acquiredTransaction.transactionId,
          idempotencyKey,
          payload,
        },
        'created payout via acquired'
      );

      const transactionEntity = this.transactionRepository.create({
        bankAccount: { id: bankAccount.id },
        acquiredTransactionId: acquiredTransaction.transactionId,
        amount: payload.amount,
        currency: payload.currency,
        status:
          acquiredTransaction.status?.toUpperCase() ??
          TransactionStatus.PENDING,
        metadata: {
          type: payload.type,
          ...payload.metadata,
        },
        idempotencyKey,
      });

      await this.transactionRepository.save(transactionEntity);

      this.logger.log(
        { transactionId: transactionEntity.id, idempotencyKey },
        'successfully created transaction'
      );

      return this.mapTransactionEntityToDTO(transactionEntity);
    } catch (error) {
      this.logger.error(
        { error, payload, idempotencyKey },
        'failed to create transaction'
      );

      throw new FailedToCreateTransactionError();
    }
  }

  async updateTransactionStatusByAcquiredId(
    acquiredTransactionId: string,
    status: TransactionStatus
  ): Promise<TransactionDTO> {
    this.logger.log(
      {
        acquiredTransactionId,
        status,
      },
      'updating transaction status based on acquired ID'
    );

    try {
      const transaction = await this.transactionRepository.findOne({
        where: {
          acquiredTransactionId,
        },
        relations: ['bankAccount'],
      });

      if (!transaction) {
        this.logger.error(
          { acquiredTransactionId, status },
          'failed to find transaction by acquired transaction id'
        );

        throw new FailedToUpdateTransactionError();
      }

      const validChanges: Record<string, TransactionStatus[]> = {
        [TransactionStatus.PENDING]: [
          TransactionStatus.SUCCESS,
          TransactionStatus.DECLINED,
          TransactionStatus.ERROR,
        ],
        [TransactionStatus.SUCCESS]: [
          TransactionStatus.DECLINED,
          TransactionStatus.ERROR,
        ],
      };

      if (!validChanges[transaction.status]?.includes(status)) {
        throw new InvalidTransactionStatusChangeError();
      }

      await this.transactionRepository.update(
        {
          acquiredTransactionId,
        },
        {
          status,
        }
      );

      transaction.status = status;

      this.logger.log({ transaction }, 'transaction status updated');

      return this.mapTransactionEntityToDTO(transaction);
    } catch (error) {
      this.logger.error(
        {
          acquiredTransactionId,
          status,
          error,
        },
        'failed to update transaction status'
      );

      if (error instanceof InvalidTransactionStatusChangeError) {
        throw error;
      }

      throw new FailedToUpdateTransactionError();
    }
  }

  async getTransactionTotals(
    bankAccountId: string,
    type: string
  ): Promise<TransactionTotal[]> {
    this.logger.log({ bankAccountId, type }, 'getting transaction totals');

    try {
      const totals = await this.transactionRepository
        .createQueryBuilder()
        .select('SUM(amount) :: float', 'amount')
        .addSelect('currency')
        .addGroupBy('currency')
        .where({
          bankAccount: { id: bankAccountId },
          metadata: JsonContains({
            type,
          }),
        })
        .getRawMany<TransactionTotal>();

      this.logger.log(
        { bankAccountId, type, totals },
        'got transaction totals'
      );

      return totals;
    } catch (error) {
      this.logger.error(
        { bankAccountId, type, error },
        'failed to get transaction totals'
      );

      throw new FailedToGetTransactionTotalsError();
    }
  }
}
