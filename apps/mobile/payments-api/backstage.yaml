apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: payments-api
  title: Payments API
  description: API providing Payments functionality
  annotations:
    github.com/project-slug: Pod-Point/destination
    backstage.io/techdocs-ref: dir:.
  tags:
    - nestjs
spec:
  type: service
  owner: group:tfm-experience-mobile
  lifecycle: production
  providesApis:
    - payments-api
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: payments-api
  description: |
    Service providing Payments functionality
spec:
  type: openapi
  owner: group:tfm-experience-mobile
  lifecycle: production
  definition:
    $text: ../../../libs/mobile/payments-api/contract/openapi3.yaml
