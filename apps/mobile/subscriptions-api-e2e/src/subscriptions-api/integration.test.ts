// eslint-disable-next-line @nx/enforce-module-boundaries
import { AppModule } from '../../../subscriptions-api/src/app.module';
import { Client } from 'pg';
import { DriverAccountDbConfigMap } from '@experience/mobile/driver-account/database';
import { HttpResponse, http } from 'msw';
import { INestApplication } from '@nestjs/common';
import { PurgeQueueCommand, SQSClient } from '@aws-sdk/client-sqs';
import { handlers as assetsApiHandlers } from '@experience/shared/axios/assets-api-client-msw';
import { bootstrap } from '@experience/shared/nest/utils';
import { createServer } from '@mswjs/http-middleware';
import { handlers as driverAccountApiHandlers } from '@experience/driver-account-api/msw-client';
import { handlers as rewardsApiHandlers } from '@experience/mobile/rewards-api/msw';
import { slickHandlers } from '@experience/mobile/slick/client';
import { handlers as smartChargingServiceHandlers } from '@experience/shared/axios/smart-charging-service-client-msw';
import { v4 } from 'uuid';
import axios from 'axios';
import {
  findByType,
  sendMessage,
  buildInstallationEvent,
  buildOrderCreatedEvent,
  waitForSubscriptionToBeCreated,
} from './helpers';
import { waitFor } from '@experience/shared/typescript/utils';
import { describePodDriveTests } from './pod-drive';
import { describePodDriveSATests } from './pod-drive-sa';
import { describePodDriveRewardsTests } from './pod-drive-rewards';

const PORT = 5120;
const BASE_URL_NEUTRAL = `http://localhost:${PORT}`;

const salesforceHandlers = [
  http.post('/services/oauth2/token', () =>
    HttpResponse.json({}, { status: 200 })
  ),

  http.patch(
    '/services/data/v63.0/sobjects/Order/OrderNumber/:orderReference',
    () => HttpResponse.json({}, { status: 200 })
  ),
];

describe('subscriptions api', () => {
  jest.setTimeout(180_000);

  let db: Client;
  let sqs: SQSClient;

  let nestApi: INestApplication;

  describe('app module', () => {
    beforeAll(async () => {
      createServer(...slickHandlers).listen(7777);
      createServer(...salesforceHandlers).listen(7778);
      createServer(...smartChargingServiceHandlers).listen(7779);
      createServer(...driverAccountApiHandlers).listen(5104);
      createServer(...rewardsApiHandlers).listen(5111);
      createServer(...assetsApiHandlers).listen(4001);

      nestApi = await bootstrap({
        module: AppModule,
        port: PORT,
        rawBody: true,
      });

      const driverAccountDbConfig: DriverAccountDbConfigMap = JSON.parse(
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        process.env.DRIVER_ACCOUNT_DB_CONFIG!
      );

      db = new Client({
        port: driverAccountDbConfig.migrate.port,
        host: driverAccountDbConfig.migrate.host,
        database: driverAccountDbConfig.migrate.database,
        user: driverAccountDbConfig.migrate.username,
        password: driverAccountDbConfig.migrate.password,
      });

      await db.connect();

      sqs = new SQSClient({
        endpoint: process.env.AWS_ENDPOINT_URL,
      });
    });

    afterAll(async () => {
      await nestApi.close();
    });

    beforeEach(async () => {
      // clean slate
      await db.query('DELETE FROM subscriptions.subscriptions;');
      await sqs.send(
        new PurgeQueueCommand({
          QueueUrl: process.env.SUBSCRIPTIONS_API_INCOMING_EVENTS_QUEUE_URL,
        })
      );
    });

    describe('health controller', () => {
      it('should perform a health check', async () => {
        const response = await axios.get(`${BASE_URL_NEUTRAL}/health`);
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });
    });

    describePodDriveTests(() => sqs);
    describePodDriveSATests(() => sqs);
    describePodDriveRewardsTests();

    describe('/subscriptions', () => {
      describe('/subscriptions/:subscriptionId/actions/:actionId', () => {
        describe('GET', () => {
          it('returns 404 if actionId does not exist', async () => {
            await expect(
              axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions/${v4()}/actions/${v4()}`
              )
            ).rejects.toThrow('Request failed with status code 404');
          });

          it('should return 400 if subscriptionId is not a valid UUID', async () => {
            const response = await axios.get(
              `${BASE_URL_NEUTRAL}/subscriptions/not-a-valid-id/actions/${v4()}`,
              {
                validateStatus: (status) => status === 400,
              }
            );

            expect(response.status).toEqual(400);
            expect(response.data).toEqual({
              error: 'Bad Request',
              message: ['subscriptionId must be a UUID'],
              statusCode: 400,
            });
          });

          it('should return 400 if actionId is not a valid UUID', async () => {
            const response = await axios.get(
              `${BASE_URL_NEUTRAL}/subscriptions/${v4()}/actions/not-a-valid-id`,
              {
                validateStatus: (status) => status === 400,
              }
            );

            expect(response.status).toEqual(400);
            expect(response.data).toEqual({
              error: 'Bad Request',
              message: ['actionId must be a UUID'],
              statusCode: 400,
            });
          });

          it('returns a 404 if the action belongs to a different subscription', async () => {
            const order1 = buildOrderCreatedEvent({
              email: '<EMAIL>',
              planType: 'POD_DRIVE',
              orderId: '972127fc-38a0-401f-9dda-0fcc8e7fd896',
            });

            await sendMessage(sqs, order1);

            const order2 = buildOrderCreatedEvent({
              email: '<EMAIL>',
              planType: 'POD_DRIVE',
              orderId: 'a0424914-b024-4d95-ae36-a99daf7ba1e6',
            });

            await sendMessage(sqs, order2);

            const subscription1 = await waitForSubscriptionToBeCreated(
              '5357be96-1495-4951-8046-c2d59ba76c33',
              order1.detail.payload.Order_Number__c
            );

            const subscription2 = await waitForSubscriptionToBeCreated(
              '7dde63e4-b8b2-44fb-8a22-96b3077a2b23',
              order2.detail.payload.Order_Number__c
            );

            await expect(
              axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription1.id}/actions/${subscription2.actions[0].id}`
              )
            ).rejects.toThrow('Request failed with status code 404');
          });

          it('returns 200 if actionId does exist', async () => {
            const order = buildOrderCreatedEvent({
              email: '<EMAIL>',
              planType: 'POD_DRIVE',
              orderId: 'd66119e4-767c-4a05-8d67-396aaf785d6c',
            });

            await sendMessage(sqs, order);

            const subscription = await waitForSubscriptionToBeCreated(
              '5357be96-1495-4951-8046-c2d59ba76c33',
              order.detail.payload.Order_Number__c
            );

            const response = await axios.get(
              `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${subscription.actions[0].id}`
            );

            expect(response.status).toEqual(200);
          });
        });

        describe('PATCH', () => {
          it('returns 400 if the input is bad', async () => {
            await expect(
              axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${v4()}/actions/${v4()}`,
                {
                  data: {
                    foo: 'bar',
                  },
                }
              )
            ).rejects.toThrow('Request failed with status code 400');
          });

          it('returns 404 if the action does not exist', async () => {
            await expect(
              axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${v4()}/actions/${v4()}`,
                {
                  type: 'CHECK_AFFORDABILITY_V1',
                  data: {
                    title: 'MR',
                    firstName: 'john',
                    lastName: 'smith',
                    email: '<EMAIL>',
                    phoneNumber: '+447234567891',
                    dateOfBirth: '1986-01-01',
                    maritalStatus: 'SINGLE',
                    residentialStatus: 'LIVING_WITH_PARENTS',
                    employmentStatus: 'FULL_TIME',
                    dependencies: '3+',
                    billingAddress: {
                      number: '222',
                      street: "Gray's Inn Road",
                      town: 'London',
                      postcode: 'WC1X 8HB',
                    },
                    monthlyTakeHomePay: 1500,
                    monthlyHousePayments: 500,
                    monthlyTravelAndLivingExpenses: 100,
                    monthlyCreditPayments: 250,
                    monthlyHouseholdExpenses: 250,
                    circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
                  },
                }
              )
            ).rejects.toThrow('Request failed with status code 404');
          });
        });
      });

      describe('/subscriptions/:subscriptionId', () => {
        describe('GET', () => {
          it('returns 404 if subscriptionId does not exist', async () => {
            await expect(
              axios.get(`${BASE_URL_NEUTRAL}/subscriptions/${v4()}`)
            ).rejects.toThrow('Request failed with status code 404');
          });

          it('return 400 if id is not a valid uuid', async () => {
            const response = await axios.get(
              `${BASE_URL_NEUTRAL}/subscriptions/123`,
              {
                validateStatus: (status) => status === 400,
              }
            );

            expect(response.status).toEqual(400);
            expect(response.data).toEqual({
              error: 'Bad Request',
              message: ['subscriptionId must be a UUID'],
              statusCode: 400,
            });
          });

          it('returns 200 if subscriptionId does exist', async () => {
            const order = buildOrderCreatedEvent({
              email: '<EMAIL>',
              planType: 'POD_DRIVE',
              orderId: '1c790f50-4c9c-4e37-a752-3845c5ee8bd8',
            });

            await sendMessage(sqs, order);

            const subscription = await waitForSubscriptionToBeCreated(
              '5357be96-1495-4951-8046-c2d59ba76c33',
              order.detail.payload.Order_Number__c
            );

            const response = await axios.get(
              `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}`
            );

            expect(response.status).toEqual(200);
          });
        });

        describe('DELETE', () => {
          it('returns a 404 if the subscription does not exist', async () => {
            await expect(
              axios.delete(
                `${BASE_URL_NEUTRAL}/subscriptions/c6678d03-92b9-4838-af04-104eec70ac07?mode=debug`
              )
            ).rejects.toThrow('Request failed with status code 404');
          });

          it('return 400 if id is not a valid uuid', async () => {
            const response = await axios.delete(
              `${BASE_URL_NEUTRAL}/subscriptions/123`,
              {
                validateStatus: (status) => status === 400,
              }
            );

            expect(response.status).toEqual(400);
            expect(response.data).toEqual({
              error: 'Bad Request',
              message: ['subscriptionId must be a UUID'],
              statusCode: 400,
            });
          });

          it("sets the subscription's status to cancelled, deleting it and associated actions and plan", async () => {
            const order = buildOrderCreatedEvent({
              email: '<EMAIL>',
              planType: 'POD_DRIVE',
              orderId: 'a641c755-fa91-4211-b61e-23492a92d590',
            });

            await sendMessage(sqs, order);

            const subscription = await waitForSubscriptionToBeCreated(
              '5357be96-1495-4951-8046-c2d59ba76c33',
              order.detail.payload.Order_Number__c
            );

            expect(subscription.status).not.toEqual('CANCELLED');

            const COUNT_ACTIONS =
              'SELECT COUNT(*) FROM subscriptions.actions WHERE subscription_id = $1 AND deleted_at IS NULL';
            const COUNT_PLANS =
              'SELECT COUNT(*) FROM subscriptions.plans WHERE subscription_id = $1 AND deleted_at IS NULL';
            const GET_SUBSCRIPTION =
              'SELECT * FROM subscriptions.subscriptions WHERE id = $1';

            const actionsBefore = await db.query(COUNT_ACTIONS, [
              subscription.id,
            ]);

            expect(actionsBefore.rows[0].count).toEqual('6');

            const planBefore = await db.query(COUNT_PLANS, [subscription.id]);

            expect(planBefore.rows[0].count).toEqual('1');

            const response = await axios.delete(
              `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}?mode=debug`
            );

            expect(response.status).toEqual(200);

            const subscriptionAfter = await db.query(GET_SUBSCRIPTION, [
              subscription.id,
            ]);

            expect(subscriptionAfter.rows[0].status).toEqual('CANCELLED');

            const actionsAfter = await db.query(COUNT_ACTIONS, [
              subscription.id,
            ]);

            expect(actionsAfter.rows[0].count).toEqual('0');

            const planAfter = await db.query(COUNT_PLANS, [subscription.id]);

            expect(planAfter.rows[0].count).toEqual('0');
          });
        });
      });

      describe('/subscriptions/:subscriptionId/direct-debit', () => {
        describe('GET', () => {
          it('returns 404 if subscription not found', async () => {
            await expect(
              axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions/${v4()}/direct-debit`
              )
            ).rejects.toThrow('Request failed with status code 404');
          });

          it('retrieves direct debit details', async () => {
            await sendMessage(
              sqs,
              buildOrderCreatedEvent({
                email: '<EMAIL>',
                planType: 'POD_DRIVE',
                orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
              })
            );

            await waitFor(async () => {
              const responseMobileTester = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?userId=5357be96-1495-4951-8046-c2d59ba76c33`
              );

              expect(responseMobileTester.data.subscriptions).toHaveLength(1);

              const [subscription] = responseMobileTester.data.subscriptions;

              const surveyAction = findByType(
                subscription.actions,
                'COMPLETE_HOME_SURVEY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${surveyAction.id}`,
                {
                  type: 'COMPLETE_HOME_SURVEY_V1',
                  data: {},
                }
              );

              const checkAffordabilityAction = findByType(
                subscription.actions,
                'CHECK_AFFORDABILITY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${checkAffordabilityAction.id}`,
                {
                  type: 'CHECK_AFFORDABILITY_V1',
                  data: {
                    title: 'MR',
                    firstName: 'john',
                    lastName: 'smith',
                    email: '<EMAIL>',
                    phoneNumber: '+447234567891',
                    dateOfBirth: '1986-01-01',
                    maritalStatus: 'SINGLE',
                    residentialStatus: 'LIVING_WITH_PARENTS',
                    employmentStatus: 'FULL_TIME',
                    dependencies: '3+',
                    billingAddress: {
                      number: '222',
                      street: "Gray's Inn Road",
                      town: 'London',
                      postcode: 'WC1X 8HB',
                    },
                    monthlyTakeHomePay: 1500,
                    monthlyHousePayments: 500,
                    monthlyTravelAndLivingExpenses: 100,
                    monthlyCreditPayments: 250,
                    monthlyHouseholdExpenses: 250,
                    circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
                  },
                }
              );

              const setupDirectDebitActionId = findByType(
                subscription.actions,
                'SETUP_DIRECT_DEBIT_V1'
              )?.id;

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${setupDirectDebitActionId}`,
                {
                  type: 'SETUP_DIRECT_DEBIT_V1',
                  data: {
                    accountNumber: '********',
                    sortCode: '123456',
                    accountName: 'barry white',
                    requiresMoreThanOneSignatory: true,
                    understandsDirectDebitGuarantee: true,
                  },
                }
              );

              const signDocumentsAction = findByType(
                subscription.actions,
                'SIGN_DOCUMENTS_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${signDocumentsAction.id}`,
                {
                  type: 'SIGN_DOCUMENTS_V1',
                  data: {},
                }
              );

              await sendMessage(
                sqs,
                buildInstallationEvent({
                  orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
                  ppid: 'PSL-123456',
                })
              );
            });

            await waitFor(async () => {
              const subscription2 = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?ppid=PSL-123456`
              );

              expect(subscription2.data?.subscriptions?.[0]?.status).toEqual(
                'ACTIVE'
              );

              const response = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription2.data.subscriptions[0].id}/direct-debit`
              );

              expect(response.status).toEqual(200);
              expect(response.data).toEqual({
                accountNumberLastDigits: '4567',
                monthlyPaymentDay: 1,
                nameOnAccount: 'Mr John Smith',
                sortCodeLastDigits: '80',
              });
            });
          });
        });
      });

      describe('/subscriptions/:subscriptionId/documents', () => {
        describe('GET', () => {
          it('returns 404 if subscription not found', async () => {
            await expect(
              axios.get(`${BASE_URL_NEUTRAL}/subscriptions/${v4()}/documents`)
            ).rejects.toThrow('Request failed with status code 404');
          });

          it('retrieves documents', async () => {
            await sendMessage(
              sqs,
              buildOrderCreatedEvent({
                email: '<EMAIL>',
                planType: 'POD_DRIVE',
                orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
              })
            );

            await waitFor(async () => {
              const responseMobileTester = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?userId=5357be96-1495-4951-8046-c2d59ba76c33`
              );

              expect(responseMobileTester.data.subscriptions).toHaveLength(1);

              const [subscription] = responseMobileTester.data.subscriptions;

              const surveyAction = findByType(
                subscription.actions,
                'COMPLETE_HOME_SURVEY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${surveyAction.id}`,
                {
                  type: 'COMPLETE_HOME_SURVEY_V1',
                  data: {},
                }
              );

              const checkAffordabilityAction = findByType(
                subscription.actions,
                'CHECK_AFFORDABILITY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${checkAffordabilityAction.id}`,
                {
                  type: 'CHECK_AFFORDABILITY_V1',
                  data: {
                    title: 'MR',
                    firstName: 'john',
                    lastName: 'smith',
                    email: '<EMAIL>',
                    phoneNumber: '+447234567891',
                    dateOfBirth: '1986-01-01',
                    maritalStatus: 'SINGLE',
                    residentialStatus: 'LIVING_WITH_PARENTS',
                    employmentStatus: 'FULL_TIME',
                    dependencies: '3+',
                    billingAddress: {
                      number: '222',
                      street: "Gray's Inn Road",
                      town: 'London',
                      postcode: 'WC1X 8HB',
                    },
                    monthlyTakeHomePay: 1500,
                    monthlyHousePayments: 500,
                    monthlyTravelAndLivingExpenses: 100,
                    monthlyCreditPayments: 250,
                    monthlyHouseholdExpenses: 250,
                    circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
                  },
                }
              );

              const setupDirectDebitActionId = findByType(
                subscription.actions,
                'SETUP_DIRECT_DEBIT_V1'
              )?.id;

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${setupDirectDebitActionId}`,
                {
                  type: 'SETUP_DIRECT_DEBIT_V1',
                  data: {
                    accountNumber: '********',
                    sortCode: '123456',
                    accountName: 'barry white',
                    requiresMoreThanOneSignatory: true,
                    understandsDirectDebitGuarantee: true,
                  },
                }
              );

              const signDocumentsAction = findByType(
                subscription.actions,
                'SIGN_DOCUMENTS_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${signDocumentsAction.id}`,
                {
                  type: 'SIGN_DOCUMENTS_V1',
                  data: {},
                }
              );

              await sendMessage(
                sqs,
                buildInstallationEvent({
                  orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
                  ppid: 'PSL-123456',
                })
              );
            });

            await waitFor(async () => {
              const subscription2 = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?ppid=PSL-123456`
              );

              expect(subscription2.data?.subscriptions?.[0]?.status).toEqual(
                'ACTIVE'
              );

              const response = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription2.data.subscriptions[0].id}/documents`
              );

              expect(response.status).toEqual(200);
              expect(response.data).toEqual({
                documents: [
                  {
                    active: true,
                    format: 'PDF',
                    issued: expect.any(String),
                    link: `/subscriptions/${subscription2.data.subscriptions[0].id}/documents/LMS-15`,
                    type: 'ha',
                  },
                  {
                    active: true,
                    format: 'PDF',
                    issued: expect.any(String),
                    link: `/subscriptions/${subscription2.data.subscriptions[0].id}/documents/LMS-16`,
                    type: 'rca',
                  },
                  {
                    active: true,
                    format: 'PDF',
                    issued: expect.any(String),
                    link: `/subscriptions/${subscription2.data.subscriptions[0].id}/documents/LMS-17`,
                    type: 'wae',
                  },
                ],
              });
            });
          });
        });
      });

      describe('/subscriptions/:subscriptionId/documents/:documentId', () => {
        describe('GET', () => {
          it('returns 404 if subscription not found', async () => {
            await expect(
              axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions/${v4()}/documents/LMS-15`
              )
            ).rejects.toThrow('Request failed with status code 404');
          });

          it('returns 404 if subscription found but unknown document', async () => {
            await sendMessage(
              sqs,
              buildOrderCreatedEvent({
                email: '<EMAIL>',
                planType: 'POD_DRIVE',
                orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
              })
            );

            await waitFor(async () => {
              const responseMobileTester = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?userId=5357be96-1495-4951-8046-c2d59ba76c33`
              );

              expect(responseMobileTester.data.subscriptions).toHaveLength(1);

              const [subscription] = responseMobileTester.data.subscriptions;

              const surveyAction = findByType(
                subscription.actions,
                'COMPLETE_HOME_SURVEY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${surveyAction.id}`,
                {
                  type: 'COMPLETE_HOME_SURVEY_V1',
                  data: {},
                }
              );

              const checkAffordabilityAction = findByType(
                subscription.actions,
                'CHECK_AFFORDABILITY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${checkAffordabilityAction.id}`,
                {
                  type: 'CHECK_AFFORDABILITY_V1',
                  data: {
                    title: 'MR',
                    firstName: 'john',
                    lastName: 'smith',
                    email: '<EMAIL>',
                    phoneNumber: '+447234567891',
                    dateOfBirth: '1986-01-01',
                    maritalStatus: 'SINGLE',
                    residentialStatus: 'LIVING_WITH_PARENTS',
                    employmentStatus: 'FULL_TIME',
                    dependencies: '3+',
                    billingAddress: {
                      number: '222',
                      street: "Gray's Inn Road",
                      town: 'London',
                      postcode: 'WC1X 8HB',
                    },
                    monthlyTakeHomePay: 1500,
                    monthlyHousePayments: 500,
                    monthlyTravelAndLivingExpenses: 100,
                    monthlyCreditPayments: 250,
                    monthlyHouseholdExpenses: 250,
                    circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
                  },
                }
              );

              const setupDirectDebitActionId = findByType(
                subscription.actions,
                'SETUP_DIRECT_DEBIT_V1'
              )?.id;

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${setupDirectDebitActionId}`,
                {
                  type: 'SETUP_DIRECT_DEBIT_V1',
                  data: {
                    accountNumber: '********',
                    sortCode: '123456',
                    accountName: 'barry white',
                    requiresMoreThanOneSignatory: true,
                    understandsDirectDebitGuarantee: true,
                  },
                }
              );

              const signDocumentsAction = findByType(
                subscription.actions,
                'SIGN_DOCUMENTS_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${signDocumentsAction.id}`,
                {
                  type: 'SIGN_DOCUMENTS_V1',
                  data: {},
                }
              );

              await sendMessage(
                sqs,
                buildInstallationEvent({
                  orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
                  ppid: 'PSL-123456',
                })
              );
            });

            await waitFor(async () => {
              const subscription2 = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?ppid=PSL-123456`
              );

              expect(subscription2.data?.subscriptions?.[0]?.status).toEqual(
                'ACTIVE'
              );

              await expect(
                axios.get(
                  `${BASE_URL_NEUTRAL}/subscriptions/${subscription2.data?.subscriptions?.[0]?.id}/documents/LMS-9999999`
                )
              ).rejects.toThrow('Request failed with status code 404');
            });
          });

          it('downloads document', async () => {
            await sendMessage(
              sqs,
              buildOrderCreatedEvent({
                email: '<EMAIL>',
                planType: 'POD_DRIVE',
                orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
              })
            );

            await waitFor(async () => {
              const responseMobileTester = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?userId=5357be96-1495-4951-8046-c2d59ba76c33`
              );

              expect(responseMobileTester.data.subscriptions).toHaveLength(1);

              const [subscription] = responseMobileTester.data.subscriptions;

              const surveyAction = findByType(
                subscription.actions,
                'COMPLETE_HOME_SURVEY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${surveyAction.id}`,
                {
                  type: 'COMPLETE_HOME_SURVEY_V1',
                  data: {},
                }
              );

              const checkAffordabilityAction = findByType(
                subscription.actions,
                'CHECK_AFFORDABILITY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${checkAffordabilityAction.id}`,
                {
                  type: 'CHECK_AFFORDABILITY_V1',
                  data: {
                    title: 'MR',
                    firstName: 'john',
                    lastName: 'smith',
                    email: '<EMAIL>',
                    phoneNumber: '+447234567891',
                    dateOfBirth: '1986-01-01',
                    maritalStatus: 'SINGLE',
                    residentialStatus: 'LIVING_WITH_PARENTS',
                    employmentStatus: 'FULL_TIME',
                    dependencies: '3+',
                    billingAddress: {
                      number: '222',
                      street: "Gray's Inn Road",
                      town: 'London',
                      postcode: 'WC1X 8HB',
                    },
                    monthlyTakeHomePay: 1500,
                    monthlyHousePayments: 500,
                    monthlyTravelAndLivingExpenses: 100,
                    monthlyCreditPayments: 250,
                    monthlyHouseholdExpenses: 250,
                    circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
                  },
                }
              );

              const setupDirectDebitActionId = findByType(
                subscription.actions,
                'SETUP_DIRECT_DEBIT_V1'
              )?.id;

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${setupDirectDebitActionId}`,
                {
                  type: 'SETUP_DIRECT_DEBIT_V1',
                  data: {
                    accountNumber: '********',
                    sortCode: '123456',
                    accountName: 'barry white',
                    requiresMoreThanOneSignatory: true,
                    understandsDirectDebitGuarantee: true,
                  },
                }
              );

              const signDocumentsAction = findByType(
                subscription.actions,
                'SIGN_DOCUMENTS_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${signDocumentsAction.id}`,
                {
                  type: 'SIGN_DOCUMENTS_V1',
                  data: {},
                }
              );

              await sendMessage(
                sqs,
                buildInstallationEvent({
                  orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
                  ppid: 'PSL-123456',
                })
              );
            });

            await waitFor(async () => {
              const subscription2 = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?ppid=PSL-123456`
              );

              expect(subscription2.data?.subscriptions?.[0]?.status).toEqual(
                'ACTIVE'
              );

              const response = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription2.data?.subscriptions?.[0]?.id}/documents/LMS-15`
              );

              expect(response.status).toEqual(200);
              expect(response.headers['content-type']).toEqual(
                'application/pdf'
              );
            });
          });
        });
      });

      describe('/subscriptions/?userId', () => {
        describe('GET', () => {
          it('returns empty array if userId does not exist', async () => {
            const response = await axios.get(
              `${BASE_URL_NEUTRAL}/subscriptions?userId=fake-news`
            );

            expect(response.status).toEqual(200);
            expect(response.data).toEqual({
              subscriptions: [],
            });
          });

          it('returns 200 if userId does exist', async () => {
            const order = buildOrderCreatedEvent({
              email: '<EMAIL>',
              planType: 'POD_DRIVE',
              orderId: '314c6fc3-d5cd-4175-a227-b03c2b0f91ad',
            });

            await sendMessage(sqs, order);

            await waitFor(async () => {
              const response = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?userId=5357be96-1495-4951-8046-c2d59ba76c33`
              );

              expect(response.data.subscriptions).toHaveLength(1);

              const [subscription] = response.data.subscriptions;

              expect(response.status).toEqual(200);
              expect(response.data).toEqual({
                subscriptions: [
                  {
                    id: subscription.id,
                    userId: '5357be96-1495-4951-8046-c2d59ba76c33',
                    order: {
                      id: order.detail.payload.Order_Number__c,
                      origin: 'SALESFORCE_TEST',
                      orderedAt: order.detail.payload.Order_Created_At__c,
                      address: {
                        line1: "222 Gray's Inn Road",
                        line2: null,
                        line3: 'LONDON',
                        postcode: 'WC1X 8HB',
                      },
                      email: '<EMAIL>',
                      firstName: 'Mobile',
                      lastName: 'Tester',
                      mpan: 'S 01 801 101 22 6130 5588 165',
                      phoneNumber: '02072474114',
                      eCommerceId: 'dr3l2e8dhu::2415',
                    },
                    status: subscription.status,
                    activatedAt: null,
                    createdAt: expect.any(String),
                    updatedAt: expect.any(String),
                    deletedAt: null,
                    plan: {
                      id: expect.any(String),
                      allowanceMiles: 10000,
                      allowancePeriod: 'ANNUAL',
                      monthlyFeePounds: 35,
                      upfrontFeePounds: 99,
                      contractDurationMonths: 18,
                      productCode: 'pod_drive_product_01',
                      rateMilesPerKwh: 3.5,
                      ratePencePerMile: 2.3,
                      type: 'POD_DRIVE',
                      milesRenewalDate: null,
                    },
                    actions: [
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'PAY_UPFRONT_FEE_V1',
                        owner: 'USER',
                        status: 'SUCCESS',
                        dependsOn: [],
                        data: {},
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'COMPLETE_HOME_SURVEY_V1',
                        owner: 'USER',
                        status: 'PENDING',
                        dependsOn: [expect.any(String)],
                        data: {
                          surveyUrl: 'https://pod-point.com',
                        },
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'CHECK_AFFORDABILITY_V1',
                        owner: 'USER',
                        status: 'PENDING',
                        dependsOn: [expect.any(String)],
                        data: {
                          applicationId: null,
                          loanId: null,
                        },
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'SETUP_DIRECT_DEBIT_V1',
                        owner: 'USER',
                        status: 'PENDING',
                        dependsOn: [expect.any(String)],
                        data: {},
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'SIGN_DOCUMENTS_V1',
                        owner: 'USER',
                        status: 'PENDING',
                        dependsOn: [expect.any(String)],
                        data: {
                          documents: [],
                        },
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'INSTALL_CHARGING_STATION_V1',
                        owner: 'SYSTEM',
                        status: 'PENDING',
                        dependsOn: [expect.any(String)],
                        data: {
                          ppid: null,
                        },
                      },
                    ],
                  },
                ],
              });
            });
          });
        });
      });

      describe('/subscriptions/?ppid', () => {
        describe('GET', () => {
          it('returns empty array if ppid does not exist', async () => {
            const response = await axios.get(
              `${BASE_URL_NEUTRAL}/subscriptions?userId=fake-news`
            );

            expect(response.status).toEqual(200);
            expect(response.data).toEqual({
              subscriptions: [],
            });
          });

          it('returns the subscriptions behind a ppid', async () => {
            const order = buildOrderCreatedEvent({
              email: '<EMAIL>',
              planType: 'POD_DRIVE',
              orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
            });

            await sendMessage(sqs, order);

            await waitFor(async () => {
              const responseMobileTester = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?userId=5357be96-1495-4951-8046-c2d59ba76c33`
              );

              expect(responseMobileTester.data.subscriptions).toHaveLength(1);

              const [subscription] = responseMobileTester.data.subscriptions;

              const surveyAction = findByType(
                subscription.actions,
                'COMPLETE_HOME_SURVEY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${surveyAction.id}`,
                {
                  type: 'COMPLETE_HOME_SURVEY_V1',
                  data: {},
                }
              );

              const checkAffordabilityAction = findByType(
                subscription.actions,
                'CHECK_AFFORDABILITY_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${checkAffordabilityAction.id}`,
                {
                  type: 'CHECK_AFFORDABILITY_V1',
                  data: {
                    title: 'MR',
                    firstName: 'john',
                    lastName: 'smith',
                    email: '<EMAIL>',
                    phoneNumber: '+447234567891',
                    dateOfBirth: '1986-01-01',
                    maritalStatus: 'SINGLE',
                    residentialStatus: 'LIVING_WITH_PARENTS',
                    employmentStatus: 'FULL_TIME',
                    dependencies: '3+',
                    billingAddress: {
                      number: '222',
                      street: "Gray's Inn Road",
                      town: 'London',
                      postcode: 'WC1X 8HB',
                    },
                    monthlyTakeHomePay: 1500,
                    monthlyHousePayments: 500,
                    monthlyTravelAndLivingExpenses: 100,
                    monthlyCreditPayments: 250,
                    monthlyHouseholdExpenses: 250,
                    circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
                  },
                }
              );

              const setupDirectDebitActionId = findByType(
                subscription.actions,
                'SETUP_DIRECT_DEBIT_V1'
              )?.id;

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${setupDirectDebitActionId}`,
                {
                  type: 'SETUP_DIRECT_DEBIT_V1',
                  data: {
                    accountNumber: '********',
                    sortCode: '123456',
                    accountName: 'barry white',
                    requiresMoreThanOneSignatory: true,
                    understandsDirectDebitGuarantee: true,
                  },
                }
              );

              const signDocumentsAction = findByType(
                subscription.actions,
                'SIGN_DOCUMENTS_V1'
              );

              await axios.patch(
                `${BASE_URL_NEUTRAL}/subscriptions/${subscription.id}/actions/${signDocumentsAction.id}`,
                {
                  type: 'SIGN_DOCUMENTS_V1',
                  data: {},
                }
              );

              await sendMessage(
                sqs,
                buildInstallationEvent({
                  orderId: '70740e5b-a3ed-4f75-bbaf-39a07eae582a',
                  ppid: 'PSL-123456',
                })
              );
            });

            await waitFor(async () => {
              const response = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?ppid=PSL-123456`
              );

              expect(response.data?.subscriptions?.[0]?.status).toEqual(
                'ACTIVE'
              );

              const [subscription] = response.data.subscriptions;

              expect(response.status).toEqual(200);
              expect(response.data).toEqual({
                subscriptions: [
                  {
                    id: subscription.id,
                    userId: '5357be96-1495-4951-8046-c2d59ba76c33',
                    order: {
                      id: order.detail.payload.Order_Number__c,
                      origin: 'SALESFORCE_TEST',
                      orderedAt: order.detail.payload.Order_Created_At__c,
                      address: {
                        line1: "222 Gray's Inn Road",
                        line2: null,
                        line3: 'LONDON',
                        postcode: 'WC1X 8HB',
                      },
                      email: '<EMAIL>',
                      firstName: 'Mobile',
                      lastName: 'Tester',
                      mpan: 'S 01 801 101 22 6130 5588 165',
                      phoneNumber: '02072474114',
                      eCommerceId: 'dr3l2e8dhu::2415',
                    },
                    status: subscription.status,
                    activatedAt: expect.any(String),
                    createdAt: expect.any(String),
                    updatedAt: expect.any(String),
                    deletedAt: null,
                    plan: {
                      id: expect.any(String),
                      allowanceMiles: 10000,
                      allowancePeriod: 'ANNUAL',
                      monthlyFeePounds: 35,
                      upfrontFeePounds: 99,
                      contractDurationMonths: 18,
                      productCode: 'pod_drive_product_01',
                      rateMilesPerKwh: 3.5,
                      ratePencePerMile: 2.3,
                      type: 'POD_DRIVE',
                      milesRenewalDate: expect.any(String),
                    },
                    actions: [
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'PAY_UPFRONT_FEE_V1',
                        owner: 'USER',
                        status: 'SUCCESS',
                        dependsOn: [],
                        data: {},
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'COMPLETE_HOME_SURVEY_V1',
                        owner: 'USER',
                        status: 'SUCCESS',
                        dependsOn: [expect.any(String)],
                        data: {
                          surveyUrl: 'https://pod-point.com',
                        },
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'CHECK_AFFORDABILITY_V1',
                        owner: 'USER',
                        status: 'SUCCESS',
                        dependsOn: [expect.any(String)],
                        data: {
                          applicationId: 1,
                          loanId: ********90,
                        },
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'SETUP_DIRECT_DEBIT_V1',
                        owner: 'USER',
                        status: 'SUCCESS',
                        dependsOn: [expect.any(String)],
                        data: {},
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'SIGN_DOCUMENTS_V1',
                        owner: 'USER',
                        status: 'SUCCESS',
                        dependsOn: [expect.any(String)],
                        data: {
                          documents: [
                            {
                              code: 'rca',
                              signed: true,
                              signingUrl: 'https://sell.your-soul.here',
                            },
                            {
                              code: 'ha',
                              signed: true,
                              signingUrl: 'https://sell.your-soul.here',
                            },
                          ],
                        },
                      },
                      {
                        id: expect.any(String),
                        subscriptionId: expect.any(String),
                        type: 'INSTALL_CHARGING_STATION_V1',
                        owner: 'SYSTEM',
                        status: 'SUCCESS',
                        dependsOn: [expect.any(String)],
                        data: {
                          ppid: 'PSL-123456',
                        },
                      },
                    ],
                  },
                ],
              });
            });
          });

          it('returns the actions in the correct order', async () => {
            await sendMessage(
              sqs,
              buildOrderCreatedEvent({
                email: '<EMAIL>',
                planType: 'POD_DRIVE',
                orderId: '6889620b-520e-4068-be15-328664458def',
              })
            );

            await waitFor(async () => {
              const response = await axios.get(
                `${BASE_URL_NEUTRAL}/subscriptions?userId=5357be96-1495-4951-8046-c2d59ba76c33`
              );

              expect(response.data.subscriptions).toHaveLength(1);

              expect(response.status).toEqual(200);
              expect(response.data.subscriptions[0].actions).toEqual([
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'PAY_UPFRONT_FEE_V1',
                  owner: 'USER',
                  status: 'SUCCESS',
                  dependsOn: [],
                  data: {},
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'COMPLETE_HOME_SURVEY_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [expect.any(String)],
                  data: {
                    surveyUrl: 'https://pod-point.com',
                  },
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'CHECK_AFFORDABILITY_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [expect.any(String)],
                  data: {
                    applicationId: null,
                    loanId: null,
                  },
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'SETUP_DIRECT_DEBIT_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [expect.any(String)],
                  data: {},
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'SIGN_DOCUMENTS_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [expect.any(String)],
                  data: {
                    documents: [],
                  },
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'INSTALL_CHARGING_STATION_V1',
                  owner: 'SYSTEM',
                  status: 'PENDING',
                  dependsOn: [expect.any(String)],
                  data: {
                    ppid: null,
                  },
                },
              ]);
            });
          });
        });
      });
    });
  });
});
