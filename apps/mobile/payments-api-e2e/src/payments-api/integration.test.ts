// eslint-disable-next-line @nx/enforce-module-boundaries
import { AppModule } from '../../../payments-api/src/app/app.module';
import { Client } from 'pg';
import {
  DockerComposeEnvironment,
  StartedDockerComposeEnvironment,
  Wait,
} from 'testcontainers';
import { DriverAccountDbConfigMap } from '@experience/mobile/driver-account/database';
import { INestApplication } from '@nestjs/common';
import { handlers as acquiredHandlers } from '@experience/shared/axios/acquired-api-client-msw';
import { bootstrap } from '@experience/shared/nest/utils';
import { createServer } from '@mswjs/http-middleware';
import { v4 } from 'uuid';
import axios from 'axios';
import path from 'path';

const PORT = 5121;
const BASE_URL_NEUTRAL = `http://localhost:${PORT}`;

describe('payments api', () => {
  jest.setTimeout(180_000);

  let nestApi: INestApplication;
  let podadminDatabase: StartedDockerComposeEnvironment;
  let db: Client;

  describe('app module', () => {
    beforeAll(async () => {
      createServer(...acquiredHandlers).listen(8989);

      podadminDatabase = await new DockerComposeEnvironment(
        path.resolve(__dirname, '../../../../../libs/shared/test/db/podadmin'),
        'docker-compose.yml'
      )
        .withEnvironment({
          PORT: '3309',
          CONTAINER_NAME: 'payments-api-e2e-podadmin',
          NETWORK_NAME: 'payments-api-e2e',
        })
        .withWaitStrategy('payments-api-e2e-podadmin', Wait.forListeningPorts())
        .withStartupTimeout(60000)
        .up();

      nestApi = await bootstrap({
        module: AppModule,
        port: PORT,
        rawBody: true,
      });

      const driverAccountDbConfig: DriverAccountDbConfigMap = JSON.parse(
        process.env.DRIVER_ACCOUNT_DB_CONFIG!
      );

      db = new Client({
        port: driverAccountDbConfig.migrate.port,
        host: driverAccountDbConfig.migrate.host,
        database: driverAccountDbConfig.migrate.database,
        user: driverAccountDbConfig.migrate.username,
        password: driverAccountDbConfig.migrate.password,
      });

      await db.connect();
    });

    afterAll(async () => {
      await nestApi.close();
      await podadminDatabase.stop();
    });

    describe('health controller', () => {
      it('should perform a health check', async () => {
        const response = await axios.get(`${BASE_URL_NEUTRAL}/health`);
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });
    });

    describe('bank account controller', () => {
      it('POST /bank-accounts', async () => {
        const userId = v4();

        const res = await axios.post(`${BASE_URL_NEUTRAL}/bank-accounts`, {
          userId,
          accountName: 'Mobile Tester',
          accountNumber: '********',
          sortCode: '223344',
        });

        expect(res.status).toEqual(201);
        expect(res.data).toEqual({
          id: expect.any(String),
          userId,
          accountName: 'Mobile Tester',
          accountNumberLastFourDigits: '3300',
          acquiredCustomerId: expect.any(String),
          acquiredPayeeId: expect.any(String),
          active: true,
          createdAt: expect.any(String),
        });
      });

      describe('GET /bank-accounts/{bankAccountId}', () => {
        it('Gets active bank account', async () => {
          const userId = v4();

          const createBankAccount = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId,
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          expect(createBankAccount.status).toEqual(201);

          const bankAccountId = createBankAccount.data.id;

          const bankAccount = await axios.get(
            `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccountId}`
          );

          expect(bankAccount.status).toEqual(200);
          expect(bankAccount.data).toEqual({
            id: expect.any(String),
            userId,
            accountName: 'Mobile Tester',
            accountNumberLastFourDigits: '3300',
            acquiredCustomerId: expect.any(String),
            acquiredPayeeId: expect.any(String),
            active: true,
            createdAt: expect.any(String),
          });
        });

        it('Gets inactive bank account', async () => {
          const userId = v4();

          const createBankAccount = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId,
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          expect(createBankAccount.status).toEqual(201);

          const bankAccountId = createBankAccount.data.id;

          const deleteBankAccount = await axios.delete(
            `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccountId}`
          );

          expect(deleteBankAccount.status).toEqual(204);

          const res = await axios.get(
            `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccountId}`
          );

          expect(res.status).toEqual(200);
          expect(res.data).toEqual({
            id: expect.any(String),
            userId,
            accountName: 'Mobile Tester',
            accountNumberLastFourDigits: '3300',
            acquiredCustomerId: expect.any(String),
            acquiredPayeeId: expect.any(String),
            active: false,
            createdAt: expect.any(String),
          });
        });
      });

      describe('GET /bank-accounts/{bankAccountId}/transaction-summary', () => {
        it('returns the totalWithdrawn across multiple transactions', async () => {
          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: 'e0671f09-743c-45e2-889e-6ba38f39a3da',
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          for (let i = 0; i < 5; i++) {
            await axios.post(
              `${BASE_URL_NEUTRAL}/transactions`,
              MOCK_CREATE_PAYLOAD
            );
          }

          const res = await axios.get(
            `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccount.id}/transaction-summary?type=REWARDS`
          );

          expect(res.status).toEqual(200);
          expect(res.data).toStrictEqual({
            totalWithdrawn: [{ amount: 500, currency: 'GBP' }],
          });
        });

        it('returns a 400 if invalid filters are provided', async () => {
          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: 'e0671f09-743c-45e2-889e-6ba38f39a3da',
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          await expect(
            axios.get(
              `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccount.id}/transaction-summary?type=gibberish`
            )
          ).rejects.toThrow('Request failed with status code 400');
        });

        it('returns a 404 if the bank account does not exist', async () => {
          await expect(
            axios.get(
              `${BASE_URL_NEUTRAL}/bank-accounts/202f7e26-1c66-4368-bdf6-d3e4efb49dc3/transaction-summary?type=REWARDS`
            )
          ).rejects.toThrow('Request failed with status code 404');
        });
      });

      describe('GET /bank-accounts?userId=x', () => {
        it('Gets active bank accounts', async () => {
          const userId = v4();

          const createBankAccount = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId,
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          expect(createBankAccount.status).toEqual(201);

          const bankAccountId = createBankAccount.data.id;

          const bankAccounts = await axios.get(
            `${BASE_URL_NEUTRAL}/bank-accounts?userId=${userId}`
          );

          expect(bankAccounts.status).toEqual(200);
          expect(bankAccounts.data).toEqual([
            {
              id: bankAccountId,
              userId,
              accountName: 'Mobile Tester',
              accountNumberLastFourDigits: '3300',
              acquiredCustomerId: expect.any(String),
              acquiredPayeeId: expect.any(String),
              active: true,
              createdAt: expect.any(String),
            },
          ]);
        });

        it('Gets inactive bank accounts', async () => {
          const userId = v4();

          const createBankAccount = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId,
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          expect(createBankAccount.status).toEqual(201);

          const bankAccountId = createBankAccount.data.id;

          const deleteBankAccount = await axios.delete(
            `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccountId}`
          );

          expect(deleteBankAccount.status).toEqual(204);

          const res = await axios.get(
            `${BASE_URL_NEUTRAL}/bank-accounts?userId=${userId}&inactive=true`
          );

          expect(res.status).toEqual(200);
          expect(res.data).toEqual([
            {
              id: expect.any(String),
              userId,
              accountName: 'Mobile Tester',
              accountNumberLastFourDigits: '3300',
              acquiredCustomerId: expect.any(String),
              acquiredPayeeId: expect.any(String),
              active: false,
              createdAt: expect.any(String),
            },
          ]);
        });

        it('does not get inactive bank accounts by default', async () => {
          const userId = v4();

          const createBankAccount = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId,
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          expect(createBankAccount.status).toEqual(201);

          const bankAccountId = createBankAccount.data.id;

          const deleteBankAccount = await axios.delete(
            `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccountId}`
          );

          expect(deleteBankAccount.status).toEqual(204);

          const res = await axios.get(
            `${BASE_URL_NEUTRAL}/bank-accounts?userId=${userId}`
          );

          expect(res.status).toEqual(200);
          expect(res.data).toEqual([]);
        });
      });

      it('DELETE /bank-accounts/{bankAccountId}', async () => {
        const userId = v4();

        const createBankAccount = await axios.post(
          `${BASE_URL_NEUTRAL}/bank-accounts`,
          {
            userId,
            accountName: 'Mobile Tester',
            accountNumber: '********',
            sortCode: '223344',
          }
        );

        expect(createBankAccount.status).toEqual(201);

        const bankAccountId = createBankAccount.data.id;

        const res = await axios.delete(
          `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccountId}`
        );

        expect(res.status).toEqual(204);
        expect(res.data).toMatchSnapshot();
      });
    });

    describe('transactions controller', () => {
      describe('GET /transaction/:id', () => {
        it('gets a transaction', async () => {
          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': '81f23521-7365-4ced-8468-d237eec8dd93',
              },
            }
          );

          expect(createTransactionResponse.status).toEqual(202);

          const response = await axios.get(
            `${BASE_URL_NEUTRAL}/transactions/${createTransactionResponse.data.id}`
          );

          expect(response.status).toEqual(200);
          expect(response.data).toEqual(createTransactionResponse.data);
        });
      });

      describe('GET /transactions?', () => {
        it('gets transaction by userId', async () => {
          const idempotencyKey = v4();

          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': idempotencyKey,
              },
            }
          );

          expect(createTransactionResponse.status).toEqual(202);

          const response = await axios.get(
            `${BASE_URL_NEUTRAL}/transactions?userId=${bankAccount.userId}`
          );

          expect(response.status).toEqual(200);
          expect(response.data).toEqual([createTransactionResponse.data]);
        });

        it('gets transaction by userId INCLUDING deleted bank account', async () => {
          const idempotencyKey = v4();

          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': idempotencyKey,
              },
            }
          );

          expect(createTransactionResponse.status).toEqual(202);

          const deleteBankAccountResponse = await axios.delete(
            `${BASE_URL_NEUTRAL}/bank-accounts/${bankAccount.id}`
          );

          expect(deleteBankAccountResponse.status).toEqual(204);

          const response = await axios.get(
            `${BASE_URL_NEUTRAL}/transactions?userId=${bankAccount.userId}`
          );

          expect(response.status).toEqual(200);
          expect(response.data).toEqual([createTransactionResponse.data]);
        });

        it('gets transaction by bank account id', async () => {
          const idempotencyKey = v4();

          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': idempotencyKey,
              },
            }
          );

          expect(createTransactionResponse.status).toEqual(202);

          const response = await axios.get(
            `${BASE_URL_NEUTRAL}/transactions?bankAccountId=${bankAccount.id}`
          );

          expect(response.status).toEqual(200);
          expect(response.data).toEqual([createTransactionResponse.data]);
        });

        it('gets transaction by wallet id', async () => {
          const idempotencyKey = v4();
          const walletId = v4();

          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId,
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': idempotencyKey,
              },
            }
          );

          expect(createTransactionResponse.status).toEqual(202);

          const response = await axios.get(
            `${BASE_URL_NEUTRAL}/transactions?walletId=${walletId}`
          );

          expect(response.status).toEqual(200);
          expect(response.data).toEqual([createTransactionResponse.data]);
        });

        it('gets transaction by rewards transaction id', async () => {
          const idempotencyKey = v4();
          const rewardsTransactionId = v4();

          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId,
            },
          };

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': idempotencyKey,
              },
            }
          );

          expect(createTransactionResponse.status).toEqual(202);

          const response = await axios.get(
            `${BASE_URL_NEUTRAL}/transactions?rewardsTransactionId=${rewardsTransactionId}`
          );

          expect(response.status).toEqual(200);
          expect(response.data).toEqual([createTransactionResponse.data]);
        });
      });

      describe('POST /transactions', () => {
        it('create a transaction', async () => {
          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          const response = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': '8d60f8ff-70f0-47c4-af04-1e722780cae1',
              },
            }
          );

          expect(response.status).toEqual(202);
          expect(response.data).toStrictEqual({
            id: expect.any(String),
            bankAccountId: bankAccount.id,
            amount: MOCK_CREATE_PAYLOAD.amount,
            currency: MOCK_CREATE_PAYLOAD.currency,
            status: 'PENDING',
            createdAt: expect.any(String),
            type: MOCK_CREATE_PAYLOAD.type,
            metadata: MOCK_CREATE_PAYLOAD.metadata,
          });

          const transaction = await db.query(
            `SELECT *
           FROM payments.transactions
           WHERE idempotency_key = '8d60f8ff-70f0-47c4-af04-1e722780cae1'
           `
          );

          expect(transaction.rows[0]).toHaveProperty('id', response.data.id);
          expect(transaction.rows[0]).toHaveProperty(
            'bank_account_id',
            response.data.bankAccountId
          );
          expect(transaction.rows[0]).toHaveProperty(
            'acquired_transaction_id',
            expect.any(String)
          );
          expect(transaction.rows[0]).toHaveProperty('amount', '100.00');
          expect(transaction.rows[0]).toHaveProperty(
            'status',
            response.data.status
          );
          expect(transaction.rows[0]).toHaveProperty('metadata', {
            type: response.data.type,
            ...response.data.metadata,
          });
        });

        it('does not re-create transaction if one already exists with idempotent key', async () => {
          const { data: bankAccount } = await axios.post(
            `${BASE_URL_NEUTRAL}/bank-accounts`,
            {
              userId: v4(),
              accountName: 'Mobile Tester',
              accountNumber: '********',
              sortCode: '223344',
            }
          );

          const MOCK_CREATE_PAYLOAD = {
            type: 'REWARDS',
            bankAccountId: bankAccount.id,
            currency: 'GBP',
            amount: 100,
            metadata: {
              walletId: '97d710f8-3000-4583-8b28-093d0729d0b0',
              subscriptionId: '787e9c8c-3827-401f-8231-fbfd4586d501',
              rewardsTransactionId: '924c0a28-00c3-4e48-be41-99ce4e611bca',
            },
          };

          const response1 = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': '6ef78d67-2b68-413a-bd0b-d1ab0cb87f37',
              },
            }
          );

          const response2 = await axios.post(
            `${BASE_URL_NEUTRAL}/transactions`,
            MOCK_CREATE_PAYLOAD,
            {
              headers: {
                'x-idempotency-key': '6ef78d67-2b68-413a-bd0b-d1ab0cb87f37',
              },
            }
          );

          expect(response1.status).toEqual(202);
          expect(response1.data).toStrictEqual({
            id: expect.any(String),
            bankAccountId: bankAccount.id,
            amount: MOCK_CREATE_PAYLOAD.amount,
            currency: MOCK_CREATE_PAYLOAD.currency,
            status: 'PENDING',
            createdAt: expect.any(String),
            type: MOCK_CREATE_PAYLOAD.type,
            metadata: MOCK_CREATE_PAYLOAD.metadata,
          });

          expect(response2.status).toEqual(202);
          expect(response2.data).toStrictEqual({
            id: expect.any(String),
            bankAccountId: bankAccount.id,
            amount: MOCK_CREATE_PAYLOAD.amount,
            currency: MOCK_CREATE_PAYLOAD.currency,
            status: 'PENDING',
            createdAt: expect.any(String),
            type: MOCK_CREATE_PAYLOAD.type,
            metadata: MOCK_CREATE_PAYLOAD.metadata,
          });

          const transactions = await db.query(
            `SELECT *
           FROM payments.transactions
           WHERE idempotency_key = '6ef78d67-2b68-413a-bd0b-d1ab0cb87f37'
           `
          );

          expect(transactions.rows.length).toEqual(1);
        });
      });
    });
  });
});
