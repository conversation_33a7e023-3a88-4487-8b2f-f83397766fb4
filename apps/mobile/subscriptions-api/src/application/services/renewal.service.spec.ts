import { RenewalService } from './renewal.service';
import { createMock, DeepMocked } from '@golevelup/ts-jest';
import { SubscriptionRepositoryInterface } from '../../domain/repositories/subscription.repository.interface';
import { PlanRepository } from '../../infrastructure/repositories/type-orm/plan.repository';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import { ConfigService } from '@nestjs/config';
import {
  MOCK_HA_SUBSCRIPTION_ENTITY,
  MOCK_POD_DRIVE_REWARDS_PLAN_ENTITY,
  MOCK_SA_SUBSCRIPTION_ENTITY,
  MOCK_SCR_SUBSCRIPTION_ENTITY,
} from '../../domain/entities/__fixtures__/entity.fixtures';
import { PlanAllowancePeriod } from '../../domain/entities/plan.entity';

describe(RenewalService.name, () => {
  let service: RenewalService;
  let mockSubscriptionRepository: DeepMocked<SubscriptionRepositoryInterface>;
  let mockPlanRepository: DeepMocked<PlanRepository>;
  let mockConfigService: DeepMocked<ConfigService>;
  let mockSqsService: DeepMocked<SqsClientService>;

  beforeEach(() => {
    mockSubscriptionRepository = createMock<SubscriptionRepositoryInterface>();
    mockPlanRepository = createMock<PlanRepository>();
    mockConfigService = createMock<ConfigService>();
    mockSqsService = createMock<SqsClientService>();

    service = new RenewalService(
      mockSubscriptionRepository,
      mockPlanRepository,
      mockConfigService,
      mockSqsService
    );
  });

  afterEach(() => jest.restoreAllMocks());

  describe(RenewalService.prototype.calculateMilesRenewalDate, () => {
    it('returns null if no activatedAt', () => {
      const res = service.calculateMilesRenewalDate({
        ...MOCK_SA_SUBSCRIPTION_ENTITY,
        activatedAt: null,
      });

      expect(res).toBeNull();
    });

    it.each<{
      condition: string;
      allowancePeriod: PlanAllowancePeriod;
      today: string;
      activatedAt: string;
      previousRenewal: string;
      nextRenewal: string;
    }>([
      {
        condition: 'next day+month as the activation date, next year',
        allowancePeriod: PlanAllowancePeriod.ANNUAL,
        today: '2024-02-08T03:00:00Z',
        activatedAt: '2023-02-08',
        previousRenewal: '2024-02-08',
        // included as renewalDate (2024-02-08T00:00:00Z) < cronDateTime
        nextRenewal: '2025-02-08',
      },
      {
        condition:
          'prefer the earliest real day if the renewal day does not exist in the next year (leap year)',
        allowancePeriod: PlanAllowancePeriod.ANNUAL,
        today: '2024-02-29T03:00:00Z',
        activatedAt: '2020-02-29',
        previousRenewal: '2024-02-29',
        // 2025-02-29 does not exist, so it should pick 2025-02-28
        nextRenewal: '2025-02-28',
      },
      {
        condition:
          'always base the next renewal date on the activation date, not the previous renewal date to prevent drift',
        allowancePeriod: PlanAllowancePeriod.ANNUAL,
        today: '2023-02-28T03:00:00Z',
        activatedAt: '2020-02-29',
        previousRenewal: '2023-02-28',
        // 2023-02-29 didn't exist, but 2024-02-29 does, so it should pick that
        nextRenewal: '2024-02-29',
      },
      {
        condition: 'cron date time being a little late graceful',
        // something went wrong, we've fixed it, and now we're running the cron a bit late
        // assume this will never be more than a few days late (i.e. never more than an interval)
        allowancePeriod: PlanAllowancePeriod.ANNUAL,
        today: '2024-02-10T10:00:00Z',
        activatedAt: '2023-02-08',
        previousRenewal: '2024-02-08',
        // included as renewalDate (2024-02-08T00:00:00Z) < cronDateTime
        nextRenewal: '2025-02-08',
      },
      // MONTHLY
      {
        condition: 'same day as the activation date, for the next month',
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2024-09-08T03:00:00Z',
        activatedAt: '2023-02-08',
        previousRenewal: '2024-09-08',
        nextRenewal: '2024-10-08',
      },
      {
        condition:
          'prefer the earliest real day if the renewal day does not exist in the next month',
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2024-05-31T03:00:00Z',
        activatedAt: '2023-03-31',
        previousRenewal: '2024-05-31',
        // 2024-06-31 does not exist, so it should pick 2024-06-30
        nextRenewal: '2024-06-30',
      },
      {
        condition: 'tolerate the earliest real day being a few days earlier',
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2025-01-31T03:00:00Z',
        activatedAt: '2023-01-31',
        previousRenewal: '2025-01-31',
        // february is significantly shorter, so it should pick 2025-02-28
        nextRenewal: '2025-02-28',
      },
      {
        condition: 'respect leap years when finding the earliest real day',
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2024-01-31T03:00:00Z',
        activatedAt: '2020-02-29',
        previousRenewal: '2024-01-31',
        // 2024 is a leap year, so it should pick 2024-02-29
        nextRenewal: '2024-02-29',
      },
      {
        condition:
          'always base the next renewal date on the activation date, not the previous renewal date to prevent drift',
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2024-02-29T03:00:00Z',
        activatedAt: '2020-01-31',
        previousRenewal: '2024-02-29',
        // 2024-03-31 does exist, so it should pick that, even thought it's more than a month later than the last renewal date
        nextRenewal: '2024-03-31',
      },
      {
        condition: 'handle the cron date time being a little late gracefully',
        // something went wrong, we've fixed it and now we're running the cron a bit late
        // assume this will never be more than a few days late (i.e. never more than an interval)
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2024-09-10T10:00:00Z',
        activatedAt: '2023-02-08',
        previousRenewal: '2024-09-08',
        nextRenewal: '2024-10-08',
      },
      {
        condition:
          'handle the cron date time being into the next month gracefully',
        // something went wrong, we've fixed it and now we're running the cron a bit late
        // assume this will never be more than a few days late (i.e. never more than an interval)
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2024-07-01T10:00:00Z',
        activatedAt: '2023-03-31',
        previousRenewal: '2024-06-30',
        nextRenewal: '2024-07-31',
      },
      {
        condition:
          'roll over to the next year if the next month is in the next year',
        allowancePeriod: PlanAllowancePeriod.MONTHLY,
        today: '2024-12-31T03:00:00Z',
        activatedAt: '2023-01-31',
        previousRenewal: '2024-12-31',
        nextRenewal: '2025-01-31',
      },
    ])(
      'calculates correct miles renewal date for $allowancePeriod - $condition',
      async ({
        today,
        allowancePeriod,
        activatedAt,
        previousRenewal,
        nextRenewal,
      }) => {
        const dateSpy = jest
          .spyOn(Date, 'now')
          .mockReturnValue(new Date(today).getTime());

        const res = service.calculateMilesRenewalDate({
          ...MOCK_SA_SUBSCRIPTION_ENTITY,
          activatedAt: new Date(activatedAt),
          plan: {
            ...MOCK_SA_SUBSCRIPTION_ENTITY.plan,
            details: {
              ...MOCK_SA_SUBSCRIPTION_ENTITY.plan.details,
              milesRenewalDate: new Date(previousRenewal),
              allowancePeriod,
            },
          },
        });

        expect(res).toEqual(new Date(nextRenewal));

        dateSpy.mockRestore();
      }
    );
  });

  describe(RenewalService.prototype.updateMilesRenewalDates, () => {
    it('does nothing if the queue env var is not set', async () => {
      mockConfigService.getOrThrow.mockImplementation(() => {
        throw new Error();
      });

      await expect(service.updateMilesRenewalDates()).rejects.toThrow(Error);

      expect(mockConfigService.getOrThrow).toHaveBeenCalledTimes(1);
      expect(mockConfigService.getOrThrow).toHaveBeenCalledWith(
        'REWARDS_API_INCOMING_EVENTS_QUEUE_URL'
      );

      expect(
        mockSubscriptionRepository.getSubscriptionsForRenewal
      ).not.toHaveBeenCalled();
      expect(mockSqsService.sendMessage).not.toHaveBeenCalled();
      expect(mockPlanRepository.updatePlanDetails).not.toHaveBeenCalled();
    });

    it('sends a SQS message for each subscription in the correct format', async () => {
      mockConfigService.getOrThrow.mockReturnValue('https://queue.aws/');
      mockSqsService.sendMessage.mockResolvedValue();
      mockSubscriptionRepository.getSubscriptionsForRenewal.mockResolvedValue([
        MOCK_SCR_SUBSCRIPTION_ENTITY,
        MOCK_HA_SUBSCRIPTION_ENTITY,
        MOCK_SA_SUBSCRIPTION_ENTITY,
      ]);

      await service.updateMilesRenewalDates();

      expect(mockSqsService.sendMessage).toHaveBeenCalledTimes(3);

      [
        { entity: MOCK_SCR_SUBSCRIPTION_ENTITY, behaviour: 'SET' },
        { entity: MOCK_HA_SUBSCRIPTION_ENTITY, behaviour: 'ADD' },
        { entity: MOCK_SA_SUBSCRIPTION_ENTITY, behaviour: 'ADD' },
      ].forEach(({ entity, behaviour }) => {
        expect(mockSqsService.sendMessage).toHaveBeenCalledWith(
          mockConfigService.getOrThrow.mock.results[0].value,
          {
            messageBody: JSON.stringify({
              type: 'renew-miles-allowance',
              userId: entity.userId,
              idempotencyKey: `${entity.id}-${entity.plan.details.milesRenewalDate?.toISOString()}`,
              miles: entity.plan.details.allowanceMiles,
              renewalBehaviour: behaviour,
            }),
          }
        );
      });
    });

    it('does not update the plan for non-successful SQS messages', async () => {
      mockConfigService.getOrThrow.mockReturnValue('https://queue.aws/');
      mockSubscriptionRepository.getSubscriptionsForRenewal.mockResolvedValue([
        MOCK_SCR_SUBSCRIPTION_ENTITY,
        MOCK_HA_SUBSCRIPTION_ENTITY,
      ]);

      mockSqsService.sendMessage
        .mockResolvedValueOnce()
        .mockRejectedValue(new Error());

      await service.updateMilesRenewalDates();

      expect(mockPlanRepository.updatePlanDetails).toHaveBeenCalledTimes(1);
      expect(mockPlanRepository.updatePlanDetails).toHaveBeenCalledWith(
        MOCK_POD_DRIVE_REWARDS_PLAN_ENTITY.id,
        {
          ...MOCK_POD_DRIVE_REWARDS_PLAN_ENTITY.details,
          milesRenewalDate: null,
        }
      );
    });
  });
});
