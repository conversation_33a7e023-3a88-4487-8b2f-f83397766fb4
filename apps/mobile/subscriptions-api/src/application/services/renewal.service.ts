import { SubscriptionRepositoryInterface } from '../../domain/repositories/subscription.repository.interface';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { DependencyInjectionToken } from '../../modules/constants';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import {
  PlanAllowancePeriod,
  PlanDetails,
} from '../../domain/entities/plan.entity';
import dayjs from 'dayjs';
import { SubscriptionEntity } from '../../domain/entities/subscription.entity';
import { BaseOrderEntity } from '../../domain/entities/order.entity';
import { FatalApplicationError } from '../errors/fatal-application.error';
import {
  countPromiseResults,
  filterFulfilledPromise,
} from '@experience/shared/typescript/utils';
import { ConfigService } from '@nestjs/config';
import { PlanRepository } from '../../infrastructure/repositories/type-orm/plan.repository';

@Injectable()
export class RenewalService {
  private readonly logger = new Logger(RenewalService.name);

  constructor(
    @Inject(DependencyInjectionToken.SUBSCRIPTIONS_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepositoryInterface,
    private readonly planRepository: PlanRepository,
    private readonly configService: ConfigService,
    private readonly sqsService: SqsClientService
  ) {}

  private getRenewalBehaviour(
    subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>
  ): 'SET' | 'ADD' {
    if (
      subscription.plan.details.allowancePeriod === PlanAllowancePeriod.ANNUAL
    ) {
      return 'SET';
    }

    if (
      subscription.plan.details.allowancePeriod === PlanAllowancePeriod.MONTHLY
    ) {
      const isOneYearAgo = dayjs(subscription.activatedAt)
        .add(1, 'year')
        .startOf('day')
        .isSame(
          dayjs(subscription.plan.details.milesRenewalDate).startOf('day'),
          'day'
        );

      return isOneYearAgo ? 'SET' : 'ADD';
    }

    throw new FatalApplicationError(
      'Plan has invalid allowance period - unable to get renewal behaviour.'
    );
  }

  calculateMilesRenewalDate(
    subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>
  ): Date | null {
    const today = new Date(Date.now());
    const { plan } = subscription;

    if (!plan.details.allowancePeriod || !subscription.activatedAt) {
      return null;
    }

    if (plan.details.allowancePeriod === PlanAllowancePeriod.ANNUAL) {
      let milesRenewalDate = dayjs(subscription.activatedAt).set(
        'year',
        today.getFullYear()
      );

      if (milesRenewalDate.startOf('day').isBefore(Date.now())) {
        milesRenewalDate = milesRenewalDate.add(1, 'year');
      }

      if (milesRenewalDate.date() !== subscription.activatedAt.getDate()) {
        const potentiallyCorrectedDate = milesRenewalDate.set(
          'date',
          subscription.activatedAt.getDate()
        );

        // DayJS automatically rolls dates over to the next month, we don't want this behaviour.
        if (
          potentiallyCorrectedDate.month() ===
          subscription.activatedAt.getMonth()
        ) {
          milesRenewalDate = potentiallyCorrectedDate;
        }
      }

      return milesRenewalDate.toDate();
    }

    if (plan.details.allowancePeriod === PlanAllowancePeriod.MONTHLY) {
      let milesRenewalDate = dayjs(subscription.activatedAt)
        .set('year', today.getFullYear())
        .set('month', today.getMonth());

      if (milesRenewalDate.isBefore(Date.now())) {
        milesRenewalDate = milesRenewalDate.add(1, 'month');
      }

      if (milesRenewalDate.date() !== subscription.activatedAt.getDate()) {
        const potentiallyCorrectedDate = milesRenewalDate.set(
          'date',
          subscription.activatedAt.getDate()
        );

        if (
          potentiallyCorrectedDate.date() === subscription.activatedAt.getDate()
        ) {
          milesRenewalDate = potentiallyCorrectedDate;
        }
      }

      return milesRenewalDate.toDate();
    }

    return null;
  }

  private async setNextRenewalDate(
    subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>
  ) {
    try {
      // TODO: In the future we will need to implement what we want do do when a plan reaches the end of contract duration, but that is a business decisision yet to be made.
      const nextRenewalDate = this.calculateMilesRenewalDate(subscription);

      this.logger.log(
        { subscriptionId: subscription.id, nextRenewalDate },
        'updating miles renewal date for subscription'
      );

      subscription.plan.details.milesRenewalDate = nextRenewalDate;

      await this.planRepository.updatePlanDetails(
        subscription.plan.id,
        subscription.plan.details
      );

      this.logger.log(
        { subscriptionId: subscription.id, nextRenewalDate },
        'updated miles renewal date'
      );
    } catch (error) {
      this.logger.error(
        { error, subscriptionId: subscription.id },
        'failed to set next renewal date for subscription'
      );

      throw new FatalApplicationError(
        'failed to set next renewal date for subscription'
      );
    }
  }

  async updateMilesRenewalDates() {
    const rewardsIncomingEventsQueue = this.configService.getOrThrow(
      'REWARDS_API_INCOMING_EVENTS_QUEUE_URL'
    );

    const subscriptions =
      await this.subscriptionRepository.getSubscriptionsForRenewal();

    if (subscriptions.length === 0) {
      this.logger.log('no subscriptions due for renewal. nothing to process');

      return;
    }

    this.logger.log(
      { subscriptionsForRenewal: subscriptions.length },
      'got subscriptions due for renewal'
    );

    const renewResults = await Promise.allSettled(
      subscriptions.map(async (subscription) => {
        await this.sqsService.sendMessage(rewardsIncomingEventsQueue, {
          messageBody: JSON.stringify({
            type: 'renew-miles-allowance',
            userId: subscription.userId,
            idempotencyKey: `${subscription.id}-${subscription.plan.details.milesRenewalDate?.toISOString()}`,
            miles: subscription.plan.details.allowanceMiles,
            renewalBehaviour: this.getRenewalBehaviour(subscription),
          }),
        });

        this.logger.log(
          { subscriptionId: subscription.id },
          'sent subscription for renewal'
        );

        return subscription;
      })
    );

    this.logger.log(
      { renewResults: countPromiseResults(renewResults) },
      'finished sending subscriptions for renewal. setting next renewal date'
    );

    const dateResults = await Promise.allSettled(
      renewResults
        .filter(filterFulfilledPromise)
        .map(({ value }) => this.setNextRenewalDate(value))
    );

    this.logger.log(
      { dateResults: countPromiseResults(dateResults) },
      'finished processing renewals'
    );
  }
}
