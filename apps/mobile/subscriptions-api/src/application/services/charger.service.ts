import {
  Address<PERSON>pi,
  ChargingStation<PERSON>pi,
  LocationApi,
} from '@experience/shared/axios/assets-api-client';
import { ChargerAddressAndMPAN } from '../../domain/types/action.types';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ChargerService {
  private readonly logger = new Logger(ChargerService.name);

  constructor(
    private readonly chargingStationApi: ChargingStationApi,
    private readonly locationApi: LocationApi,
    private readonly addressApi: AddressApi
  ) {}

  async getChargerAddressAndMPAN(ppid: string): Promise<ChargerAddressAndMPAN> {
    this.logger.log({ ppid }, 'getting charger address and mpan');

    const response: ChargerAddressAndMPAN = {
      mpan: null,
      address: {
        postcode: null,
      },
    };

    try {
      const { data: chargingStation } =
        await this.chargingStationApi.getChargingStation(ppid);

      if (!chargingStation.location?.id) {
        return response;
      }

      const { data: location } = await this.locationApi.getLocation(
        chargingStation.location.id
      );

      if (location.mpan) {
        response.mpan = location.mpan;
      }

      if (location.addressId) {
        const { data: address } = await this.addressApi.getAddress(
          location.addressId
        );

        if (address.postcode) {
          response.address.postcode = address.postcode;
        }
      }

      return response;
    } catch (error) {
      this.logger.error({ error, ppid }, 'failed to get charger address');

      throw error;
    }
  }
}
