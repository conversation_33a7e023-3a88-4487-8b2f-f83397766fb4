import { ActionEntityToDTOTransformer } from '../transformers/action-entity-to-dto.transformer';
import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import {
  CheckAffordabilityActionDTO,
  CheckAffordabilityActionV2DTO,
} from './actions/check-affordability-action.dto';
import { ConfirmChargerAddressAndMPANActionDTO } from './actions/confirm-charger-address-and-mpan-action.dto';
import { HomeSurveyActionDTO } from './actions/home-survey-action.dto';
import { InstallChargingStationActionDTO } from './actions/install-charging-station-action.dto';
import { LinkExistingChargerActionDTO } from './actions/link-existing-charger-action.dto';
import { PayUpfrontFeeActionDTO } from './actions/pay-upfront-fee-action.dto';
import { PersistedSubscriptionWithActionsDTO } from '../../../application/dto/persisted-subscription-with-actions.dto';
import { PlanEntityToDTOTransformer } from '../transformers/plan-entity-to-dto.transformer';
import {
  PodDrivePlanDTO,
  PodDriveRewardsPlanDTO,
  PodDriveSAPlanDTO,
} from './plan.dto';
import { SalesforceOrderDTO, SelfServiceOrderDTO } from './order.dto';
import { SetupDirectDebitActionDTO } from './actions/setup-direct-debit-action.dto';
import { SignDocumentsActionDTO } from './actions/sign-documents-action.dto';
import { SignRewardsTOSActionDTO } from './actions/sign-rewards-tos-action.dto';
import { SubscriptionStatus } from '../../../domain/entities/subscription.entity';
import { SubscriptionWithActionsDTOToPersistedSubscriptionDTOTransformer } from '../transformers/subscription-entity-to-dto.transformer';

@ApiExtraModels(
  SetupDirectDebitActionDTO,
  CheckAffordabilityActionDTO,
  CheckAffordabilityActionV2DTO,
  InstallChargingStationActionDTO,
  HomeSurveyActionDTO,
  PayUpfrontFeeActionDTO,
  SignDocumentsActionDTO,
  ConfirmChargerAddressAndMPANActionDTO,
  SignRewardsTOSActionDTO,
  LinkExistingChargerActionDTO,
  SalesforceOrderDTO,
  SelfServiceOrderDTO,
  PodDrivePlanDTO,
  PodDriveRewardsPlanDTO,
  PodDriveSAPlanDTO
)
export class PersistedSubscriptionDTO {
  static from(
    entity: PersistedSubscriptionWithActionsDTO
  ): PersistedSubscriptionDTO {
    const transformer =
      new SubscriptionWithActionsDTOToPersistedSubscriptionDTOTransformer(
        new ActionEntityToDTOTransformer(),
        new PlanEntityToDTOTransformer()
      );

    const transformed = transformer.transform(entity);

    return transformed;
  }

  @ApiProperty({
    name: 'id',
    description: 'subscription id',
    type: 'string',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    name: 'userId',
    description: 'firebase user id',
    type: 'string',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    name: 'status',
    description: 'subscription status',
    enum: Object.values(SubscriptionStatus),
  })
  status: SubscriptionStatus;

  @ApiProperty({
    name: 'order',
    description: 'order associated with the subscription',
    oneOf: [
      { $ref: getSchemaPath(SalesforceOrderDTO) },
      { $ref: getSchemaPath(SelfServiceOrderDTO) },
    ],
  })
  order: SalesforceOrderDTO | SelfServiceOrderDTO;

  @ApiProperty({
    name: 'actions',
    description: 'actions associated with the subscription',
    isArray: true,
    oneOf: [
      { $ref: getSchemaPath(HomeSurveyActionDTO) },
      { $ref: getSchemaPath(CheckAffordabilityActionDTO) },
      { $ref: getSchemaPath(CheckAffordabilityActionV2DTO) },
      { $ref: getSchemaPath(InstallChargingStationActionDTO) },
      { $ref: getSchemaPath(SetupDirectDebitActionDTO) },
      { $ref: getSchemaPath(PayUpfrontFeeActionDTO) },
      { $ref: getSchemaPath(SignDocumentsActionDTO) },
      { $ref: getSchemaPath(ConfirmChargerAddressAndMPANActionDTO) },
      { $ref: getSchemaPath(SignRewardsTOSActionDTO) },
      { $ref: getSchemaPath(LinkExistingChargerActionDTO) },
    ],
  })
  actions: (
    | HomeSurveyActionDTO
    | CheckAffordabilityActionDTO
    | CheckAffordabilityActionV2DTO
    | InstallChargingStationActionDTO
    | SetupDirectDebitActionDTO
    | PayUpfrontFeeActionDTO
    | SignDocumentsActionDTO
    | ConfirmChargerAddressAndMPANActionDTO
    | SignRewardsTOSActionDTO
    | LinkExistingChargerActionDTO
  )[];

  @ApiProperty({
    name: 'activatedAt',
    description: 'subscription activation date',
    type: 'string',
    format: 'date-time',
    example: '2025-01-01T00:00:00.000Z',
    nullable: true,
  })
  activatedAt: Date | null;

  @ApiProperty({
    name: 'createdAt',
    description: 'subscription creation date',
    type: 'string',
    format: 'date-time',
    example: '2025-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    name: 'updatedAt',
    description: 'subscription last updated date',
    type: 'string',
    format: 'date-time',
    example: '2025-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    name: 'deletedAt',
    description: 'subscription deleted date',
    type: 'string',
    format: 'date-time',
    example: '2025-01-01T00:00:00.000Z',
    nullable: true,
  })
  deletedAt: Date | null;

  @ApiProperty({
    name: 'plan',
    description:
      'The plan information for the given subscription - currently only PodDrive',
    oneOf: [
      { $ref: getSchemaPath(PodDrivePlanDTO) },
      { $ref: getSchemaPath(PodDriveRewardsPlanDTO) },
      { $ref: getSchemaPath(PodDriveSAPlanDTO) },
    ],
  })
  plan: PodDrivePlanDTO | PodDriveRewardsPlanDTO | PodDriveSAPlanDTO;
}
