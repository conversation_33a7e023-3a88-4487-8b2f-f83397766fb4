import { CreatePodDrivePlanDTO } from '../dto/plan.dto';
import { OrderEventDTO } from '../dto/order-event.dto';
import {
  PodDrivePlanDetails,
  UnpersistedPlanEntity,
} from '../../../domain/entities/plan.entity';
import { SalesforceOrderEntity } from '../../../domain/entities/order.entity';
import {
  SubscriptionStatus,
  UnpersistedSubscriptionEntity,
} from '../../../domain/entities/subscription.entity';
import { Transformer } from '@experience/mobile/clean-architecture';

export class OrderEventDTOToEntityTransformer extends Transformer<
  {
    orderEventDto: OrderEventDTO;
    userId: UnpersistedSubscriptionEntity<
      SalesforceOrderEntity,
      PodDrivePlanDetails
    >['userId'];
  },
  UnpersistedSubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>
> {
  constructor(
    // TODO: make this generic to other plans
    private readonly planTransformer: Transformer<
      CreatePodDrivePlanDTO,
      UnpersistedPlanEntity<PodDrivePlanDetails>
    >
  ) {
    super();
  }

  transform(input: {
    orderEventDto: OrderEventDTO;
    userId: UnpersistedSubscriptionEntity<
      SalesforceOrderEntity,
      PodDrivePlanDetails
    >['userId'];
  }): UnpersistedSubscriptionEntity<
    SalesforceOrderEntity,
    PodDrivePlanDetails
  > {
    return new UnpersistedSubscriptionEntity({
      userId: input.userId,
      // TODO: this might need a dedicated transformer
      order: input.orderEventDto.order,
      plan: this.planTransformer.transform(input.orderEventDto.plan),
      status: SubscriptionStatus.PENDING,
      activatedAt: null,
    });
  }
}
