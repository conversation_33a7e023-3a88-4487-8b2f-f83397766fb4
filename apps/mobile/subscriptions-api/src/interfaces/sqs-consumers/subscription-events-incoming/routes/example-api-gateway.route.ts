import {
  BaseEventRoute,
  isApiGateway,
} from '@experience/mobile/nest/sqs-event-router';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ExampleApiGatewayRoute implements BaseEventRoute {
  private readonly logger = new Logger(ExampleApiGatewayRoute.name);

  async handleEvent(message: unknown): Promise<boolean> {
    if (!isApiGateway(message)) {
      return false;
    }

    this.logger.log(
      {
        data: message.body,
      },
      'got from api gateway'
    );

    return true;
  }
}
