import { RenewMilesAllowanceRoute } from './renew-miles-allowance.route';
import { createMock, DeepMocked } from '@golevelup/ts-jest';
import { RenewalService } from '../../../../application/services/renewal.service';

describe(RenewMilesAllowanceRoute.name, () => {
  let route: RenewMilesAllowanceRoute;
  let mockRenewalService: DeepMocked<RenewalService>;

  beforeEach(() => {
    mockRenewalService = createMock<RenewalService>();

    route = new RenewMilesAllowanceRoute(mockRenewalService);
  });

  afterEach(() => jest.restoreAllMocks());

  describe(RenewMilesAllowanceRoute.prototype.handleEvent, () => {
    it('returns false if the given message is not in the correct format', async () => {
      const res = await route.handleEvent({ invalid: true });

      expect(res).toBeFalsy();
    });

    it('processes valid events and calls the renewal service', async () => {
      const res = await route.handleEvent({ type: 'renew-miles-allowance' });

      expect(res).toBeTruthy();

      expect(mockRenewalService.updateMilesRenewalDates).toHaveBeenCalledTimes(
        1
      );
    });
  });
});
