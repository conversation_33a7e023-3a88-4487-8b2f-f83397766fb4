import {
  <PERSON><PERSON><PERSON>ty,
  ActionOwner,
  ActionStatus,
  ActionType,
} from '../../../domain/entities/action.entity';
import {
  CouldNotPersistEntityError,
  FatalRepositoryError,
} from '@experience/mobile/clean-architecture';
import { Entity<PERSON>anager, In, JsonContains } from 'typeorm';
import {
  MOCK_CREATE_CHECK_AFFORDABILITY_ACTION,
  MOCK_INSTALL_CHARGING_STATION_ACTION,
  MOCK_INSTALL_CHARGING_STATION_ACTION_WITH_DATA,
  MOCK_SURVEY_ACTION,
} from '../__fixtures__/action.fixtures';
import { ClsService } from 'nestjs-cls';
import { MOCK_INSTALL_CHARGING_STATION_ACTION_WITH_DATA_ENTITY } from '../../../domain/entities/__fixtures__/entity.fixtures';
import { SubscriptionAction } from '@experience/mobile/driver-account/database';
import { TypeOrmActionRepository } from './action.repository';
import { UnpersistedActionEntityFactory } from '../../../domain/entity-factories/unpersisted-action-entity.factory';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { mockEntityManagerCreateOnce } from '@experience/mobile/nest/typeorm-transactions';
import { v4 } from 'uuid';

describe(TypeOrmActionRepository.name, () => {
  let entityManager: jest.Mocked<EntityManager>;
  let clsService: jest.Mocked<ClsService>;
  let repository: TypeOrmActionRepository;

  beforeEach(() => {
    entityManager = createMock();
    clsService = createMock();

    repository = new TypeOrmActionRepository(entityManager, clsService);

    jest.resetAllMocks();

    clsService.get.mockReturnValue(entityManager);
  });

  describe(TypeOrmActionRepository.prototype.create, () => {
    it('calls create', async () => {
      const unpersistedActionEntity =
        UnpersistedActionEntityFactory.createCompleteSurveyAction({
          subscriptionId: v4(),
          surveyUrl: 'http://0.0.0.0/',
        });

      entityManager.save.mockResolvedValueOnce(MOCK_SURVEY_ACTION);

      await repository.create(unpersistedActionEntity);

      expect(entityManager.create).toHaveBeenCalledTimes(1);
    });

    it('calls save', async () => {
      const unpersistedActionEntity =
        UnpersistedActionEntityFactory.createCompleteSurveyAction({
          subscriptionId: v4(),
          surveyUrl: 'http://0.0.0.0/',
        });

      mockEntityManagerCreateOnce(entityManager, MOCK_SURVEY_ACTION);
      entityManager.save.mockResolvedValueOnce(MOCK_SURVEY_ACTION);

      await repository.create(unpersistedActionEntity);

      expect(entityManager.save).toHaveBeenCalledTimes(1);
      expect(entityManager.save).toHaveBeenCalledWith(
        SubscriptionAction,
        MOCK_SURVEY_ACTION
      );
    });

    it('returns the entity', async () => {
      const unpersistedActionEntity =
        UnpersistedActionEntityFactory.createCompleteSurveyAction({
          subscriptionId: v4(),
          surveyUrl: 'http://0.0.0.0/',
        });

      entityManager.save.mockResolvedValueOnce(MOCK_SURVEY_ACTION);

      const expected = new ActionEntity({
        id: MOCK_SURVEY_ACTION.id,
        subscriptionId: MOCK_SURVEY_ACTION.subscription.id,
        owner: MOCK_SURVEY_ACTION.owner as ActionOwner,
        status: MOCK_SURVEY_ACTION.status as ActionStatus,
        data: {
          type: MOCK_SURVEY_ACTION.type,
          surveyUrl: MOCK_SURVEY_ACTION.data?.surveyUrl,
        } as ActionEntity['data'],
        dependsOn: MOCK_SURVEY_ACTION.dependsOn.map((id) => ({ id })),
        createdAt: MOCK_SURVEY_ACTION.createdAt,
        updatedAt: MOCK_SURVEY_ACTION.updatedAt,
        deletedAt: MOCK_SURVEY_ACTION.deletedAt,
      });

      const actual = await repository.create(unpersistedActionEntity);

      expect(actual).toEqual(expected);
    });

    it('throws CouldNotPersistEntityError if data layer fails', async () => {
      const unpersistedActionEntity =
        UnpersistedActionEntityFactory.createCompleteSurveyAction({
          subscriptionId: v4(),
          surveyUrl: 'http://0.0.0.0/',
        });

      entityManager.save.mockRejectedValueOnce(new Error('boom'));

      await expect(repository.create(unpersistedActionEntity)).rejects.toThrow(
        CouldNotPersistEntityError
      );
    });
  });

  describe(TypeOrmActionRepository.prototype.findBySubscriptionId, () => {
    it('calls entityManager.find', async () => {
      const id = v4();

      entityManager.find.mockResolvedValueOnce([
        MOCK_INSTALL_CHARGING_STATION_ACTION,
      ]);

      await repository.findBySubscriptionId(id);

      expect(entityManager.find).toHaveBeenCalledWith(SubscriptionAction, {
        where: { subscription: { id } },
        relations: ['subscription'],
      });
    });

    it('returns the entities', async () => {
      const id = v4();

      entityManager.find.mockResolvedValueOnce([
        MOCK_INSTALL_CHARGING_STATION_ACTION,
      ]);

      const expected = [
        new ActionEntity({
          id: MOCK_INSTALL_CHARGING_STATION_ACTION.id,
          subscriptionId: MOCK_INSTALL_CHARGING_STATION_ACTION.subscription.id,
          owner: MOCK_INSTALL_CHARGING_STATION_ACTION.owner as ActionOwner,
          status: MOCK_INSTALL_CHARGING_STATION_ACTION.status as ActionStatus,
          data: {
            type: MOCK_INSTALL_CHARGING_STATION_ACTION.type,
            ppid: MOCK_INSTALL_CHARGING_STATION_ACTION.data.ppid,
          } as ActionEntity['data'],
          dependsOn: MOCK_INSTALL_CHARGING_STATION_ACTION.dependsOn.map(
            (id) => ({ id })
          ),
          createdAt: MOCK_INSTALL_CHARGING_STATION_ACTION.createdAt,
          updatedAt: MOCK_INSTALL_CHARGING_STATION_ACTION.updatedAt,
          deletedAt: MOCK_INSTALL_CHARGING_STATION_ACTION.deletedAt,
        }),
      ];

      const actual = await repository.findBySubscriptionId(id);

      expect(actual).toEqual(expected);
    });

    it('throws FatalRepositoryError when unknown error occurs', async () => {
      const id = v4();

      entityManager.find.mockRejectedValueOnce(new Error('Knowledge...'));

      await expect(repository.findBySubscriptionId(id)).rejects.toThrow(
        FatalRepositoryError
      );
    });
  });

  describe(TypeOrmActionRepository.prototype.findByApplicationId, () => {
    it('should call entityManager.find', async () => {
      const id = 1982;

      entityManager.find.mockResolvedValueOnce([
        {
          ...MOCK_CREATE_CHECK_AFFORDABILITY_ACTION,
          data: {
            applicationId: id,
          },
        },
      ]);

      const actual = await repository.findByApplicationId(
        [ActionType.CHECK_AFFORDABILITY_V1],
        id
      );

      expect(entityManager.find).toHaveBeenCalledWith(SubscriptionAction, {
        relations: ['subscription'],
        where: {
          type: In([ActionType.CHECK_AFFORDABILITY_V1]),
          data: JsonContains({
            applicationId: id,
          }),
        },
      });

      expect(actual).toEqual([
        {
          createdAt: expect.any(Date),
          data: {
            applicationId: id,
            loanId: null,
            type: 'CHECK_AFFORDABILITY_V1',
          },
          deletedAt: null,
          dependsOn: [],
          id: expect.any(String),
          owner: 'SYSTEM',
          status: 'PENDING',
          subscriptionId: expect.any(String),
          updatedAt: expect.any(Date),
        },
      ]);
    });

    it('should throw FatalRepositoryError when errors occur', async () => {
      entityManager.find.mockRejectedValueOnce(new Error('Knowledge...'));

      await expect(repository.findBySubscriptionId(v4())).rejects.toThrow(
        FatalRepositoryError
      );
    });
  });

  describe(TypeOrmActionRepository.prototype.update, () => {
    it('updates the data of an action', async () => {
      const action = await repository.update(
        MOCK_INSTALL_CHARGING_STATION_ACTION_WITH_DATA_ENTITY,
        {
          data: {
            type: ActionType.INSTALL_CHARGING_STATION_V1,
            ppid: 'PSL-654321',
          },
        }
      );

      expect(action).toEqual({
        ...MOCK_INSTALL_CHARGING_STATION_ACTION_WITH_DATA_ENTITY,
        data: {
          type: ActionType.INSTALL_CHARGING_STATION_V1,
          ppid: 'PSL-654321',
        },
      });

      expect(entityManager.update).toHaveBeenCalledTimes(1);
      expect(entityManager.update).toHaveBeenCalledWith(
        SubscriptionAction,
        MOCK_INSTALL_CHARGING_STATION_ACTION_WITH_DATA.id,
        {
          data: {
            ppid: 'PSL-654321',
          },
        }
      );
    });

    it('throws a FatalRepositoryException if update fails', async () => {
      entityManager.update.mockRejectedValueOnce(new Error('oops'));

      await expect(
        repository.update(
          MOCK_INSTALL_CHARGING_STATION_ACTION_WITH_DATA_ENTITY,
          {
            data: {
              type: ActionType.INSTALL_CHARGING_STATION_V1,
              ppid: 'PSL-654321',
            },
          }
        )
      ).rejects.toThrow(FatalRepositoryError);
    });
  });

  describe(TypeOrmActionRepository.prototype.read, () => {
    it('calls mockedReadActionRepository.findOne', async () => {
      entityManager.findOne.mockResolvedValueOnce(MOCK_SURVEY_ACTION);

      await repository.read(MOCK_SURVEY_ACTION.id);

      expect(entityManager.findOne).toHaveBeenCalledWith(SubscriptionAction, {
        where: { id: MOCK_SURVEY_ACTION.id },
        relations: ['subscription'],
      });
    });

    it('returns null when action cannot be found', async () => {
      entityManager.findOne.mockResolvedValueOnce(null);

      await expect(repository.read(MOCK_SURVEY_ACTION.id)).resolves.toEqual(
        null
      );
    });

    it('throws FatalRepositoryError when unknown error occurs', async () => {
      entityManager.findOne.mockRejectedValueOnce(new Error('Error'));

      await expect(repository.read(MOCK_SURVEY_ACTION.id)).rejects.toThrow(
        FatalRepositoryError
      );
    });
  });
});
