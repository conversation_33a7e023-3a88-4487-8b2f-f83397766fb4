import { PlanRepository } from './plan.repository';
import { createMock, DeepMocked } from '@golevelup/ts-jest';
import { EntityManager } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { MOCK_POD_DRIVE_SA_PLAN_ENTITY } from '../../../domain/entities/__fixtures__/entity.fixtures';
import { FatalRepositoryError } from '@experience/mobile/clean-architecture';
import { SubscriptionPlan } from '@experience/mobile/driver-account/database';
import { MOCK_PLAN } from '../__fixtures__/plan.fixtures';

describe(PlanRepository.name, () => {
  let repository: PlanRepository;
  let mockEntityManager: DeepMocked<EntityManager>;
  let mockClsService: DeepMocked<ClsService>;

  beforeEach(() => {
    mockEntityManager = createMock<EntityManager>();
    mockClsService = createMock<ClsService>();

    mockClsService.get.mockReturnValue(mockEntityManager);

    repository = new PlanRepository(mockEntityManager, mockClsService);
  });

  afterEach(() => jest.restoreAllMocks());

  describe(PlanRepository.prototype.updatePlanDetails, () => {
    it('throws a FatalRepositoryError if no matching plan is found', async () => {
      mockEntityManager.findOneBy.mockResolvedValue(null);

      await expect(
        repository.updatePlanDetails(MOCK_POD_DRIVE_SA_PLAN_ENTITY.id, {
          ...MOCK_POD_DRIVE_SA_PLAN_ENTITY.details,
          milesRenewalDate: new Date('2025-01-01T00:00:00.000Z'),
        })
      ).rejects.toThrow(FatalRepositoryError);

      expect(mockEntityManager.findOneBy).toHaveBeenCalledTimes(1);
      expect(mockEntityManager.findOneBy).toHaveBeenCalledWith(
        SubscriptionPlan,
        {
          id: MOCK_POD_DRIVE_SA_PLAN_ENTITY.id,
        }
      );

      expect(mockEntityManager.save).not.toHaveBeenCalled();
    });

    it('throws a FatalRepositoryError if an error occurs', async () => {
      mockEntityManager.findOneBy.mockRejectedValue(new Error());

      await expect(
        repository.updatePlanDetails(MOCK_POD_DRIVE_SA_PLAN_ENTITY.id, {
          ...MOCK_POD_DRIVE_SA_PLAN_ENTITY.details,
          milesRenewalDate: new Date('2025-01-01T00:00:00.000Z'),
        })
      ).rejects.toThrow(FatalRepositoryError);

      expect(mockEntityManager.findOneBy).toHaveBeenCalledTimes(1);
      expect(mockEntityManager.findOneBy).toHaveBeenCalledWith(
        SubscriptionPlan,
        {
          id: MOCK_POD_DRIVE_SA_PLAN_ENTITY.id,
        }
      );

      expect(mockEntityManager.save).not.toHaveBeenCalled();
    });

    it('updates the plan details', async () => {
      mockEntityManager.findOneBy.mockResolvedValue(
        MOCK_POD_DRIVE_SA_PLAN_ENTITY
      );
      mockEntityManager.save.mockResolvedValue(MOCK_PLAN);

      await repository.updatePlanDetails(MOCK_POD_DRIVE_SA_PLAN_ENTITY.id, {
        ...MOCK_POD_DRIVE_SA_PLAN_ENTITY.details,
        milesRenewalDate: new Date('2025-01-01T00:00:00.000Z'),
      });

      expect(mockEntityManager.findOneBy).toHaveBeenCalledTimes(1);
      expect(mockEntityManager.findOneBy).toHaveBeenCalledWith(
        SubscriptionPlan,
        {
          id: MOCK_POD_DRIVE_SA_PLAN_ENTITY.id,
        }
      );

      expect(mockEntityManager.save).toHaveBeenCalledTimes(1);
      expect(mockEntityManager.save).toHaveBeenCalledWith(SubscriptionPlan, {
        ...MOCK_POD_DRIVE_SA_PLAN_ENTITY,
        details: {
          ...MOCK_POD_DRIVE_SA_PLAN_ENTITY.details,
          milesRenewalDate: '2025-01-01T00:00:00.000Z',
        },
      });
    });
  });
});
