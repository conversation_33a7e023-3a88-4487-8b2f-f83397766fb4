/**
 * Shape of any typescript function
 *
 * @private do not export
 */
type AnyFunction = (...args: unknown[]) => unknown;

/**
 * Removes properties keys which are functions
 */
type NoneFunctions<
  Entity extends object,
  Key extends keyof Entity
> = Entity[Key] extends AnyFunction ? never : Key;

/**
 * Removes properties which are functions
 */
export type StripFunctions<T extends object> = {
  [Key in keyof T as NoneFunctions<T, Key>]: T[Key];
};
