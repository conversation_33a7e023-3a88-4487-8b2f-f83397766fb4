FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:cb3143549582cc5f74f26f0992cdef4a422b22128cb517f94173a5f910fa4ee7
ARG APPLICATION_VERSION
ARG SENTRY_RELEASE
ENV APPLICATION_VERSION=$APPLICATION_VERSION
ENV SENTRY_RELEASE=$SENTRY_RELEASE
WORKDIR /app
COPY .npmrc .
COPY dist/apps/mobile/subscriptions-api .
COPY assets/subscriptions-api ./assets/subscriptions-api
RUN npm install --omit=dev

# Add RDS CA certs to trust store to be able to connect to RDS over TLS
ADD https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem global-bundle.pem
RUN cat global-bundle.pem >> /etc/ssl/certs/ca-certificates.crt && rm global-bundle.pem

# Configure Node to use the OpenSSL CA store to use certs from above
ENV NODE_OPTIONS=--use-openssl-ca

CMD [ "node", "main.js" ]
EXPOSE 5120
