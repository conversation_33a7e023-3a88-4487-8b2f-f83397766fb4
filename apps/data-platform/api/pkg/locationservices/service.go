package locationservices

import (
	"context"
	"experience/apps/data-platform/api/pkg/locationservices/models"
)

type serviceImpl struct {
	repository Repository
}

func NewService(repository Repository) Service {
	return serviceImpl{repository}
}

func (s serviceImpl) RetrieveDnoRegionByPpid(ctx context.Context, ppid string) (int16, error) {
	coords, err := s.repository.RetrieveCoordinatesByPpid(ctx, ppid)
	if err != nil {
		return 0, err
	}
	return s.repository.RetrieveDnoRegionByCoordinates(ctx, coords)
}

func (s serviceImpl) RetrieveDnoRegions(ctx context.Context) ([]models.DnoRegion, error) {
	regions, err := s.repository.RetrieveDnoRegions(ctx)
	if err != nil {
		return nil, err
	}
	return regions, nil
}
