package models_test

import (
	"experience/apps/data-platform/api/pkg/chargers/models"
	"experience/apps/data-platform/api/pkg/payments"
	exchangerates "experience/libs/shared/go/service/exchange-rates"
	mockexchangerates "experience/libs/shared/go/service/exchange-rates/mock"
	"testing"

	"go.uber.org/mock/gomock"

	"github.com/biter777/countries"

	"github.com/stretchr/testify/assert"
)

func TestChargerSettings_MinimumBalance(t *testing.T) {
	type fields struct {
		isHome          bool
		isPublic        bool
		paygEnabled     bool
		currency        string
		supportsOCPP    bool
		authoriserLevel payments.AuthoriserLevel
		revenueProfiles models.RevenueProfiles
	}
	tests := []struct {
		name   string
		fields fields
		want   int
	}{
		{
			name: "Minimum balance of 0 when is home",
			fields: fields{
				isHome:      true,
				isPublic:    false,
				paygEnabled: true,
			},
			want: models.MinBalanceZero,
		},
		{
			name: "Minimum balance of 0 when is not pay as you go",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     false,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceZero,
		},
		{
			name: "Minimum balance of 0 when there is no revenue profile",
			fields: fields{
				isHome:      false,
				isPublic:    true,
				paygEnabled: true,
			},
			want: models.MinBalanceZero,
		},
		{
			name: "Minimum balance of 1 when all is not home, is pay as you go and there is a revenue profile",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceInCredit,
		},
		{
			name: "Minimum balance of 1 when is not home, is pay as you go and there is a revenue profile and is not public",
			fields: fields{
				isHome:          false,
				isPublic:        false,
				paygEnabled:     true,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceInCredit,
		},
		{
			name: "Minimum balance of 500 when balance check required and supports ocpp with simple tariff for GBP account currency",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    true,
				currency:        "GBP",
				authoriserLevel: payments.PUBLIC,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceGBPPence,
		},
		{
			name: "Minimum balance of 500 when balance check required and supports ocpp with simple tariff for GBP account currency and is not public",
			fields: fields{
				isHome:          false,
				isPublic:        false,
				paygEnabled:     true,
				supportsOCPP:    true,
				currency:        "GBP",
				authoriserLevel: payments.PUBLIC,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceGBPPence,
		},
		{
			name: "Minimum balance of 500 when balance check required and supports ocpp with simple tariff for EUR account currency",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    true,
				currency:        "EUR",
				authoriserLevel: payments.PUBLIC,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceEURCents,
		},
		{
			name: "Minimum balance of 5000 when balance check required and supports ocpp with simple tariff for NOK account currency",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    true,
				currency:        "NOK",
				authoriserLevel: payments.PUBLIC,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceNOKØre,
		},
		{
			name: "Minimum balance of 500 when balance check required and supports ocpp with simple tariff for unknown account currency",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    true,
				currency:        "BTC",
				authoriserLevel: payments.PUBLIC,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceCurrencyUnknown,
		},
		{
			name: "Minimum balance of 500 when balance check required and supports ocpp with simple tariff of 0",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    true,
				currency:        "GBP",
				authoriserLevel: payments.PUBLIC,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)},
			},
			want: models.MinBalanceGBPPence,
		},
		{
			name: "Minimum balance of 1 when balance check required and supports ocpp with other tariff for GBP",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    true,
				currency:        "GBP",
				authoriserLevel: payments.PUBLIC,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.OtherRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceInCredit,
		},
		{
			name: "Minimum balance of 1 when balance check required and does not support ocpp",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    false,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: models.MinBalanceInCredit,
		},
		{
			name: "Minimum balance of 1 when balance check required and no mappable revenue profile",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				supportsOCPP:    true,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
				authoriserLevel: payments.MEMBER,
			},
			want: models.MinBalanceInCredit,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := models.NewChargerSettings(tt.fields.isHome, tt.fields.isPublic, tt.fields.paygEnabled, tt.fields.supportsOCPP, tt.fields.revenueProfiles, countries.UnitedKingdom)
			if got := l.MinimumBalance(tt.fields.currency, tt.fields.authoriserLevel); got != tt.want {
				t.Errorf("MinimumBalance() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChargerSettings_BalanceCheckRequired(t *testing.T) {
	type fields struct {
		isHome          bool
		isPublic        bool
		paygEnabled     bool
		revenueProfiles models.RevenueProfiles
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "No balance check required when is home",
			fields: fields{
				isHome:          true,
				paygEnabled:     true,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: false,
		},
		{
			name: "No balance check required when is not pay as you go",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     false,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: false,
		},
		{
			name: "No balance check required when there is no revenue profile",
			fields: fields{
				isHome:      false,
				isPublic:    true,
				paygEnabled: true,
			},
			want: false,
		},
		{
			name: "Balance check required when all is not home, is pay as you go and there is a revenue profile",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)},
			},
			want: true,
		},
		{
			name: "Balance check required even when rate is 0 on pay as you go and there is a revenue profile",
			fields: fields{
				isHome:          false,
				isPublic:        true,
				paygEnabled:     true,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)},
			},
			want: true,
		},
		{
			name: "Balance check required even charger is not public",
			fields: fields{
				isHome:          false,
				isPublic:        false,
				paygEnabled:     true,
				revenueProfiles: map[payments.AuthoriserLevel]models.RevenueProfile{payments.MEMBER: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := models.NewChargerSettings(tt.fields.isHome, tt.fields.isPublic, tt.fields.paygEnabled, false, tt.fields.revenueProfiles, countries.UnitedKingdom)
			if got := l.BalanceCheckRequired(); got != tt.want {
				t.Errorf("BalanceCheckRequired() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChargerSettings_ChargingLimit(t *testing.T) {
	mockCurrencyRates := mockexchangerates.NewMockAPI(gomock.NewController(t))
	mockCurrencyRatesGbpToEurReturnValue := exchangerates.ExchangeRate{
		CurrencyFrom: "GBP",
		CurrencyTo:   "EUR",
		Value:        1.15996,
	}
	mockCurrencyRatesGbpToNokReturnValue := exchangerates.ExchangeRate{
		CurrencyFrom: "GBP",
		CurrencyTo:   "NOK",
		Value:        12.987454,
	}
	mockCurrencyRates.EXPECT().GetLatest(gomock.Eq("GBP"), gomock.Eq("EUR")).Return(&mockCurrencyRatesGbpToEurReturnValue, nil).AnyTimes()
	mockCurrencyRates.EXPECT().GetLatest(gomock.Eq("GBP"), gomock.Eq("NOK")).Return(&mockCurrencyRatesGbpToNokReturnValue, nil).AnyTimes()

	tests := []struct {
		name            string
		authoriser      *payments.Authoriser
		chargerSettings *models.ChargerSettings
		want            *models.ChargingLimit
		wantErr         bool
	}{
		{
			name:            "Authoriser with RFID type on home charger returns no limit charge",
			authoriser:      payments.NewAuthoriser(payments.RFID, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(true, true, false, false, map[payments.AuthoriserLevel]models.RevenueProfile{}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(0, "GBP"),
				0,
				nil,
			),
		},
		{
			name:            "Authoriser with APP type returns no limit charge",
			authoriser:      payments.NewAuthoriser(payments.APP, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(0, "GBP"),
				0,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge limit allowed with minimum balance of 1 on simple tariff",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, false, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge limit allowed in NOK with minimum balance of 1 on simple tariff",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, false, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)}, countries.Norway),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(35000, "NOK"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge limit allowed in EUR with minimum balance of 1 on simple tariff",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, false, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)}, countries.Ireland),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "EUR"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge limit allowed with minimum balance of 1 on simple tariff",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, false, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.OtherRevenueProfile, 130000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge limit allowed with no balance on ocpp simple tariff of 10000",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 10000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: float64(3000),
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge not allowed on charger that is not public",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, false, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 10000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceGBPPence,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns rounded charge limit allowed with no balance on ocpp simple tariff of 23p",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 230000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: 130.43,
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with GUEST type returns no energy limit with no balance on ocpp free simple tariff of 0p",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns energy limit based on a rounded revenue_profile tariff of 1.5p",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 15000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: 2000,
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge limit allowed with irrelevant account balance of less than fixed amount on ocpp simple tariff of 10000",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(models.MinBalanceGBPPence-1, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 10000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: float64(3000),
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with GUEST type returns charge limit allowed and no energy limit when not on simple tariff and support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.OtherRevenueProfile, 130000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns not allowed when charger is not public but has no public revenue profile tier",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, false, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.MEMBER: *models.NewRevenueProfile(models.OtherRevenueProfile, 130000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Authoriser with GUEST type returns not allowed when charger is not public even if a public revenue tier has a rate of 0",
			authoriser:      payments.NewAuthoriser(payments.GUEST, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, false, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.OtherRevenueProfile, 0, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(3000, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with minimum balance of 1 on simple tariff with no support ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(1, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, false, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 123, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(1, "GBP"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with minimum balance of 1 not on simple tariff with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(1, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, false, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.OtherRevenueProfile, 130000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(1, "GBP"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with minimum balance of 500 authoriser currency on simple tariff of 20000 (2p) with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(500, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 20000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(500, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: float64(250),
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with minimum balance of 500 authoriser currency on simple tariff of 230000 (23p) with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(2234, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 230000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(2234, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: 97.13,
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with minimum balance of 0 authoriser currency on simple tariff with max rate of 0 and support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(0, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with negative minimum balance on simple tariff with max rate of 0 and support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(-1, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(-1, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with minimum balance of 500 authoriser currency on simple tariff of 15000 (1.5p) with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(500, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 15000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(500, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: 333.33,
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed with minimum balance of 500 authoriser currency on simple tariff of 14000 (1.4p) with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(500, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 14000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(500, "GBP"),
				models.MinBalanceGBPPence,
				[]*models.Limit{{
					Amount: 357.14,
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed and no energy limit with minimum balance of 500 authoriser currency on simple free tariff (0p) with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(499, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(499, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit not allowed with minimum balance of less than fixed amount authoriser currency on simple tariff of 10000",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(models.MinBalanceGBPPence-1, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 10000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(models.MinBalanceGBPPence-1, "GBP"),
				models.MinBalanceGBPPence,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type returns charge limit allowed not on simple tariff",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(true, true, false, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.OtherRevenueProfile, 130000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(0, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Authoriser with UNKNOWN type returns no limit charge",
			authoriser:      payments.NewAuthoriser(payments.UNKNOWN, payments.NewBalance(0, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(true, true, false, false, map[payments.AuthoriserLevel]models.RevenueProfile{}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(0, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Public USER on charger with member tariff only, user is allowed to charge for free, no balance check",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(-2, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.MEMBER: *models.NewRevenueProfile(models.SimpleRevenueProfile, 1000000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(-2, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Public USER on charger with no tariff, user is allowed to charge for free, no balance check",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(-1, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(-1, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Public USER on charger with non-simple public tariff, user is not allowed to charge when balance is less than 0",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(-2, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.OtherRevenueProfile, 1000000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(-2, "GBP"),
				models.MinBalanceInCredit,
				nil,
			),
		},
		{
			name:            "Public USER returns charge not allowed when charger is not public even if revenue profile is missing",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(500, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, false, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(500, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Public USER returns charge not allowed when charger is not public even if revenue profile is free",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(500, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, false, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 0, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(500, "GBP"),
				models.MinBalanceZero,
				nil,
			),
		},
		{
			name:            "Public USER returns charge not allowed when charger is not public",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(500, "GBP"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, false, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 20000, "GBP", nil)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				false,
				payments.NewBalance(500, "GBP"),
				models.MinBalanceGBPPence,
				nil,
			),
		},
		{
			name:            "Authoriser with USER type and euro balance returns currency exchange rate aware charge limit allowed with minimum balance of 500 authoriser currency on simple tariff of 230000 (23p) with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(2234, "EUR"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 230000, "GBP", mockCurrencyRates)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(2234, "EUR"),
				models.MinBalanceEURCents,
				[]*models.Limit{{
					Amount: 83.74,
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
		{
			name:            "Authoriser with USER type and krone balance returns currency exchange rate aware charge limit allowed with minimum balance of 5000 authoriser currency on simple tariff of 230000 (23p) with support for ocpp",
			authoriser:      payments.NewAuthoriser(payments.USER, payments.NewBalance(22345, "NOK"), payments.PUBLIC),
			chargerSettings: models.NewChargerSettings(false, true, true, true, map[payments.AuthoriserLevel]models.RevenueProfile{payments.PUBLIC: *models.NewRevenueProfile(models.SimpleRevenueProfile, 230000, "GBP", mockCurrencyRates)}, countries.UnitedKingdom),
			want: models.NewChargingLimit(
				true,
				payments.NewBalance(22345, "NOK"),
				models.MinBalanceNOKØre,
				[]*models.Limit{{
					Amount: 74.8,
					Unit:   "kWh",
					Type:   "energy",
				}},
			),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.chargerSettings.ChargingLimit(tt.authoriser)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChargingLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			assert.Equal(t, tt.want, got)
		})
	}
}
