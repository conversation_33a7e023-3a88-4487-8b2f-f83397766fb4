package submittedcharges

import (
	"context"
	"errors"
	"experience/apps/data-platform/api/pkg/common/drivers"
	mock_submittedcharges "experience/apps/data-platform/api/pkg/submittedcharges/mock"
	"experience/apps/data-platform/api/pkg/submittedcharges/models"
	projectioncharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	"experience/libs/shared/go/service/uuidprefix"
	"log"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestSubmittedChargesService_GetByOrgIDAndStatus(t *testing.T) {
	type args struct {
		ctx      context.Context
		orgID    uuid.UUID
		status   *string
		driverID int
	}
	tests := []struct {
		name              string
		args              args
		submittedCharges  models.SubmittedChargeCollection
		wantCharges       models.SubmittedChargeCollection
		wantErr           error
		wantProjection    []*projectioncharges.Projection
		wantProjectionErr error
	}{
		{
			name: "Orchestrates calls to repository",
			args: args{
				ctx:      context.Background(),
				orgID:    uuid.New(),
				status:   ptr.To(""),
				driverID: 0,
			},
			wantCharges: models.SubmittedChargeCollection{},
		},
		{
			name: "Orchestrates calls to repository for request with status",
			args: args{
				ctx:      context.Background(),
				orgID:    uuid.New(),
				status:   ptr.To("NEW"),
				driverID: 0,
			},
			wantCharges: models.SubmittedChargeCollection{},
		},
		{
			name: "Passes up errors from repository",
			args: args{
				ctx:      context.Background(),
				orgID:    uuid.New(),
				status:   ptr.To("NEW"),
				driverID: 0,
			},
			wantCharges: nil,
			wantErr:     ErrOrganisationNotFound,
		},
		{
			name: "Passes up errors from projections",
			args: args{
				ctx:      context.Background(),
				orgID:    uuid.New(),
				status:   ptr.To("NEW"),
				driverID: 0,
			},
			wantProjectionErr: errors.New("error"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockRepository := mock_submittedcharges.NewMockRepository(ctrl)
			s := serviceImpl{
				logger:     log.Default(),
				repository: mockRepository,
			}

			var chargeUIDs []uuid.UUID
			if tt.submittedCharges != nil {
				for _, charge := range tt.submittedCharges {
					chargeUIDs = append(chargeUIDs, uuidprefix.FabricateUUIDFromNumericID(charge.ChargeID))
				}
			}
			if tt.args.status != nil {
				mockRepository.EXPECT().GetByOrganisationAndStatus(gomock.Any(), gomock.Eq(tt.args.orgID), gomock.Eq(tt.args.status), gomock.Any(), gomock.Any()).Times(1).Return(tt.submittedCharges, chargeUIDs, tt.wantErr)
			} else {
				mockRepository.EXPECT().GetByOrganisation(gomock.Any(), gomock.Eq(tt.args.orgID), gomock.Any(), gomock.Any()).Times(1).Return(tt.submittedCharges, chargeUIDs, tt.wantErr)
			}
			if tt.wantErr == nil {
				mockRepository.EXPECT().FindProjectionsByAggregateIDs(gomock.Any(), gomock.Any()).Times(1).Return(tt.wantProjection, tt.wantProjectionErr)
			}
			gotCharges, gotErr := s.GetByOrgIDAndStatus(tt.args.ctx, tt.args.orgID, tt.args.status, time.Time{}, time.Time{})

			require.Equal(t, tt.wantCharges, gotCharges)

			if tt.wantProjectionErr != nil {
				require.Equal(t, tt.wantProjectionErr, gotErr)
			} else {
				require.Equal(t, tt.wantErr, gotErr)
			}
		})
	}
}

func TestSubmittedChargesService_GetByOrgIDAndDriverID(t *testing.T) {
	type args struct {
		ctx      context.Context
		orgID    uuid.UUID
		driverID int
	}
	tests := []struct {
		name             string
		args             args
		wantDriver       drivers.Driver
		wantSummary      models.SubmittedChargesSummary
		submittedCharges models.SubmittedChargeCollection
		wantCharges      models.SubmittedChargeCollection
		wantProjection   []*projectioncharges.Projection
	}{
		{
			name: "Orchestrates calls to repository",
			args: args{
				ctx:      context.Background(),
				orgID:    uuid.New(),
				driverID: 0,
			},
			wantDriver:  drivers.Driver{},
			wantSummary: models.SubmittedChargesSummary{},
			wantCharges: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ChargeID:  1,
					StartTime: "2021-01-01T01:00:00Z",
					EndTime:   "2021-01-01T01:00:00Z",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockRepository := mock_submittedcharges.NewMockRepository(ctrl)
			s := serviceImpl{
				logger:     log.Default(),
				repository: mockRepository,
			}

			var chargeUIDs []uuid.UUID
			if tt.submittedCharges != nil {
				for _, charge := range tt.submittedCharges {
					chargeUIDs = append(chargeUIDs, uuidprefix.FabricateUUIDFromNumericID(charge.ChargeID))
				}
			}

			mockRepository.EXPECT().GetDriverByID(gomock.Any(), gomock.Eq(int64(tt.args.driverID))).Times(1).Return(tt.wantDriver, nil)
			mockRepository.EXPECT().GetSummaryByOrganisationAndDriver(gomock.Any(), gomock.Eq(tt.args.orgID), gomock.Eq(int64(tt.args.driverID))).Times(1).Return(tt.wantSummary, nil)
			mockRepository.EXPECT().GetByOrganisationAndDriver(gomock.Any(), gomock.Eq(tt.args.orgID), gomock.Eq(int64(tt.args.driverID))).Times(1).Return(tt.wantCharges, chargeUIDs, nil)
			mockRepository.EXPECT().FindProjectionsByAggregateIDs(gomock.Any(), gomock.Any()).Times(1).Return(tt.wantProjection, nil)
			gotDriver, gotSummary, gotCharges, err := s.GetByOrgIDAndDriverID(tt.args.ctx, tt.args.orgID, tt.args.driverID)
			require.NoError(t, err)
			require.Equal(t, tt.wantDriver, gotDriver)
			require.Equal(t, tt.wantSummary, gotSummary)
			require.Equal(t, tt.wantCharges, gotCharges)
		})
	}
}

func TestSubmittedChargesService_GetUsageByOrgID(t *testing.T) {
	type args struct {
		ctx   context.Context
		orgID uuid.UUID
	}
	tests := []struct {
		name string
		args args
		want *models.FleetUsage
	}{
		{
			name: "Orchestrates calls to repository",
			args: args{
				ctx:   context.Background(),
				orgID: uuid.New(),
			},
			want: &models.FleetUsage{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockRepository := mock_submittedcharges.NewMockRepository(ctrl)
			s := serviceImpl{
				logger:     log.Default(),
				repository: mockRepository,
			}
			mockRepository.EXPECT().RetrieveFleetUsage(gomock.Any(), gomock.Eq(tt.args.orgID)).Times(1).Return(tt.want, nil)
			fleetUsage, err := s.GetUsageByOrgID(context.Background(), tt.args.orgID)
			require.NoError(t, err)
			require.Equal(t, tt.want, fleetUsage)
		})
	}
}
