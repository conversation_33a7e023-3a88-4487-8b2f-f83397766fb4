package models

import (
	"experience/libs/shared/go/service"

	"k8s.io/utils/ptr"
)

func NewLocation() Location {
	return Location{
		ID:           123,
		LocationType: "home",
		Address:      NewAddress(),
	}
}

func NewAddress() Address {
	return Address{
		Town:     "London",
		Line1:    "234 Banner St",
		Postcode: ptr.To("EC1Y 8QE"),
	}
}

type Address struct {
	// Country full name
	Country *string
	// Town
	Town string
	// Address line 1
	Line1 string
	// Address line 2
	Line2 *string
	// Postcode
	Postcode *string
}

func (a Address) PrettyPrint() string {
	return service.Join(", ", a.Line1, a.Town, *a.Postcode)
}

type Location struct {
	// ID of the charge location
	ID int
	// Type of the location: home or public
	LocationType string
	Address      Address
}
