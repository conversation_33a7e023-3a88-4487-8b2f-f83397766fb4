package models

import (
	projectionCharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	"experience/libs/shared/go/service/uuidprefix"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"
)

func TestSubmittedCharge_ProcessedByFullName(t *testing.T) {
	type fields struct {
		Status               Status
		ProcessedByFirstName string
		ProcessedByLastName  string
	}
	tests := []struct {
		name   string
		fields fields
		want   *string
	}{
		{
			name: "Full name present when processed",
			fields: fields{
				Status:               PROCESSED,
				ProcessedByFirstName: "Firstname",
				ProcessedByLastName:  "Lastname",
			},
			want: ptr.To("Firstname Lastname"),
		},
		{
			name: "Full name nil when not processed",
			fields: fields{
				Status:               "NEW",
				ProcessedByFirstName: "Firstname",
				ProcessedByLastName:  "Lastname",
			},
			want: nil,
		},
		{
			name: "Full name trims whitespace",
			fields: fields{
				Status:               PROCESSED,
				ProcessedByFirstName: "  Firstname     ",
				ProcessedByLastName:  "  Lastname ",
			},
			want: ptr.To("Firstname Lastname"),
		},
		{
			name: "Last name only",
			fields: fields{
				Status:               PROCESSED,
				ProcessedByFirstName: "",
				ProcessedByLastName:  "  Lastname ",
			},
			want: ptr.To("Lastname"),
		},
		{
			name: "Fist name only",
			fields: fields{
				Status:               PROCESSED,
				ProcessedByFirstName: "  Firstname     ",
				ProcessedByLastName:  "",
			},
			want: ptr.To("Firstname"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sc := SubmittedCharge{
				Status:               tt.fields.Status,
				ProcessedByFirstName: tt.fields.ProcessedByFirstName,
				ProcessedByLastName:  tt.fields.ProcessedByLastName,
			}
			got := sc.ProcessedByFullName()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestSubmittedCharge_ProcessedTime(t *testing.T) {
	now := time.Now()
	type fields struct {
		Status      Status
		ProcessedAt time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   *string
	}{
		{
			name: "Formats time when processed",
			fields: fields{
				Status:      PROCESSED,
				ProcessedAt: now,
			},
			want: ptr.To(now.Format(time.RFC3339)),
		},
		{
			name: "Returns nil when not processed",
			fields: fields{
				Status:      "NEW",
				ProcessedAt: now,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sc := SubmittedCharge{
				Status:      tt.fields.Status,
				ProcessedAt: tt.fields.ProcessedAt,
			}
			got := sc.ProcessedTime()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestSubmittedChargeCollection_OverlayWithProjections(t *testing.T) {
	type args struct {
		submittedCharges SubmittedChargeCollection
		projections      []*projectionCharges.Projection
	}
	tests := []struct {
		name        string
		args        args
		wantCharges SubmittedChargeCollection
	}{
		{
			name: "Overlay with valid projections",
			args: args{
				submittedCharges: SubmittedChargeCollection{
					&SubmittedCharge{
						ChargeID:  1,
						StartTime: "2021-01-01T00:00:00Z",
						EndTime:   "2021-01-01T01:00:00Z",
					},
				},
				projections: []*projectionCharges.Projection{
					{
						ChargeUUID:  uuidprefix.FabricateUUIDFromNumericID(1),
						StartedAt:   ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
						EndedAt:     ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
						UnpluggedAt: ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
						PluggedInAt: ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
					},
				},
			},
			wantCharges: SubmittedChargeCollection{
				&SubmittedCharge{
					ChargeID:    1,
					StartTime:   "2024-02-02T10:00:00Z",
					EndTime:     "2024-02-02T10:00:00Z",
					UnpluggedAt: ptr.To("2024-02-02T10:00:00Z"),
					PluggedInAt: ptr.To("2024-02-02T10:00:00Z"),
				},
			},
		},
		{
			name: "Overlay with nil StartedAt and EndedAt",
			args: args{
				submittedCharges: SubmittedChargeCollection{
					&SubmittedCharge{
						ChargeID:  2,
						StartTime: "2021-01-01T00:00:00Z",
						EndTime:   "2021-01-01T01:00:00Z",
					},
				},
				projections: []*projectionCharges.Projection{
					{
						ChargeUUID:  uuidprefix.FabricateUUIDFromNumericID(2),
						StartedAt:   nil,
						EndedAt:     nil,
						UnpluggedAt: nil,
						PluggedInAt: nil,
					},
				},
			},
			wantCharges: SubmittedChargeCollection{
				&SubmittedCharge{
					ChargeID:    2,
					StartTime:   "2021-01-01T00:00:00Z",
					EndTime:     "2021-01-01T01:00:00Z",
					UnpluggedAt: nil,
					PluggedInAt: nil,
				},
			},
		},
		{
			name: "Overlay with energy cost",
			args: args{
				submittedCharges: SubmittedChargeCollection{
					&SubmittedCharge{
						ChargeID:  1,
						StartTime: "2021-01-01T00:00:00Z",
						EndTime:   "2021-01-01T01:00:00Z",
					},
				},
				projections: []*projectionCharges.Projection{
					{
						ChargeUUID:  uuidprefix.FabricateUUIDFromNumericID(1),
						StartedAt:   ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
						EndedAt:     ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
						UnpluggedAt: ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
						PluggedInAt: ptr.To(time.Date(2024, 2, 2, 10, 0, 0, 0, time.UTC)),
						EnergyCost:  ptr.To(int32(1234)),
					},
				},
			},
			wantCharges: SubmittedChargeCollection{
				&SubmittedCharge{
					ChargeID:    1,
					StartTime:   "2024-02-02T10:00:00Z",
					EndTime:     "2024-02-02T10:00:00Z",
					UnpluggedAt: ptr.To("2024-02-02T10:00:00Z"),
					PluggedInAt: ptr.To("2024-02-02T10:00:00Z"),
					ChargeCost:  1234,
				},
			},
		},
		{
			name: "nil projections and nil submitted charges",
			args: args{
				submittedCharges: nil,
				projections:      nil,
			},
			wantCharges: SubmittedChargeCollection{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotCharges := tt.args.submittedCharges.OverlayWithProjections(tt.args.projections)
			require.Equal(t, tt.wantCharges, gotCharges)
		})
	}
}
