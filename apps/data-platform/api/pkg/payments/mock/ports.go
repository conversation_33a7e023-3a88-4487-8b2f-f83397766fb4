// Code generated by MockGen. DO NOT EDIT.
// Source: apps/data-platform/api/pkg/payments/ports.go
//
// Generated by this command:
//
//	mockgen -destination apps/data-platform/api/pkg/payments/mock/ports.go -source=apps/data-platform/api/pkg/payments/ports.go
//

// Package mock_payments is a generated GoMock package.
package mock_payments

import (
	context "context"
	payments "experience/apps/data-platform/api/pkg/payments"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockPodadminRepository is a mock of PodadminRepository interface.
type MockPodadminRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPodadminRepositoryMockRecorder
	isgomock struct{}
}

// MockPodadminRepositoryMockRecorder is the mock recorder for MockPodadminRepository.
type MockPodadminRepositoryMockRecorder struct {
	mock *MockPodadminRepository
}

// NewMockPodadminRepository creates a new mock instance.
func NewMockPodadminRepository(ctrl *gomock.Controller) *MockPodadminRepository {
	mock := &MockPodadminRepository{ctrl: ctrl}
	mock.recorder = &MockPodadminRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPodadminRepository) EXPECT() *MockPodadminRepositoryMockRecorder {
	return m.recorder
}

// RetrieveAuthoriserByUID mocks base method.
func (m *MockPodadminRepository) RetrieveAuthoriserByUID(ctx context.Context, uid, ppid string) (*payments.Authoriser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveAuthoriserByUID", ctx, uid, ppid)
	ret0, _ := ret[0].(*payments.Authoriser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveAuthoriserByUID indicates an expected call of RetrieveAuthoriserByUID.
func (mr *MockPodadminRepositoryMockRecorder) RetrieveAuthoriserByUID(ctx, uid, ppid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveAuthoriserByUID", reflect.TypeOf((*MockPodadminRepository)(nil).RetrieveAuthoriserByUID), ctx, uid, ppid)
}

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
	isgomock struct{}
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// RetrieveAuthoriserByUID mocks base method.
func (m *MockService) RetrieveAuthoriserByUID(ctx context.Context, uid, ppid string) (*payments.Authoriser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveAuthoriserByUID", ctx, uid, ppid)
	ret0, _ := ret[0].(*payments.Authoriser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveAuthoriserByUID indicates an expected call of RetrieveAuthoriserByUID.
func (mr *MockServiceMockRecorder) RetrieveAuthoriserByUID(ctx, uid, ppid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveAuthoriserByUID", reflect.TypeOf((*MockService)(nil).RetrieveAuthoriserByUID), ctx, uid, ppid)
}
