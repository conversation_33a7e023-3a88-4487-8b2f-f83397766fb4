// Code generated by MockGen. DO NOT EDIT.
// Source: apps/data-platform/api/pkg/usage/ports.go
//
// Generated by this command:
//
//	mockgen -destination apps/data-platform/api/pkg/usage/mock/ports.go -source=apps/data-platform/api/pkg/usage/ports.go
//

// Package mock_usage is a generated GoMock package.
package mock_usage

import (
	context "context"
	models "experience/apps/data-platform/api/pkg/usage/models"
	reflect "reflect"
	time "time"

	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
	isgomock struct{}
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// RetrieveBasicUsageByOrganisation mocks base method.
func (m *MockService) RetrieveBasicUsageByOrganisation(ctx context.Context, groupID uuid.UUID, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveBasicUsageByOrganisation", ctx, groupID, from, to, interval)
	ret0, _ := ret[0].([]models.BasicUsage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveBasicUsageByOrganisation indicates an expected call of RetrieveBasicUsageByOrganisation.
func (mr *MockServiceMockRecorder) RetrieveBasicUsageByOrganisation(ctx, groupID, from, to, interval any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveBasicUsageByOrganisation", reflect.TypeOf((*MockService)(nil).RetrieveBasicUsageByOrganisation), ctx, groupID, from, to, interval)
}

// RetrieveBasicUsageByOrganisationAndLocation mocks base method.
func (m *MockService) RetrieveBasicUsageByOrganisationAndLocation(ctx context.Context, groupID uuid.UUID, locationID int32, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveBasicUsageByOrganisationAndLocation", ctx, groupID, locationID, from, to, interval)
	ret0, _ := ret[0].([]models.BasicUsage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveBasicUsageByOrganisationAndLocation indicates an expected call of RetrieveBasicUsageByOrganisationAndLocation.
func (mr *MockServiceMockRecorder) RetrieveBasicUsageByOrganisationAndLocation(ctx, groupID, locationID, from, to, interval any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveBasicUsageByOrganisationAndLocation", reflect.TypeOf((*MockService)(nil).RetrieveBasicUsageByOrganisationAndLocation), ctx, groupID, locationID, from, to, interval)
}

// RetrieveBasicUsageByOrganisationAndSite mocks base method.
func (m *MockService) RetrieveBasicUsageByOrganisationAndSite(ctx context.Context, groupID uuid.UUID, siteID int32, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveBasicUsageByOrganisationAndSite", ctx, groupID, siteID, from, to, interval)
	ret0, _ := ret[0].([]models.BasicUsage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveBasicUsageByOrganisationAndSite indicates an expected call of RetrieveBasicUsageByOrganisationAndSite.
func (mr *MockServiceMockRecorder) RetrieveBasicUsageByOrganisationAndSite(ctx, groupID, siteID, from, to, interval any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveBasicUsageByOrganisationAndSite", reflect.TypeOf((*MockService)(nil).RetrieveBasicUsageByOrganisationAndSite), ctx, groupID, siteID, from, to, interval)
}

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// RetrieveBasicUsageByOrganisation mocks base method.
func (m *MockRepository) RetrieveBasicUsageByOrganisation(ctx context.Context, groupID uuid.UUID, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveBasicUsageByOrganisation", ctx, groupID, from, to, interval)
	ret0, _ := ret[0].([]models.BasicUsage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveBasicUsageByOrganisation indicates an expected call of RetrieveBasicUsageByOrganisation.
func (mr *MockRepositoryMockRecorder) RetrieveBasicUsageByOrganisation(ctx, groupID, from, to, interval any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveBasicUsageByOrganisation", reflect.TypeOf((*MockRepository)(nil).RetrieveBasicUsageByOrganisation), ctx, groupID, from, to, interval)
}

// RetrieveBasicUsageByOrganisationAndLocation mocks base method.
func (m *MockRepository) RetrieveBasicUsageByOrganisationAndLocation(ctx context.Context, groupID uuid.UUID, locationID int32, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveBasicUsageByOrganisationAndLocation", ctx, groupID, locationID, from, to, interval)
	ret0, _ := ret[0].([]models.BasicUsage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveBasicUsageByOrganisationAndLocation indicates an expected call of RetrieveBasicUsageByOrganisationAndLocation.
func (mr *MockRepositoryMockRecorder) RetrieveBasicUsageByOrganisationAndLocation(ctx, groupID, locationID, from, to, interval any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveBasicUsageByOrganisationAndLocation", reflect.TypeOf((*MockRepository)(nil).RetrieveBasicUsageByOrganisationAndLocation), ctx, groupID, locationID, from, to, interval)
}

// RetrieveBasicUsageByOrganisationAndSite mocks base method.
func (m *MockRepository) RetrieveBasicUsageByOrganisationAndSite(ctx context.Context, groupID uuid.UUID, siteID int32, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveBasicUsageByOrganisationAndSite", ctx, groupID, siteID, from, to, interval)
	ret0, _ := ret[0].([]models.BasicUsage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveBasicUsageByOrganisationAndSite indicates an expected call of RetrieveBasicUsageByOrganisationAndSite.
func (mr *MockRepositoryMockRecorder) RetrieveBasicUsageByOrganisationAndSite(ctx, groupID, siteID, from, to, interval any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveBasicUsageByOrganisationAndSite", reflect.TypeOf((*MockRepository)(nil).RetrieveBasicUsageByOrganisationAndSite), ctx, groupID, siteID, from, to, interval)
}
