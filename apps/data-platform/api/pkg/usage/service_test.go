package usage

import (
	"context"
	"errors"
	"experience/apps/data-platform/api/pkg/common/addresses"
	mock_addresses "experience/apps/data-platform/api/pkg/common/addresses/mock"
	"experience/apps/data-platform/api/pkg/common/groups"
	mock_groups "experience/apps/data-platform/api/pkg/common/groups/mock"
	"experience/apps/data-platform/api/pkg/common/locations"
	mock_locations "experience/apps/data-platform/api/pkg/common/locations/mock"
	mock_usage "experience/apps/data-platform/api/pkg/usage/mock"
	"experience/apps/data-platform/api/pkg/usage/models"
	"log"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

const unexpectedError = "unexpected error"

func TestServiceImpl_RetrieveBasicUsageByOrganisation(t *testing.T) {
	tests := []struct {
		name                         string
		wantGroupsError              error
		usageRepositoryNumberOfCalls int
		expectedError                error
		expected                     []models.BasicUsage
		repositoryError              error
	}{
		{
			name:                         "Returns data from repository",
			usageRepositoryNumberOfCalls: 1,
			expected:                     expected(),
		},
		{
			name:                         "Returns error from usage repository",
			usageRepositoryNumberOfCalls: 1,
			expected:                     expected(),
			repositoryError:              errors.New("repository error"),
			expectedError:                errors.New("repository error"),
		},
		{
			name:                         "When group not found expect appropriate error",
			wantGroupsError:              groups.ErrGroupNotFound,
			usageRepositoryNumberOfCalls: 0,
			expectedError:                ErrOrganisationNotFound,
		},
		{
			name:                         "When group returns unexpected error",
			wantGroupsError:              errors.New(unexpectedError),
			usageRepositoryNumberOfCalls: 0,
			expectedError:                errors.New(unexpectedError),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(st *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(st)
			mockUsageRepository := mock_usage.NewMockService(ctrl)
			mockGroupsRepository := mock_groups.NewMockRepository(ctrl)
			s := NewService(log.Default(), mockUsageRepository, mockGroupsRepository, nil, nil)
			mockGroupsRepository.EXPECT().FindGroupByUUID(gomock.Any(), gomock.Any()).Times(1).Return(nil, tt.wantGroupsError)
			mockUsageRepository.EXPECT().RetrieveBasicUsageByOrganisation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(tt.usageRepositoryNumberOfCalls).Return(tt.expected, tt.repositoryError)
			actual, err := s.RetrieveBasicUsageByOrganisation(ctx, uuid.New(), time.Now(), time.Now(), "month")
			require.Equal(t, tt.expectedError, err)
			require.Equal(t, tt.expected, actual)
		})
	}
}

func expected() []models.BasicUsage {
	return []models.BasicUsage{{
		IntervalStartDate: "",
		TotalUsage:        0,
		RevenueGenerated:  0,
		Cost:              0,
	},
	}
}

func TestServiceImpl_RetrieveOrganisationSiteBasicUsage(t *testing.T) {
	tests := []struct {
		name                         string
		wantGroupsError              error
		usageRepositoryNumberOfCalls int
		expectedError                error
		expected                     []models.BasicUsage
		repositoryError              error
		addressesErr                 error
	}{
		{
			name:          "Site not found when no address exists",
			addressesErr:  addresses.ErrAddressNotFound,
			expectedError: ErrSiteNotFound,
		},
		{
			name:     "Returns data from repository",
			expected: expected(),
		},
		{
			name:            "Returns error from usage repository",
			expected:        expected(),
			repositoryError: errors.New("repository error"),
			expectedError:   errors.New("repository error"),
		},
		{
			name:            "When group not found expect appropriate error",
			wantGroupsError: groups.ErrGroupNotFound,
			expectedError:   ErrOrganisationNotFound,
		},
		{
			name:            "When group returns unexpected error",
			wantGroupsError: errors.New(unexpectedError),
			expectedError:   errors.New(unexpectedError),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(st *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(st)
			mockUsageRepository := mock_usage.NewMockService(ctrl)
			mockGroupsRepository := mock_groups.NewMockRepository(ctrl)
			mockAddressesRepository := mock_addresses.NewMockRepository(ctrl)
			s := NewService(log.Default(), mockUsageRepository, mockGroupsRepository, mockAddressesRepository, nil)
			mockGroupsRepository.EXPECT().FindGroupByUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, tt.wantGroupsError)
			mockUsageRepository.EXPECT().RetrieveBasicUsageByOrganisationAndSite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.expected, tt.repositoryError)
			mockAddressesRepository.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, tt.addressesErr)
			actual, err := s.RetrieveBasicUsageByOrganisationAndSite(ctx, uuid.New(), 123, time.Now(), time.Now(), "month")
			require.Equal(t, tt.expectedError, err)
			require.Equal(t, tt.expected, actual)
		})
	}
}

func TestServiceImpl_RetrieveBasicUsageByOrganisationAndLocation(t *testing.T) {
	tests := []struct {
		name                         string
		wantGroupsError              error
		usageRepositoryNumberOfCalls int
		expectedError                error
		expected                     []models.BasicUsage
		repositoryError              error
		locationsError               error
	}{
		{
			name:           "Charger not found when no location exists",
			locationsError: locations.ErrLocationNotFound,
			expectedError:  ErrLocationNotFound,
		},
		{
			name:     "Returns data from repository",
			expected: expected(),
		},
		{
			name:            "Returns error from usage repository",
			repositoryError: errors.New("repository error"),
			expectedError:   errors.New("repository error"),
		},
		{
			name:            "When group not found expect appropriate error",
			wantGroupsError: groups.ErrGroupNotFound,
			expectedError:   ErrOrganisationNotFound,
		},
		{
			name:            "Return unhandled when finding group",
			wantGroupsError: errors.New(unexpectedError),
			expectedError:   errors.New(unexpectedError),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(st *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(st)
			mockUsageRepository := mock_usage.NewMockService(ctrl)
			mockGroupsRepository := mock_groups.NewMockRepository(ctrl)
			mockLocationRepository := mock_locations.NewMockRepository(ctrl)
			s := NewService(log.Default(), mockUsageRepository, mockGroupsRepository, nil, mockLocationRepository)

			mockGroupsRepository.EXPECT().FindGroupByUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, tt.wantGroupsError)
			mockUsageRepository.EXPECT().RetrieveBasicUsageByOrganisationAndLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.expected, tt.repositoryError)
			mockLocationRepository.EXPECT().FindLocationByID(gomock.Any(), gomock.Any()).Return(nil, tt.locationsError)

			actual, err := s.RetrieveBasicUsageByOrganisationAndLocation(ctx, uuid.New(), 123, time.Now(), time.Now(), "month")
			require.Equal(t, tt.expectedError, err)
			require.Equal(t, tt.expected, actual)
		})
	}
}
