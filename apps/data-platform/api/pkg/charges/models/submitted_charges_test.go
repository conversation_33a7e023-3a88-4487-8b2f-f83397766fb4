package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestSubmittedCharges_ListIds(t *testing.T) {
	tests := []struct {
		name     string
		input    SubmittedCharges
		expected []uint32
	}{
		{
			name: "Lists multiple charge ids",
			input: SubmittedCharges{
				&SubmittedCharge{
					ID:             12,
					ChargeID:       125,
					OrganisationID: 1678,
					DriverID:       87612,
					SubmittedAt:    time.Date(2022, time.April, 12, 5, 2, 1, 3, time.UTC),
					CreatedBy:      "Tod Point",
					CreatedAt:      time.Date(2022, time.April, 12, 5, 2, 1, 3, time.UTC),
					Status:         "NEW",
				},
				&SubmittedCharge{
					ID:             14,
					ChargeID:       127,
					OrganisationID: 1678,
					DriverID:       87612,
					SubmittedAt:    time.Date(2021, time.April, 12, 5, 2, 1, 3, time.UTC),
					CreatedBy:      "Tod Point",
					CreatedAt:      time.Date(2021, time.April, 12, 5, 2, 1, 3, time.UTC),
					Status:         "NEW",
				},
			},
			expected: []uint32{125, 127},
		},
		{
			name: "Lists single charge id",
			input: SubmittedCharges{
				&SubmittedCharge{
					ID:             123,
					ChargeID:       62,
					OrganisationID: 246,
					DriverID:       346,
					SubmittedAt:    time.Date(2022, time.April, 12, 5, 2, 1, 3, time.UTC),
					CreatedBy:      "Tod Point",
					CreatedAt:      time.Date(2022, time.April, 12, 5, 2, 1, 3, time.UTC),
					Status:         "NEW",
				},
			},
			expected: []uint32{62},
		},
		{
			name:     "Returns empty slice for given empty charges struct",
			input:    SubmittedCharges{},
			expected: []uint32{},
		},
	}

	for _, tt := range tests {
		chargeIDs, _ := tt.input.ListChargeIDs()
		require.Equal(t, tt.expected, chargeIDs)
	}
}
