-- name: CreateSubmittedChargesForOrganisation :many
INSERT INTO commercial.submitted_charges (organisation_id, driver_id, charge_id, submitted_at, created_by, created_at,
                   status)
VALUES (UNNEST(@organisation_ids::bigint[]),
        UNNEST(@driver_ids::bigint[]),
        UNNEST(@charge_ids::bigint[]),
        UNNEST(@submitted_ats::timestamptz[]),
        UNNEST(@created_bys::text[]),
        UNNEST(@created_ats::timestamptz[]),
        UNNEST(@statuses::submitted_charge_status[]))
RETURNING *;

-- name: SetStatusOnSubmittedChargesById :many
UPDATE commercial.submitted_charges
SET status       = $1,
    processed_by = $2,
    processed_at = $3
WHERE id = ANY (@id::bigint[])
  AND status != $1
RETURNING *;

-- name: RetrieveSubmittedChargesByOrganisationPerDriver :many
SELECT q.driver_id,
       q.email,
       q.first_name,
       q.last_name,
       SUM(q.total_charges)          AS total_charges,
       ARRAY(SELECT id
             FROM commercial.submitted_charges
             WHERE driver_id = q.driver_id
               AND organisation_id = q.organisation_id
               AND submitted_at >= sqlc.arg(from_date)::timestamptz
               AND submitted_at < sqlc.arg(to_date)::timestamptz
             ORDER BY id)::bigint[]  AS charge_ids,
       SUM(kwh_used_home)::numeric   AS kwh_home,
       SUM(kwh_used_public)::numeric AS kwh_public,
       SUM(energy_cost_home)         AS cost_home,
       SUM(energy_cost_public)       AS cost_public
FROM (SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                             AS total_charges,
             SUM(c.kwh_used)                          AS kwh_used_home,
             0.0                                      AS kwh_used_public,
             COALESCE(SUM(c.energy_cost)::integer, 0) AS energy_cost_home,
             0                                        AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
      WHERE g.uid = $1
        AND pl.is_home = 1
        AND c.is_closed = 1
        AND sc.submitted_at >= sqlc.arg(from_date)::timestamptz
        AND sc.submitted_at < sqlc.arg(to_date)::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      UNION

      SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                                          AS total_charges,
             0.0                                                   AS kwh_used_home,
             SUM(c.kwh_used)                                       AS kwh_used_public,
             0                                                     AS energy_cost_home,
             COALESCE(SUM(ABS(be.presentment_amount))::integer, 0) AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
             LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
      WHERE g.uid = $1
        AND pl.is_home = 0
        AND c.is_closed = 1
        AND sc.submitted_at >= sqlc.arg(from_date)::timestamptz
        AND sc.submitted_at < sqlc.arg(to_date)::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      ) AS q
GROUP BY q.driver_id, q.organisation_id, q.email, q.first_name, q.last_name
ORDER BY q.last_name, q.first_name;

-- name: RetrieveSubmittedChargesByOrganisationAndStatusPerDriver :many
SELECT q.driver_id,
       q.email,
       q.first_name,
       q.last_name,
       SUM(q.total_charges)          AS total_charges,
       ARRAY(SELECT id
             FROM commercial.submitted_charges sc
             WHERE sc.driver_id = q.driver_id
               AND sc.organisation_id = q.organisation_id
               AND sc.status = $2
               AND submitted_at >= sqlc.arg(from_date)::timestamptz
               AND submitted_at < sqlc.arg(to_date)::timestamptz
             ORDER BY id)::bigint[]  AS charge_ids,
       SUM(kwh_used_home)::numeric   AS kwh_home,
       SUM(kwh_used_public)::numeric AS kwh_public,
       SUM(energy_cost_home)         AS cost_home,
       SUM(energy_cost_public)       AS cost_public
FROM (SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                             AS total_charges,
             SUM(c.kwh_used)                          AS kwh_used_home,
             0.0                                      AS kwh_used_public,
             COALESCE(SUM(c.energy_cost)::integer, 0) AS energy_cost_home,
             0                                        AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
      WHERE g.uid = $1
        AND sc.status = $2
        AND pl.is_home = 1
        AND c.is_closed = 1
        AND sc.submitted_at >= sqlc.arg(from_date)::timestamptz
        AND sc.submitted_at < sqlc.arg(to_date)::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      UNION

      SELECT sc.driver_id,
             sc.organisation_id,
             u.email,
             u.first_name,
             u.last_name,
             COUNT(sc.id)                                          AS total_charges,
             0.0                                                   AS kwh_used_home,
             SUM(c.kwh_used)                                       AS kwh_used_public,
             0                                                     AS energy_cost_home,
             COALESCE(SUM(ABS(be.presentment_amount))::integer, 0) AS energy_cost_public
      FROM podpoint.charges c
             INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
             INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
             INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
             INNER JOIN podpoint.users u ON u.id = sc.driver_id
             LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
      WHERE g.uid = $1
        AND sc.status = $2
        AND pl.is_home = 0
        AND c.is_closed = 1
        AND sc.submitted_at >= sqlc.arg(from_date)::timestamptz
        AND sc.submitted_at < sqlc.arg(to_date)::timestamptz
      GROUP BY sc.driver_id, sc.organisation_id, u.email, u.first_name, u.last_name

      ) AS q
GROUP BY q.driver_id, q.organisation_id, q.email, q.first_name, q.last_name
ORDER BY q.last_name, q.first_name;

-- name: RetrieveSubmittedChargesByOrganisationAndStatus :many
SELECT sc.id,
       sc.driver_id,
       sc.organisation_id,
       sc.status,
       u.email,
       u.first_name,
       u.last_name,
       c.id                                                                                 AS charge_id,
       c.kwh_used,
       COALESCE(CASE WHEN pl.is_home = 1 THEN c.energy_cost ELSE ABS(be.presentment_amount) END,
                0)::integer                                                                 AS energy_cost,
       pl.is_home,
       c.starts_at,
       c.ends_at,
       COALESCE(CASE WHEN pl.is_home = 1 THEN pu.ppid ELSE pu.name END, 'UNKNOWN')::varchar AS unit_name,
       c.location_id,
       pa.line_1                                                                            AS address_line_1,
       pa.line_2                                                                            AS address_line_2,
       pa.postal_town,
       pa.postcode,
       pa.country,
       sc.processed_by,
       sc.processed_at,
       sc.submitted_at,
       pru.first_name                                                                       AS processed_by_first_name,
       pru.last_name                                                                        AS processed_by_last_name
FROM podpoint.charges c
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       INNER JOIN podpoint.pod_addresses pa ON pa.id = pl.address_id
       INNER JOIN podpoint.users u ON u.id = sc.driver_id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
       LEFT JOIN podpoint.users pru ON sc.processed_by = pru.id
WHERE g.uid = $1
  AND sc.status = $2
  AND c.is_closed = 1
  AND submitted_at >= sqlc.arg(from_date)::timestamptz
  AND submitted_at < sqlc.arg(to_date)::timestamptz
ORDER BY c.starts_at DESC;

-- name: RetrieveSubmittedChargesByOrganisation :many
SELECT sc.id,
       sc.driver_id,
       sc.organisation_id,
       sc.status,
       u.email,
       u.first_name,
       u.last_name,
       c.id                                                                                 AS charge_id,
       c.kwh_used,
       COALESCE(CASE WHEN pl.is_home = 1 THEN c.energy_cost ELSE ABS(be.presentment_amount) END,
                0)::integer                                                                 AS energy_cost,
       pl.is_home,
       c.starts_at,
       c.ends_at,
       COALESCE(CASE WHEN pl.is_home = 1 THEN pu.ppid ELSE pu.name END, 'UNKNOWN')::varchar AS unit_name,
       c.location_id,
       pa.line_1                                                                            AS address_line_1,
       pa.line_2                                                                            AS address_line_2,
       pa.postal_town,
       pa.postcode,
       pa.country,
       sc.processed_by,
       sc.processed_at,
       sc.submitted_at,
       pru.first_name                                                                       AS processed_by_first_name,
       pru.last_name                                                                        AS processed_by_last_name
FROM podpoint.charges c
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       INNER JOIN podpoint.pod_addresses pa ON pa.id = pl.address_id
       INNER JOIN podpoint.users u ON u.id = sc.driver_id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
       LEFT JOIN podpoint.users pru ON sc.processed_by = pru.id
WHERE g.uid = $1
  AND c.is_closed = 1
  AND submitted_at >= sqlc.arg(from_date)::timestamptz
  AND submitted_at < sqlc.arg(to_date)::timestamptz
ORDER BY c.starts_at DESC;

-- name: RetrieveSubmittedChargesSummaryByOrganisationAndDriver :one
SELECT COALESCE(SUM(CASE WHEN pl.is_home = 1 THEN c.kwh_used END), 0)::numeric                AS kwh_used_home,
       COALESCE(SUM(CASE WHEN pl.is_home = 0 THEN c.kwh_used END), 0)::numeric                AS kwh_used_public,
       COALESCE(SUM(CASE WHEN pl.is_home = 1 THEN c.energy_cost END), 0)::bigint              AS energy_cost_home,
       COALESCE(SUM(CASE WHEN pl.is_home = 0 THEN ABS(be.presentment_amount) END), 0)::bigint AS energy_cost_public
FROM podpoint.charges c
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
WHERE g.uid = $1
  AND sc.driver_id = $2;

-- name: RetrieveSubmittedChargesByOrganisationAndDriver :many
SELECT sc.id                                                                                AS submitted_charge_id,
       sc.status,
       sc.submitted_at,
       c.id                                                                                 AS charge_id,
       c.starts_at,
       c.ends_at,
       c.duration,
       COALESCE(CASE WHEN pl.is_home = 1 THEN c.energy_cost ELSE ABS(be.presentment_amount) END,
                0)::integer                                                                 AS energy_cost,
       c.kwh_used,
       COALESCE(CASE WHEN pl.is_home = 1 THEN pu.ppid ELSE pu.name END, 'UNKNOWN')::varchar AS unit_name,
       pl.is_home,
       sc.processed_at,
       CASE WHEN sc.status = 'NEW' THEN '' ELSE us.first_name END::varchar                  AS processed_by_first_name,
       CASE WHEN sc.status = 'NEW' THEN '' ELSE us.last_name END::varchar                   AS processed_by_last_name,
       pl.id                                                                                AS location_id,
       pa.line_1                                                                            AS address_line_1,
       pa.line_2                                                                            AS address_line_2,
       pa.postal_town,
       pa.postcode,
       pa.country
FROM podpoint.charges c
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN commercial.submitted_charges sc ON c.id = sc.charge_id
       LEFT OUTER JOIN podpoint.users us ON us.id = sc.processed_by
       INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
       INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
       INNER JOIN podpoint.pod_addresses pa ON pl.address_id = pa.id
       LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id
WHERE g.uid = $1
  AND sc.driver_id = $2
ORDER BY c.starts_at DESC;

-- name: RetrieveUserById :one
SELECT u.id, u.first_name, u.last_name, u.email
FROM podpoint.users u
WHERE u.id = $1;


-- name: RetrieveFleetUsage :one
WITH base AS (SELECT COUNT(c.id)                           AS total_charges,
                     COALESCE(SUM(c.kwh_used), 0)::numeric AS kwh_used,
                     COUNT(DISTINCT (sc.driver_id))        AS number_of_drivers,
                     COALESCE(SUM(CASE
                                    WHEN pl.is_home = 1 THEN c.kwh_used
                       END), 0)::numeric                   AS kwh_used_home,
                     COALESCE(SUM(CASE
                                    WHEN pl.is_home = 0 THEN c.kwh_used
                       END), 0)::numeric                   AS kwh_used_public,
                     COUNT(CASE
                             WHEN pl.is_home = 1 THEN c.id
                       END)::bigint                        AS total_charges_home,
                     COUNT(CASE
                             WHEN pl.is_home = 0 THEN c.id
                       END)::bigint                        AS total_charges_public
              FROM commercial.submitted_charges sc
                     INNER JOIN podpoint.groups g ON g.id = sc.organisation_id
                     INNER JOIN podpoint.charges c ON c.id = sc.charge_id
                     INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
                     INNER JOIN podpoint.users u ON u.id = sc.driver_id
                     INNER JOIN podpoint.pod_locations pl ON pl.id = c.location_id
                     INNER JOIN podpoint.pod_addresses pa ON pa.id = pl.address_id
              WHERE g.uid = $1
                AND c.ends_at > (SELECT (DATE_TRUNC('month', NOW()))))
SELECT *
FROM base
WHERE EXISTS(SELECT id FROM podpoint.groups g WHERE g.uid = $1);

