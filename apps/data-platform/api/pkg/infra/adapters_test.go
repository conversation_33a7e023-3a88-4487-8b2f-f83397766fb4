package infra_test

import (
	"database/sql"
	"experience/apps/data-platform/api/pkg/common/drivers"
	"experience/apps/data-platform/api/pkg/infra"
	"experience/apps/data-platform/api/pkg/infra/sqlc"
	"experience/apps/data-platform/api/pkg/submittedcharges/models"

	"k8s.io/utils/ptr"

	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestToSubmittedChargeCollection(t *testing.T) {
	tests := []struct {
		name string
		rows []sqlc.RetrieveSubmittedChargesByOrganisationAndDriverRow
		want models.SubmittedChargeCollection
	}{
		{
			name: "Adapts db model to domain model",
			rows: []sqlc.RetrieveSubmittedChargesByOrganisationAndDriverRow{
				{
					SubmittedChargeID:    0,
					Status:               "NEW",
					SubmittedAt:          time.Time{},
					StartsAt:             sql.NullTime{},
					EndsAt:               sql.NullTime{},
					Duration:             sql.NullInt64{},
					EnergyCost:           0,
					KwhUsed:              "123.45",
					UnitName:             "Kent-John",
					IsHome:               0,
					ProcessedAt:          sql.NullTime{},
					ProcessedByFirstName: "Firstname",
					ProcessedByLastName:  "Lastname",
					LocationID:           0,
					AddressLine1:         "Line 1",
					AddressLine2:         "Line 2",
					PostalTown:           "London",
					Postcode:             "NW1 1ES",
					Country:              "GB",
				},
			},
			want: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            0,
					Driver:        drivers.Driver{},
					StartTime:     "0001-01-01T00:00:00Z",
					EndTime:       "0001-01-01T00:00:00Z",
					SubmittedTime: "0001-01-01T00:00:00Z",
					ChargeCost:    0,
					EnergyUsage:   123.45,
					Location: models.Location{
						ID:           0,
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "Line 1",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("NW1 1ES"),
						},
					},
					ChargerName:          "Kent-John",
					Status:               "NEW",
					ProcessedBy:          0,
					ProcessedByFirstName: "Firstname",
					ProcessedByLastName:  "Lastname",
					ProcessedAt:          time.Time{},
					Duration:             0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var got models.SubmittedChargeCollection
			for _, row := range tt.rows {
				got = append(got, infra.ToSubmittedCharge(&row))
			}
			require.Equal(t, tt.want, got)
		})
	}
}
