package main

import (
	"context"
	"experience/apps/data-platform/carbon-intensity-store/pkg/config"
	"experience/apps/data-platform/carbon-intensity-store/pkg/db/sqlc"
	producer "experience/apps/data-platform/carbon-intensity-store/pkg/nationalgrid"
	"experience/apps/data-platform/carbon-intensity-store/pkg/populate"
	consumer "experience/apps/data-platform/carbon-intensity-store/pkg/store"
	dbmigrate "experience/libs/data-platform/golang-migrate"
	"experience/libs/shared/go/db/postgres"
	forecastdate "experience/libs/shared/go/service/forecast-date"
	"net/http"
	"time"
)

const (
	appName = "data-platform-carbon-intensity-store"
	baseURL = "https://api.carbonintensity.org.uk"
)

func main() {
	ctx := context.Background()
	c, logger := config.NewCarbonIntensityConfig(ctx, appName)
	db := postgres.NewReadWriteDB(&c.<PERSON>urce, c.<PERSON>(), postgres.WithXray())
	querier := sqlc.New(db.WriteDB)
	nationalGridClient := producer.NewNationalGridClient(baseURL, http.DefaultClient, logger)
	carbonIntensityStore := consumer.NewStore(querier, logger)

	mc := dbmigrate.NewMigrateConfig(c.Config, c.MigrateDatasource)
	dbmigrate.MigrateUp(mc, logger)

	populateService := populate.NewService(nationalGridClient, carbonIntensityStore)
	from := forecastdate.From(time.Now()).Add(-24 * time.Hour)
	to := forecastdate.From(time.Now()).Add(49 * time.Hour)
	err := populateService.ForecastPeriodsFromTo(from, to)
	if err != nil {
		logger.Printf(" Error populating forecast periods: %s", err)
	}
}
