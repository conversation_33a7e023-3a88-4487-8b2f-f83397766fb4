package store

import (
	"context"
	"encoding/json"
	"errors"
	"experience/apps/data-platform/carbon-intensity-store/pkg/db/sqlc"
	"experience/apps/data-platform/carbon-intensity-store/pkg/forecast"
	"experience/apps/data-platform/carbon-intensity-store/pkg/nationalgrid"
	"log"
	"time"

	// side effect import to load postgres driver
	_ "github.com/lib/pq"
)

const LAYOUT = "2006-01-02T15:04Z"

type Store interface {
	SaveRegionalIntensityForecastPeriod(region int16, data *nationalgrid.Period) (*forecast.RegionForecastPeriod, error)
}

type IntensityStore struct {
	querier *sqlc.Queries
	logger  *log.Logger
}

func NewStore(querier *sqlc.Queries, logger *log.Logger) Store {
	return IntensityStore{
		querier: querier,
		logger:  logger,
	}
}

func (i IntensityStore) SaveRegionalIntensityForecastPeriod(region int16, data *nationalgrid.Period) (*forecast.RegionForecastPeriod, error) {
	if data == nil {
		return nil, errors.New("cannot save nil regional forecast period")
	}
	message, err := json.Marshal(data)

	if err != nil {
		return nil, err
	}

	parse, err := time.Parse(LAYOUT, data.From)
	if err != nil {
		return nil, err
	}

	params := sqlc.CreateRegionalIntensityForecastPeriodParams{
		DnoRegionID: region,
		StartTime:   parse,
		UpdatedAt:   time.Now(),
		Data:        message,
	}
	intensityForecastPeriod, err := i.querier.CreateRegionalIntensityForecastPeriod(context.Background(), params)

	if err != nil {
		return nil, err
	}

	var period *nationalgrid.Period
	marshallErr := json.Unmarshal(intensityForecastPeriod.Data, &period)
	if marshallErr != nil {
		return nil, marshallErr
	}

	return forecast.NewRegionForecastPeriod(intensityForecastPeriod.DnoRegionID, period), nil
}
