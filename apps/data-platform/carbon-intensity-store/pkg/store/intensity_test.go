package store

import (
	"errors"
	"experience/apps/data-platform/carbon-intensity-store/pkg/db/sqlc"
	"experience/apps/data-platform/carbon-intensity-store/pkg/forecast"
	"experience/apps/data-platform/carbon-intensity-store/pkg/nationalgrid"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/db/postgres/test"
	"fmt"
	"log"
	"os"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/stretchr/testify/require"
)

type IntensityTestSuite struct {
	suite.Suite
	testDB    *test.Database
	underTest Store
	sqlc      *sqlc.Queries
}

func TestIntensityTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping TestIntensityTestSuite integration test")
	}
	suite.Run(t, new(IntensityTestSuite))
}

func (s *IntensityTestSuite) SetupSuite() {
	s.testDB = test.NewDatabase(s.T())
	passwordConfig := s.testDB.PasswordConfig(s.T())

	_, err := migrate.MigrateUp(
		"file://../../../../../libs/data-platform/golang-migrate/migration",
		passwordConfig,
		nil,
		true,
	)
	require.NoError(s.T(), err)

	db, err := postgres.NewPasswordDB(passwordConfig, true)
	require.NoError(s.T(), err)
	logger := log.New(os.Stderr, fmt.Sprintf("[%s] ", "TestIntensityTestSuite"), log.Ltime)

	s.sqlc = sqlc.New(db)
	s.underTest = NewStore(s.sqlc, logger)
}

func (s *IntensityTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *IntensityTestSuite) TestSaveForecastPeriod() {
	data := nationalgrid.Period{
		From: "2018-05-15T11:30Z",
	}
	expected := forecast.NewRegionForecastPeriod(1, &data)
	actual, err := s.underTest.SaveRegionalIntensityForecastPeriod(1, &data)
	require.NoError(s.T(), err)
	require.Equal(s.T(), expected, actual)
}

func (s *IntensityTestSuite) TestErrorSavingForecastWithInvalidFromDate() {
	data := nationalgrid.Period{
		From: "Second of May Twenty twenty three",
	}
	_, err := s.underTest.SaveRegionalIntensityForecastPeriod(1, &data)
	require.ErrorContains(s.T(), err, "Second of May Twenty twenty three\" as \"2006-01-02T15:04Z\": cannot parse \"Second of May Twenty twenty three\" as \"2006\"")
}

func (s *IntensityTestSuite) TestErrorSavingForecastWithNilData() {
	_, err := s.underTest.SaveRegionalIntensityForecastPeriod(1, nil)
	require.Equal(s.T(), err, errors.New("cannot save nil regional forecast period"))
}
