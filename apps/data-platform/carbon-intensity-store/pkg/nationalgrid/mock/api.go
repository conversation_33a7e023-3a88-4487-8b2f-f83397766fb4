// Code generated by MockGen. DO NOT EDIT.
// Source: apps/data-platform/carbon-intensity-store/pkg/nationalgrid/api.go
//
// Generated by this command:
//
//	mockgen -destination apps/data-platform/carbon-intensity-store/pkg/nationalgrid/mock/api.go -source=apps/data-platform/carbon-intensity-store/pkg/nationalgrid/api.go
//

// Package mock_nationalgrid is a generated GoMock package.
package mock_nationalgrid

import (
	nationalgrid "experience/apps/data-platform/carbon-intensity-store/pkg/nationalgrid"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// GetRegionalIntensityForecastBetween mocks base method.
func (m *MockAPI) GetRegionalIntensityForecastBetween(from, to time.Time) (*nationalgrid.RegionalDateBoundForecastResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRegionalIntensityForecastBetween", from, to)
	ret0, _ := ret[0].(*nationalgrid.RegionalDateBoundForecastResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegionalIntensityForecastBetween indicates an expected call of GetRegionalIntensityForecastBetween.
func (mr *MockAPIMockRecorder) GetRegionalIntensityForecastBetween(from, to any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegionalIntensityForecastBetween", reflect.TypeOf((*MockAPI)(nil).GetRegionalIntensityForecastBetween), from, to)
}
