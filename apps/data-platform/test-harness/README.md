# Data Platform API Test Harness

Dockerised XDP services allowing for a single-command to provision all services provided by XDP. This service will
provision a `MySQL`
instance from [shared](libs/shared/test/db/podadmin/docker-compose.yml) and a `postgres`
from [XDP](libs/data-platform/db/postgres/test/docker-compose.yml)

## Prerequisites

Please ensure you have completed the prerequisites in the root [README](/README.md)

## Run - Local Development

As defined in [project.json](project.json)

Start: `npx nx run data-platform-test-harness:compose:up`

Recreate: `npx nx run data-platform-test-harness:compose:recreate`

Stop: `npx nx run data-platform-test-harness:compose:down`

Remove: `npx nx run data-platform-test-harness:compose:remove`

## Updating sample data

Sample data can be found in [shared](libs/shared/test/db/podadmin/migrations) and [XDP](libs/data-platform/db-seed/data), respectively.
Data is populated using SQL inserts. IDs should match across the schemas to ensure data integrity.

<img src="https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg" alt="Pod Point logo" style="float: right;" />

---

Driving shouldn’t cost the earth 🌍

Made with ❤️&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
