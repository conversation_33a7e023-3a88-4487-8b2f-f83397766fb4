package full

import (
	"context"
	"experience/apps/data-platform/api-e2e/compose"
	"experience/apps/data-platform/api-e2e/env"
	"experience/apps/data-platform/api-e2e/pkg/config"
	sqlccharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	testSqlc "experience/libs/data-platform/test/sqlc"
	sqlcpodadmin "experience/libs/data-platform/test/sqlc-podadmin"
	"experience/libs/shared/go/aws/test"
	"experience/libs/shared/go/db/mysql"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/sqs"
	"log"
	"testing"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/aws/aws-xray-sdk-go/v2/xray"
	"github.com/aws/aws-xray-sdk-go/v2/xraylog"
)

var host = "local"
var skipDockerCompose = false
var testQueries *testSqlc.Queries
var chargesQueries *sqlccharges.Queries
var podadminQueries *sqlcpodadmin.Queries
var chargeNotificationSQSClient *sqs.Client
var chargeCommandSQSClient *sqs.Client
var chargeCompletedSQSClient *sqs.Client

func TestMain(m *testing.M) {
	ctx := context.Background()
	err := gofakeit.Seed(0) // for reproducible test scenarios hard code seed to non-zero value
	if err != nil {
		panic("could not initialise gofakeit")
	}

	if env.IsLocal(host) {
		test.SetLocalStackEnvs(test.LocalStackDefaultPort)

		if !skipDockerCompose {
			if cleanup, composeErr := compose.WithStackIdentifier(ctx, "e2e-tests"); composeErr == nil {
				defer cleanup()
			} else {
				log.Panic("error during compose.Up()", composeErr)
			}
		}
	}

	c, _ := config.NewEndToEndTestConfig(ctx, "data-platform-test", env.IsLocal(host))
	podadminDB, err := mysql.NewPasswordDB(c.PodadminDatasource.WriteConfig.PasswordConfig, c.IsLocal(), false)
	if err != nil {
		log.Panic("cannot connect to podadmin db:", err)
	}
	db := postgres.NewIAMDB(&c.ServiceDatasource.WriteConfig.IAMConfig, c.IsLocal())

	testQueries = testSqlc.New(db)
	podadminQueries = sqlcpodadmin.New(podadminDB)
	chargesQueries = sqlccharges.New(db)

	sqsClient, err := sqs.NewSQSClient(ctx)
	if err != nil {
		log.Panic("cannot configure SQS client:", err)
	}
	chargeNotificationSQSClient = sqs.NewClient(sqsClient, c.ChargeEventsURL)
	chargeCommandSQSClient = sqs.NewClient(sqsClient, c.ChargeCommandsURL)
	chargeCompletedSQSClient = sqs.NewClient(sqsClient, c.ChargeCompletedURL)

	xray.SetLogger(xraylog.NullLogger)

	m.Run()

	purgeCommandErr := chargeCommandSQSClient.PurgeQueue(ctx)
	if purgeCommandErr != nil {
		log.Printf("cannot purge command queue: %v", purgeCommandErr)
	}

	purgeCompletedErr := chargeCompletedSQSClient.PurgeQueue(ctx)
	if purgeCompletedErr != nil {
		log.Printf("cannot purge completed queue: %v", purgeCompletedErr)
	}

	purgeNotificationErr := chargeNotificationSQSClient.PurgeQueue(ctx)
	if purgeNotificationErr != nil {
		log.Printf("cannot purge notification queue: %v", purgeNotificationErr)
	}
}
