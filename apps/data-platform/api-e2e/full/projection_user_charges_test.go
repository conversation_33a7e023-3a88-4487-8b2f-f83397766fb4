package full

import (
	"context"
	usercharges "experience/libs/data-platform/api/contract/gen/user_charges"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	carbonsavings "experience/libs/shared/go/carbon-savings"
	"experience/libs/shared/go/service/uuidprefix"
	"fmt"
	"net/http"
	"sort"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestGroupAndUserCharges(t *testing.T) {
	baseURL := setup.BaseURL(t, host)
	groupUID := uuid.NewString()
	userID := fixtures.Sequence.Get()
	userUID := uuidprefix.FabricateUUIDFromNumericID(int(userID))
	fromDate := "2024-07-01"
	toDate := "2024-07-31"

	chargeEvents, cleanup := seedChargesForGroupAndUser(t, groupUID, userID, userUID)
	defer cleanup()

	sort.Slice(chargeEvents, func(i, j int) bool {
		return chargeEvents[i].Payload.Charge.UnpluggedAt.After(*chargeEvents[j].Payload.Charge.UnpluggedAt)
	})

	expectedResponse := generateExpectedResponse(chargeEvents)
	expectedResponse.Meta = &usercharges.Meta{
		Params: map[string]interface{}{
			"From":    fromDate,
			"To":      toDate,
			"GroupID": groupUID,
			"UserID":  userUID.String(),
		},
	}

	requestURL := fmt.Sprintf(
		"%s/groups/%s/users/%s/charges?from=%s&to=%s",
		baseURL,
		groupUID,
		userUID,
		fromDate,
		toDate,
	)

	sendRequestAndCompareResponse(t, requestURL, http.MethodGet, http.NoBody, expectedResponse)
}

func generateExpectedResponse(chargeEvents []*chargeevents.Completed) usercharges.ProjectionGroupAndUserChargesResponse {
	userCharges := make([]*usercharges.UserCharge, len(chargeEvents))

	var pluggedInDuration int

	for i, chargeEvent := range chargeEvents {
		pluggedInDuration = calculatePluggedInDuration(chargeEvent.Payload.Charge.UnpluggedAt, chargeEvent.Payload.Charge.PluggedInAt)
		userCharges[i] = &usercharges.UserCharge{
			StartTime:         chargeEvent.Payload.Charge.PluggedInAt.Format(time.RFC3339),
			EndTime:           chargeEvent.Payload.Charge.UnpluggedAt.Format(time.RFC3339),
			ChargingDuration:  int(ptr.Deref(chargeEvent.Payload.Charge.ChargeDurationTotal, 0)),
			PluggedInDuration: pluggedInDuration,
			EnergyUsage:       ptr.Deref(chargeEvent.Payload.Charge.EnergyTotal, 0),
			ChargeCost:        int(ptr.Deref(chargeEvent.Payload.Charge.EnergyTotal, 0) * float64(defaultTariff)),
			RevenueGenerated:  seeding.FixedRate,
			Co2Savings:        carbonsavings.Co2Savings(*chargeEvent.Payload.Charge.EnergyTotal),
		}
	}

	return usercharges.ProjectionGroupAndUserChargesResponse{
		Data: &usercharges.UserChargesSchema{Charges: userCharges},
	}
}

func calculatePluggedInDuration(unpluggedAt, pluggedInAt *time.Time) int {
	if unpluggedAt != nil && pluggedInAt != nil {
		return int(unpluggedAt.Sub(*pluggedInAt).Seconds())
	}
	return 0
}

func seedChargesForGroupAndUser(t *testing.T, groupUUID string, userID int64, userUID uuid.UUID) (chargeEvents []*chargeevents.Completed, cleanup func()) {
	t.Helper()
	ctx := context.Background()

	numCharges := gofakeit.IntRange(10, 30)
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)

	aggregateIDsToCleanUp := make([]uuid.UUID, 0, numCharges)
	addressIDsToCleanUp := make([]int, 0, numCharges)
	chargeEvents = make([]*chargeevents.Completed, 0, numCharges)

	seededData := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
		AuthPK:   &userID,
		UserUUID: &userUID,
		GroupID:  groupUUID,
	})

	for i := 0; i < numCharges; i++ {
		dayOfMonth := gofakeit.IntRange(1, 31)
		aggregateID := uuidprefix.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams := random.ChargeCompletedEventParams{
			ChargeID:    chargeID,
			AggregateID: aggregateID,
			LocationID:  int(seededData.Location.ID),
			Duration:    ptr.To(42),
			StartedAt:   ptr.To(time.Date(2024, 7, dayOfMonth, 14, 0, 0, 0, time.UTC)),
			EndedAt:     ptr.To(time.Date(2024, 7, dayOfMonth, 17, 15, 16, 0, time.UTC)),
			PluggedInAt: ptr.To(time.Date(2024, 7, dayOfMonth, 13, 59, 55, 0, time.UTC)),
			UnpluggedAt: ptr.To(time.Date(2024, 7, dayOfMonth, 19, 34, 29, 0, time.UTC)),
			Energy: &random.EnergyParams{
				EnergyCost:  ptr.To(134),
				EnergyTotal: ptr.To(float64(156)),
			},
			SettlementAmount:  ptr.To(135),
			AuthoriserPK:      int(userID),
			AuthoriserType:    ptr.To(chargeevents.User),
			UnconfirmedCharge: false,
		}

		seedCompletedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
		require.NoError(t, err)

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))
		chargeEvents = append(chargeEvents, seedCompletedEvent)

		chargeID++
	}

	return chargeEvents, func() {
		cleanupData(ctx, t, aggregateIDsToCleanUp, addressIDsToCleanUp)
	}
}
