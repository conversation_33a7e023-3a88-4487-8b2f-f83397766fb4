package full

import (
	"context"
	"encoding/json"
	"experience/apps/data-platform/api-e2e/pkg/infra"
	"experience/libs/data-platform/api/contract/gen/drivers"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	eventstore "experience/libs/shared/go/event-store"
	httphelpers "experience/libs/shared/go/http"
	sharedtest "experience/libs/shared/go/test"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/assert"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func TestRetrieveCharge(t *testing.T) {
	seed := ptr.To(random.ChargeParamsSeed{
		// need to set timezone here to know its value, as not available to event data otherwise
		ChargerTimezone: ptr.To(random.TimezoneString()),
		// The Cost() charge getter function gets settlement values for private and public chargers only, home chargers are special
		ChargerType: ptr.To(sqlc.AccessPublic),
	})
	driverID := ptr.To(uuid.New())
	driver := &random.Driver{
		DriverID:     driverID,
		AuthoriserID: fixtures.Sequence.Get(),
	}
	locationID := fixtures.Sequence.Get()
	apiCharge, seedEvents, charger := random.ChargeWithEvents(t, driver, seed, locationID)
	chargers := make([]*random.Charger, 0)
	chargers = append(chargers, &charger)

	chargeID := uuid.MustParse(apiCharge.ID)

	expectedResponse := drivers.DriversChargeResponse{
		Data: &apiCharge,
		Meta: (*drivers.Meta)(httphelpers.PopulateMetaParams(drivers.RetrieveChargePayload{
			ChargeID: chargeID.String(),
			DriverID: driverID.String(),
		})),
	}

	siteNames := make([]string, len(chargers))
	for i, charger := range chargers {
		if charger.SiteName != nil {
			siteNames[i] = *charger.SiteName
		}
	}

	ctx := context.Background()
	defer infra.CleanupEventData(ctx, t, testQueries, chargeID)
	defer infra.CleanupSiteStatsProjectionDataByName(ctx, t, testQueries, siteNames)
	defer seeding.Drivers(t, testQueries, driver, chargers, nil)()

	for _, event := range seedEvents {
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, event)
		assert.NoError(t, err)
	}

	requestURL := fmt.Sprintf("%s/drivers/%s/charges/%s", setup.BaseURL(t, host), driverID, chargeID)
	sendRequestAndCompareResponse(t, requestURL, http.MethodGet, http.NoBody, expectedResponse)
}

func TestRetrieveCharges(t *testing.T) {
	tests := []rdcsFixture{
		newRetrieveDriversChargesFixture(t, "response with single charge", 1, 30),
		newRetrieveDriversChargesFixture(t, "response with multiple charges", random.TwoTo(10), 45),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			siteNames := make([]string, len(tt.chargers))
			for i, charger := range tt.chargers {
				if charger.SiteName != nil {
					siteNames[i] = *charger.SiteName
				}
			}
			ctx := context.Background()
			defer infra.CleanupEventsData(ctx, t, testQueries, tt.aggregateIDs)
			defer infra.CleanupSiteStatsProjectionDataByName(ctx, t, testQueries, siteNames)
			defer seeding.Drivers(t, testQueries, tt.driver, tt.chargers, nil)()
			for _, event := range tt.events {
				err := seeding.Event(ctx, t, chargeNotificationSQSClient, event)
				assert.NoError(t, err)
			}

			timeout := time.NewTimer(tt.timeout)
			done := make(chan bool)
			var lastResult *sharedtest.AsyncResult[drivers.DriversChargesResponse]
			go func() {
				match := false
				for !match {
					er := tt.expectedResponse
					lastResult, match = checkResponseOnDriversCharges(t, tt.driver.DriverID, &er)
					time.Sleep(1000 * time.Millisecond)
				}
				done <- true
			}()

			select {
			case <-timeout.C:
				if lastResult != nil {
					lastResult.AssertEqual(t)
				}
				t.Fatal("test didn't finish in time")
			case <-done:
			}
		})
	}
}

func TestRetrieveStats(t *testing.T) {
	ctx := context.Background()
	driverID := uuid.New()
	fromDate := time.Date(2023, 10, 1, 0, 0, 0, 0, time.UTC).Format(time.DateOnly)
	toDate := time.Date(2023, 11, 30, 0, 0, 0, 0, time.UTC).Format(time.DateOnly)
	interval := "month"

	seededCharges := seeding.ChargesForDriver(ctx, t, chargesQueries, driverID)
	uuids := make([]uuid.UUID, len(seededCharges))

	for i, ch := range seededCharges {
		uuids[i] = ch.ChargeUUID
	}
	defer infra.CleanupEventsData(ctx, t, testQueries, uuids)

	expectedResponse := drivers.DriverStatsResponse{
		Data: &drivers.DriverStatsData{
			Intervals: []*drivers.Interval{
				{
					From: "2023-10-01T00:00:00Z",
					To:   "2023-11-01T00:00:00Z",
					Stats: &drivers.StatsSummary{
						Energy: &drivers.Energy{
							Home: &drivers.Breakdown{
								Grid:       ptr.To(0.0),
								Generation: ptr.To(0.0),
								Total:      0,
							},
							Private: &drivers.Breakdown{},
							Public: &drivers.Breakdown{
								Total: 7.7,
							},
							Total: &drivers.Breakdown{
								Total: 7.7,
							},
						},
						Cost: &drivers.Cost{
							Home:    []*drivers.MoneyInt64{{Amount: 0, Currency: "GBP"}},
							Private: []*drivers.MoneyInt64{{Amount: 0, Currency: "GBP"}},
							Public:  []*drivers.MoneyInt64{{Amount: 300, Currency: "GBP"}},
							Total:   []*drivers.MoneyInt64{{Amount: 300, Currency: "GBP"}},
						},
						Duration: &drivers.Duration{
							Home:    0,
							Private: 0,
							Public:  267,
							Total:   267,
						},
					},
				},
				{
					From: "2023-11-01T00:00:00Z",
					To:   "2023-12-01T00:00:00Z",
					Stats: &drivers.StatsSummary{
						Energy: &drivers.Energy{
							Home: &drivers.Breakdown{
								Grid:       ptr.To(4.4),
								Generation: ptr.To(1.1),
								Total:      5.5,
							},
							Private: &drivers.Breakdown{
								Total: 6.6,
							},
							Public: &drivers.Breakdown{},
							Total: &drivers.Breakdown{
								Total: 12.1,
							},
						},
						Cost: &drivers.Cost{
							Home:    []*drivers.MoneyInt64{{Amount: 215, Currency: "GBP"}},
							Private: []*drivers.MoneyInt64{{Amount: 200, Currency: "GBP"}},
							Public:  []*drivers.MoneyInt64{{Amount: 0, Currency: "GBP"}},
							Total:   []*drivers.MoneyInt64{{Amount: 415, Currency: "GBP"}},
						},
						Duration: &drivers.Duration{
							Home:    133,
							Private: 180,
							Public:  0,
							Total:   313,
						},
					},
				},
			},
			Summary: &drivers.StatsSummary{
				Energy: &drivers.Energy{
					Home: &drivers.Breakdown{
						Grid:       ptr.To(4.4),
						Generation: ptr.To(1.1),
						Total:      5.5,
					},
					Private: &drivers.Breakdown{
						Total: 6.6,
					},
					Public: &drivers.Breakdown{
						Total: 7.7,
					},
					Total: &drivers.Breakdown{
						Total: 19.8,
					},
				},
				Cost: &drivers.Cost{
					Home:    []*drivers.MoneyInt64{{Amount: 215, Currency: "GBP"}},
					Private: []*drivers.MoneyInt64{{Amount: 200, Currency: "GBP"}},
					Public:  []*drivers.MoneyInt64{{Amount: 300, Currency: "GBP"}},
					Total:   []*drivers.MoneyInt64{{Amount: 715, Currency: "GBP"}},
				},
				Duration: &drivers.Duration{
					Home:    133,
					Private: 180,
					Public:  267,
					Total:   580,
				},
			}},
		Meta: (*drivers.Meta)(httphelpers.PopulateMetaParams(drivers.RetrieveStatsPayload{
			DriverID: driverID.String(),
			From:     fromDate,
			To:       toDate,
			Interval: &interval,
		})),
	}

	sendRequestAndCompareResponse(
		t,
		fmt.Sprintf("%s/drivers/%s/stats?from=%s&to=%s&interval=%s", setup.BaseURL(t, host), driverID, fromDate, toDate, interval),
		http.MethodGet,
		http.NoBody,
		expectedResponse,
	)
}

type rdcsFixture struct {
	name             string
	expectedResponse drivers.DriversChargesResponse
	driver           *random.Driver
	chargers         []*random.Charger
	location         int64
	events           []eventstore.Event
	aggregateIDs     []uuid.UUID
	timeout          time.Duration
}

func newRetrieveDriversChargesFixture(t *testing.T, name string, count uint, timeout time.Duration) rdcsFixture {
	t.Helper()
	driver := random.Driver{
		DriverID:     ptr.To(uuid.New()),
		AuthoriserID: fixtures.Sequence.Get(),
	}

	seed := ptr.To(random.ChargeParamsSeed{
		// need to set timezone here to know its value, as not available to event data otherwise
		ChargerTimezone: ptr.To(random.TimezoneString()),
		// The Cost() charge getter function gets settlement values for private and public chargers only, home chargers are special
		ChargerType: ptr.To(sqlc.AccessPublic),
	})

	expectedResponse, events, chargers := random.TimesChargeWithEvents(t, count, driver, seed)
	aggregateIDs := make([]uuid.UUID, expectedResponse.Data.Count)
	for i := 0; i < expectedResponse.Data.Count; i++ {
		aggregateIDs[i] = uuid.MustParse(expectedResponse.Data.Charges[i].ID)
	}

	return rdcsFixture{
		name:             name,
		driver:           &driver,
		chargers:         chargers,
		expectedResponse: expectedResponse,
		events:           events,
		aggregateIDs:     aggregateIDs,
		timeout:          timeout * time.Second,
	}
}

func checkResponseOnDriversCharges(t *testing.T, driverID *uuid.UUID, expected *drivers.DriversChargesResponse) (*sharedtest.AsyncResult[drivers.DriversChargesResponse], bool) {
	t.Helper()

	from, to := fixtures.GenerateDateRange()
	requestURL := fmt.Sprintf("%s/drivers/%s/charges?from=%s&to=%s", setup.BaseURL(t, host), driverID, from.Format(time.DateOnly), to.Format(time.DateOnly))

	response := sendRequest(t, http.MethodGet, requestURL, http.NoBody)
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return nil, false
	}

	var responseStruct drivers.DriversChargesResponse
	bodyBytes, err := io.ReadAll(response.Body)
	require.NoError(t, err)
	require.NoError(t, json.Unmarshal(bodyBytes, &responseStruct))

	if RetrieveChargesResponseComparison(t, expected, &responseStruct)() {
		return nil, true
	}

	return ptr.To(sharedtest.NewAsyncResult(*expected, responseStruct)), false
}

func RetrieveChargesResponseComparison(t *testing.T, expected, actual *drivers.DriversChargesResponse) assert.Comparison {
	t.Helper()

	return func() (success bool) {
		if expected == nil && actual == nil {
			return true
		}
		if expected == nil || actual == nil {
			return false
		}

		if expected.Data.Count != actual.Data.Count {
			return false
		}

		expectedData := expected.Data.Charges
		actualData := actual.Data.Charges

		if len(expectedData) != len(actualData) {
			return false
		}

		for _, value := range expectedData {
			if !contains(value, actualData) {
				return false
			}
		}
		return true
	}
}

func contains(charge *drivers.Charge, charges []*drivers.Charge) bool {
	for _, matchValue := range charges {
		if assert.ObjectsAreEqual(matchValue, charge) {
			return true
		}
	}
	return false
}
