package full

import (
	"context"
	"experience/apps/data-platform/api-e2e/pkg/infra"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/stretchr/testify/assert"

	"github.com/google/uuid"
)

func TestRetrieveLinkedCharges(t *testing.T) {
	tests := []rdcsFixture{
		newRetrieveDriversChargesFixture(t, "response with single charge", 1, 30),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			siteNames := make([]string, len(tt.chargers))
			for i, charger := range tt.chargers {
				if charger.SiteName != nil {
					siteNames[i] = *charger.SiteName
				}
			}
			ctx := context.Background()

			defer infra.CleanupEventsData(ctx, t, testQueries, tt.aggregateIDs)
			defer infra.CleanupSiteStatsProjectionDataByName(ctx, t, testQueries, siteNames)
			driverToLink := uuid.New()
			defer seeding.Drivers(t, testQueries, tt.driver, tt.chargers, &driverToLink)()

			for _, event := range tt.events {
				err := seeding.Event(ctx, t, chargeNotificationSQSClient, event)
				assert.NoError(t, err)
			}

			charger := tt.chargers[gofakeit.IntRange(0, len(tt.chargers)-1)]
			linkUserToCharger(t, setup.BaseURL(t, host), driverToLink.String(), charger.ID)

			from, to := fixtures.GenerateDateRange()
			fromParam := from.Format(time.DateOnly)
			toParam := to.Format(time.DateOnly)

			tt.expectedResponse.Meta.Params = map[string]any{
				"From":     fromParam,
				"To":       toParam,
				"DriverID": tt.driver.DriverID.String(),
			}
			sendRequestAndCompareResponse(
				t,
				fmt.Sprintf("%s/drivers/%s/charges?from=%s&to=%s", setup.BaseURL(t, host), tt.driver.DriverID.String(), fromParam, toParam),
				http.MethodGet,
				http.NoBody,
				tt.expectedResponse,
			)
		})
	}
}
