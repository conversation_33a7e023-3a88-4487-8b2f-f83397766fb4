{"environment": "local", "migrate_datasource": {"host": "localhost", "port": 5432, "name": "data_platform", "username": "postgres", "password": "secret"}, "service_datasource": {"read": {"host": "localhost", "port": 5432, "name": "data_platform", "username": "xdp_api"}, "write": {"host": "localhost", "port": 5432, "name": "data_platform", "username": "xdp_api"}}, "sqs": {"charge_events_url": "http://localhost:4566/000000000000/charge-events", "charge_commands_url": "http://localhost:4566/000000000000/charge-commands"}}