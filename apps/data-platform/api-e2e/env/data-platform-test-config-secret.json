{"environment": "local", "service_datasource": {"read": {"host": "postgres_data_platform", "port": 5432, "name": "data_platform", "username": "xdp_test"}, "write": {"host": "postgres_data_platform", "port": 5432, "name": "data_platform", "username": "xdp_test"}}, "podadmin_datasource": {"read": {"host": "<PERSON><PERSON><PERSON>", "port": 3306, "name": "podpoint", "username": "user", "password": "password"}, "write": {"host": "<PERSON><PERSON><PERSON>", "port": 3306, "name": "podpoint", "username": "user", "password": "password"}}, "sqs": {"charge_events_url": "http://localhost:4566/000000000000/charge-events", "charge_commands_url": "http://localhost:4566/000000000000/charge-commands", "charge_completed_url": "http://localhost:4566/000000000000/event-bridge-target-queue"}, "openexchangerates_app_id": "dummy_app_id", "transactions_api_host": "localhost"}